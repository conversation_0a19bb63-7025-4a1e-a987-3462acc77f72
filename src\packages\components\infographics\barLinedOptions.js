import { Colors } from './Colors'

const timeFormat = d3.timeFormat('%d-%m-%Y')

export const barLinedOptions = (dc, bar, dimension, dx, ylabel) => {
  bar
    .gap(20)
    .margins({ left: 80, top: 20, right: 10, bottom: 20 })
    .brushOn(false)
    .clipPadding(10)
    .title(function (d) {
      return d.key + '[' + this.layer + ']: ' + d.value[this.layer]
    })
    .legend(dc.legend())

  bar.xAxis().tickFormat(d3.timeFormat('%b,%Y'))

  return bar
}
