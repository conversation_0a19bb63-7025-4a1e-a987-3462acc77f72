`/* eslint-disable */`
<template>
  <div class="col-md-11" style="margin: 0 auto">
    <div
      class="row align-items-center py-4"
      style="padding-right:0px; padding-left:0"
    >
      <div v-if="jwtuser !== undefined" class="col-lg-6 col-6">
        <h6 class="h2 d-inline-block mb-0">Disaster</h6>
        <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
          <ol class="breadcrumb breadcrumb-links">
            <li class="breadcrumb-item">
              <router-link to="/districtcommissioner/dashboard">
                <i class="fas fa-home"></i>
              </router-link>
            </li>
            <li class="breadcrumb-item">
              <a href="#">Report</a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
              Detailed
            </li>
          </ol>
        </nav>
      </div>

      <!-- <div class="col-lg-6 col-6 text-right">
          <downloadexcel
            class="btn"
            :data="Array.from(download)"
            :fields="filters"
            name="DisasterData.csv"
            type="csv"
          >
            <base-button size="sm" type="neutral">
              <i class="text-primary ni ni-cloud-download-95"></i> EXPORT TO CSV
            </base-button>
          </downloadexcel>
          <base-button
            size="sm"
            type="neutral"
            @click="printdiv('section-to-print')"
          >
            <span class="btn-inner--icon"> -->
      <!--  <i class="ni ni-fat-add"></i> -->
      <!-- </span>
            <span class="btn-inner--text">Print</span>
          </base-button>
        </div> -->

      <div v-if="jwtuser !== undefined" class="col-lg-6 col-6 text-right pr-3">
        <base-dropdown
            title-classes="btn btn-sm btn-primary mr-0"
            menu-on-right
            :has-toggle="false"
          >
            <a slot="title" class="dropdown-toggle" type="neutral" size="sm">
               Options
            </a>

            <downloadexcel
              class="dropdown-item"
              :data="Array.from(download)"
              name="DisasterData.csv"
              type="csv"
              style="color:#26b6b2">
              EXPORT TO CSV
            </downloadexcel>

            <a
              class="dropdown-item"
              type="neutral"
              @click="printdiv('section-to-print')"
              >
              <span class="btn-inner--icon">
                <!--  <i class="ni ni-fat-add"></i> -->
              </span>
              <span class="btn-inner--text" style="color:#26b6b2">Print</span>
            </a>
            <a
              class="dropdown-item"
              type="neutral"
              @click="downloadPDF()"
              >
              <span class="btn-inner--icon">
                <!--  <i class="ni ni-fat-add"></i> -->
              </span>
              <span class="btn-inner--text" style="color:#26b6b2">Download PDF</span>
            </a>

          </base-dropdown>

          <base-button  class="ml-2" size="sm" type="neutral" @click="goBack">
            Back
          </base-button>
      </div>
    </div>

    <div id="section-to-print">
      <card
        class="no-border-card"
        body-classes="px-0 pb-1"
        footer-classes="pb-2"
      >
        <div>
          <div class="component-example__container">
            <DetailedReportHeader />
            <DetailedReportBody />

            <table class="col-md-11">
              <!-- Response heading row -->
              <tr>
                <td colspan="12" style="text-align: center;">
                  <h3><b>Response Provided</b></h3>
                </td>
              </tr>

              <!-- Response text row -->
              <tr>
                <td colspan="12" style="text-align: center;">
                  {{ dinrFormsData.response || "No response provided" }}
                </td>
              </tr>
            </table>
            <table
              class="col-md-11"
              v-if="numberOfTAs > 0"
              width="100%"
              style="text-weight: bold; text-size: 120%"
            >
              <tr>
                <td width="50%">
                  <strong>
                    Checked and Verified by
                    <span v-if="this.officerSignature">
                      {{ this.officerSignature.role }}
                    </span>
                  </strong>
                </td>
                <td>
                  <strong>
                    Approved by
                    <span v-if="this.dcSignature">
                      {{ this.dcSignature.role }}
                    </span>
                  </strong>
                </td>
              </tr>
              <tr>
                <td width="50%">
                  <strong>Name :</strong>
                  <span v-if="this.officerSignature">
                    {{
                      (this.officerSignature.signatory_fname
                        ? this.officerSignature.signatory_fname
                        : "") +
                        " " +
                        (this.officerSignature.signatory_lname
                          ? this.officerSignature.signatory_lname
                          : "")
                    }}
                  </span>
                </td>
                <td width="50%">
                  <strong>Name :</strong>
                  <span v-if="this.dcSignature">
                    {{
                      (this.dcSignature.signatory_fname
                        ? this.dcSignature.signatory_fname
                        : "") +
                        " " +
                        (this.dcSignature.signatory_lname
                          ? this.dcSignature.signatory_lname
                          : "")
                    }}
                  </span>
                </td>
              </tr>
              <tr>
                <td width="50%">
                  <strong>Signature :</strong>
                  <img
                    v-if="this.officerSignature && officerpath"
                    :src="officerpath"
                    style="box-shadow: 0 0 2px 2px white inset"
                    class="mx-2"
                    width="100"
                    height="30"
                  />
                </td>
                <td width="50%">
                  <strong>Signature :</strong>
                  <img
                    v-if="this.dcSignature && dcpath"
                    :src="dcpath"
                    style="box-shadow: 0 0 2px 2px white inset"
                    class="mx-2"
                    width="100"
                    height="30"
                  />
                </td>
              </tr>

              <tr>
                <td width="50%">
                  <strong>Date :</strong>
                  <span v-if="this.officerSignature">
                    {{ formatdate(this.officerSignature.createdon) }}
                  </span>
                </td>
                <td width="50%">
                  <strong>Date :</strong>
                  <span v-if="this.dcSignature">
                    {{
                      dinrFormsData.createdon
                        ? formatdate(dinrFormsData.createdon)
                        : ""
                    }}
                  </span>
                </td>
              </tr>
              <tr >
                <td colspan="12" style="text-align: center;">
                  <strong>
                    Acted on By DoDMA
                  </strong>

                </td>


              </tr>
              <tr><td>
                <strong>Date:</strong>
                <span v-if="dinrFormsData.actedonDate">
                  {{ formatdate(dinrFormsData.actedonDate) }}
                </span>

              </td>
              <td>
                <strong>Acted By:</strong> {{ dinrFormsData.actedBy }}
              </td>
              </tr>
            </table>

            <br />
            <table
              class="col-md-11"
              v-if="hasPictures"
              width="100%"
              style="text-weight: bold; text-size: 120%"
            >
              <tr>
                <td style="text-align: center">
                  <strong>Disaster Photos</strong>
                </td>
              </tr>
              <tr>
                <td colspan="2">
                  <div class="row">
                    <div class="col-md-12">
                      <div class="row" style="">
                        <div class="row">
                          <!--images  -->

                          <div
                            class="image-wrapper col-md-4"
                            v-for="(image, i) in dinrFormsData.disasterImages"
                            :key="i"
                          >
                            <div class="inner">
                              <img
                                :src="
                                  disasterImage(
                                    dinrFormsData._id,
                                    image.filename
                                  )
                                "
                                style="margin: 8px; height: 340px; width: 340px"
                                class="img-responsive img-thumbnail"
                              />
                            </div>
                            <div class="caption" style="margin-left: 12px">
                              {{ image.caption }}
                            </div>
                          </div>
                          <!-- endImage -->
                        </div>
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
            </table>

            <br />
            <br />
            <br />
            <p v-if="numberOfTAs <= 0">
              <center>
                <h3>
                  <strong>NO DATA</strong>
                </h3>
              </center>
            </p>
          </div>
        </div>
      </card>
    </div>
  </div>
</template>

<script>
import { auth } from "@/api/auth";
import { MongoReports } from "../api/MongoReports";
import downloadexcel from "vue-json-excel";
import moment from "moment";
import checkEmails from "../../../api/pass";
import { accounts } from "../../admin/api/accounts/accounts";
import swal from "sweetalert2";
import { Relay } from "../api/relay";
import html2pdf from "html2pdf.js"
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
var QRCode = require("qrcode");
import { signatures } from "../api/accounts/signatures";
import dateformat from "dateformat";
import { mapGetters, mapActions } from "vuex";
import { dinrforms } from "../../districtmanager/api/forms/dinrforms.js";
import { logs } from "../api/logs";
import DetailedReportHeader from "../../dashboards/components/detailedReportHeader.vue";
import DetailedReportBody from "../../dashboards/components/detailedReportBody.vue";

export default {
  name: "ManagerDetailed",
  components: {
    downloadexcel,
    DetailedReportHeader,
    DetailedReportBody
  },
  data() {
    return {
      usersArr: null,
      jwtuser: null,
      signatureText: "",
      emailValid: null,
      signature: {},
      dcSignature: {},
      officerSignature: {},
      officerPath: "",
      dcPath: "",
      numberOfTAs: "",
      dinrFormsData: {},
      villagesArray: [],
      campsArray: [],
      bufferDraFormsData: [],
      TAarray: [],
      draFormsData: [],
      download: [],
      downloadData: [],
      otherData: [],
      vhhousesvalue: "",
      filters: {
        Disaster: "all.dinrform.disaster",
        Country: "all.dinrform.district.admin0_name_en",
        "Country PCODE": "all.dinrform.district.admin0_pcode",
        District: "all.dinrform.district.admin2_name_en",
        Region: "all.dinrform.district.admin1_name_en",
        "TA PCODE": "all.admin3.admin3Pcode",
        TA: "all.admin3.admin3Name_en",
        "GVHS affected": "all.gvhsAffected",

        // "GVH affected": "gvhs.name",

        "Date of assessment ACPC": "all.dinrform.doaAcpc",
        "Date of assessment DCPC": "all.dinrform.doaDcpc",
        "Date of Disaster from": "all.dinrform.dodFrom",
        "Date of Disaster to": "all.dinrform.dodTo",
        "Date of submission": "all.dinrform.submited",

        //shelter

        "Houses partly damaged (shelter)": "all.sectors.shelter.partly_damaged",
        "Houses underwater (shelter)": "all.sectors.shelter.under_water",
        "Houses completely (shelter)": "all.sectors.shelter.completely_damaged",

        "number of males without shelter(shelter)":
          "all.shelter_without_shelter_male",
        "number of females without shelter (shelter)":
          "all.shelter_without_shelter_female",

        "number of female HH affected (shelter)": "all.shelter_fhh_affected",
        "number of male HH affected(shelter)": "all.shelter_mhh_affected",

        "Total Households affected (shelter)": "all.shelter_affected_hh",

        "number of injured females(shelter)":
          "all.shelter_people_injured_female",
        "number of injured males in category (shelter)":
          "all.shelter_people_injured_female",

        "Total people injured (shelter)": "all.shelter_injured",

        "number of dead females (shelter)": "all.shelter_people_dead_female",
        "number of dead males (shelter)": "all.shelter_people_dead_male",

        "Total people dead (shelter)": "all.shelter_dead",

        "Urgent needed (shelter)": "all.sectors.shelter.urgent_response_needed",
        "General response needed (shelter)":
          "all.sectors.shelter.response_needed",

        //displaced

        "number of displaced male Households (displaced)":
          "all.PeopleAffectedrows_male",
        "number of displaced females Households (displaced)":
          "all.PeopleAffectedrows_female",

        "Total displaced Households (shelter)": "all.displaced_hh",

        "number of male HH accomodated (displaced)":
          "all.displaced_households_accommodated_male",
        "number of female HH accomodated (displaced)":
          "all.displaced_households_accommodated_female",

        "number of males disaggregated (displaced)":
          "all.displaced_disagregated_male",
        "number of females disaggregated (displaced)":
          "all.displaced_disagregated_female",

        "number of males accomodated (displaced)":
          "all.displaced_individuals_accommodated_male",
        "number of females accomodated (displaced)":
          "all.displaced_individuals_accommodated_female",

        "Urgent response needed (displaced)":
          "all.sectors.displaced.urgent_response_needed",
        "general response needed (displaced)":
          "all.sectors.displaced.response_needed",

        //wash

        "FHH with safe water (wash)": "all.sectors.wash.with_safe_water_fhh",
        "MHH with safe water (wash)": "all.sectors.wash.with_safe_water_mhh",
        "FHH with toilet access (wash)":
          "all.sectors.wash.access_to_toilets_fhh",
        "MHH with toilet access (wash)":
          "all.sectors.wash.access_to_toilets_mhh",
        "FHH risking contamination (wash)":
          "all.sectors.wash.risk_contamination_fhh",
        "MHH risking contamination  (wash)":
          "all.sectors.wash.risk_contamination_mhh",

        "Urgent response needed (wash)":
          "all.sectors.wash.urgent_response_needed",

        "general response needed (wash)": "all.sectors.wash.response_needed",

        //health

        "number of facilities partially functioning (health)":
          "all.health_partially_functioning",
        "number of  facilities on verge of closing (health)":
          "all.health_verge_of_closing",
        "number  facilities closed (health)": "all.health_closed",

        "state of medical supply availability (health)":
          "all.medical_supply_availability",

        "state of health personnel availability (health)":
          "all.health_personel_availability",
        "Urgent response needed (health)":
          "all.sectors.health.urgent_response_needed",

        "general response needed (health)":
          "all.sectors.health.response_needed",

        //food

        "is food available? (food)": "all.is_food_available_food",

        "No. of FHH with 1-2 month Food Availability (food)":
          "all.food_1_2_months",
        "No. of FHH with less than 1 month Food Availability (food)":
          "all.food_less_1_month",

        "No. of FHH with more than 2 months Food Availability (food)":
          "all.food_2_months",

        "No. of FHH who lost Food Stock (food)": "all.food_stock_lost",

        "No. of MHH with 1-2 month Food Availability (food)":
          "all.food_1_2_months_male",
        "No. of MHH with less than 1 month Food Availability (food)":
          "all.food_less_1_month_male",

        "No. of MHH with more than 2 months Food Availability (food)":
          "all.food_2_months_male",

        "No. of MHH who lost Food Stock (food)": "all.food_stock_lost_male",

        "Urgent response needed (food)":
          "all.sectors.food.urgent_response_needed",
        "general response needed (food)": "all.sectors.food.response_needed",

        //logistics

        "state of access to main roads (logistics)": "all.road_access",

        "Urgent response needed (logistics)":
          "all.sectors.logistics.urgent_response_needed",

        "general response needed (logistics)":
          "all.sectors.logistics.response_needed",

        //agriculture
        "food items damaged (KGs) (agriculture)": "all.food_item_damage",
        "number of crop hectares submerged  (agriculture)":
          "all.hectares_submerged",
        "number crop hectares washed off  (agriculture)":
          "all.hectares_washed_away",
        "number of households whose crops are impacted (agriculture)":
          "all.impact_on_crops_hh_affected",
        "number of crop hectares damaged in affected households  (agriculture)":
          "all.impact_on_crops_hectares_damaged",

        "hh affected per impacted livestock  (agriculture)":
          "all.impact_on_livestock_hh",
        "number of impacted livestock  (agriculture)":
          "all.impact_on_livestock_la",

        "Urgent response needed (agriculture)":
          "all.sectors.agriculture.response_needed",

        "general response needed (agriculture)":
          "all.sectors.agriculture.response_needed",

        //protection

        "impacted females (protection)":
          "all.impact_on_vulnerable_persons_females",

        "impacted males (protection)": "all.impact_on_vulnerable_persons_males",

        "Urgent response needed (protection)":
          "all.sectors.protection.urgent_response_needed",

        "general response needed (protection)":
          "all.sectors.protection.response_needed",

        //nutrition

        "number of affected males (nutrition)":
          "all.nutrition_affected_pop_male",
        "number of affected females (nutrition)":
          "all.nutrition_affected_pop_female",

        "general response needed (nutrition)":
          "all.sectors.nutrition.response_needed",

        //education

        "number of school buildings functioning (education)":
          "all.education_building_functioning",
        "number of school buildings underwater (education)":
          "all.education_underwater",
        "number of school buildings completely damaged (education)":
          "all.education_completely_damaged",

        "number of school buildings partially functioning (education)":
          "all.education_building_partly_functioning",

        "number of school buildings closed  (education)":
          "all.education_closed_buildings",

        "males of out school (education)": "all.education_males_out_of_school",
        "females of out school (education)":
          "all.education_females_out_of_school",

        //livelihoods

        "number severely affected (livelihoods)":
          "all.livelihoods_severely_affected",

        "number slightly affected (livelihoods)":
          "all.livelihoods_slightly_affected",

        "Urgent response needed (livelihoods)":
          "all.sectors.livelihoods.response_needed",

        "Urgent response needed (environment)":
          "all.sectors.environment.urgent_response_needed",

        "general response needed (livelihoods)":
          "all.sectors.livelihoods.response_needed",

        "general response needed (environment)":
          "all.sectors.environment.response_needed"
      }
    };
  },

  computeLogo() {
    return "../../../../static/logo.png";
  },

  updated() {
    delete this.signatureText.signature;
    delete this.signatureText.status;
    var opts = {
      errorCorrectionLevel: "H",
      type: "image/jpeg",
      quality: 1,
      margin: 1
    };

    QRCode.toDataURL(
      JSON.stringify(this.signatureText || "not electronically signed by DC"),
      opts,
      function(err, url) {
        if (err) throw err;

        var img = document.getElementById("qr-code");
        img.src = url;
      }
    );
  },

  computed: {
    ...mapGetters({
      getDinrById: "dinrs/getById"
    }),
    ...mapGetters({
      getDraById: "dras/getById"
    }),
    officerpath: function() {
      if (this.officerSignature && this.officerSignature.signature)
        return (
          process.env.VUE_APP_ENGINE_URL +
          "/" +
          this.officerSignature.signature.replace("\\", "/")
        );
    },
    dcpath: function() {
      if (this.dcSignature && this.dcSignature.signature)
        return (
          process.env.VUE_APP_ENGINE_URL +
          "/" +
          this.dcSignature.signature.replace("\\", "/")
        );
    },
    hasPictures: function() {
      if ("disasterImages" in this.dinrFormsData) {
        return this.dinrFormsData.disasterImages.length > 0;
      }
      return false;
    }
  },
  beforeDestroy() {},
  async mounted() {
    await this.getDinrsAction();
    await this.getDrasAction();
    //Get DINR Form

    let response = await this.getDinrById(
      this.$route.params.uuid /* , this.$session.get("jwt") */
    );

    await signatures.get(this.$session.get("jwt")).then(response => {
      this.jwtuser = this.$session.get("jwtuser");
      //this.signature = this.dinrFormsData.approvalMetadata.signature[1];
    });
    try {
      if (this.jwtuser == undefined) {
        let token = null;
        let user = checkEmails.userPass;
        await auth.login(user).then(response => {
          token = response.id;
        });
        await accounts.get(token).then(response => {
          this.usersArr = response.data.filter(u => u.ackEmail === "1").map((item, index, arr) => {
  return item.email
});
        });
        let email = this.$route.query.email;
        let obj = {
          user: { user: email },
          type: "",
          comment: `The user ${email} accessed a report`,
          metadata: {}
        };
        logs.create({ email });

       // this.emailValid = checkEmails.checkEmails.includes(email);
        this.emailValid = this.usersArr.includes(email);
      }
    } catch (error) {}

    this.dinrFormsData = {};
    this.dinrFormsData = response;
    this.signatureText = this.dinrFormsData.approvalMetadata
      ? [...this.dinrFormsData.approvalMetadata.signature]
      : "";
    this.signature = this.dinrFormsData.approvalMetadata
      ? [...this.dinrFormsData.approvalMetadata.signature]
      : [];

    this.dcSignature = this.signature.filter(
      item => item && item.role === "district commissioner"
    )[0];
    this.officerSignature = this.signature.filter(
      item => item && item.role !== "district commissioner"
    )[0];

    //console.log(this.$route);

    //initialise number TAs in DNIR
    this.numberOfTAs = 0;

    let count = 0;
    //Get DRA Form

    response = this.getDraById(
      this.$route.params.uuid /* , this.$session.get("jwt") */
    );
    this.bufferDraFormsData = response;
    this.draFormsData = [];
    this.downloadData.dinrform = {};

    this.bufferDraFormsData.forEach(item => {
      this.processExcel(item);

      this.TAarray.push(item.admin3.admin3Name_en.trim());
      for (let i in item.villages) {
        this.villagesArray.push(item.villages[i].name);
      }
      for (let i in item.camps) {
        this.campsArray.push(item.camps[i].name);
      }

      this.TAarray = this.TAarray.sort();

      this.villagesArray = this.villagesArray
        .join()
        .replace(/ /g, "")
        .split(",")
        .sort()
        .filter(function(item, pos, self) {
          return self.indexOf(item) == pos;
        });
      this.campsArray = this.campsArray
        .join()
        .replace(/ /g, "")
        .split(",")
        .sort()
        .filter(function(item, pos, self) {
          return self.indexOf(item) == pos;
        });
    });
  },

  methods: {
    goBack() {
      this.$router.go(-1);
    },
    disasterImage(disasterId, filename) {
      const resource = process.env.VUE_APP_ENGINE_URL + "/forms/dinr";

      return `${resource}/${disasterId}/images/${filename}`;
    },

    ...mapActions("dinrs", {
      getDinrsAction: "get"
    }),
    ...mapActions("dras", {
      getDrasAction: "get"
    }),
    downloadPDF() {
      var element = document.getElementById('section-to-print');
      var disasterdistrict = document.getElementById('district').innerHTML;
      var disastertype = document.getElementById('disastertype').innerHTML;
      var disasterdate = document.getElementById('disasterstart').innerHTML;


      var opt = {
            margin: 0,
            filename: disasterdate + "_" + disasterdistrict + "_"+ disastertype,
            // pagebreak: { mode: ['avoid-all', 'css', 'img'] },
            image: { type: 'jpeg', quality: 1 },
            html2canvas:  { scale: 2, logging: true, dpi: 192, letterRendering: true },
            jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
        };

      html2pdf().set(opt).from(element).save();
    },
    formatedDatePlusOne(data) {
      const cur_date = this.addDays(data, 1);
      const finalDate = dateformat(cur_date, "dd-mm-yyyy");
      return finalDate;
    },
    async sendAckEmail() {
      let email = {
        district: this.$route.query.district,
        disaster: this.$route.query.disaster,
        status: "confirmation"
      };
      let ping = await Relay.ping();
      if (ping == "Relay is running!") {
        Relay.sendMail(email).then(res => {
          swal.fire({
            title: "Email Sent",
            text:
              "The report receipt acknowledgement email has been sent successfully",
            type: "success",
            animation: false
          });
        });
      } else {
        swal.fire({
          title: "Sending Failed",
          text: "The relay service is unavailable",
          type: "warning",
          animation: false
        });
      }
    },
    handleSignOut() {
      this.$session.destroy();
      this.$router.push({ name: "Login", params: { session: "lost" } });
    },
    addDays(date, days) {
      var result = new Date(date);
      result.setDate(result.getDate() + days);
      return result;
    },
    numberWithCommas(number) {
      try {
        var parts = number.toString().split(".");
        parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        return parts.join(".");
      } catch (e) {}
    },
    comparator(key, order = "asc") {
      return function innerSort(a, b) {
        if (!a.hasOwnProperty(key) || !b.hasOwnProperty(key)) {
          return 0;
        }

        const varA = typeof a[key] === "string" ? a[key].toUpperCase() : a[key];
        const varB = typeof b[key] === "string" ? b[key].toUpperCase() : b[key];

        let comparison = 0;
        if (varA > varB) {
          comparison = 1;
        } else if (varA < varB) {
          comparison = -1;
        }
        return order === "desc" ? comparison * -1 : comparison;
      };
    },

    sortArrayByKey(arrayName) {
      return arrayName.slice().sort(this.comparator("name", "asc"));
    },
    printdiv(printpage) {
      var headstr = "<html><head><title></title></head><body>";
      var footstr = "</body>";
      var newstr = document.all.item(printpage).innerHTML;
      var oldstr = document.body.innerHTML;
      var disasterdistrict = document.getElementById("district").innerHTML;
      var disastertype = document.getElementById("disastertype").innerHTML;
      var disasterdate = document.getElementById("disasterstart").innerHTML;

      document.body.innerHTML = headstr + newstr + footstr;
      document.title =
        disasterdate + "_" + disasterdistrict + "_" + disastertype;
      window.print();
      document.body.innerHTML = oldstr;
      location.reload();
      return false;
    },
    dateFormate(dateData) {
      const cur_date = dateData;
      const formattedDate = moment(cur_date).format("DD/MM/YYYY");

      return formattedDate;
    },
    formatdate(data) {
      const cur_date = data;
      const formattedDate = moment(cur_date).format("DD/MM/YYYY");
      return formattedDate;
    },

    formatedDate(data) {
      const cur_date = data;
      const formDate = moment(cur_date).format("YYYY/MM/DD");

      return formDate;
    },

    sumArrayValues(item, sector, array, key) {
      if (typeof item["sectors"][sector][array] !== "undefined") {
        return item["sectors"][sector][array]
          .map(function(item) {
            return parseInt(item[key]) ? parseInt(item[key]) : 0;
          })
          .reduce((sum, value) => sum + value, 0);
      } else if (typeof item[sector][array] === "undefined") {
        return 0;
      }
    },

    returnFieldvalue(item, sector, array, key) {
      if (typeof item["sectors"][sector][array] !== "undefined") {
        return item["sectors"][sector][array][0][key];
      } else if (typeof item["sectors"][sector][array] === "undefined") {
        return "NULL";
      }
    },

    returnFieldItemvalue(item, sector, array, key) {
      if (typeof item["sectors"][sector][array] !== "undefined") {
        for (let i in item["sectors"][sector][array]) {
          if (item["sectors"][sector][array][i].name === key) {
            return item["sectors"][sector][array][i].status;
          } else if (
            typeof item["sectors"][sector][array][i].key === "undefined"
          ) {
            return "NULL";
          }
        }
      }
    },

    sumArrayValuesNoAggregate(item, sector, array, key, filterBy, filterValue) {
      if (typeof item["sectors"][sector][array] !== "undefined") {
        return item["sectors"][sector][array]
          .filter(item => item[filterBy] === filterValue)
          .map(function(item) {
            return parseInt(item[key]) ? parseInt(item[key]) : 0;
          })
          .reduce((sum, value) => sum + value, 0);
      } else if (typeof item[sector][array] === "undefined") {
        return 0;
      }
    },

    processExcel(item) {
      this.downloadData.all = item;

      this.draFormsData.push(item);

      for (let i = 0; i < this.draFormsData.length; i++) {
        for (let a = 0; a < this.draFormsData[i].villages.length; a++) {
          this.villagesArray.push(this.draFormsData[i].villages[a].name);
        }

        for (let a = 0; a < this.draFormsData[i].camps.length; a++) {
          this.campsArray.push(this.draFormsData[i].camps[a].name);
        }
      }

      this.numberOfTAs++;

      let Gvharray = [];

      this.downloadData.dinrform = this.dinrFormsData;

      for (let i = 0; i < item.gvhs.length; i++) {
        let GVHname = item.gvhs[i].name;

        Gvharray.push(GVHname);
      }

      this.downloadData.all.dinrform = this.downloadData.dinrform;

      try {
        this.downloadData.all.dinrform.doaAcpc = this.formatedDate(
          this.downloadData.dinrform.doaAcpc
        );
      } catch (error) {
        this.downloadData.all.dinrform.doaAcpc = "NULL";
      }

      try {
        this.downloadData.all.dinrform.submited = this.formatedDatePlusOne(
          this.downloadData.all.dinrform.submitted_at
        );
      } catch (error) {
        this.downloadData.all.dinrform.submited = "";
      }

      try {
        this.downloadData.all.dinrform.doaDcpc = this.formatedDate(
          this.downloadData.dinrform.doaDcpc
        );
      } catch (error) {
        this.downloadData.all.dinrform.doaDcpc = "NULL";
      }

      try {
        this.downloadData.all.dinrform.dodFrom = this.formatedDate(
          this.downloadData.dinrform.dodFrom
        );
      } catch (error) {
        this.downloadData.all.dinrform.dodFrom = "NULL";
      }

      try {
        this.downloadData.all.dinrform.dodTo = this.formatedDate(
          this.downloadData.dinrform.dodTo
        );
      } catch (error) {
        this.downloadData.all.dinrform.dodTo = "NULL";
      }

      try {
        this.downloadData.all.gvhsAffected = Gvharray.join();
      } catch (error) {
        this.downloadData.all.gvhsAffected = "NULL";
      }

      try {
        this.downloadData.all.is_food_available_food = this.returnFieldvalue(
          item,
          "food",
          "food_availability",
          "foodavailable"
        );
      } catch (error) {
        this.downloadData.all.is_food_available_food = "NULL";
      }

      try {
        this.downloadData.all.medical_supply_availability = this.returnFieldItemvalue(
          item,
          "health",
          "available_health_medical",
          "Medical supply"
        );
      } catch (error) {
        this.downloadData.all.medical_supply_availability = "NULL";
      }

      try {
        this.downloadData.all.health_personel_availability = this.returnFieldItemvalue(
          item,
          "health",
          "available_health_medical",
          "Health personel"
        );
      } catch (error) {
        this.downloadData.all.health_personel_availability = "NULL";
      }

      try {
        this.downloadData.all.road_access =
          item.sectors.logistics.access_of_structures[0].accessibility;
      } catch (error) {
        this.downloadData.all.road_access = "NULL";
      }

      try {
        this.downloadData.all.food_1_2_months = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "female_HH",
          "category",
          "No. of HH with 1-2 month Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_1_2_months = 0;
      }

      try {
        this.downloadData.all.food_2_months = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "female_HH",
          "category",
          "No. of HH with more than 2 months Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_2_months = 0;
      }

      try {
        this.downloadData.all.food_stock_lost = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "female_HH",
          "category",
          "No. of HH who lost Food Stock"
        );
      } catch (error) {
        this.downloadData.all.food_stock_lost = 0;
      }

      try {
        this.downloadData.all.food_less_1_month = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "female_HH",
          "category",
          "No. of HH with less than 1 month Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_less_1_month = 0;
      }

      try {
        this.downloadData.all.food_less_1_month_male = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "male_HH",
          "category",
          "No. of HH with less than 1 month Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_less_1_month_male = 0;
      }

      try {
        this.downloadData.all.food_1_2_months_male = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "male_HH",
          "category",
          "No. of HH with 1-2 month Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_1_2_months_male = 0;
      }

      try {
        this.downloadData.all.food_2_months_male = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "male_HH",
          "category",
          "No. of HH with more than 2 months Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_2_months_male = 0;
      }

      try {
        this.downloadData.all.food_stock_lost_male = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "male_HH",
          "category",
          "No. of HH who lost Food Stock"
        );
      } catch (error) {
        this.downloadData.all.food_stock_lost_male = 0;
      }

      try {
        this.downloadData.all.food_item_damage = this.sumArrayValues(
          item,
          "agriculture",
          "food_item_damage",
          "number_of_kilos"
        );
      } catch (error) {
        this.downloadData.all.food_item_damage = 0;
      }
      try {
        this.downloadData.all.PeopleAffectedrows_female = this.sumArrayValues(
          item,
          "displaced",
          "PeopleAffectedrows",
          "number_displaced_by_gender_fhh"
        );
      } catch (error) {
        this.downloadData.all.PeopleAffectedrows_female = 0;
      }

      try {
        this.downloadData.all.PeopleAffectedrows_male = this.sumArrayValues(
          item,
          "displaced",
          "PeopleAffectedrows",
          "number_displaced_by_gender_mhh"
        );
      } catch (error) {
        this.downloadData.all.PeopleAffectedrows_male = 0;
      }

      try {
        this.downloadData.all.hectares_submerged = this.sumArrayValues(
          item,
          "agriculture",
          "crops_damaged",
          "hectares_submerged"
        );
      } catch (error) {
        this.downloadData.all.hectares_submerged = 0;
      }

      try {
        this.downloadData.all.hectares_washed_away = this.sumArrayValues(
          item,
          "agriculture",
          "crops_damaged",
          "hectares_washed_away"
        );
      } catch (error) {
        this.downloadData.all.hectares_washed_away = 0;
      }

      try {
        this.downloadData.all.impact_on_crops_hh_affected = this.sumArrayValues(
          item,
          "agriculture",
          "impact_on_crops",
          "hh_affected"
        );
      } catch (error) {
        this.downloadData.all.impact_on_crops_hh_affected = 0;
      }

      try {
        this.downloadData.all.impact_on_crops_hectares_damaged = this.sumArrayValues(
          item,
          "agriculture",
          "impact_on_crops",
          "hectares_damaged"
        );
      } catch (error) {
        this.downloadData.all.impact_on_crops_hectares_damaged = 0;
      }

      try {
        this.downloadData.all.impact_on_livestock_hh = this.sumArrayValues(
          item,
          "agriculture",
          "impact_on_livestock",
          "hh_affected_l"
        );
      } catch (error) {
        this.downloadData.all.impact_on_livestock_hh = 0;
      }

      try {
        this.downloadData.all.impact_on_livestock_la = this.sumArrayValues(
          item,
          "agriculture",
          "impact_on_livestock",
          "livestock_affected"
        );
      } catch (error) {
        this.downloadData.all.impact_on_livestock_la = 0;
      }

      try {
        this.downloadData.all.displaced_households_accommodated_male = this.sumArrayValues(
          item,
          "displaced",
          "displaced_households_accommodated",
          "accomodated_males_hh"
        );
      } catch (error) {
        this.downloadData.all.displaced_households_accommodated_male = 0;
      }
      try {
        this.downloadData.all.displaced_households_accommodated_female = this.sumArrayValues(
          item,
          "displaced",
          "displaced_households_accommodated",
          "accomodated_females_hh"
        );
      } catch (error) {
        this.downloadData.all.displaced_households_accommodated_female = 0;
      }

      try {
        this.downloadData.all.displaced_disagregated_male = this.sumArrayValues(
          item,
          "displaced",
          "displaced_disaggregated",
          "displaced_males"
        );
      } catch (error) {
        this.downloadData.all.displaced_disagregated_male = 0;
      }

      try {
        this.downloadData.all.displaced_disagregated_female = this.sumArrayValues(
          item,
          "displaced",
          "displaced_disaggregated",
          "displaced_females"
        );
      } catch (error) {
        this.downloadData.all.displaced_disagregated_female = 0;
      }

      try {
        this.downloadData.all.displaced_individuals_accommodated_male = this.sumArrayValues(
          item,
          "displaced",
          "displaced_individuals_accommodated",
          "accomodated_males"
        );
      } catch (error) {
        this.downloadData.all.displaced_individuals_accommodated_male = 0;
      }

      try {
        this.downloadData.all.displaced_individuals_accommodated_female = this.sumArrayValues(
          item,
          "displaced",
          "displaced_individuals_accommodated",
          "accomodated_females"
        );
      } catch (error) {
        this.downloadData.all.displaced_individuals_accommodated_female = 0;
      }

      try {
        this.downloadData.all.education_building_functioning = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "functioning_buildings"
        );
      } catch (error) {
        this.downloadData.all.education_building_functioning = 0;
      }

      try {
        this.downloadData.all.education_building_partly_functioning = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "partially_functioning_buildings"
        );
      } catch (error) {
        this.downloadData.all.education_building_partly_functioning = 0;
      }

      try {
        this.downloadData.all.education_closed_buildings = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "closed_buildings"
        );
      } catch (error) {
        this.downloadData.all.education_closed_buildings = 0;
      }

      try {
        this.downloadData.all.education_females_out_of_school = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "females_out_of_school"
        );
      } catch (error) {
        this.downloadData.all.education_females_out_of_school = 0;
      }

      try {
        this.downloadData.all.education_males_out_of_school = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "males_out_of_school"
        );
      } catch (error) {
        this.downloadData.all.education_males_out_of_school = 0;
      }

      try {
        this.downloadData.all.education_underwater = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "underwater"
        );
      } catch (error) {
        this.downloadData.all.education_underwater = 0;
      }

      try {
        this.downloadData.all.education_completely_damaged = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "completely_damaged"
        );
      } catch (error) {
        this.downloadData.all.education_completely_damaged = 0;
      }

      try {
        this.downloadData.all.health_partially_functioning =
          this.sumArrayValues(
            item,
            "health",
            "available_health_facilities",
            "partially_functioning"
          ) +
          this.sumArrayValues(
            item,
            "health",
            "other_health_facilities",
            "partialy_functioning"
          );
      } catch (error) {
        this.downloadData.all.health_partially_functioning = 0;
      }

      try {
        this.downloadData.all.health_verge_of_closing =
          this.sumArrayValues(
            item,
            "health",
            "available_health_facilities",
            "verge_of_closing"
          ) +
          this.sumArrayValues(
            item,
            "health",
            "other_health_facilities",
            "verge_of_closing"
          );
      } catch (error) {
        this.downloadData.all.health_verge_of_closing = 0;
      }

      try {
        this.downloadData.all.health_closed =
          this.sumArrayValues(
            item,
            "health",
            "available_health_facilities",
            "closed"
          ) +
          this.sumArrayValues(
            item,
            "health",
            "other_health_facilities",
            "closed"
          );
      } catch (error) {
        this.downloadData.all.health_closed = 0;
      }

      try {
        this.downloadData.all.livelihoods_slightly_affected = this.sumArrayValues(
          item,
          "livelihoods",
          "livelihoods_affected",
          "severely_affected"
        );
      } catch (error) {
        this.downloadData.all.livelihoods_slightly_affected = 0;
      }
      try {
        this.downloadData.all.livelihoods_severely_affected = this.sumArrayValues(
          item,
          "livelihoods",
          "livelihoods_affected",
          "slightly_affected"
        );
      } catch (error) {
        this.downloadData.all.livelihoods_severely_affected = 0;
      }

      try {
        this.downloadData.all.impact_on_vulnerable_persons_males = this.sumArrayValues(
          item,
          "protection",
          "impact_on_vulnerable_persons",
          "impacted_males"
        );
      } catch (error) {
        this.downloadData.all.impact_on_vulnerable_persons_males = 0;
      }

      try {
        this.downloadData.all.impact_on_vulnerable_persons_females = this.sumArrayValues(
          item,
          "protection",
          "impact_on_vulnerable_persons",
          "impacted_females"
        );
      } catch (error) {
        this.downloadData.all.impact_on_vulnerable_persons_females = 0;
      }

      try {
        this.downloadData.all.nutrition_affected_pop_male = this.sumArrayValues(
          item,
          "nutrition",
          "affected_population",
          "affected_males"
        );
      } catch (error) {
        this.downloadData.all.nutrition_affected_pop_male = 0;
      }

      try {
        this.downloadData.all.nutrition_affected_pop_female = this.sumArrayValues(
          item,
          "nutrition",
          "affected_population",
          "affected_females"
        );
      } catch (error) {
        this.downloadData.all.nutrition_affected_pop_female = 0;
      }

      try {
        this.downloadData.all.shelter_without_shelter_male = this.sumArrayValues(
          item,
          "shelter",
          "people_without_shelter",
          "males"
        );
      } catch (error) {
        this.downloadData.all.shelter_without_shelter_male = 0;
      }

      try {
        this.downloadData.all.shelter_without_shelter_female = this.sumArrayValues(
          item,
          "shelter",
          "people_without_shelter",
          "females"
        );
      } catch (error) {
        this.downloadData.all.shelter_without_shelter_female = 0;
      }

      try {
        this.downloadData.all.shelter_people_dead_female = this.sumArrayValues(
          item,
          "shelter",
          "PeopleDeadrows",
          "females_dead"
        );
      } catch (error) {
        this.downloadData.all.shelter_people_dead_female = 0;
      }

      try {
        this.downloadData.all.shelter_people_dead_male = this.sumArrayValues(
          item,
          "shelter",
          "PeopleDeadrows",
          "males_dead"
        );
      } catch (error) {
        this.downloadData.all.shelter_people_dead_male = 0;
      }

      try {
        this.downloadData.all.shelter_people_injured_female = this.sumArrayValues(
          item,
          "shelter",
          "PeopleInjuredrows",
          "people_injured_females"
        );
      } catch (error) {
        this.downloadData.all.shelter_people_injured_female = 0;
      }

      try {
        this.downloadData.all.shelter_people_injured_male = this.sumArrayValues(
          item,
          "shelter",
          "PeopleInjuredrows",
          "people_injured_males"
        );
      } catch (error) {
        this.downloadData.all.shelter_people_injured_male = 0;
      }

      try {
        this.downloadData.all.shelter_fhh_affected = this.sumArrayValues(
          item,
          "shelter",
          "PeopleAffectedrows",
          "damaged_fhh"
        );
      } catch (error) {
        this.downloadData.all.shelter_fhh_affected = 0;
      }

      try {
        this.downloadData.all.shelter_mhh_affected = this.sumArrayValues(
          item,
          "shelter",
          "PeopleAffectedrows",
          "damaged_mhh"
        );
      } catch (error) {
        this.downloadData.all.shelter_mhh_affected = 0;
      }

      this.download.push({
        all: this.downloadData.all
      });
    }
  }
};
</script>

<style lang="stylus" scoped>
.modal-body .tags-input__wrapper {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.el-tag.el-tag--primary {
  position: relative !important;
  display: inline;
}

.image-previewer, .row {
  display: flex;
  flex-wrap: wrap;
  padding: 10px;
  justify-items: baseline;
}

.image-previewer .img, .row .img {
  height: 300px;
  border-radius: 10px;
  cursor: pointer;
}

.image-previewer .image--wrapper {
  position: relative;
  margin: 10px;
}

.row .image--wrapper {
  position: relative;
  margin: 10px;
}

.image-previewer .row .image--wrapper {
  margin: 10px;
}

.image--wrapper .img--overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  z-index: 10;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

.image--wrapper .img--overlay .btn {
  background: rgb(238, 5, 5);
  width: 100%;
  position: absolute;
  bottom: 0;
}

.progress, .progress-bar {
  height: 30px;
  font-weight: bold;
}

.inner {
  overflow: hidden;
}

.inner img {
  transition: all 1.5s ease;
}

.inner:hover img {
  transform: scale(2);
  display: flex;
  flex-wrap: wrap;
}

table {
  border-collapse: collapse;
  width: 100%;
  margin: 0 auto;
}

table, th, td {
  border: 1px solid black;
  margin-top: 1%;
}

td {
  padding: 1%;
}

.rotated {
  writing-mode: tb-rl;
  transform: rotate(-180deg);
  color: teal;
}

.noborder {
  border: none;
}

.vertical {
  writing-mode: vertical-rl;
}

.qcont:first-letter {
  text-transform: capitalize;
}

.right-align {
  text-align: right;
}

@media print {
  .section-not-to-print {
    visibility: hidden;
  }

  #section-to-print {
    visibility: visible;
  }
}
</style>
