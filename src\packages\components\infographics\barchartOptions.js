import { Colors } from './Colors'
import utils from '../../../util/dashboard'

export const barOptions = (dc, chart, dimension, dx, ylabel, bottom) => {
  chart

  .colorAccessor((d) => {
    return d.key;
  })
   .elasticX(true)
  .colors(Colors[1])
  .renderLabel(true)
  .margins({ top: 40, right: 10, bottom: 40, left: 10 })
  .render()
  .xAxis()
   .ticks(5)

   .tickFormat(function (v) {
    var csv = document.getElementById('exportCSV')
    var excel = document.getElementById('exportExcel')

    let data = dimension.top(Infinity)
    data = data.map(item => {
      //console.log(item)
      return {
        disaster: item.disaster,
        //tas: item.tas.join('|'),
        district: item.district,
        region: item.region,
        dead_males: item.dead_males,
        dead_females: item.dead_females,
        injured_males: item.injured_males,
        injured_females: item.injured_females,
        without_shelter_males: item.without_shelter_males,
        without_shelter_females: item.without_shelter_females,
        without_shelter_mhh: item.without_shelter_mhh,
        without_shelter_fhh: item.without_shelter_fhh,
        total_without_shelter_hh: item.total_without_shelter_hh,
        contacts: item.contacts,
        created_on: item.created_on,
        disasterId: item.disasterId
      }
    })

    data = data.sort(function (a, b) {
      return b.created_on - a.created_on
    })

    ///console.log(data)

    csv.onclick = function () {
      utils.download(utils.generateCSV(data), 'csv')
    }

    excel.onclick = function () {
      utils.generateExcel(data)
    }

    return v
  })
  
  return chart
}
