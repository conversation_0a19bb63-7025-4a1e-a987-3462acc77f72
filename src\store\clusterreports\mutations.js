export const set = (state, val) => {
  state.Clusterreports = val
}

export const update = (state, val) => {
  state.Clusterreports.splice(state.Clusterreports.map(o => o.id).indexOf(val.id), 1, val)
}

export const remove = (state, val) => {
  state.Clusterreports = state.Clusterreports.filter(x => {
    return x.id != val
  })
}

export const setOne = (state, val) => {
  state.Clusterreports.push(val)
}

// roles

export const setroles = (state, val) => {
  state.roles = val
}
