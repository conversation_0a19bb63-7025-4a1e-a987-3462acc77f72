<template>
  <div class="content">
    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Disaster</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">SUBMITTED REPORTS</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">All</li>
            </ol>
          </nav>
        </div>
        <div class="col-lg-6 col-5 text-right"></div>
      </div>
      <!-- Card stats -->
    </base-header>
    <div class="container-fluid mt--6">
      <div>
        <card
          class="no-border-card"
          body-classes="px-0 pb-1"
          footer-classes="pb-2"
        >
          <template slot="header">
            <h3 class="mb-0">Submitted Disaster Reports</h3>
          </template>
          <div>
            <div
              class="
                col-12
                d-flex
                justify-content-center justify-content-sm-between
                flex-wrap
              "
            >
              <el-select
                class="select-primary pagination-select"
                v-model="pagination.perPage"
                placeholder="Per page"
              >
                <el-option
                  class="select-primary"
                  v-for="item in pagination.perPageOptions"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>

              <div>
                <base-input
                  v-model="searchQuery"
                  prepend-icon="fas fa-search"
                  placeholder="Search..."
                ></base-input>
              </div>
            </div>
            <el-table
              :data="queriedData"
              :filter="searchQuery"
              row-key="id"
              header-row-class-name="thead-light"
              @sort-change="sortChange"
            >
              <el-table-column
                v-for="column in tableColumns"
                :key="column.label"
                v-bind="column"
                id="reportForm"
              ></el-table-column>
              <el-table-column min-width="340px" align="right" label="Actions">
                <div slot-scope="{ $index, row }" class="d-flex">
                  <!--  <base-button
                    @click.native="handleInfographics($index, row)"
                    class="like btn-link"
                    type="info"
                    size="sm"
                    icon
                  >
                    <i class="text-white ni ni-chart-bar-32"></i>
                  </base-button>-->
                  <base-button
                    @click.native="review(row)"
                    class="edit"
                    type="primary"
                    size="sm"
                    icon
                  >
                    <i class="text-white ni ni-single-copy-04"></i>Detailed
                  </base-button>
                  <base-button
                    @click.native="reviewSummary(row)"
                    style="color: white; background: #ab6f00; border: #ab6f00"
                    class="edit"
                    type="primary"
                    size="sm"
                    icon
                  >
                    <i class="text-white ni ni-badge"></i>Summary
                  </base-button>

                  <!-- <base-button
                    @click.native="handleInfographics(row)"
                    style="color: white; background: #454545; border: #454545"
                    class="edit"
                    type="dark"
                    size="sm"
                    icon
                  >
                    <i class="text-white ni ni-chart-bar-32"></i>Infographics
                  </base-button> -->

                  <base-button
                    :disabled="hasPictures(row)" 
                    @click="downloadDpictures(row)"
                    style="color: white; background: #077757; border: #077757; width:100px;"
                    class="edit"
                    type="dark background"
                    size="sm"
                    icon
                  >
                    <i class="text-white ni ni-cloud-download-95"></i>{{buttonText}}
                  </base-button>
                </div>
              </el-table-column>
            </el-table>
          </div>
          <div
            slot="footer"
            class="
              col-12
              d-flex
              justify-content-center justify-content-sm-between
              flex-wrap
            "
          >
            <div class>
              <p class="card-category">
                Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
                <span v-if="selectedRows.length"
                  >&nbsp; &nbsp; {{ selectedRows.length }} rows selected</span
                >
              </p>
            </div>
            <base-pagination
              class="pagination-no-border"
              v-model="pagination.currentPage"
              :per-page="pagination.perPage"
              :total="total"
            ></base-pagination>
          </div>
        </card>
      </div>
    </div>
  </div>
</template>
<script>
import { Table, TableColumn, Select, Option } from "element-ui";
import RouteBreadCrumb from "@/components/Breadcrumb/RouteBreadcrumb";
import { BasePagination } from "@/components";
import clientPaginationMixin from "@/components/PaginatedTables/clientPaginationMixin";
import swal from "sweetalert2";
import { MongoReports } from "../api/MongoReports";
import { dinrforms } from "../../districtmanager/api/forms/dinrforms.js";
import { mapGetters, mapActions } from "vuex";
var moment = require("moment");

export default {
  mixins: [clientPaginationMixin],
  components: {
    BasePagination,
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn,
  },
  data() {
    return {
      propsToSearch: ["disaster", "district", "officer","date"],
      tableColumns: [
        {
          prop: "id",
          label: "ID",
          minWidth: 50,
          sortable: true,
        },
        {
          prop: "disaster",
          label: "Disaster",
          minWidth: 150,
          sortable: true,
        },
        {
          prop: "district",
          label: "District",
          minWidth: 100,
          sortable: true,
        },
        {
          prop: "officer",
          label: "Officer",
          minWidth: 120,
          sortable: true,
        },
        {
          prop: "date",
          label: "Date Created",
          minWidth: 135,
          sortable: true,
        },
      ],
      tableData: [],
      selectedRows: [],
    };
  },

  computed: {
    ...mapGetters({
      getDinrs: "dinrs/get",
      getDraById: "dras/getById",
      getDinrById: "dinrs/getById",
    }),
    ...mapActions({
      loadFlatdinr: "loadFlatdinrs",
    })
  },
  methods: {
     hasPictures(row) {
      let response =  this.getDinrById(row.uuid);
      
      if (response.disasterImages == "undefined" || response.disasterImages ==null) {
        this.buttonText  = "No Photos"
        return true;
      }
      
      this.buttonText  = "Photos"
      return false;
    },
    ...mapActions("dinrs", {
      getDinrsAction: "get",
    }),
    ...mapActions("dras", {
      getDrasAction: "get",
    }),

    downloadDpictures(form) {
      const resource = process.env.VUE_APP_ENGINE_URL + "/forms/dinr";
      const url = `${resource}/${form.uuid}/archives`;
      axios
        .get(url, {
          responseType: "blob",
          onDownloadProgress: (e) => {
            console.log(e);
          },
        })
        .then((response) => {
          const blob = new Blob([response.data]);
          const a = document.createElement("a");
          a.href = window.URL.createObjectURL(blob);
          a.download = `${form.disaster.split(" ").join("_")}_${moment(
            form.dodFrom
          ).format("DDMMYYYY")}.zip`;

          a.click();
        });
    },

    review(row) {
      this.$router.push({
        path: "/districtcommissioner/detailsreport/" + row.uuid,
      });
    },
    reviewSummary(row) {
      this.$router.push({
        path: "/districtcommissioner/summaryreport/" + row.uuid,
      });
    },
    handleInfographics(row) {
      this.$router.push({
        path: "/districtcommissioner/infographics/" + row.uuid,
      });
    },
  },
  mounted() {
    MongoReports.getDinrs().then((response) => {
      let data = response.data;
      let district = this.$session.get("user").admin2_name_en;


      data = data.filter((x) =>
        x.district ? x.district.admin2_name_en.includes(district) : false
      );

      data.sort(function (a, b) {
        return new Date(b.createdon) - new Date(a.createdon);
      });

      for (let i = 0; i < data.length; i++) {
        let row = data[i]._id;

        this.tableData.push({
          id: i + 1,
          disaster: data[i].disaster,
          district: data[i].district.admin2_name_en,
          date: moment(data[i].createdon).format("DD-MM-YYYY"),
          officer: data[i].account.firstName + " " + data[i].account.lastName,
          uuid: data[i]._id,
        });
      }
    });
  },
};
</script>
<style>
.no-border-card .card-footer {
  border-top: 0;
}
</style>
