<template>
  <div class="content">
    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Disaster</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/districtmanager/dashboard">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">UNSIGNED REPORTS</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">All</li>
            </ol>
          </nav>
        </div>
        <div class="col-lg-6 col-5 text-right"></div>
      </div>
      <!-- Card stats -->
    </base-header>
    <div class="container-fluid mt--6">
      <div>
        <card
          class="no-border-card"
          body-classes="px-0 pb-1"
          footer-classes="pb-2"
        >
          <template slot="header">
            <h3 class="mb-0">UNSIGNED DISASTER REPORTS</h3>
          </template>
          <div>
            <div
              class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
            >
              <el-select
                class="select-primary pagination-select"
                v-model="pagination.perPage"
                placeholder="Per page"
              >
                <el-option
                  class="select-primary"
                  v-for="item in pagination.perPageOptions"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>

              <div>
                <base-input
                  v-model="searchQuery"
                  prepend-icon="fas fa-search"
                  placeholder="Search..."
                ></base-input>
              </div>
            </div>
            <div>
              <b-table
                responsive
                sticky-header
                :striped="striped"
                :bordered="bordered"
                :borderless="borderless"
                :outlined="outlined"
                :small="small"
                :hover="hover"
                :dark="dark"
                :sort-icon="true"
                :fixed="fixed"
                :foot-clone="footClone"
                :no-border-collapse="noCollapse"
                head-variant="light"
                :table-variant="tableVariant"
                :items="queriedData"
                :fields="tableColumns"
              >
                <template #cell(actions)="row">
                  <b-button
                    @click="review(row.item, row.index, $event.target)"
                    class="edit bg-primary text-white"
                    type="primary"
                    size="sm"
                    icon
                  >
                    CHECK & SIGN
                  </b-button>
                </template>
              </b-table>
            </div>
          </div>
          <div
            slot="footer"
            class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
          >
            <div class>
              <p class="card-category">
                Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
                <span v-if="selectedRows.length"
                  >&nbsp; &nbsp; {{ selectedRows.length }} rows selected</span
                >
              </p>
            </div>
            <base-pagination
              class="pagination-no-border"
              v-model="pagination.currentPage"
              :per-page="pagination.perPage"
              :total="total"
            ></base-pagination>
          </div>
        </card>
      </div>
    </div>
  </div>
</template>
<script>
import { Table, TableColumn, Select, Option } from "element-ui";
import RouteBreadCrumb from "@/components/Breadcrumb/RouteBreadcrumb";
import { BasePagination } from "@/components";
import clientPaginationMixin from "@/components/PaginatedTables/clientPaginationMixin";
import swal from "sweetalert2";
import { MongoReports } from "../api/MongoReports";
var moment = require("moment");

export default {
  mixins: [clientPaginationMixin],
  components: {
    BasePagination,
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn
  },
  data() {
    return {
      propsToSearch: ["disaster", "district", "officer", "date"],
      tableColumns: [
        {
          key: "id",
          label: "ID",
          sortable: true
        },
        {
          key: "disaster",
          label: "Disaster",
          sortable: true
        },
        {
          key: "officer",
          label: "Officer",
          sortable: true
        },
        {
          key: "date",
          label: "Date Created",
          sortable: true
        },
        { key: "actions", label: "Actions" }
      ],
      tableData: [],
      selectedRows: []
    };
  },

  computed: {},

  methods: {
    review(index) {
      this.$router.push({
        path: "/districtmanager/unapproveddetailsreport/" + index.uuid
      });
    },
    reviewSummary(row) {
      this.$router.push({ path: "/districtmanager/summaryreport/" + row.uuid });
    },
    handleInfographics(row) {
      this.$router.push({ path: "/districtmanager/infographics/" + row.uuid });
    }
  },
  created() {
    MongoReports.getUnapprovedDinrs().then(response => {
      let data = response.data
        .filter(data =>
          data.district.admin2_name_en.includes(
            this.$session.get("user").admin2_name_en
          )
        )
        .filter(
          item =>
            !item ||
            ((!item.isApproved &&
              item.isApproved == false &&
              !item &&
              (!item.isRejected && item.isRejected == false)) ||
              (!item.approvalMetadata ||
                !item.approvalMetadata.signature ||
                ((!item.isRejected || item.isRejected == false) &&
                  item.approvalMetadata.signature.length == 0)))
        );

      data.sort(function(a, b) {
        return new Date(b.createdon) - new Date(a.createdon);
      });

      for (let i = 0; i < data.length; i++) {
        
        let row = data[i]._id;
        this.tableData.push({
          id: i + 1,
          disaster: data[i].disaster,
          district: data[i].district.admin2_name_en,
          date: moment(data[i].createdon).format("DD-MM-YYYY"),
          officer: data[i].account.firstName + " " + data[i].account.lastName,
          uuid: data[i]._id
        });
      }
    });
  }
};
</script>
<style>
.no-border-card .card-footer {
  border-top: 0;
}
</style>
