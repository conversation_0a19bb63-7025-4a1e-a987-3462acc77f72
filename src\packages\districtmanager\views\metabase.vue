<template>
  <div>
    <base-header class="pb-1" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0"></h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">Reports</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">
                Info-graphics
              </li>
            </ol>
          </nav>
        </div>

        <div class="col-lg-6 col-6 text-right pr-5">
          <base-button size="sm" type="neutral">
            <span class="btn-inner--icon">
              <!--  <i class="ni ni-fat-add"></i> -->
            </span>
            <span class="btn-inner--text" @click="printdiv('section-to-print')"
              >Print</span
            >
          </base-button>
        </div>
      </div>
    </base-header>

    <div class="col-md-11" id="section-to-print" style="margin:auto">
      <tabs
        tabNavClasses="nav-fill flex-column flex-sm-row nav-wrapper"
        tabContentClasses="card shadow"
        :value="model"
        v-model="model"
      >
        <tabpane :id="1" title="1" :key="1">
          <span slot="title">
            <center>
              <span
                style="font-size:300%;"
                data-toggle="tooltip"
                data-placement="top"
                title="SHELTER"
                class="humanitarianicons-Shelter"
              ></span>
            </center>
          </span>
          <iframe
            :src="
              metabaseLink +
                '/public/dashboard/17d88971-a198-4ede-998e-b434185edda9'
            "
            frameborder="0"
            width="100%"
            height="1000px"
            allowtransparency
            id="printf"
          ></iframe>
        </tabpane>

        <tabpane :id="2" title="2" :key="2">
          <span slot="title">
            <center>
              <span
                style="font-size:300%;"
                data-toggle="tooltip"
                data-placement="top"
                title="DISPLACED"
                class="humanitarianicons-Internally-displaced"
              ></span>
            </center>
          </span>
          <iframe
            :src="
              metabaseLink +
                '/public/dashboard/79277b0a-1b3f-4a85-b129-fe7aa43e3389'
            "
            frameborder="0"
            width="100%"
            height="1000px"
            allowtransparency
          ></iframe>
        </tabpane>

        <tabpane :id="3" title="3" :key="3">
          <span slot="title">
            <center>
              <span
                style="font-size:300%;"
                data-toggle="tooltip"
                data-placement="top"
                title="AGRICULTURE"
                class="humanitarianicons-Agriculture"
              ></span>
            </center>
          </span>
          <iframe
            :src="
              metabaseLink +
                '/public/dashboard/2f9107f9-df2d-4880-9e28-6a1b4faf4932'
            "
            frameborder="0"
            width="100%"
            height="1000px"
            allowtransparency
          ></iframe>
        </tabpane>
        <tabpane :id="4" title="4" :key="4">
          <span slot="title">
            <center>
              <span
                style="font-size:300%;"
                data-toggle="tooltip"
                data-placement="top"
                title="WASH"
                class="humanitarianicons-Water-Sanitation-and-Hygiene"
              ></span>
            </center>
          </span>
          <iframe
            :src="
              metabaseLink +
                '/public/dashboard/c162f7f6-d775-4b1b-b704-e0692bbb8ac1'
            "
            frameborder="0"
            width="100%"
            height="1000px"
            allowtransparency
          ></iframe>
        </tabpane>
        <tabpane :id="5" title="5" :key="5">
          <span slot="title">
            <center>
              <span
                style="font-size:300%;"
                data-toggle="tooltip"
                data-placement="top"
                title="HEALTH"
                class="humanitarianicons-Health"
              ></span>
            </center>
          </span>
          <iframe
            :src="
              metabaseLink +
                '/public/dashboard/d4fb0763-ac19-46eb-be35-0e1f1f2e4248'
            "
            frameborder="0"
            width="100%"
            height="1000px"
            allowtransparency
          ></iframe>
        </tabpane>
        <tabpane :id="6" title="6" :key="6">
          <span slot="title">
            <center>
              <span
                style="font-size:300%;"
                data-toggle="tooltip"
                data-placement="top"
                title="FOOD"
                class="humanitarianicons-Food-Security"
              ></span>
            </center>
          </span>
          <iframe
            :src="
              metabaseLink +
                '/public/dashboard/685924f1-df6d-42a7-ab26-aca9ce078ef2'
            "
            frameborder="0"
            width="100%"
            height="1000px"
            allowtransparency
          ></iframe>
        </tabpane>

        <tabpane :id="8" title="8" :key="8">
          <span slot="title">
            <center>
              <span
                style="font-size:300%;"
                data-toggle="tooltip"
                data-placement="top"
                title="PROTECTION"
                class="humanitarianicons-Protection"
              ></span>
            </center>
          </span>
          <iframe
            :src="
              metabaseLink +
                '/public/dashboard/187262f7-0c69-4e5d-8752-f86419f2de01'
            "
            frameborder="0"
            width="100%"
            height="1000px"
            allowtransparency
          ></iframe>
        </tabpane>
        <!--         <tabpane :id="9" title="9" :key="9">
          <span slot="title">
            <center>
              <span
                style="font-size:300%;"
                data-toggle="tooltip"
                data-placement="top"
                title="NUTRITION"
                class="humanitarianicons-Nutrition"
              ></span>
            </center>
          </span>
          <iframe
            :src="metabaseLink+'/public/dashboard/c162f7f6-d775-4b1b-b704-e0692bbb8ac1"
            frameborder="0"
            width="100%"
            height="1000px"
            allowtransparency
          ></iframe>
        </tabpane>
        <tabpane :id="10" title="10" :key="10">
          <span slot="title">
            <center>
              <span
                style="font-size:300%;"
                data-toggle="tooltip"
                data-placement="top"
                title="EDUCATION"
                class="humanitarianicons-Education"
              ></span>
            </center>
          </span>
          <iframe
            :src="metabaseLink+'/public/dashboard/c162f7f6-d775-4b1b-b704-e0692bbb8ac1"
            frameborder="0"
            width="100%"
            height="1000px"
            allowtransparency
          ></iframe>
        </tabpane>
        <tabpane :id="11" title="11" :key="11">
          <span slot="title">
            <center>
              <span
                style="font-size:300%;"
                data-toggle="tooltip"
                data-placement="top"
                title="LIVELIHOODS"
                class="humanitarianicons-Livelihood"
              ></span>
            </center>
          </span>
          <iframe
            :src="metabaseLink+'/public/dashboard/c162f7f6-d775-4b1b-b704-e0692bbb8ac1"
            frameborder="0"
            width="100%"
            height="1000px"
            allowtransparency
          ></iframe>
        </tabpane>-->
      </tabs>
    </div>
  </div>
</template>
<script>
import tabs from "../../../components/Tabs/Tabs";
import tabpane from "../../../components/Tabs/Tab";
import shelter from "../views/infographics/shelter";
import displaced from "../views/infographics/displaced";
import agriculture from "../views/infographics/agriculture";
import food from "../views/infographics/food";
import wash from "../views/infographics/wash";
import health from "../views/infographics/health";
import protection from "../views/infographics/protection";
import nutrition from "../views/infographics/nutrition";
import education from "../views/infographics/education";
import livehoods from "../views/infographics/livelihood";

import { Reports } from "../api/reports";

import Swal from "sweetalert2";

import $ from "jquery";

export default {
  components: {
    tabs,
    tabpane,
    shelter,
    displaced,
    agriculture,
    food,
    wash,
    health,
    education,
    protection,
    livehoods,
    nutrition
  },

  data() {
    return {
      childDataLoaded: false,
      data: [],
      metabaseLink: process.env.VUE_APP_METABASE_URL,
      uuid: null,
      tabIndex: 1,
      district: "",
      dialog: false,
      notifications: false,
      isVisible: true,
      sound: true,
      widgets: false,
      i: 0, // set i to track the tab index
      model: "1",
      Ta: "",
      step: 1,
      e13: 1,
      max: 50000,
      value: 0,
      accessToken: null,
      malecounter: 0,
      femalecounter: 0,
      sector: {}
    };
  },

  computed: {},

  mounted() {},

  methods: {
    gotoPrint() {
      this.$router.push({
        name: "DINRFormsDRAPreview",
        params: { uuid: "HHSHS-4JJSJAKK" }
      });
    },
    printdiv(printpage) {
      /* var headstr = "<html><head><title></title></head><body>";
      var footstr = "</body>";
      var newstr = document.all.item(printpage).innerHTML;
      var oldstr = document.body.innerHTML;
      document.body.innerHTML = headstr + newstr + footstr;
      window.print();
      document.body.innerHTML = oldstr;
      location.reload();
      return false; */

      document.getElementById("printf").contentWindow.print();
    },
    offlineSaver() {
      localStorage.setItem(this.uuid, JSON.stringify(this.sector));
      //console.log(localStorage.getItem(this.uuid));
    },

    addItemRow(sector, member, array_name, array2 = [], key_value) {
      for (let i = 0; i < array_name.length; i++) {
        for (let x = 0; x < array2.length; x++) {
          if (array_name[i][key_value].name === array2[x].name) {
            array2.splice(x, 1);
          }
        }
      }

      this.sector[sector][member] = array_name;

      let FilteredData = this.sector[sector][member].filter(
        value => Object.keys(value).length === 0
      );

      if (FilteredData.length === 0) {
        this.sector[sector][member].push({});
      } else {
        Swal.fire({
          title: "Unfilled data row",
          text: "Please fill data row before adding new one",
          type: "warning",
          toast: true,
          animation: false,
          position: "top-end",
          timer: 4000,
          showConfirmButton: false
        });
      }
    },
    removeItemRow(sector, member, array_name, array2 = [], index, key_value) {
      Swal.fire({
        title: "Are you sure?",
        text: "You won't be able to revert this!",
        animation: false,
        showCancelButton: true,
        confirmButtonColor: "primary",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, delete it!"
      }).then(result => {
        if (result.value) {
          {
            array_name[index][key_value] === undefined
              ? console.log("")
              : array2.push({ name: array_name[index][key_value] });

            this.sector[sector][member] = array_name;

            this.sector[sector][member].splice(index, 1);
          }
        }
      });
    },

    syncToRemote() {
      //var session = "F0gB8UFnGzpiUDwDTr8WmHErtSCSAohtogbOB88F3yoKA9g5rpMK96Dph6f08V7I"
      var data = JSON.parse(localStorage.getItem(this.uuid));
      //console.log(data);
      if (data) {
        draforms.createForm(data, this.accessToken, this.uuid).then(
          response => {
            
            //remove item since its saved
            localStorage.removeItem(this.uuid);
            Swal.fire({
              title: "Synchronised",
              text: "Your offline data is synchronied with the remote",
              type: "success",
              toast: true,
              animation: false,
              position: "top-end",
              timer: 3000,
              showConfirmButton: false
            });
            if (response.status === 200) {
              
            }
          },
          error => {
            Swal.fire({
              title: "Failed",
              text: "Synching failed, try again another time",
              type: "error",
              toast: true,
              animation: false,
              position: "top-end",
              timer: 6000,
              showConfirmButton: false
            });
          }
        );
      } else {
        Swal.fire({
          title: "No offline data",
          text: "There is no offline data to sync",
          type: "warning",
          toast: true,
          animation: false,
          position: "top-end",
          timer: 4000,
          showConfirmButton: false
        });
      }
    },
    autoSave() {
      var interval = setInterval(this.AutosaveSections, 120000);
      localStorage.setItem("intervalId", interval);
      //put alert
    },

    save(data, sector, hasError = false) {
      let CurrentPosition = this.model.replace(/\D/g, "") % 12;

      this.model = `${parseInt(CurrentPosition) + 1}`;

      this.$router.push({ hash: "" });

      window.scrollTo(500, 0);
    },

    saveSections() {
      draforms.createForm(this.sector, this.accessToken, this.uuid).then(
        response => {
          if (response.status === 200) {
           
          }
          Swal.fire({
            title: "Saved",
            text: "All Form data saved successfully",
            type: "success",
            toast: true,
            animation: false,
            position: "top-end",
            timer: 3000,
            showConfirmButton: false
          });
        },
        error => {
          //console.log("offline");
          if (this.sector != null) {
            Swal.fire({
              title: "Offline",
              text: "You are offline, please sync after some time",
              type: "error",
              toast: true,
              animation: false,
              position: "top-end",
              timer: 6000,
              showConfirmButton: false
            });
            this.offlineSaver();
          }
        }
      );
    },

    AutosaveSections() {
      draforms.createForm(this.sector, this.accessToken, this.uuid).then(
        response => {
          if (response.status === 200) {
           
          }
        },
        error => {
          //console.log("offline");
          if (this.sector != null) {
            this.offlineSaver();
          }
        }
      );
    }
  },
  created() {
    //console.log(this.$route.params.uuid);
    Reports.getOneReport(this.$route.params.uuid).then(response => {
      //console.log(response.data);
      this.data = response.data;
      this.childDataLoaded = true;
    });

    $(function() {});
  }
};
</script>

<style scoped>
.alert-suc {
  color: #006666;
}
@media print {
  .section-not-to-print {
    visibility: hidden;
  }

  #section-to-print {
    visibility: visible;
  }

  .tab-pane {
    display: block;
  }
}

@import "../../../assets/style.css";
@import "../../../assets/ie7/ie7.css";
</style>
