<template>
  <div class="md-layout">
    <div class="md-layout-item">
      <md-card>
        <md-card-header class="md-card-header-icon md-card-header-green">
          <div class="card-icon">
            <md-icon>perm_identity</md-icon>
          </div>
          <h4 class="title">Users</h4>
        </md-card-header>
        <md-card-content>
          <hr />
          <md-button @click.native="handleCreate()" class="md-success">
            <md-icon class="material-icons">add</md-icon>Register
          </md-button>
          <hr />
          <md-table
            :value="queriedData"
            :md-sort.sync="currentSort"
            :md-sort-order.sync="currentSortOrder"
            :md-sort-fn="customSort"
            class="paginated-table table-striped table-hover"
          >
            <md-table-toolbar>
              <md-field>
                <label for="pages">Per page</label>
                <md-select v-model="pagination.perPage" name="pages">
                  <md-option
                    v-for="item in pagination.perPageOptions"
                    :key="item"
                    :label="item"
                    :value="item"
                  >{{ item }}</md-option>
                </md-select>
              </md-field>
              <md-field>
                <md-input
                  type="search"
                  class="mb-3"
                  clearable
                  style="width: 200px"
                  placeholder="Search records"
                  v-model="searchQuery"
                ></md-input>
              </md-field>
            </md-table-toolbar>

            <md-table-row slot="md-table-row" slot-scope="{ item }">
              <md-table-cell md-label="Name" md-sort-by="name">{{item.firstName}} {{item.lastName}}</md-table-cell>
              <md-table-cell md-label="Email" md-sort-by="email">
                {{
                item.email
                }}
              </md-table-cell>
              <md-table-cell md-label="Phone">{{ item.phone }}</md-table-cell>
              <md-table-cell md-label="Role">{{ item.roleName }}</md-table-cell>
              <md-table-cell md-label="Status">
                <md-icon v-if="item.status == 1 ">lock_open</md-icon>
                <md-icon v-else>lock</md-icon>
              </md-table-cell>
              <md-table-cell md-label="Actions">
                <md-button
                  style="margin: 0 10px 0 10px;"
                  class="md-just-icon md-warning md-simple"
                  @click.native="handleEdit(item)"
                >
                  <md-icon>edit</md-icon>
                </md-button>

                <md-button
                  style="margin: 0 10px 0 10px;"
                  class="md-just-icon md-danger md-simple"
                  @click.native="handleDelete(item)"
                >
                  <md-icon>close</md-icon>
                </md-button>
              </md-table-cell>
            </md-table-row>
          </md-table>
          <div class="footer-table md-table">
            <table>
              <tfoot>
                <tr>
                  <th v-for="item in footerTable" :key="item.firstName" class="md-table-head">
                    <div class="md-table-head-container md-ripple md-disabled">
                      <div class="md-table-head-label">{{ item }}</div>
                    </div>
                  </th>
                </tr>
              </tfoot>
            </table>
          </div>
          <hr />
        </md-card-content>
        <md-card-actions md-alignment="space-between">
          <div class>
            <p class="card-category">Showing {{ from + 1 }} to {{ to }} of {{ total }} entries</p>
          </div>
          <pagination
            class="pagination-no-border pagination-success"
            v-model="pagination.currentPage"
            :per-page="pagination.perPage"
            :total="total"
          ></pagination>
        </md-card-actions>
      </md-card>
    </div>
  </div>
</template>

<script>
import { Pagination } from "@/components";
import { users } from "../../Api/users";
import Fuse from "fuse.js";
import Swal from "sweetalert2";

export default {
  components: {
    Pagination
  },
  computed: {
    /***
     * Returns a page from the searched data or the whole data. Search is performed in the watch section below
     */
    queriedData() {
      let result = this.tableData;
      if (this.searchedData.length > 0) {
        result = this.searchedData;
      }
      return result.slice(this.from, this.to);
    },
    to() {
      let highBound = this.from + this.pagination.perPage;
      if (this.total < highBound) {
        highBound = this.total;
      }
      return highBound;
    },
    from() {
      return this.pagination.perPage * (this.pagination.currentPage - 1);
    },
    total() {
      return this.searchedData.length > 0
        ? this.searchedData.length
        : this.tableData.length;
    }
  },
  data() {
    return {
      currentSort: "name",
      currentSortOrder: "asc",
      pagination: {
        perPage: 5,
        currentPage: 1,
        perPageOptions: [5, 10, 25, 50],
        total: 0
      },
      footerTable: ["Name", "Email", "Phone", "Role", "Status", "Actions"],
      searchQuery: "",
      propsToSearch: ["firstName", "lastName", "email"],
      tableData: [],
      searchedData: [],
      fuseSearch: null
    };
  },
  methods: {
    customSort(value) {
      return value.sort((a, b) => {
        const sortBy = this.currentSort;
        if (this.currentSortOrder === "desc") {
          return a[sortBy].localeCompare(b[sortBy]);
        }
        return b[sortBy].localeCompare(a[sortBy]);
      });
    },
    handleCreate() {
      this.$router.push({ name: "Register User", params: {} });
    },

    handleEdit(item) {
      Swal.fire({
        title: `You want to edit ${item.firstName + " " + item.lastName}`,
        buttonsStyling: false,
        confirmButtonClass: "md-button md-info"
      });
    },
    handleDelete(item) {
      Swal.fire({
        title: "Are you sure?",
        text: `You won't be able to revert this!`,
        type: "warning",
        showCancelButton: true,
        confirmButtonClass: "md-button md-success btn-fill",
        cancelButtonClass: "md-button md-danger btn-fill",
        confirmButtonText: "Yes, delete it!",
        buttonsStyling: false
      }).then(result => {
        if (result.value) {
          users
            .remove(
              item.id,
              "P2KHLaJQv2GK1ZE7aMncyRgAdIUxWBmyI9DknRipdvoQ9si8zY8IBYZXaXPZBdsq"
            )
            .then(
              response => {
                
                Swal.fire({
                  title: "Deleted!",
                  text: `You deleted ${item.firstName + " " + item.lastName}`,
                  type: "success",
                  confirmButtonClass: "md-button md-success btn-fill",
                  buttonsStyling: false
                });
                users
                  .get(
                    "P2KHLaJQv2GK1ZE7aMncyRgAdIUxWBmyI9DknRipdvoQ9si8zY8IBYZXaXPZBdsq"
                  )
                  .then(response => {
                    this.tableData = response.data.filter(item => {
                      return item.id !== this.$session.get("jwtuid");
                    });
                  });
              },
              reason => {
                this.loading = false;
                this.$swal({
                  title: "Failed to delete account",
                  text: "possible invalid account (" + reason + ")",
                  type: "error",
                  animation: false
                });
              }
            );
          this.deleteRow(item);
        }
      });
    },
    deleteRow(item) {
      let indexToDelete = this.tableData.findIndex(
        tableRow => tableRow.id === item.id
      );
      if (indexToDelete >= 0) {
        this.tableData.splice(indexToDelete, 1);
      }
    }
  },
  mounted() {
    //this.$session.get("jwt")
    users
      .get("P2KHLaJQv2GK1ZE7aMncyRgAdIUxWBmyI9DknRipdvoQ9si8zY8IBYZXaXPZBdsq")
      .then(response => {
        this.tableData = response.data;
        this.fuseSearch = new Fuse(this.tableData, {
          keys: ["firstName", "lastName", "email"],
          threshold: 0.3
        });
        /*.filter(item => { return item.id !== this.$session.get("jwtuid");  }); */
      });
    // Fuse search initialization.
  },
  watch: {
    /**
     * Searches through the table data by a given query.
     * NOTE: If you have a lot of data, it's recommended to do the search on the Server Side and only display the results here.
     * @param value of the query
     */
    searchQuery(value) {
      let result = this.tableData;
      if (value !== "") {
        result = this.fuseSearch.search(this.searchQuery);
      }
      this.searchedData = result;
    }
  }
};
</script>

<style lang="css" scoped>
.md-card .md-card-actions {
  border: 0;
  margin-left: 20px;
  margin-right: 20px;
}
</style>
