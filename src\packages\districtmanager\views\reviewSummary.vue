<template>
  <div class="col-md-11" style="margin:0 auto">

      <div class="row align-items-center py-4" style="padding-right:0px; padding-left:0">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Disaster</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">Report</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">
                Summary
              </li>
            </ol>
          </nav>
        </div>

        <div class="col-lg-6 col-6 text-right pr-3">
          <base-dropdown
            title-classes="btn btn-sm btn-primary mr-0"
            menu-on-right
            :has-toggle="false"
          >
            <a slot="title" class="dropdown-toggle" type="neutral" size="sm">
               Options
            </a>

            <downloadexcel
              class="dropdown-item"
              :data="Array.from(download)"
              name="DisasterData.csv"
              type="csv"
              style="color:#26b6b2">
              EXPORT TO CSV
            </downloadexcel>

            <a
              class="dropdown-item"
              type="neutral"
              @click="printdiv('section-to-print')"
              >
              <span class="btn-inner--icon">
                <!--  <i class="ni ni-fat-add"></i> -->
              </span>
              <span class="btn-inner--text" style="color:#26b6b2">Print</span>
            </a>
            <a
              class="dropdown-item"
              type="neutral"
              @click="downloadPDF()"
              >
              <span class="btn-inner--icon">
                <!--  <i class="ni ni-fat-add"></i> -->
              </span>
              <span class="btn-inner--text" style="color:#26b6b2">Download PDF</span>
            </a>

          </base-dropdown>
          <base-button class="ml-2" size="sm" type="neutral" @click="goBack">
            Back
          </base-button>
        </div>

        <!-- <div class="col-lg-6 col-6 text-right pr-3">
          <base-button size="sm" type="neutral" @click.native="gotoPrint()">
            <span class="btn-inner--icon">
              <!--  <i class="ni ni-fat-add"></i> -->
            <!-- </span> -->
            <!-- <span class="btn-inner--text" @click="printdiv('section-to-print')"
              >Print</span
            >
          </base-button>
          <base-button size="sm" type="neutral" @click="downloadPDF()">
            <span class="btn-inner--icon"> -->
              <!--  <i class="ni ni-fat-add"></i> -->
               <!-- </span
            >Download PDF
            <span class="btn-inner--text"></span>
          </base-button> -->
        <!-- </div> -->
      </div>

    <div id="section-to-print" ref="content">
      <card
        class="no-border-card"
        body-classes="px-0 pb-1"
        footer-classes="pb-2"
      >
        <div>
          <div class="component-example__container">
            <SummaryReportHeader />
          </div>
          <SummaryReportBody />

          <table class="col-md-11">
            <!-- Response heading row -->
            <tr>
              <td colspan="12" style="text-align: center;">
                <h3><b>Response Provided</b></h3>
              </td>
            </tr>

            <!-- Response text row -->
            <tr>
              <td colspan="12" style="text-align: center;">
                {{ dinrFormsData.response || "No response provided" }}
              </td>
            </tr>
          </table>



          <table
            class="col-md-11"
            v-if="numberOfTAs > 0"
            width="100%"
            style="text-weight:bold;text-size:120%"
          >
            <tr>
              <td width="50%">
                <strong
                  >Checked and Verified by
                  <span v-if="this.officerSignature">{{
                    this.officerSignature.role
                  }}</span></strong
                >
              </td>
              <td>
                <strong
                  >Approved by
                  <span v-if="this.dcSignature">{{
                    this.dcSignature.role
                  }}</span></strong
                >
              </td>
            </tr>
            <tr>
              <td width="50%">
                <strong>Name :</strong>
                <span v-if="this.officerSignature">
                  {{
                    (this.officerSignature.signatory_fname
                      ? this.officerSignature.signatory_fname
                      : "") +
                      " " +
                      (this.officerSignature.signatory_lname
                        ? this.officerSignature.signatory_lname
                        : "")
                  }}
                </span>
              </td>
              <td width="50%">
                <strong>Name : </strong
                ><span v-if="this.dcSignature">{{
                  (this.dcSignature.signatory_fname
                    ? this.dcSignature.signatory_fname
                    : "") +
                    " " +
                    (this.dcSignature.signatory_lname
                      ? this.dcSignature.signatory_lname
                      : "")
                }}</span>
              </td>
            </tr>
            <tr>
              <td width="50%">
                <strong>Signature :</strong>
                <img
                  v-if="this.officerSignature && officerpath"
                  :src="officerpath"
                  style="box-shadow: 0 0 2px 2px white inset;"
                  class="mx-2"
                  width="100"
                  height="30"
                />
              </td>
              <td width="50%">
                <strong>Signature :</strong
                ><img
                  v-if="this.dcSignature && dcpath"
                  :src="dcpath"
                  style="box-shadow: 0 0 2px 2px white inset;"
                  class="mx-2"
                  width="100"
                  height="30"
                />
              </td>
            </tr>

            <tr>
              <td width="50%">
                <strong>Date :</strong>
                <span v-if="this.officerSignature">{{
                  formatdate(this.officerSignature.createdon)
                }}</span>
              </td>
              <td width="50%">
                <strong>Date : </strong
                ><span v-if="this.dcSignature">{{
                  dinrFormsData.createdon
                    ? formatdate(dinrFormsData.createdon)
                    : ""
                }}</span>
              </td>
            </tr>
            <tr >
              <td colspan="12" style="text-align: center;">
                <strong>
                  Acted on By DoDMA
                </strong>

              </td>


            </tr>
            <tr><td>
              <strong>Date:</strong>
              <span v-if="dinrFormsData.actedonDate">
                {{ formatdate(dinrFormsData.actedonDate) }}
              </span>

            </td>
            <td>
              <strong>Acted By:</strong> {{ dinrFormsData.actedBy }}
            </td>
            </tr>
          </table>

          <p v-if="numberOfTAs <= 0">
            <center>
              <h3>
                <strong>NO DATA</strong>
              </h3>
            </center>
          </p>
        </div>
      </card>
    </div>
  </div>
</template>

<script>
import { MongoReports } from "../api/MongoReports";
import downloadexcel from "vue-json-excel";
import moment from "moment";
import axios from "axios";
import lodash from "lodash";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
var QRCode = require("qrcode");
import SummaryReportHeader from "../../dashboards/components/summaryReportHeader.vue";
import SummaryReportBody from "../../dashboards/components/summaryReportBody.vue";
import { mapGetters, mapActions } from "vuex";
export default {
  name: "ManagerSummary",
  components: {
    downloadexcel,
    SummaryReportHeader,
    SummaryReportBody
  },

  data() {
    return {
      isLoaded: false,
      signatureText: "",
      signature: {},
      dcSignature: {},
      officerSignature: {},
      officerPath: "",
      dcPath: "",
      draFormsDataOther: [],
      food_stocks_avaliability_array: [],
      impact_on_schools_array: [],
      nutritionAffected_populationArray: [],
      numberOfTAs: "",
      dinrFormsData: {},
      villagesArray: [],
      bufferDraFormsData: [],
      draFormsData: [],
      download: [],
      TAarray: [],
      downloadData: [],
      otherData: [],
      vhhousesvalue: "",
      shelterArray: [],
      displacedArray: [],
      agricultureArray: [],
      cropDamageLossArray: [],
      healthArray: [],
      washArray: [],
      livelihoodsArray: [],
      protectionArray: [],
      foodArray: [],
      nutritionArray: [],
      educationArray: [],
      environmentArray: [],
      people_without_shelter: [],
      PeopleAffectedrows: [],
      PeopleAffectedrowsD: [],
      PeopleInjuredrows: [],
      PeopleDeadrows: [],
      crops_damaged: [],
      food_item_damage: [],
      food_stocks_avaliability: [],
      impact_on_schools: [],
      available_health_facilities: [],
      other_health_facilities: [],
      livelihoods_affected: [],
      affected_population: [],
      impact_on_vulnerable_persons: []
    };
  },
  computed: {
    ...mapGetters({
      getDinrById: "dinrs/getById"
    }),
    ...mapGetters({
      getDraById: "dras/getById"
    }),
    officerpath: function() {
      if (this.officerSignature && this.officerSignature.signature)
        return (
          process.env.VUE_APP_ENGINE_URL +
          "/" +
          this.officerSignature.signature.replace("\\", "/")
        );
    },
    dcpath: function() {
      if (this.dcSignature && this.dcSignature.signature)
        return (
          process.env.VUE_APP_ENGINE_URL +
          "/" +
          this.dcSignature.signature.replace("\\", "/")
        );
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
    ...mapActions("dinrs", {
      getDinrsAction: "get"
    }),
    ...mapActions("dras", {
      getDrasAction: "get"
    }),
    numberWithCommas(number) {
      try {
        var parts = number.toString().split(".");
        parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        return parts.join(".");
      } catch (e) {}
    },
    arrayAggregator(arrayData) {
      var result = [
        arrayData.reduce((acc, n) => {
          for (var prop in n) {
            if (acc.hasOwnProperty(prop)) {
              if (isNaN(acc[prop])) {
                acc[prop] = acc[prop] + "," + n[prop];
              } else acc[prop] = parseFloat(acc[prop]) + parseFloat(n[prop]);
            } else acc[prop] = n[prop];
          }
          return acc;
        }, {})
      ];
    },
    dateFormate(dateData) {
      const cur_date = dateData;
      const formattedDate = moment(cur_date).format("DD/MM/YYYY");

      return formattedDate;
    },
    sumArrays(data, objectname, member, males, females) {
      // alert(data.length);

      for (let i = 0; i < data.length; i++) {
        try {
          if (objectname == "displaced" && i > 0) {
            member = member.replace("D", "");
          }
          let sumFemales = 0;
          let sumMales = 0;
          let x = data[i];
          let actualObject = x[objectname];

          actualObject[member].forEach(function(obj) {
            sumFemales += parseInt(obj[females] ? obj[females] : 0);
            sumMales += parseInt(obj[males] ? obj[males] : 0);
          });

          if (objectname == "displaced") {
            member = member + "D";
          }

          this[member].push({
            ta: data[i].admin3.admin3Name_en,
            females: sumFemales,
            males: sumMales
          });
        } catch (error) {}
      }
    },

    sumEducationArrays(
      data,
      objectname,
      member,
      roofs_affected,
      partially_functioning,
      underwater,
      completely_damaged,
      females_out_of_school,
      males_out_of_school
    ) {
      // alert(data.length);

      for (let i = 0; i < data.length; i++) {
        try {
          let sum1 = 0,
            sum2 = 0,
            sum3 = 0,
            sum4 = 0,
            sum5 = 0,
            sum6 = 0;

          let x = data[i];
          let actualObject = x[objectname];

          actualObject[member].forEach(function(obj) {
            sum1 += parseInt(obj[roofs_affected] ? obj[roofs_affected] : 0);
            sum2 += parseInt(
              obj[partially_functioning] ? obj[partially_functioning] : 0
            );
            sum3 += parseInt(obj[underwater] ? obj[underwater] : 0);
            sum4 += parseInt(
              obj[completely_damaged] ? obj[completely_damaged] : 0
            );
            sum5 += parseInt(
              obj[females_out_of_school] ? obj[females_out_of_school] : 0
            );
            sum6 += parseInt(
              obj[males_out_of_school] ? obj[males_out_of_school] : 0
            );
          });

          this[member].push({
            ta: data[i].admin3.admin3Name_en,
            roofs_affected: sum1,
            partially_functioning: sum2,
            underwater: sum3,
            completely_damaged: sum4,
            females_out_of_school: sum5,
            males_out_of_school: sum6
          });
        } catch (error) {}
      }
    },

    sumHealthArrays(
      data,
      objectname,
      member,
      partially_functioning,
      verge_of_closing,
      closed
    ) {
      // alert(data.length);

      for (let i = 0; i < data.length; i++) {
        try {
          let sum1 = 0,
            sum2 = 0,
            sum3 = 0;

          let x = data[i];
          let actualObject = x[objectname];

          actualObject[member].forEach(function(obj) {
            sum1 += parseInt(
              obj[partially_functioning] ? obj[partially_functioning] : 0
            );
            sum2 += parseInt(obj[verge_of_closing] ? obj[verge_of_closing] : 0);
            sum3 += parseInt(obj[closed] ? obj[closed] : 0);
          });

          this[member].push({
            ta: data[i].admin3.admin3Name_en,
            verge_of_closing: sum1,
            partially_functioning: sum2,
            closed: sum3
          });
        } catch (error) {}
      }
    },
    //alert(JSON.stringify(this.PeopleAffectedrows));
    downloadPDF() {
      var quotes = document.getElementById("section-to-print");
      html2canvas(quotes).then(canvas => {
        var imgData = canvas.toDataURL("image/png");

        var imgWidth = 210;
        var pageHeight = 295;
        var imgHeight = (canvas.height * imgWidth) / canvas.width;
        var heightLeft = imgHeight;

        var doc = new jsPDF("p", "mm", "A4");
        var position = 0;

        doc.addImage(imgData, "PNG", 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;

        while (heightLeft >= 0) {
          position = heightLeft - imgHeight;
          doc.addPage();
          doc.addImage(imgData, "PNG", 0, position, imgWidth, imgHeight);
          heightLeft -= pageHeight;
        }

        const disasterdistrict = document.getElementById("district").innerHTML;
        const disastertype = document.getElementById("disastertype").innerHTML;
        const disasterdate = document.getElementById("disasterstart").innerHTML;

        const filename = disasterdistrict + disastertype + disasterdate;

        doc.save(filename);
      });
    },
    printdiv(printpage) {
      var headstr = "<html><head><title></title></head><body>";
      var footstr = "</body>";
      var newstr = document.all.item(printpage).innerHTML;
      var oldstr = document.body.innerHTML;
      var disasterdistrict = document.getElementById("district").innerHTML;
      var disastertype = document.getElementById("disastertype").innerHTML;
      var disasterdate = document.getElementById("disasterstart").innerHTML;

      document.body.innerHTML = headstr + newstr + footstr;
      document.title =
        disasterdate + "_" + disasterdistrict + "_" + disastertype;
      window.print();
      document.body.innerHTML = oldstr;
      location.reload();
      return false;
    },
    formatdate(data) {
      const cur_date = data;
      const formattedDate = moment(cur_date).format("DD/MM/YYYY");
      return formattedDate;
    }
  },
  computeLogo() {
    return "../../../../static/logo.png";
  },

  updated() {
    delete this.signatureText.signature;
    delete this.signatureText.status;
    /*    console.log(this.signatureText)
    QRCode.toDataURL(JSON.stringify(this.signatureText),{width:"90",type: 'image/jpeg',
  quality: 1}, function(err, url) {
      if (error) console.error(error);
      console.log("success!");

      var img = document.getElementById('qr-code')
      img.src = url
    }); */

    var opts = {
      errorCorrectionLevel: "H",
      type: "image/jpeg",
      quality: 1,
      margin: 1
    };

    QRCode.toDataURL(
      JSON.stringify(this.signatureText || "not electronically signed by DC"),
      opts,
      function(err, url) {
        if (err) throw err;

        var img = document.getElementById("qr-code");
        img.src = url;
      }
    );
  },
  async mounted() {
    //Get DINR Form
    this.shelterArray = [];
    this.displacedArray = [];
    this.agricultureArray = [];
    this.healthArray = [];
    this.washArray = [];
    this.livelihoodsArray = [];
    this.protectionArray = [];
    this.foodArray = [];
    this.nutritionArray = [];
    this.educationArray = [];
    this.cropDamageLossArray = [];
    this.environmentArray = [];
    this.draFormsData = [];

    //console.log(this.$route);

    //initialise number TAs in DNIR
    this.numberOfTAs = 0;

    let count = 0;
    //Get DRA Form

    await this.getDinrsAction();
    await this.getDrasAction();
    //Get DINR Form
    let response = this.getDinrById(
      this.$route.params.uuid /* , this.$session.get("jwt") */
    );
    this.dinrFormsData = response;
    this.signatureText = this.dinrFormsData.approvalMetadata
      ? Object.assign({}, this.dinrFormsData.approvalMetadata.signature)
      : "";

    //console.log(response);

    this.signature = this.dinrFormsData.approvalMetadata
      ? [...this.dinrFormsData.approvalMetadata.signature]
      : [];

    this.dcSignature = this.signature.filter(
      item => item.role === "district commissioner"
    )[0];

    this.officerSignature = this.signature.filter(
      item => item.role !== "district commissioner"
    )[0];

    this.food_stocks_avaliability_array = [];
    this.nutritionAffected_populationArray = [];
    this.impact_on_schools_Array = [];
    var self = this;

    response = this.getDraById(
      this.$route.params.uuid /* , this.$session.get("jwt") */
    );
    this.bufferDraFormsData = response;

    this.downloadData.dinrform = {};

    this.bufferDraFormsData.forEach(item => {
      this.downloadData.all = item;

      this.TAarray.push(item.admin3.admin3Name_en.trim());

      for (let i in item.villages) {
        item.villages[i] && item.villages[i].name
          ? this.villagesArray.push(item.villages[i].name)
          : "";
      }

      this.TAarray = this.TAarray.sort();
      this.villagesArray = this.villagesArray
        .join()
        .replace(/ /g, "")
        .split(",")
        .sort()
        .filter(function(item, pos, self) {
          return self.indexOf(item) == pos;
        });

      this.villagesArray = this.villagesArray.sort();

      if (!item.sectors) {
        return;
      }

      this.numberOfTAs++;

      this.draFormsDataOther.push(item);
      var preparedData = {};
      preparedData.sectors = item.sectors;
      preparedData.sectors.admin3 = item.admin3;
      this.draFormsData.push(preparedData.sectors);
      this.shelterArray.push(item.sectors.shelter);
      this.displacedArray.push(item.sectors.displaced);
      this.agricultureArray.push(item.sectors.agriculture);
      this.healthArray.push(item.sectors.health);
      if (item.sectors.food && item.sectors.food.food_stocks_avaliability)
        for (
          let i = 0;
          i < item.sectors.food.food_stocks_avaliability.length;
          i++
        ) {
          item.sectors.food.food_stocks_avaliability[i].ta =
            item.admin3.admin3Name_en;
          item.sectors.food.food_stocks_avaliability[i]
            ? this.food_stocks_avaliability_array.push(
                item.sectors.food.food_stocks_avaliability[i]
              )
            : {};
        }

      if (item.sectors.nutrition && item.sectors.nutrition.affected_population)
        for (
          let i = 0;
          i < item.sectors.nutrition.affected_population.length;
          i++
        ) {
          item.sectors.nutrition.affected_population[i].ta =
            item.admin3.admin3Name_en;
          item.sectors.nutrition.affected_population[i]
            ? this.nutritionAffected_populationArray.push(
                item.sectors.nutrition.affected_population[i]
              )
            : {};
        }

      if (item.sectors.education && item.sectors.education.impact_on_schools)
        for (
          let i = 0;
          i < item.sectors.education.impact_on_schools.length;
          i++
        ) {
          item.sectors.education.impact_on_schools[i]
            ? self.impact_on_schools_Array.push(
                item.sectors.education.impact_on_schools[i]
              )
            : {};
        }

      this.washArray.push(item.sectors.wash);
      this.livelihoodsArray.push(item.sectors.livelihoods);
      this.protectionArray.push(item.sectors.protection);
      this.foodArray.push(item.sectors.food);
      this.nutritionArray.push(item.sectors.nutrition);
      this.educationArray.push(item.sectors.education);
      this.environmentArray.push(item.sectors.environment);
    });

    var food_stocks_avaliability_arrayResult = [];
    var nutritionAffected_populationArrayResult = [];
    var impact_on_schools_ArrayResult = [];

    this.food_stocks_avaliability_array.reduce(function(res, value) {
      if (!res[value.category]) {
        res[value.category] = {
          ta: value.ta,
          category: value.category,
          female_HH: 0,
          male_HH: 0
        };
        food_stocks_avaliability_arrayResult.push(res[value.category]);
      }
      res[value.category].female_HH += value.female_HH;
      res[value.category].male_HH += value.male_HH;
      return res;
    }, {});

    this.food_stocks_avaliability_array = food_stocks_avaliability_arrayResult;

    this.nutritionAffected_populationArray.reduce(function(res, value) {
      if (!res[value.name]) {
        res[value.name] = {
          ta: value.ta,
          name: value.name,
          affected_females: 0,
          affected_males: 0
        };
        nutritionAffected_populationArrayResult.push(res[value.name]);
      }
      res[value.name].affected_females += value.affected_females;
      res[value.name].affected_males += value.affected_males;
      return res;
    }, {});

    this.nutritionAffected_populationArray = nutritionAffected_populationArrayResult;
    //console.log(this);
    this.impact_on_schools_Array.reduce(function(res, value) {
      if (!res[value.structure_type]) {
        res[value.structure_type] = {
          school_type: value.school_type ? value.school_type : "",
          structure_type: value.structure_type ? value.structure_type : "",
          partially_functioning: 0,
          completely_damaged: 0,
          roofs_affected: 0,
          underwater: 0,
          females_out_of_school: 0,
          males_out_of_school: 0
        };
        impact_on_schools_ArrayResult.push(res[value.structure_type]);
      }
      res[value.structure_type].partially_functioning += parseInt(
        value.partially_functioning ? value.partially_functioning : 0
      );
      res[value.structure_type].completely_damaged += parseInt(
        value.completely_damaged ? value.completely_damaged : 0
      );

      res[value.structure_type].roofs_affected += parseInt(
        value.roofs_affected ? value.roofs_affected : 0
      );
      res[value.structure_type].underwater += parseInt(
        value.underwater ? value.underwater : 0
      );

      res[value.structure_type].females_out_of_school += parseInt(
        value.females_out_of_school ? value.females_out_of_school : 0
      );
      res[value.structure_type].males_out_of_school += parseInt(
        value.males_out_of_school ? value.males_out_of_school : 0
      );
      return res;
    }, {});

    this.impact_on_schools_Array = impact_on_schools_ArrayResult;

    this.villagesArray = this.villagesArray
      .join()
      .split(",")
      .filter(function(item, pos, self) {
        return self.indexOf(item) == pos;
      });

    this.sumArrays(
      this.draFormsData,
      "shelter",
      "people_without_shelter",
      "males",
      "females"
    );

    this.sumArrays(
      this.draFormsData,
      "shelter",
      "PeopleAffectedrows",
      "damaged_mhh",
      "damaged_fhh"
    );

    this.sumArrays(
      this.draFormsData,
      "displaced",
      "PeopleAffectedrows",
      "number_displaced_by_gender_mhh",
      "number_displaced_by_gender_fhh"
    );

    this.sumArrays(
      this.draFormsData,
      "shelter",
      "PeopleInjuredrows",
      "people_injured_males",
      "people_injured_females"
    );

    this.sumArrays(
      this.draFormsData,
      "shelter",
      "PeopleDeadrows",
      "males_dead",
      "females_dead"
    );

    this.sumArrays(
      this.draFormsData,
      "agriculture",
      "crops_damaged",
      "hectares_submerged",
      "hectares_washed_away"
    );

    this.sumArrays(
      this.draFormsData,
      "agriculture",
      "food_item_damage",
      "number_of_kilos",
      "food_item_name"
    );

    this.sumArrays(
      this.draFormsData,
      "food",
      "food_stocks_avaliability",
      "male_HH",
      "female_HH"
    );

    this.sumEducationArrays(
      this.draFormsData,
      "education",
      "impact_on_schools",
      "roofs_affected",
      "partially_functioning",
      "underwater",
      "completely_damaged",
      "females_out_of_school",
      "males_out_of_school"
    );

    this.sumHealthArrays(
      this.draFormsData,
      "health",
      "available_health_facilities",
      "partially_functioning",
      "verge_of_closing",
      "closed"
    );

    this.sumHealthArrays(
      this.draFormsData,
      "health",
      "other_health_facilities",
      "partially_functioning",
      "verge_of_closing",
      "closed"
    );

    this.sumArrays(
      this.draFormsData,
      "livelihoods",
      "livelihoods_affected",
      "severely_affected",
      "slightly_affected"
    );

    this.sumArrays(
      this.draFormsData,
      "nutrition",
      "affected_population",
      "affected_males",
      "affected_females"
    );

    this.sumArrays(
      this.draFormsData,
      "protection",
      "impact_on_vulnerable_persons",
      "impacted_males",
      "impacted_females"
    );
  }
};
</script>

<style lang="stylus" scoped>
table {
    border-collapse: collapse;
    width: 100%;
    margin: 0 auto;
}

.verticalize {
    transform: rotate(270deg);
}

.rotated {
    writing-mode: tb-rl;
    transform: rotate(-180deg);
    color: Teal;
}

table,
th,
td {
    border: 1px solid black;
    margin-top: 1%;
}

td {
    padding: 1%;
}

.noborder {
    border: 0;
}

.qcont:first-letter {
    text-transform: capitalize;
}

.right-align {
    text-align: right;
}

@media print {
    .section-not-to-print {
        visibility: hidden;
    }

    #section-to-print {
        visibility: visible;
    }
}
</style>
