<template>
  <div id="mapId" style="position: relative; width:300px; height:600px;"></div>
</template>
<script>
require("topojson");
import json from "./malawi.topo.json";
import Datamap from "datamaps";
import * as d3 from "d3";

global.jQuery = require("jquery");
var $ = global.jQuery;
window.$ = $;

export default {
  mounted() {
    this.$nextTick(function() {
      var element = document.getElementById("mapId");
      var map = new Datamap({
        element: element,
        geographyConfig: {
          dataJson: json
        },
        scope: "mwi",
        setProjection: function(element) {
          var projection = d3
            .geoMercator()
            .scale(4000)
            .center([34.3015278, -13.2512161])
            .translate([element.offsetWidth / 2, element.offsetHeight / 2]);
          var path = d3.geoPath().projection(projection);

          return { path: path, projection: projection };
        },
        done: function(map) {
          map.svg
            .selectAll(".datamaps-subunit")
            .on("click", function(geography) {
              alert(geography.properties.name);
            });
        }
      });
    });
  }
};
</script>

<style>
#map {
  font-family: "Avenir", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  margin-top: 60px;
}
</style>
