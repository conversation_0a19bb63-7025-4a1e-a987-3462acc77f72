import axios from 'axios'
import rootapi from './../config'
const resource = rootapi.engine + '/formsFinal/dra'
export default class Dras {
  static get () {
    return axios.get(resource).then(response => {
      return response
    })
  }
  static async remove (id) {
    let response = await axios.delete(resource + '/' + id).then(response => {
      return response.data
    })
    return response
  }
  static getOne (id, token) {
    return axios
      .get(resource + '/' + id + '?access_token=' + token)
      .then(response => {
        return response.data
      })
  }
  static update (model) {
    return axios.put(resource, model).then(response => {
      return response.data
    })
  }
  static async create (model) {
    return await axios.post(resource, model).then(response => {
      return response.data
    })
  }
  static async count (id) {
    let response = await axios
      .get(resource + '/count' + '?access_token=' + id)
      .then(response => {
        return response.data
      })
    return response
  }
}
