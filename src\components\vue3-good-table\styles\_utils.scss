/* Utility styles
************************************************/
.vgt-right-align{
  text-align: right;
}

.vgt-left-align{
  text-align: left;
}

.vgt-center-align{
  text-align: center;
}

.vgt-pull-left{
  float:  left !important;
}

.vgt-pull-right{
  float:  right !important;
}

.vgt-clearfix::after {
  display: block;
  content: "";
  clear: both;
}

.vgt-responsive {
  width: 100%;
  overflow-x: auto;
  position: relative;
}

.vgt-text-disabled{
  color: $secondary-text-color;
}

.sr-only {
  clip: rect(0 0 0 0); 
  clip-path: inset(50%);
  height: 1px;
  overflow: hidden;
  position: absolute;
  white-space: nowrap; 
  width: 1px;
}