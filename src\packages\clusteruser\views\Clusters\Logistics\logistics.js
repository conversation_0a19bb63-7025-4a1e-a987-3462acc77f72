import { Table, TableColumn, Select, Option } from 'element-ui'
import RouteBreadCrumb from '@/components/Breadcrumb/RouteBreadcrumb'
import { BasePagination } from '@/components'
import clientPaginationMixin from '@/components/PaginatedTables/clientPaginationMixin'
import {
  generateExcel,
  generateTAGVHExcel
} from '../../../../../util/generateExcel'
import { generateCSV } from '../../../../../util/generateCSV'
import utils from '../../../../../util/dashboard'
import { MongoReports } from '../../../api/MongoReports'
import downloadexcel from 'vue-json-excel'
import dateformat from 'dateformat'
import { mapGetters, mapActions } from 'vuex'
import JSZip from 'jszip'
import { saveAs } from 'file-saver'

import moment from 'moment'

export default {
  mixins: [clientPaginationMixin],
  components: {
    BasePagination,
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    [Table.name]: Table,
    downloadexcel,
    [TableColumn.name]: TableColumn
  },
  data() {
    return {
      propsToSearch: [
        'disasterdate',
        'structure_damaged',
        'max_vehicle_size',
        'condition_of_structure',
        'submitedby'
      ],
      currentpage: 1,

      loading: true,
      searchQuery: '',
      bufferDraFormsData: [],
      disablebutton: false,
      images: [],
      counter: 0,
      clusterreports: [],
      telecomreports: [],

      engreports: [],
      gridOptions: {},
      tableColumns: [
        /*  {
          key: 'id',
          label: 'ID',
          sortable: true
        }, */
        {
          key: 'district',
          label: 'District',
          sortable: true
        },
        {
          key: 'ta',
          label: 'TA affected',
          sortable: true
        },

        {
          key: 'approximateDate',
          label: 'Approx Date of occurence',
          sortable: true
        },

        {
          key: 'dateOfVisit',
          label: 'Date of visit',
          sortable: true
        },
        {
          key: 'structure_damaged',
          label: 'Structure damaged at site',
          thStyle: { width: '1px', fontsize: '34px' },
          sortable: true
        },

        {
          key: 'is_structrure_damaged',
          label: 'Is structure damaged?',
          tdClass: 'wrap',
          sortable: true
        },

        {
          key: 'is_structrure_operational',
          label: 'Is structure operational?',
          tdClass: 'wrap',
          sortable: true
        },

        {
          key: 'condition_of_site',
          label: 'Surface condition of site',
          sortable: true
        },

        /*
        {
          key: 'structure_if_other',
          label: 'Structure damaged (If other)',
          sortable: true
        },

        {
          key: 'structure_name_if_other',
          label: 'Structure damaged (If not Road)',
          sortable: true
        },

        {
          key: 'structure_name_if_other',
          label: 'Structure damaged (If not Road)',
          sortable: true
        },
 */

        /*  {
           key: 'max_vehicle_size',
           label: 'Maximum Size Vehicle',
           sortable: true
         }, */

        {
          key: 'gps_lat',
          label: 'GPS lat',
          sortable: true
        },

        {
          key: 'gps_long',
          label: 'GPS long',
          sortable: true
        },

        {
          key: 'submitedby',
          label: 'Submmited by',
          sortable: true
        },

        {
          key: 'date',
          label: 'Submmited on',
          sortable: true
        },

        {
          key: 'status',
          label: 'Status',
          sortable: true
        },
        {
          key: 'actions',
          label: 'Actions'
        }
      ],
      tableData: [],
      selectedRows: []
    }
  },
  methods: {
    ...mapActions('clusterreports', {
      getclusterreportsAction: 'get'
    }),

    formatedDate(data) {
      let finalDate = dateformat(data, 'dd-mm-yyyy hh:mm:ss')
      return finalDate
    },

    formateTime(dataObject) {
      let finalTime = dateformat(dataObject, 'HH:mm:ss')
      return finalTime
    },

    downloadExcelSheetTelecom() {
      let telecomData = this.tableData.filter(
        data => data.structure_damaged == 'Telecommunication infrastructure'
      )

      let data = telecomData.map(item => {
        return {
          District: item.district,
          TA: item.ta,
          'Date of visit': item.dateOfVisit,
          'Approximate Date of occurence': item.approximateDate,
          'Which type of structure has been damaged?': item.structure_damaged,
          'What is the type of telecommunication infrastructure affected':
            item.tele_infrastructure,
          'What is the functionality of the infrastructure?':
            item.tele_infrastructure_state,
          'What is the telecommunications provider?':
            item.tele_infrastructure_provider,
          'GPS lat': item.gps_lat,
          'GPS long': item.gps_long,
          'Submitted by': item.submitedby,

          'Status': item.status == '4' ? 'Reviewed' : 'Not Reviewed',
          'Submitted on': item.date,
          'Edited by': item.editedby,
          'Edited on': item.editedon,
          sheet: 'Telecommications'
        }
      })

      this.telecomreports = data

      generateTAGVHExcel(this.telecomreports)
    },



    downloadExcelSheetEng() {

      let EngData = this.tableData.filter(
        data => data.add_engineering_specs == true
      )


      let data = EngData.map(item => {
        return {
          District: item.district,
          TA: item.ta,
          'Date of visit': item.dateOfVisit,
          'Approximate Date of occurence': item.approximateDate,
          'Is there an alternative route': item.is_alternative_route,
          'If yes, what is the road designation for alternative route?': item.alternative_route,
          'Are construction material available locally?': item.construction_material_available,
          'Drainage structure (If Applicable)': item.drainage_structure,
          'Type of Bridge (If Applicable)': item.type_of_bridge,
          'Type of Culvert (If Applicable)': item.type_of_culvert,
          'Type of Drift (If Applicable)': item.type_of_drift,

          'GPS lat': item.gps_lat,
          'GPS long': item.gps_long,

          'Comments': item.comments,
          'Status': item.status == '4' ? 'Reviewed': 'Not Reviewed',
      
          'Submitted by': item.submitedby,
          'Submitted on': item.date,
          'Edited by': item.editedby,
          'Edited on': item.editedon,
          sheet: 'Engineering Data'
        }
      })

      this.engreports = data

      generateTAGVHExcel(this.engreports)
    },

    downloadExcelSheet() {
      let logisticsData = this.tableData.filter(
        data => data.structure_damaged != 'Telecommunication infrastructure'
      )

      let data = logisticsData.map(item => {
        return {
          District: item.district,
          TA: item.ta,
          'Date of visit': item.dateOfVisit,
          'Approximate Date of occurence': item.approximateDate,
          'Which type of structure has been damaged?': item.structure_damaged,
          'Which type of structure (If other)': item.structure_if_other,
          'Name of structure (If Not Road)': item.structure_name_if_other,
          'What is the link #? (If Road)': item.linknumber,
          'What is the approximate length of the damage? (If Road) (Meters)':
            item.length_of_damage,
          'What is the current maximum size vehicle type that can access this road?':
            item.max_vehicle_size,
          'What is the condition of the damaged structure?':
            item.condition_of_structure,
          'What is the surface condition at the damaged site?':
            item.condition_of_site,

          'Is the structure damaged?': item.is_structrure_damaged,

          'Is the structure operational?': item.is_structrure_operational,
          'Status': item.status == '4' ? 'Reviewed': 'Not Reviewed',
      
          'GPS lat': item.gps_lat,
          'GPS long': item.gps_long,
          'Submitted by': item.submitedby,
          'Submitted on': item.date,
          'Edited by': item.editedby,
          'Edited on': item.editedon,
          sheet: 'Logistics'
        }
      })

      this.clusterreports = data

      generateTAGVHExcel(this.clusterreports)
    },

    convertBase64ToFile(base64String, fileName) {
      let arr = base64String.split(',')
      let mime = arr[0].match(/:(.*?);/)[1]
      let bstr = atob(arr[1])
      let n = bstr.length
      let uint8Array = new Uint8Array(n)
      while (n--) {
        uint8Array[n] = bstr.charCodeAt(n)
      }
      let file = new File([uint8Array], fileName, { type: mime })
      return file
    },

    createArchive(images, name) {
      var zip = new JSZip()
      var img = zip.folder('images')

      for (var i = 0; i < images.length; i++) {
        img.file(name + '-' + i + '.jpg', images[i], { base64: true })
      }
      zip.generateAsync({ type: 'blob' }).then(function (file) {
        saveAs(file, name + '-' + 'images' + '.zip')
      })
    },

    downloadBase64Data(base64Strings) {
      let baseImages = base64Strings.image
      let archiveName = ''
      for (let i in baseImages) {
        archiveName = base64Strings.ta + '-' + Date.now()
        let imgFile = this.convertBase64ToFile(baseImages[i], archiveName)
        this.images.push(imgFile)
      }
      this.createArchive(this.images, archiveName)
    },


    downloadBase64Dataset() {
      let images = []
      let archiveName = Date.now()

      let dataArray = this.tableData

      for (let i in dataArray) {
        let imageArray = dataArray[i].image
        if (Array.isArray(imageArray)) {
          for (let j in imageArray) {
            let imgFile = null
            try {
              imgFile = this.convertBase64ToFile(
                imageArray[j],
                `${archiveName}-${i}-${j}`
              )
            } catch (error) {
              console.log(
                `Error converting image at index ${j} of element ${i}: ${error.message}`
              )
            }
            if (imgFile) {
              images.push(imgFile)
            }
          }
        }
      }

      this.createArchive(images, archiveName)
    },

    gotoEdit(id) {
      this.$router.push({
        name: 'clusterEditreports',
        params: { id: id }
      })
    },

    gotoReview(id) {
      this.$router.push({
        name: 'clusterReviewreports',
        params: { id: id }
      })
    }
  },

  async mounted() {
    this.loading = true
    const clusterdata = await this.getclusterreportsAction()
    this.loading = false
    const formattedData = clusterdata.map((cluster, index) => {
      let cData = clusterdata.sort(function (a, b) {
        return new Date(b.createdon) - new Date(a.createdon)
      })

      return {
        id: index + 1,
        _id: cData[index]._id,

        user: cData[index].user,
        approximateDate:
          cData[index].approximateDate == undefined
            ? 'N/A'
            : cData[index].approximateDate,

        dateOfVisit:
          cData[index].dateOfVisit == undefined
            ? 'N/A'
            : cData[index].dateOfVisit,

        district:
          cData[index].user.district.admin2Name_en == undefined
            ? 'N/A'
            : cData[index].user.district.admin2Name_en,
        image:
          cData[index].impact.images == undefined
            ? []
            : cData[index].impact.images,
        ta:
          cData[index].TA == undefined ? 'N/A' : cData[index].TA.admin3_name_en,
        district:
          cData[index].TA == undefined ? 'N/A' : cData[index].TA.admin2_name_en,

        editcounter:
          cData[index].editcounter == undefined
            ? 'N/A'
            : cData[index].editcounter,
        date:
          this.formatedDate(cData[index].createdon) == undefined
            ? 'N/A'
            : this.formatedDate(cData[index].createdon),
        structure_damaged:
          cData[index].impact.structure_affected == undefined
            ? 'N/A'
            : cData[index].impact.structure_affected.response,

        add_engineering_specs:
          cData[index].impact.add_engineering_specs == undefined
            ? false
            : cData[index].impact.add_engineering_specs,
        structure_if_other:
          cData[index].impact.structure_affected == undefined
            ? 'N/A'
            : cData[index].impact.structure_affected.other_name,
        structure_name_if_other:
          cData[index].impact.structure_affected.name == undefined
            ? 'N/A'
            : cData[index].impact.structure_affected.name,
        max_vehicle_size:
          cData[index].impact.vehicle_size.response == undefined || null
            ? 'N/A'
            : cData[index].impact.vehicle_size.response,
        condition_of_structure:
          cData[index].impact.condition_of_structrure.response == undefined ||
            null
            ? 'N/A'
            : cData[index].impact.condition_of_structrure.response,

        is_structrure_damaged:
          cData[index].impact.is_structrure_damaged == undefined || null
            ? 'N/A'
            : cData[index].impact.is_structrure_damaged.response,
        is_structrure_operational:
          cData[index].impact.is_structrure_operational == undefined || null
            ? 'N/A'
            : cData[index].impact.is_structrure_operational.response,

        linknumber:
          cData[index].impact.structure_affected.linknumber == undefined || null
            ? 'N/A'
            : cData[index].impact.structure_affected.linknumber.response,
        length_of_damage:
          cData[index].impact.structure_affected.length_of_damage ==
            undefined || null
            ? 'N/A'
            : cData[index].impact.structure_affected.length_of_damage,
        condition_of_site:
          cData[index].impact.condition_of_site.response == undefined || null
            ? 'N/A'
            : cData[index].impact.condition_of_site.response,
        gps_lat: cData[index].impact.site_cordinates.lat,
        gps_long: cData[index].impact.site_cordinates.long,
        tele_infrastructure: cData[index].impact.tele_infrastructure.response,
        tele_infrastructure_state:
          cData[index].impact.tele_infrastructure.state,
        tele_infrastructure_provider:
          cData[index].impact.tele_infrastructure.service_provider,
        submitedby:
          cData[index].user.firstName + ' ' + cData[index].user.lastName,
        editedby:
          cData[index].editedhistory[cData[index].editedhistory.length - 1]
            .editedby,
        editedon:
          cData[index].editedhistory[cData[index].editedhistory.length - 1]
            .editedon,
        uuid: cData[index]._id,
        add_engineering_specs: cData[index].impact.add_engineering_specs ? cData[index].impact.add_engineering_specs : undefined,

        is_alternative_route: cData[index].impact.engineering_specs ? cData[index].impact.engineering_specs.is_alternative_route : undefined,
        alternative_route: cData[index].impact.engineering_specs ? cData[index].impact.engineering_specs.alternative_route : undefined,
        construction_material_available: cData[index].impact.engineering_specs ? cData[index].impact.engineering_specs.construction_material_available : undefined,
        drainage_structure: cData[index].impact.engineering_specs ? cData[index].impact.engineering_specs.drainage_structure : undefined,
        type_of_bridge: cData[index].impact.engineering_specs ? cData[index].impact.engineering_specs.type_of_bridge : undefined,
        type_of_culvert: cData[index].impact.engineering_specs ? cData[index].impact.engineering_specs.type_of_culvert : undefined,
        type_of_drift: cData[index].impact.engineering_specs ? cData[index].impact.engineering_specs.type_of_drift : undefined,

        status: cData[index].status
      }
    })

    formattedData.sort((a, b) => new Date(b.createdon) - new Date(a.createdon))

    const userClusterUserType = this.$session.get('userObj').cluster_user_type;

    // Check if the user's cluster user type is 'Roads Authority'
    if (userClusterUserType === 'Roads Authority') {
      // Filter the data based on the condition item.add_engineering_specs === true
      return this.tableData = formattedData.filter(item => item.add_engineering_specs == true && item.user.cluster_user_type == 'Roads Authority');
    } else {
      // If the user's cluster user type is not 'Roads Authority', return the original data
      return this.tableData = formattedData;
    }

  },

  computed: {
    paginatedData() {
      const startIndex = (this.currentPage - 1) * pageSize
      const endIndex = startIndex + pageSize
      return this.tableData.slice(startIndex, endIndex)
    }
  }
}
