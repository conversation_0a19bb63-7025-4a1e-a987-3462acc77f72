<template>
  <div class="row m-3">
    <div class="col-12">
      <download :files="files" :title="'District Profile'"></download>

      <download :files="files" :title="'Disaster Profile'"></download>

      <download :files="files" :title="'Data sheet'"></download>

      <download :files="files" :title="'Map'"></download>

      <download :files="files" :title="'Other'"></download>
    </div>
  </div>
</template>
<script>
import RouteBreadcrumb from "../../../components/Breadcrumb/RouteBreadcrumb";
import BaseHeader from "../../../components/BaseHeader";
import download from "../components/download";
import { uploads } from "../api/uploads/uploads";

export default {
  name: "icons",
  components: {
    BaseHeader,
    RouteBreadcrumb,
    download
  },
  data() {
    return {
      files: []
    };
  },

  mounted() {
    uploads.get(this.$session.get("jwt")).then(response => {
      this.files = response.data;
    });
  }
};
</script>
<style></style>
