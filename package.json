{"name": "vue-argon-dashboard-pro", "version": "1.1.0", "private": true, "scripts": {"serve": "node --max_old_space_size=20096 node_modules/@vue/cli-service/bin/vue-cli-service.js serve --open", "build": "node --max_old_space_size=20096 node_modules/@vue/cli-service/bin/vue-cli-service.js build", "dev": "npm run serve", "lint": "sass-lint -v", "lint:fix": "sass-lint-auto-fix", "start": "cd server.js", "deploy": "git subtree push --prefix dist heroku master", "heroku-postbuild": "npm install --only=dev --no-shrinkwrap && npm run build"}, "dependencies": {"@fullcalendar/core": "4.0.2", "@fullcalendar/daygrid": "4.0.1", "@fullcalendar/interaction": "4.0.2", "@fullcalendar/timegrid": "4.0.1", "@fullcalendar/vue": "4.0.2-beta", "@platyplus/humanitarian-icons": "0.1.3", "@wesselkuipers/leaflet.markercluster": "1.4.4", "amqplib": "0.6.0", "axios": "0.19.0", "babel-loader": "8.2.3", "bootstrap": "4.3.1", "bootstrap-vue": "2.21.2", "chart.js": "2.7.2", "circular-json": "0.5.9", "colorbrewer": "1.3.0", "console": "0.7.2", "core-js": "3.27.2", "cross-blob": "2.0.0", "crossfilter": "1.3.12", "crossfilter2": "1.5.4", "d3": "6.7.0", "datamaps": "0.5.9", "date-fns": "1.30.1", "dateformat": "4.4.1", "dc": "4.2.7", "dc.leaflet": "0.5.6", "dom-to-image": "2.6.0", "dom-to-pdf": "0.3.1", "dropzone": "5.5.1", "element-ui": "2.4.11", "es6-promise": "4.1.1", "exceljs": "4.2.1", "file-saver": "2.0.5", "flatpickr": "4.5.7", "fuse.js": "3.2.0", "google-maps": "3.2.1", "html-to-pdfmake": "2.3.7", "html2canvas": "1.0.0-rc.5", "html2pdf.js": "0.10.1", "http2": "3.3.7", "jquerry": "0.0.1-security", "jquery": "3.4.1", "jspdf": "1.4.1", "leaflet": "1.6.0", "leaflet.markercluster": "1.5.0", "lodash": "4.17.21", "log.js": "0.0.0", "mime-types": "2.1.31", "net-browserify": "0.2.4", "nouislider": "12.1.0", "perfect-scrollbar": "1.3.0", "popper.js": "1.16.1", "pretty-file-icons": "2.2.1", "qrcode": "1.4.4", "query": "0.2.0", "quill": "1.3.6", "reductio": "1.0.0", "sass-lint-auto-fix": "0.21.0", "stylus": "0.54.7", "stylus-loader": "3.0.2", "svg.js": "2.7.1", "sweetalert2": "7.29.2", "underscore": "1.10.2", "v-spinner": "1.0.5", "vee-validate": "2.2.4", "vue": "2.6.10", "vue-axios": "2.1.4", "vue-chartjs": "3.4.0", "vue-clickaway": "2.2.2", "vue-clipboard2": "0.3.0", "vue-flatpickr-component": "8.1.2", "vue-good-table": "2.19.5", "vue-html2canvas": "0.0.4", "vue-js-modal": "2.0.0-rc.6", "vue-json-excel": "0.2.98", "vue-loading-overlay": "3.2.0", "vue-notification": "1.3.20", "vue-notification-bell": "0.9.12", "vue-router": "3.0.6", "vue-session": "1.0.0", "vue-spinner": "1.0.4", "vue-template-compiler": "2.6.10", "vue-window-size": "1.1.2", "vue2-transitions": "0.2.3", "vuex": "3.6.2", "worker-thread": "1.1.0", "yarn": "1.22.10"}, "devDependencies": {"@vue/cli-plugin-babel": "3.7.0", "@vue/cli-plugin-eslint": "3.12.1", "@vue/cli-service": "3.7.0", "@vue/eslint-config-prettier": "4.0.1", "babel-plugin-component": "1.1.1", "node-sass": "4.14.1", "sass-loader": "7.1.0"}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "author": "cristi<PERSON><PERSON> <<EMAIL>>", "description": "Vue Argon Dashboard PRO"}