<template>
  <div>
    <h2>LIVELIHOODS</h2>
    <h3>
      *
      <small>Hint &nbsp;</small>
      <b
        ><font color="primary"
          >(HH: Households, FHH : Female Headed Households, MHH : Male Headed
          Households)</font
        ></b
      >
    </h3>
    <hr class="mt-3 mb-3" />
    <h2>
      <b class="alert-suc">Livelihoods Affected (Households)</b>
    </h2>
    <form>
      <div
        class="row row-example"
        v-for="(value, index) in livelihoods_affected"
        v-bind:key="index"
      >
        <div class="col-md">
          <base-input label="Please choose option">
            <select
              class="form-control"
              id="exampleFormControlSelect1"
              v-model="value.name"
              data-toggle="tooltip"
              data-placement="top"
              title="Choose an option"
            >
              <option v-for="types in livelihood_types" :value="types.name">{{
                types.name
              }}</option>
            </select>
          </base-input>
        </div>
        <div class="col-md">
          <base-input
            label="Severely affected"
            placeholder="# of HH severely affected"
            type="number"
            v-model.number="value.severely_affected"
            oninput="validity.valid||(value='');"
            data-toggle="tooltip"
            data-placement="top"
            title="Enter number of HH severely affected"
            min="0"
            :disabled="value.name == null || value.name === ''"
            v-bind:max="maxValue"
            @input="
              init_totals(
                'severely_affected',
                livelihoods_affected,
                'severely_affected'
              )
            "
          />
        </div>
        <div class="col-md">
          <base-input
            label="Slightly affected "
            type="number"
            placeholder="# of HH slightly affected "
            v-model.number="value.slightly_affected"
            data-toggle="tooltip"
            data-placement="top"
            title="Enter number of HH slightly affected"
            oninput="validity.valid||(value='');"
            min="0"
            :rules="[v => !!v || 'value is required']"
            :disabled="value.name == null || value.name === ''"
            @input="
              init_totals(
                'slightly_affected',
                livelihoods_affected,
                'slightly_affected'
              )
            "
            v-bind:max="maxValue"
          />
        </div>
        <div class="col-md pt-5">
          <base-button
            size="sm"
            type="warning"
            data-toggle="tooltip"
            data-placement="top"
            title="Delete livelihood affected"
            class="btn-icon-only rounded-circle noprint"
            v-if="livelihoods_affected.length > 0"
            @click="
              removeItemRow(
                'livelihoods_affected',
                livelihoods_affected,
                livelihood_types,
                index,
                'name',
                ['slightly_affected', 'severely_affected']
              )
            "
            >X</base-button
          >
        </div>
      </div>
    </form>
    <base-button
      size="md"
      type="info"
      data-toggle="tooltip"
      data-placement="top"
      title="Add livelihood affected"
      class="btn-icon-only rounded-circle noprint"
      @click="
        addItemRow(
          'livelihoods_affected',
          livelihoods_affected,
          livelihood_types,
          'name'
        )
      "
    >
      <i class="ni ni-fat-add"></i>
    </base-button>

    <hr />
    <div class="row row-example">
      <div class="col-sm-6">
        <h5 slot="header" class="mb-0">Total HH affected: {{ tt_affected }}</h5>
      </div>
    </div>

    <hr />

    <b>Response Needed for the Livelihoods Cluster</b>
    <hr class="mt-3 mb-3" />
    <base-input label="General Response needed for Livelihoods cluster">
      <textarea
        class="form-control"
        id="exampleFormControlTextarea3"
        data-toggle="tooltip"
        data-placement="top"
        title="Type the general response needed"
        placeholder="Type the response needed for the Livelihoods cluster"
        v-model="response_needed"
        rows="3"
      ></textarea>
    </base-input>
    <base-input label="Urgent Response needed for Livelihoods cluster">
      <textarea
        class="form-control"
        id="exampleFormControlTextarea3"
        data-toggle="tooltip"
        data-placement="top"
        title="Type the urgent response needed"
        placeholder="Type the urgent response needed for the Livelihoods cluster"
        v-model="urgent_response_needed"
        rows="3"
      ></textarea>
    </base-input>
    <base-button
      size="lg"
      type="primary"
      data-toggle="tooltip"
      data-placement="top"
      title="Save and goto next section"
      @click.stop="save"
      class="noprint"
    >
      Save & Continue
    </base-button>
  </div>
</template>
<script src="./index.js"/>

<style scoped>
@media print {
  .page-break {
    overflow-y: visible;
    display: block;
  }
  .not-active {
    pointer-events: none;
    cursor: default;
    text-decoration: none;
    color: black;
  }
  .noprint {
    visibility: hidden;
  }
}
</style>
