<template>
  <div>
    <table class="col-md-11">
              <tr>
                <td colspan="9">
                  <center>
                    <h2>
                      <b style="text-transform:uppercase">DETAILED REPORT FOR
                        <span id="district">{{ dinrFormsData.district.admin2_name_en }}</span>
                      </b>
                    </h2>
                  </center>
                </td>
              </tr>
              <tr>
                <td colspan="6" width="50%">
                  <b>Affected TAs</b>
                  :
                  <span>
                    {{
                    TAarray.join() == "" ? "Not reported" : TAarray.join(", ")
                    }}
                  </span>
                </td>

                <td colspan="6" width="50%">
                  <b>Affected Villages</b>
                  :
                  <span>
                    {{
                    villagesArray.filter(Boolean).sort((a, b) => a.localeCompare(b)).join(", ")
                    || "Not reported"
                    }}
                  </span>
                </td>
              </tr>
              <tr>
                <td colspan="6" width="50%" style="text-transform:capitalize">
                  <b>Type of disasters</b>
                  : <span id="disastertype"> {{ dinrFormsData.disaster }} </span>
                </td>
                <td colspan="6" width="50%">
                  <b>Report submitted at</b>
                  :
                  {{
                  dateFormate(
                  new Date(dinrFormsData.createdon).toLocaleDateString(
                  "en-US"
                  )
                  )
                  }}
                </td>
              </tr>
              <tr>
                <td colspan="6" width="50%">
                  <b>Date Disaster started</b>
                  :
                  <span id="disasterstart"> {{
                  dateFormate(
                  new Date(dinrFormsData.dodFrom).toLocaleDateString(
                  "en-US"
                  )
                  )
                  }} </span>
                </td>
                <td colspan="6" width="50%">
                  <b>Date Disaster ended</b>
                  :
                  {{
                  dateFormate(
                  new Date(dinrFormsData.dodTo).toLocaleDateString("en-US")
                  )
                  }}
                </td>
              </tr>

              <tr>
                <td colspan="4">
                  <b>Date of assessment by ADRMC/VDRMC</b>
                  :
                  {{
                  dateFormate(
                  new Date(dinrFormsData.doaAcpc).toLocaleDateString("en-US")
                  )
                  }}
                </td>
                <td colspan="4">
                  <b>Date of assessment/verification by DDRMC</b>
                  :
                  {{
                  dateFormate(
                  new Date(dinrFormsData.doaDcpc).toLocaleDateString("en-US")
                  )
                  }}
                </td>
                <td colspan="4">
                  <b>Date reported to the DEC</b>
                  :
                  {{
                  dinrFormsData.dateReported?
                  dateFormate(
                  new Date(dinrFormsData.dateReported).toLocaleDateString("en-US")
                  )
                  :""
                  }}
                </td>

              </tr>
            </table>


            <table
      v-for="(item, index) in draFormsData"
      v-bind:key="index"
      class="col-md-11 "
    >
      <!-- display ta name -->
      <tr>
        <td colspan="15">
          <h3 class="text-center" style="text-transform:uppercase">
            <b>{{ item.admin3.admin3Name_en }}</b>
          </h3>
        </td>
      </tr>
      <!-- end display ta name -->

      <tr>
        <td colspan="4" style="width:200px">
          <b>
            <b>GVH</b>
          </b>
          :
          <span class="text-wrap">{{
            sortArrayByKey(item.gvhs)
              .map(e => e.name.replace(/(\r\n|\n|\r)/gm, ""))
              .join(", ") || "Not reported"
          }}</span>
        </td>
        <td colspan="5" style="width:35%">
          <b>Camps</b>
          :
          {{
            item.camps
              .map(i => i.name)
              .filter(e => (e ? e.replace(/(\r\n|\n|\r)/gm, "") : ""))
              .sort((a, b) => a.localeCompare(b))
              .join(", ") || "Not reported"
          }}
        </td>

        <td colspan="4" style="max-width:300px !important;">
          <b>Affected Villages</b>
          :
          <span class="text-wrap">{{
            item.villages
              .map(i => i.name)
              .sort((a, b) => a.localeCompare(b))
              .join(", ") || "Not reported"
          }}</span>
        </td>
      </tr>
      <!-- general -->
      <tr
        v-if="item.sectors.shelter"
        style="margin-botton:9padding:0 !important;border:0px !important;"
      >
        <td
          colspan="15"
          style="margin:0 !important;padding:0 !important;border:0px"
        >
          <table
            width="100%"
            style="margin:0;padding:0 !important;border:0px !important;"
          >
            <tr
              v-if="
                item.sectors.shelter.PeopleInjuredrows &&
                  (item.sectors.shelter.PeopleInjuredrows.length > 0 ||
                    (item.sectors.shelter.PeopleDeadrows &&
                      item.sectors.shelter.PeopleDeadrows.length > 0))
              "
            >
              <td colspan="13" class="text-center">
                <span><b>GENERAL INFORMATION </b></span>
              </td>
            </tr>
            <!-- people injured start -->
            <tr
              v-if="
                item.sectors.shelter.PeopleInjuredrows &&
                  item.sectors.shelter.PeopleInjuredrows.length > 0
              "
            >
              <td
                width="20%"
                :rowspan="
                  item.sectors.shelter.PeopleInjuredrows.length > 0
                    ? item.sectors.shelter.PeopleInjuredrows.length + 2
                    : item.sectors.shelter.PeopleInjuredrows.length + 1
                "
              >
                <strong>People Injured</strong>
              </td>
              <td colspan="2" width="20%">
                <b>GVH</b>
              </td>
              <td colspan="4" width="20%">
                <b>Population group</b>
              </td>
              <td colspan="2" width="10%">
                <b>
                  <center>Male</center>
                </b>
              </td>
              <td colspan="2" width="10%">
                <b>
                  <center>Female</center>
                </b>
              </td>
              <td colspan="2" width="10%" style="font-weight:bold">
                <b>
                  <center>Total</center>
                </b>
              </td>
            </tr>
            <tr
              v-if="
                item.sectors.shelter &&
                  item.sectors.shelter.PeopleInjuredrows &&
                  item.sectors.shelter.PeopleInjuredrows.length > 0
              "
              v-for="(row, index) in item.sectors.shelter.PeopleInjuredrows"
            >
              <td
                colspan="2"
                v-if="
                  index ==
                    item.sectors.shelter.PeopleInjuredrows.findIndex(
                      x => x.name == row.name
                    )
                "
                :rowspan="
                  item.sectors.shelter.PeopleInjuredrows.filter(
                    x => x.name == row.name
                  ).length
                "
              >
                {{ row.name }}
              </td>
              <td width="30%" colspan="4">
                {{
                  row.category == "Other"
                    ? "Other(" + row.other_category_name + ")"
                    : row.category
                }}
              </td>
              <td width="10%" colspan="2" class="right-align">
                {{ numberWithCommas(row.males_injured || 0) }}
              </td>
              <td width="10%" colspan="2" class="right-align">
                {{ numberWithCommas(row.females_injured || 0) }}
              </td>
              <td
                style="font-weight:bold"
                width="10%"
                colspan="2"
                class="right-align"
              >
                {{
                  numberWithCommas(
                    parseInt(row.males_injured || 0) +
                      parseInt(row.females_injured || 0)
                  )
                }}
              </td>
            </tr>
            <tr
              v-if="
                item.sectors.shelter &&
                  item.sectors.shelter.PeopleInjuredrows &&
                  item.sectors.shelter.PeopleInjuredrows.length > 0
              "
            >
              <td width="30%" style="font-weight:bold" colspan="6">
                Total
              </td>
              <td
                width="10%"
                colspan="2"
                style="font-weight:bold;"
                class="right-align"
              >
                {{
                  numberWithCommas(
                    item.sectors.shelter.PeopleInjuredrows
                      ? item.sectors.shelter.PeopleInjuredrows.reduce(
                          (sum, value) =>
                            sum + +parseInt(value.males_injured || 0),
                          0
                        )
                      : 0
                  )
                }}
              </td>
              <td
                width="10%"
                colspan="2"
                style="font-weight:bold"
                class="right-align"
              >
                {{
                  numberWithCommas(
                    item.sectors.shelter.PeopleInjuredrows
                      ? item.sectors.shelter.PeopleInjuredrows.reduce(
                          (sum, value) =>
                            sum + +parseInt(value.females_injured || 0),
                          0
                        )
                      : 0
                  )
                }}
              </td>
              <td
                width="10%"
                colspan="2"
                style="font-weight:bold"
                class="right-align"
              >
                {{
                  numberWithCommas(
                    item.sectors.shelter.PeopleInjuredrows
                      ? item.sectors.shelter.PeopleInjuredrows.reduce(
                          (sum, value) =>
                            sum +
                            (+parseInt(value.males_injured || 0) +
                              parseInt(value.females_injured || 0)),
                          0
                        )
                      : 0
                  )
                }}
              </td>
            </tr>
            <!-- people injured end -->
            <!-- people dead start -->
            <tr
              v-if="
                item.sectors.shelter &&
                  item.sectors.shelter.PeopleDeadrows &&
                  item.sectors.shelter.PeopleDeadrows.length > 0
              "
            >
              <td
                width="20%"
                :rowspan="
                  item.sectors.shelter.PeopleDeadrows.length > 0
                    ? item.sectors.shelter.PeopleDeadrows.length + 2
                    : item.sectors.shelter.PeopleDeadrows + 1
                "
              >
                <b>People Dead</b>
              </td>
              <td width="20%" colspan="2">
                <b>GVH</b>
              </td>
              <td width="20%" colspan="3">
                <b>Population Group</b>
              </td>
              <td colspan="2" width="10%">
                <b>
                  <center>Male</center>
                </b>
              </td>
              <td colspan="2" width="10%">
                <b>
                  <center>Female</center>
                </b>
              </td>
              <td colspan="2" width="10%">
                <b>
                  <center>Total</center>
                </b>
              </td>
            </tr>
            <tr
              v-if="
                item.sectors.shelter &&
                  item.sectors.shelter.PeopleDeadrows &&
                  item.sectors.shelter.PeopleDeadrows.length > 0
              "
              v-for="(row, index) in item.sectors.shelter.PeopleDeadrows"
            >
              <td
                width="20%"
                colspan="2"
                v-if="
                  index ==
                    item.sectors.shelter.PeopleDeadrows.findIndex(
                      x => x.name == row.name
                    )
                "
                :rowspan="
                  item.sectors.shelter.PeopleDeadrows.filter(
                    x => x.name == row.name
                  ).length
                "
              >
                {{ row.name }}
              </td>
              <td width="20%" colspan="3">
                {{
                  row.category == "Other"
                    ? "Other(" + row.other_category_name + ")"
                    : row.category
                }}
              </td>
              <td width="10%" colspan="2" class="right-align">
                {{ row.males_dead || 0 }}
              </td>
              <td width="10%" colspan="2" class="right-align">
                {{ row.females_dead || 0 }}
              </td>
              <td
                style="font-weight:bold"
                width="10%"
                colspan="2"
                class="right-align"
              >
                {{
                  parseInt(row.males_dead || 0) +
                    parseInt(row.females_dead || 0)
                }}
              </td>
            </tr>
            <tr
              v-if="
                item.sectors.shelter &&
                  item.sectors.shelter.PeopleDeadrows &&
                  item.sectors.shelter.PeopleDeadrows.length > 0
              "
            >
              <td width="30%" style="font-weight:bold" colspan="5">
                Total
              </td>
              <td
                width="10%"
                colspan="2"
                style="font-weight:bold;"
                class="right-align"
              >
                {{
                  numberWithCommas(
                    item.sectors.shelter.PeopleDeadrows
                      ? item.sectors.shelter.PeopleDeadrows.reduce(
                          (sum, value) =>
                            sum + +parseInt(value.males_dead || 0),
                          0
                        )
                      : 0
                  )
                }}
              </td>
              <td
                width="10%"
                colspan="2"
                style="font-weight:bold"
                class="right-align"
              >
                {{
                  numberWithCommas(
                    item.sectors.shelter.PeopleDeadrows
                      ? item.sectors.shelter.PeopleDeadrows.reduce(
                          (sum, value) =>
                            sum + +parseInt(value.females_dead || 0),
                          0
                        )
                      : 0
                  )
                }}
              </td>
              <td
                width="10%"
                colspan="2"
                style="font-weight:bold"
                class="right-align"
              >
                {{
                  numberWithCommas(
                    item.sectors.shelter.PeopleDeadrows
                      ? item.sectors.shelter.PeopleDeadrows.reduce(
                          (sum, value) =>
                            sum +
                            (+parseInt(value.males_dead || 0) +
                              parseInt(value.females_dead || 0)),
                          0
                        )
                      : 0
                  )
                }}
              </td>
            </tr>
            <!-- people dead end -->
            <!-- people missing start -->
            <tr
              v-if="
                item.sectors.shelter.PeopleMissingrows &&
                  item.sectors.shelter.PeopleMissingrows.length > 0
              "
            >
              <td
                width="20%"
                :rowspan="
                  item.sectors.shelter.PeopleMissingrows.length > 0
                    ? item.sectors.shelter.PeopleMissingrows.length + 2
                    : item.sectors.shelter.PeopleMissingrows.length + 1
                "
              >
                <strong>People Missing</strong>
              </td>
              <td
                colspan="2
                      "
                width="20%"
              >
                <b>GVH</b>
              </td>
              <td width="20%" colspan="3">
                <b>Population Group</b>
              </td>
              <td colspan="2" width="10%">
                <b>
                  <center>Male</center>
                </b>
              </td>
              <td colspan="2" width="10%">
                <b>
                  <center>Female</center>
                </b>
              </td>
              <td colspan="2" width="10%" style="font-weight:bold">
                <b>
                  <center>Total</center>
                </b>
              </td>
            </tr>
            <tr
              v-if="
                item.sectors.shelter.PeopleMissingrows &&
                  item.sectors.shelter.PeopleMissingrows.length > 0
              "
              v-for="(row, index) in item.sectors.shelter.PeopleMissingrows"
            >
              <td
                width="20%"
                colspan="2"
                v-if="
                  index ==
                    item.sectors.shelter.PeopleMissingrows.findIndex(
                      x => x.name == row.name
                    )
                "
                :rowspan="
                  item.sectors.shelter.PeopleMissingrows.filter(
                    x => x.name == row.name
                  ).length
                "
              >
                {{ row.name }}
              </td>
              <td width="10%" colspan="3">
                {{
                  row.category == "Other"
                    ? "Other(" + row.other_category_name + ")"
                    : row.category
                }}
              </td>
              <td width="10%" colspan="2" class="right-align">
                {{ parseInt(row.males_missing || 0) }}
              </td>
              <td width="10%" colspan="2" class="right-align">
                {{ parseInt(row.females_missing || 0) }}
              </td>
              <td
                style="font-weight:bold"
                width="10%"
                colspan="2"
                class="right-align"
              >
                {{
                  (row.males_missing ? parseInt(row.males_missing) : 0) +
                    (row.females_missing ? parseInt(row.females_missing) : 0)
                }}
              </td>
            </tr>

            <tr
              v-if="
                item.sectors.shelter.PeopleMissingrows &&
                  item.sectors.shelter.PeopleMissingrows.length > 0
              "
            >
              <td width="30%" style="font-weight:bold" colspan="5">
                Total
              </td>
              <td
                width="10%"
                colspan="2"
                style="font-weight:bold;"
                class="right-align"
              >
                {{
                  numberWithCommas(
                    item.sectors.shelter.PeopleMissingrows
                      ? item.sectors.shelter.PeopleMissingrows.reduce(
                          (sum, value) =>
                            sum + (+parseInt(value.males_missing) || 0),
                          0
                        )
                      : 0
                  )
                }}
              </td>
              <td
                width="10%"
                colspan="2"
                style="font-weight:bold"
                class="right-align"
              >
                {{
                  numberWithCommas(
                    item.sectors.shelter.PeopleMissingrows
                      ? item.sectors.shelter.PeopleMissingrows.reduce(
                          (sum, value) =>
                            sum + (+parseInt(value.females_missing) || 0),
                          0
                        )
                      : 0
                  )
                }}
              </td>
              <td
                width="10%"
                colspan="2"
                style="font-weight:bold"
                class="right-align"
              >
                {{
                  numberWithCommas(
                    item.sectors.shelter.PeopleMissingrows
                      ? item.sectors.shelter.PeopleMissingrows.reduce(
                          (sum, value) =>
                            sum +
                            (+parseInt(value.females_missing || 0) +
                              parseInt(value.males_missing || 0)),
                          0
                        )
                      : 0
                  )
                }}
              </td>
            </tr>

            <!-- people missing end -->
          </table>
        </td>
      </tr>
      <tr
        style="border:none"
        v-if="
          item.sectors.shelter &&
            ((item.sectors.shelter.PeopleInjuredrows &&
              item.sectors.shelter.PeopleInjuredrows.length > 0) ||
              (item.sectors.shelter.PeopleDeadrows &&
                item.sectors.shelter.PeopleDeadrows.length > 0))
        "
      >
        <td class="col-md-12" colspan="16"></td>
      </tr>
      <!-- end general -->
      <!-- shelter -->
      <tr
        v-if="item.sectors.shelter"
        style="margin:0 !important;padding:0 !important;border:0px !important;"
      >
        <td
          colspan="15"
          style="margin:0 !important;padding:0 !important;border:0px"
        >
          <table
            width="100%"
            style="margin:0;padding:0 !important;border:0px !important;"
          >
            <tr>
              <td style="text-align:left;">
                <b>CLUSTER</b>
              </td>
              <td colspan="13" class="text-center">
                <b>IMPACT ASSESSMENT</b>
              </td>
            </tr>
            <tr>
              <td
                width="9%"
                class="clearfix"
                :rowspan="
                  (item.sectors.shelter.people_without_shelter
                    ? item.sectors.shelter.people_without_shelter.length > 0
                      ? item.sectors.shelter.people_without_shelter.length + 1
                      : 1
                    : 1) +
                    (item.sectors.shelter.PeopleAffectedrows
                      ? item.sectors.shelter.PeopleAffectedrows.length > 0
                        ? item.sectors.shelter.PeopleAffectedrows.length + 1
                        : 1
                      : 1) +

                      +
                    (item.sectors.shelter.ImpactOnHouses
                      ? item.sectors.shelter.ImpactOnHouses.length > 0
                        ? item.sectors.shelter.ImpactOnHouses.length + 1
                        : 1
                      : 1) +

                    (item.sectors.shelter.other_structures_damaged
                      ? item.sectors.shelter.other_structures_damaged.length > 0
                        ? item.sectors.shelter.other_structures_damaged.length *
                          17
                        : item.sectors.shelter.other_structures_damaged.length *
                          17
                      : 1) +
                    11
                "
              >
                
                <center><img
                  src="../../../../static/cluster_shelter_100px_bluebox.png"
                  height="150"
                /></center>
              </td>
            </tr>
            <tr v-if="item.sectors.shelter.people_without_shelter">
              <td width="20%">
                <b>Indicators</b>
              </td>
              <td colspan="5" width="40%">
                <b>Population group</b>
              </td>
              <td width="10%" colspan="2">
                <b>
                  <center>Male</center>
                </b>
              </td>
              <td width="10%" colspan="2">
                <b>
                  <center>Female</center>
                </b>
              </td>
              <td width="10%" colspan="2">
                <b>
                  <center>Total</center>
                </b>
              </td>
            </tr>

            <tr v-if="item.sectors.shelter.people_without_shelter">
              <td
                width="20%"
                :rowspan="
                  item.sectors.shelter.people_without_shelter.length > 0
                    ? item.sectors.shelter.people_without_shelter.length + 2
                    : item.sectors.shelter.people_without_shelter.length + 1
                "
              >
                <strong>People without shelter</strong>
              </td>
            </tr>
            <tr
              v-for="row in item.sectors.shelter.people_without_shelter"
              v-if="item.sectors.shelter.people_without_shelter"
              style="border:1px"
            >
              <td width="40%" colspan="5">
                {{
                  row.name == "Other"
                    ? "Other(" + row.other_category_name + ")"
                    : row.name || "Not reported"
                }}
              </td>
              <td width="10%" colspan="2" class="right-align">
                {{ numberWithCommas(row.without_shelter_males || 0) }}
              </td>
              <td width="10%" colspan="2" class="right-align">
                {{ numberWithCommas(row.without_shelter_females || 0) }}
              </td>
              <td
                width="10%"
                colspan="2"
                style="font-weight:bold"
                class="right-align"
              >
                {{
                  numberWithCommas(
                    parseInt(row.without_shelter_females || 0) +
                      parseInt(row.without_shelter_males || 0)
                  )
                }}
              </td>
            </tr>

            <tr
              v-if="
                item.sectors.shelter.people_without_shelter &&
                  item.sectors.shelter.people_without_shelter.length > 0
              "
            >
              <td width="40%" style="font-weight:bold" colspan="5">
                Total
              </td>

              <td
                width="10%"
                colspan="2"
                style="font-weight:bold;"
                class="right-align"
              >
                {{
                  numberWithCommas(
                    item.sectors.shelter.people_without_shelter
                      ? item.sectors.shelter.people_without_shelter.reduce(
                          (sum, value) =>
                            sum + +parseInt(value.without_shelter_males || 0),
                          0
                        )
                      : 0
                  )
                }}
              </td>
              <td
                width="10%"
                colspan="2"
                style="font-weight:bold"
                class="right-align"
              >
                {{
                  numberWithCommas(
                    item.sectors.shelter.people_without_shelter
                      ? item.sectors.shelter.people_without_shelter.reduce(
                          (sum, value) =>
                            sum + +parseInt(value.without_shelter_females || 0),
                          0
                        )
                      : 0
                  )
                }}
              </td>
              <td
                width="10%"
                colspan="2"
                style="font-weight:bold"
                class="right-align"
              >
                {{
                  numberWithCommas(
                    item.sectors.shelter.people_without_shelter
                      ? item.sectors.shelter.people_without_shelter.reduce(
                          (sum, value) =>
                            sum +
                            (+parseInt(value.without_shelter_females || 0) +
                              +parseInt(value.without_shelter_males || 0)),
                          0
                        )
                      : 0
                  )
                }}
              </td>
            </tr>

            <!-- start -->
            <tr v-if="item.sectors.shelter.PeopleAffectedrows">
              <td
                width="20%"
                :rowspan="
                  item.sectors.shelter.PeopleAffectedrows.length > 0
                    ? item.sectors.shelter.PeopleAffectedrows.length + 2
                    : item.sectors.shelter.PeopleAffectedrows.length + 1
                "
              >
                <strong>Households Affected</strong>
              </td>

              <td colspan="2">
                <b>GVH</b>
              </td>
              <td colspan="3" width="15%">
                <b>Population group</b>
              </td>
              <td width="10%" colspan="2">
                <b>
                  <center>MHH</center>
                </b>
              </td>
              <td width="10%" colspan="2">
                <b>
                  <center>FHH</center>
                </b>
              </td>
              <td width="10%" colspan="2">
                <b>
                  <center>Total</center>
                </b>
              </td>
            </tr>
            <tr
              v-if="
                item.sectors.shelter.PeopleAffectedrows &&
                  item.sectors.shelter.PeopleAffectedrows.length > 0
              "
              v-for="(row, index) in item.sectors.shelter.PeopleAffectedrows"
            >
              <td
                width="10%"
                colspan="2"
                v-if="
                  index == item.sectors.shelter.PeopleAffectedrows.findIndex(x => x.name == row.name)
                "
                :rowspan="
                  item.sectors.shelter.PeopleAffectedrows.filter(
                    x => x.name == row.name
                  ).length
                "
              >

                {{ row.name }}
              </td>
              <td width="14%" colspan="3">
                {{
                  row.category == "Other"
                    ? "Other(" + row.other_category_name + ")"
                    : row.category || "Not reported"
                }}
              </td>
              <td width="10%" colspan="2" class="right-align">
                {{ numberWithCommas(parseInt(row.damaged_mhh || 0)) }}
              </td>
              <td width="10%" colspan="2" class="right-align">
                {{ numberWithCommas(parseInt(row.damaged_fhh || 0)) }}
              </td>
              <td
                style="font-weight:bold"
                width="10%"
                colspan="2"
                class="right-align"
              >
                {{
                  numberWithCommas(
                    (row.damaged_mhh ? parseInt(row.damaged_mhh) : 0) +
                      (row.damaged_fhh ? parseInt(row.damaged_fhh) : 0)
                  )
                }}
              </td>
            </tr>
            <tr
              v-if="
                item.sectors.shelter.PeopleAffectedrows &&
                  item.sectors.shelter.PeopleAffectedrows.length > 0
              "
            >
              <td width="40%" style="font-weight:bold" colspan="5">
                Total
              </td>
              <td
                width="10%"
                colspan="2"
                style="font-weight:bold;"
                class="right-align"
              >
                {{
                  numberWithCommas(
                    item.sectors.shelter.PeopleAffectedrows
                      ? item.sectors.shelter.PeopleAffectedrows.reduce(
                          (sum, value) =>
                            sum + +parseInt(value.damaged_mhh || 0),
                          0
                        )
                      : 0
                  )
                }}
              </td>
              <td
                width="10%"
                colspan="2"
                style="font-weight:bold"
                class="right-align"
              >
                {{
                  numberWithCommas(
                    item.sectors.shelter.PeopleAffectedrows
                      ? item.sectors.shelter.PeopleAffectedrows.reduce(
                          (sum, value) =>
                            sum + +parseInt(value.damaged_fhh || 0),
                          0
                        )
                      : 0
                  )
                }}
              </td>
              <td
                width="10%"
                colspan="2"
                style="font-weight:bold"
                class="right-align"
              >
                {{
                  numberWithCommas(
                    item.sectors.shelter.PeopleAffectedrows
                      ? item.sectors.shelter.PeopleAffectedrows.reduce(
                          (sum, value) =>
                            sum +
                            (+parseInt(value.damaged_mhh || 0) +
                              +parseInt(value.damaged_fhh || 0)),
                          0
                        )
                      : 0
                  )
                }}
              </td>
            </tr>

            <!--  -->
            <tr v-if="item.sectors.shelter.ImpactOnHouses">
              <td
                width="20%"
                :rowspan="
                  item.sectors.shelter.ImpactOnHouses.length > 0
                    ? item.sectors.shelter.ImpactOnHouses.length + 2
                    : item.sectors.shelter.ImpactOnHouses.length + 1
                "
              >
                <strong>Impact on Houses</strong>
              </td>
            </tr>
            <tr v-if="item.sectors.shelter.ImpactOnHouses">


              <td colspan="2">
                <b>GVH</b>
              </td>
              <td colspan="3" width="15%">
                <b>Partly blown roof</b>
              </td>
              <td width="10%" colspan="2">
                <b>
                  <center>Wall damaged</center>
                </b>
              </td>
              <td width="10%" colspan="2">
                <b>
                  <center>Fully blown roof</center>
                </b>
              </td>
              <td width="10%" colspan="2">
                <b>
                  <center>Burnt</center>
                </b>
              </td>
            </tr>
            <tr
              v-if="item.sectors.shelter.ImpactOnHouses"
              v-for="row in item.sectors.shelter.ImpactOnHouses"
            >
              <td colspan="2">
                {{ row.name }}
              </td>
              <td width="15%" colspan="3" class="right-align">
                {{ row.partly_blown_roof || "0"}}
              </td>
              <td width="10%" colspan="2" class="right-align">
                {{ row.wall_damaged || 0}}
              </td>
              <td width="10%" colspan="2" class="right-align">
                {{ row.fully_blown_roof || 0}}
              </td>

              <td width="10%" colspan="2" class="right-align">
                {{ row.burnt || 0}}
              </td>
            </tr>
            <tr v-if="item.sectors.shelter.other_structures_damaged">
              <td
                width="20%"
                :rowspan="
                  item.sectors.shelter.other_structures_damaged.length > 0
                    ? item.sectors.shelter.other_structures_damaged.length *
                        6 +
                      1
                    : item.sectors.shelter.other_structures_damaged.length *
                        6 +
                      1
                "
              >
                <strong>Structures Damaged</strong>
              </td>
              <td width="30%" colspan="5">
                <b>Structure</b>
              </td>
              <td width="40%" colspan="6" style>
                <b>
                  <center>Impact</center>
                </b>
              </td>
            </tr>

            <template
              v-if="item.sectors.shelter.other_structures_damaged != 0"
              v-for="(row, index) in item.sectors.shelter.other_structures_damaged"
            >
              <tr :key="index">
                <td width="30%" colspan="5" rowspan="4">
                  {{ row.name }}
                </td>

                <td width="10%" colspan="4" class="left-align">
                  Partly damaged
                </td>

                <td width="10%" colspan="4" class="left-align">
                  {{ row.partly_damaged || 0 }}
                </td>
              </tr>

              <tr>
                <td width="10%" colspan="4" class="left-align">
                  Under Water
                </td>

                <td width="10%" colspan="4" class="left-align">
                  {{ row.underwater || 0 }}
                </td>
              </tr>

              <tr>
                <td width="10%" colspan="4" class="left-align">
                  Completely Damaged
                </td>

                <td width="10%" colspan="4" class="left-align">
                  {{ row.completely_damaged || 0 }}
                </td>
              </tr>

              <tr>
                <td width="10%" colspan="4" class="left-align">
                  Roofs Affected
                </td>

                <td width="10%" colspan="4" class="left-align">
                  {{ row.roofs_affected || 0 }}
                </td>
              </tr>

              <!-- <tr>
                <td width="10%" colspan="4" class="left-align">
                  Bricks Partly Damaged
                </td>

                <td width="10%" colspan="2" class="left-align">
                  {{ row.bricks_partly_damaged || 0 }}
                </td>
              </tr>

              <tr>
                <td width="10%" colspan="4" class="left-align">
                  Cement Partly Damaged
                </td>

                <td width="10%" colspan="2" class="left-align">
                  {{ row.cement_partly_damaged || 0 }}
                </td>
              </tr>

              <tr>
                <td width="10%" colspan="4" class="left-align">
                  Iron Sheets Partly Damaged
                </td>

                <td width="10%" colspan="2" class="left-align">
                  {{ row.iron_sheets_partly_damaged || 0 }}
                </td>
              </tr>

              <tr>
                <td width="10%" colspan="4" class="left-align">
                  Nails Partly Damaged
                </td>

                <td width="10%" colspan="2" class="left-align">
                  {{ row.nails_partly_damaged || 0 }}
                </td>
              </tr>

              <tr>
                <td width="10%" colspan="4" class="left-align">
                  Poles Partly Damaged
                </td>

                <td width="10%" colspan="2" class="left-align">
                  {{ row.poles_partly_damaged || 0 }}
                </td>
              </tr>

              <tr>
                <td width="10%" colspan="4" class="left-align">
                  Sand Aggregates Partly Damaged
                </td>

                <td width="10%" colspan="2" class="left-align">
                  {{ row.sand_aggregates_partly_damaged || 0 }}
                </td>
              </tr>

              <tr>
                <td width="10%" colspan="4" class="left-align">
                  Cement Roofs Affected
                </td>

                <td width="10%" colspan="2" class="left-align">
                  {{ row.cement_roofs_affected || 0 }}
                </td>
              </tr> -->

              <!-- <tr>
                <td width="10%" colspan="4" class="left-align">
                  Iron Sheets Roofs Affected
                </td>

                <td width="10%" colspan="2" class="left-align">
                  {{ row.iron_sheets_roofs_affected || 0 }}
                </td>
              </tr>

              <tr>
                <td width="10%" colspan="4" class="left-align">
                  Poles Roofs Affected
                </td>

                <td width="10%" colspan="2" class="left-align">
                  {{ row.poles_roofs_affected || 0 }}
                </td>
              </tr> -->

              <!-- <tr>
                <td width="10%" colspan="4" class="left-align">
                  Sand Aggregates Roofs Affected
                </td>

                <td width="10%" colspan="2" class="left-align">
                  {{ row.sand_aggregates_roofs_affected || 0 }}
                </td>
              </tr> -->
            </template>
            <tr>
              <td colspan="2" rowspan="2">
                <b>Extent Of Damage (HH)</b>
              </td>
              <td colspan="3" width="15%">
                <b>Partly Damaged</b>
              </td>
              <td width="10%" colspan="4">
                <b>
                  <center>Under Water/Submerged</center>
                </b>
              </td>
              <td width="10%" colspan="2">
                <b>
                  <center>Completely Damaged</center>
                </b>
              </td>
<!-- <td width="10%" colspan="2">
  <b>
    <center>Burnt</center>
  </b>
</td> -->
            </tr>
            <tr
            v-if="item.sectors.shelter.ImpactOnHouses"
            v-for="row in item.sectors.shelter.ImpactOnHouses"
            >
            <td width="15%" colspan="3" class="right-align">
              {{ item.sectors.shelter.partly_damaged || 0}}
            </td>
            <td width="10%" colspan="4" class="right-align">
              {{ item.sectors.shelter.under_water || 0 }}
            </td>
            <td width="10%" colspan="2" class="right-align">
              {{ item.sectors.shelter.completely_damaged || 0 }}
            </td>

            <!-- <td width="10%" colspan="2" class="right-align">
              {{ row.burnt }}
            </td> -->
            </tr>
            <tr>
              <td style="width:30%">
                <strong>Emergency Needs</strong>
              </td>
              <td colspan="14">
                <li style="margin-left:2%" class="qcont">
                  {{
                    !item.sectors.shelter.response_needed_emergency
                      ? "Not reported"
                      : item.sectors.shelter.response_needed_emergency
                          .map(item => {
                            if (item.specification) {
                              return `${item.name} (${item.specification})`;
                            } else {
                              return item.name;
                            }
                          })
                          .join(",")
                  }}
                </li>
              </td>
            </tr>
            <tr>
              <td style="width:30%">
                <strong>Non-Food Items Needs</strong>
              </td>
              <td colspan="14">
                <li style="margin-left:2%" class="qcont">
                  {{
                    !item.sectors.shelter.response_needed_non_food
                      ? "Not reported"
                      : item.sectors.shelter.response_needed_non_food
                          .map(item => {
                            if (item.specification) {
                              return `${item.name} (${item.specification})`;
                            } else {
                              return item.name;
                            }
                          })
                          .join(",")
                  }}
                </li>
              </td>
            </tr>

            <tr>
              <td style="width:30%">
                <strong>Response Needed</strong>
              </td>
              <td colspan="14">
                <u>
                  <strong>GENERAL</strong>
                </u>
                <br />
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.shelter
                      ? item.sectors.shelter.urgent_response_needed
                        ? item.sectors.shelter.urgent_response_needed
                        : "Not reported"
                      : "Not reported"
                  }}
                </li>
                <!-- <hr style="margin:1%;" />
                <u>
                  <strong>GENERAL</strong>
                </u>
                <br />
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.shelter
                      ? item.sectors.shelter.response_needed
                        ? item.sectors.shelter.response_needed
                        : "N/A"
                      : "N/A"
                  }}
                </li> -->
              </td>
            </tr>
          </table>
        </td>
      </tr>
      <!-- end shelter -->
      <!--=======================DISPLACED START HERE======================-->
      <tr
        v-if="hasDisplacedProperties(item)"
        style="margin:0 !important;padding:0 !important;border:0px !important;"
      >
        <td
          colspan="15"
          style="margin:0 !important;padding:0 !important;border:0px"
        >
          <table
            width="100%"
            style="margin:0;padding:0 !important;border:0px !important;"
          >
            <tr>
              <td
                width="9%"
                class="clearfix"
                :rowspan="
                  (item.sectors.displaced.displaced_households_accommodated
                    ? item.sectors.displaced.displaced_households_accommodated
                        .length > 0
                      ? item.sectors.displaced.displaced_households_accommodated
                          .length + 2
                      : 1
                    : 1) +
                    (item.sectors.displaced.PeopleAffectedrows
                      ? item.sectors.displaced.PeopleAffectedrows.length > 0
                        ? item.sectors.displaced.PeopleAffectedrows.length + 2
                        : 1
                      : 1) +
                    (item.sectors.displaced.displaced_individuals_accommodated
                      ? item.sectors.displaced
                          .displaced_individuals_accommodated.length > 0
                        ? item.sectors.displaced
                            .displaced_individuals_accommodated.length + 2
                        : 1
                      : 1) 
                      +
                      
                      
                      
                      (structureDisplacedAccommodatedArray
                      ? structureDisplacedAccommodatedArray.length > 0
                        ? structureDisplacedAccommodatedArray.length + 2
                        : 1
                      : 1) 
                      
                      
                      
                      +
                    (item.sectors.displaced.displaced_disaggregated
                      ? item.sectors.displaced.displaced_disaggregated.length >
                        0
                        ? item.sectors.displaced.displaced_disaggregated
                            .length + 2
                        : 1
                      : 1) +
                    4
                "
              >
                <center><img
                  src="../../../../static/crisis_population_displacement_100px_bluebox.png"
                  height="150"
                /></center>
              </td>

              <td
                v-if="item.sectors.displaced.displaced_households_accommodated"
                :rowspan="
                  1 +
                      structureDisplacedAccommodatedArray.length
                "
                colspan="4"
                style="width:20%"
              >
                <strong
                  >Number of HH accomodated in different structures</strong
                >
              </td>
              <td
                colspan="3"
                v-if="item.sectors.displaced.displaced_households_accommodated"
                style="width:15%"
              >
                <b>Category</b>
              </td>
              <td
                colspan="2"
                v-if="item.sectors.displaced.displaced_households_accommodated"
                style="width:15%"
              >
                <b>Name</b>
              </td>
              <td
                v-if="item.sectors.displaced.displaced_households_accommodated"
                colspan="2"
              >
                <b>Lat</b>
              </td>
              <td
                v-if="item.sectors.displaced.displaced_households_accommodated"
                colspan="2"
              >
                <b>Long</b>
              </td>
              <td
                v-if="item.sectors.displaced.displaced_households_accommodated"
                colspan="2"
                
              >
                <b>MHH</b>
              </td>
              <td
                v-if="item.sectors.displaced.displaced_households_accommodated"
                colspan="2"
               
              >
                <b>FHH</b>
              </td>
            </tr>

            <tr
              v-if="item.sectors.displaced.displaced_households_accommodated"
              v-for="row in structureDisplacedAccommodatedArray"
            >
              <td colspan="3">{{ row.name }}</td>
              <td colspan="2">{{ row.structure_name }}</td>
              <td colspan="2">{{ row.lat || "Not recorded"}}</td>
              <td colspan="2">{{ row.long || "Not recorded"}}</td>
              <td colspan="2">{{ row.accomodated_males_hh  || 0}}</td>
              <td colspan="2" style="min-width:100px !important;">{{ row.accomodated_females_hh  || 0}}</td>
            </tr>
            <!-- e -->
            <tr
              v-if="
                item.sectors.displaced.displaced_disaggregated &&
                  item.sectors.displaced.displaced_disaggregated.length > 0
              "
            >
              <td
                colspan="8"
                width="20%"
                :rowspan="
                  item.sectors.displaced.displaced_disaggregated &&
                  item.sectors.displaced.displaced_disaggregated.length > 0
                    ? item.sectors.displaced.displaced_disaggregated.length + 2
                    : 1
                "
              >
                <strong>Number of persons displaced by gender</strong>
              </td>
              <td colspan="3" width="15%">
                <b>
                  Population group
                </b>
              </td>
              <td colspan="2" width="15%">
                <b>
                  <center>Male</center>
                </b>
              </td>
              <td colspan="2" width="15%">
                <b>
                  <center>Female</center>
                </b>
              </td>
              <td colspan="2" width="15%">
                <b>
                  <center>Total</center>
                </b>
              </td>
            </tr>

            <tr
              v-if="
                item.sectors.displaced.displaced_disaggregated &&
                  item.sectors.displaced.displaced_disaggregated.length > 0
              "
              v-for="(row, index) in item.sectors.displaced
                .displaced_disaggregated"
            >
              <td
                width="10%"
                colspan="3"
                v-if="
                  index ==
                    item.sectors.displaced.displaced_disaggregated.findIndex(
                      x => x.name == row.name
                    )
                "
                :rowspan="
                  item.sectors.displaced.displaced_disaggregated.filter(
                    x => x.name == row.name
                  ).length
                "
              >
                {{
                  row.name == "Other"
                    ? "Other(" + row.other_displaced_persons_category + ")"
                    : row.name
                }}
              </td>
              <td width="10%" colspan="2" class="right-align">
                {{ numberWithCommas(row.displaced_males || 0) }}
              </td>
              <td width="10%" colspan="2" class="right-align">
                {{ numberWithCommas(row.displaced_females || 0) }}
              </td>
              <td
                style="font-weight:bold"
                width="10%"
                colspan="2"
                class="right-align"
              >
                {{
                  numberWithCommas(
                    parseInt(row.displaced_females || 0) +
                      parseInt(row.displaced_males || 0)
                  )
                }}
              </td>
            </tr>

            <tr
              v-if="
                item.sectors.displaced.displaced_disaggregated &&
                  item.sectors.displaced.displaced_disaggregated.length > 0
              "
            >
              <td width="15%" style="font-weight:bold" colspan="3">
                Total
              </td>
              <td
                width="10%"
                colspan="2"
                style="font-weight:bold;"
                class="right-align"
              >
                {{
                  numberWithCommas(
                    item.sectors.displaced.displaced_disaggregated
                      ? item.sectors.displaced.displaced_disaggregated.reduce(
                          (sum, value) =>
                            sum + +parseInt(value.displaced_males || 0),
                          0
                        )
                      : 0
                  )
                }}
              </td>
              <td
                width="10%"
                colspan="2"
                style="font-weight:bold"
                class="right-align"
              >
                {{
                  numberWithCommas(
                    item.sectors.displaced.displaced_disaggregated
                      ? item.sectors.displaced.displaced_disaggregated.reduce(
                          (sum, value) =>
                            sum + +parseInt(value.displaced_females || 0),
                          0
                        )
                      : 0
                  )
                }}
              </td>
              <td
                width="10%"
                colspan="2"
                style="font-weight:bold"
                class="right-align"
              >
                {{
                  numberWithCommas(
                    item.sectors.displaced.displaced_disaggregated
                      ? item.sectors.displaced.displaced_disaggregated.reduce(
                          (sum, value) =>
                            sum +
                            (+parseInt(value.displaced_females || 0) +
                              +parseInt(value.displaced_males || 0)),
                          0
                        )
                      : 0
                  )
                }}
              </td>
            </tr>
            <!--  -->
            <tr
              v-if="
                item.sectors.displaced.displaced_individuals_accommodated &&
                  item.sectors.displaced.displaced_individuals_accommodated
                    .length > 0
              "
            >
              <td
                colspan="8"
                width="20%"
                :rowspan="
                  item.sectors.displaced.displaced_individuals_accommodated
                    ? item.sectors.displaced.displaced_individuals_accommodated
                        .length > 0
                      ? 2
                      : 1
                    : 1
                "
              >
                <strong
                  >Number of People accomodated in difference structures</strong
                >
              </td>
              <td colspan="2" width="15%">
                <b>
                  <center>Male</center>
                </b>
              </td>
              <td colspan="2" width="15%">
                <b>
                  <center>Female</center>
                </b>
              </td>
              <td colspan="2" width="15%">
                <b>
                  <center>Total</center>
                </b>
              </td>
            </tr>

            <tr
              v-if="
                item.sectors.displaced.displaced_individuals_accommodated &&
                  item.sectors.displaced.displaced_individuals_accommodated
                    .length > 0
              "
            >
              <td width="15%" colspan="2" class="right-align">
                {{
                  numberWithCommas(
                    item.sectors.displaced.displaced_individuals_accommodated
                      ? item.sectors.displaced.displaced_individuals_accommodated
                          .map(function(item) {
                            return item.accomodated_males
                              ? parseInt(item.accomodated_males)
                              : 0;
                          })
                          .reduce((sum, value) => sum + value)
                      : 0
                  )
                }}
              </td>
              <td width="15%" colspan="2" class="right-align">
                {{
                  numberWithCommas(
                    item.sectors.displaced.displaced_individuals_accommodated
                      ? item.sectors.displaced.displaced_individuals_accommodated
                          .map(function(item) {
                            return item.accomodated_females
                              ? parseInt(item.accomodated_females)
                              : 0;
                          })
                          .reduce((sum, value) => sum + value)
                      : 0
                  )
                }}
              </td>
              <td width="15%" colspan="2" class="right-align">
                {{
                  numberWithCommas(
                    parseInt(
                      item.sectors.displaced.displaced_individuals_accommodated
                        ? item.sectors.displaced.displaced_individuals_accommodated
                            .map(function(item) {
                              return item.accomodated_males
                                ? parseInt(item.accomodated_males)
                                : 0;
                            })
                            .reduce((sum, value) => sum + value)
                        : 0
                    ) +
                      parseInt(
                        item.sectors.displaced
                          .displaced_individuals_accommodated
                          ? item.sectors.displaced.displaced_individuals_accommodated
                              .map(function(item) {
                                return item.accomodated_females
                                  ? parseInt(item.accomodated_females)
                                  : 0;
                              })
                              .reduce((sum, value) => sum + value)
                          : 0
                      )
                  )
                }}
              </td>
            </tr>
            <!-- start -->

            <!-- start -->

            <tr
              v-if="
                item.sectors.displaced.PeopleAffectedrows &&
                  item.sectors.displaced.PeopleAffectedrows.length > 0
              "
            >
              <td
                width="20%"
                v-bind:rowspan="
                  item.sectors.displaced.PeopleAffectedrows.length
                    ? item.sectors.displaced.PeopleAffectedrows.length > 0
                      ? item.sectors.displaced.PeopleAffectedrows.length + 2
                      : 1
                    : 1
                "
                colspan="5"
              >
                <strong>Number of Households displaced</strong>
              </td>
              <td colspan="3">
                <b>GVH</b>
              </td>
              <td colspan="3" width="10%">
                <b>Household group</b>
              </td>
              <td width="10%" colspan="2">
                <b>
                  <center>MHH</center>
                </b>
              </td>
              <td width="10%" colspan="2">
                <b>
                  <center>FHH</center>
                </b>
              </td>
              <td width="10%" colspan="2">
                <b>
                  <center>Total</center>
                </b>
              </td>
            </tr>
            <tr
              v-if="
                item.sectors.displaced.PeopleAffectedrows &&
                  item.sectors.displaced.PeopleAffectedrows.length > 0
              "
              v-for="(row, index) in item.sectors.displaced.PeopleAffectedrows"
            >
              <td
                width="10%"
                colspan="3"
                v-if="
                  index ==
                    item.sectors.displaced.PeopleAffectedrows.findIndex(
                      x => x.name == row.name
                    )
                "
                :rowspan="
                  item.sectors.displaced.PeopleAffectedrows.filter(
                    x => x.name == row.name
                  ).length
                "
              >
                {{ row.name }}
              </td>
              <td width="10%" colspan="3">
                {{
                  row.category == "Other"
                    ? "Other(" + row.other_category_name + ")"
                    : row.category
                }}
              </td>
              <td width="10%" colspan="2" class="right-align">
                {{ numberWithCommas(row.number_displaced_by_gender_mhh || 0) }}
              </td>
              <td width="10%" colspan="2" class="right-align">
                {{ numberWithCommas(row.number_displaced_by_gender_fhh  || 0) }}
              </td>
              <td
                style="font-weight:bold"
                width="10%"
                colspan="2"
                class="right-align"
              >
                {{
                  numberWithCommas(
                    (row.number_displaced_by_gender_fhh
                      ? parseInt(row.number_displaced_by_gender_fhh)
                      : 0) +
                      (row.number_displaced_by_gender_mhh
                        ? parseInt(row.number_displaced_by_gender_mhh)
                        : 0)
                  )
                }}
              </td>
            </tr>
            <tr
              v-if="
                item.sectors.displaced.PeopleAffectedrows &&
                  item.sectors.displaced.PeopleAffectedrows.length > 0
              "
            >
              <td width="20%" style="font-weight:bold" colspan="6">
                Total
              </td>
              <td
                width="10%"
                colspan="2"
                style="font-weight:bold;"
                class="right-align"
              >
                {{
                  numberWithCommas(
                    item.sectors.displaced.PeopleAffectedrows
                      ? item.sectors.displaced.PeopleAffectedrows.reduce(
                          (sum, value) =>
                            (sum + parseInt(value.number_displaced_by_gender_mhh || 0)),
                          0
                        )
                      : 0
                  )
                }}
              </td>
              <td
                width="10%"
                colspan="2"
                style="font-weight:bold"
                class="right-align"
              >
            
                {{
                  numberWithCommas(
                    item.sectors.displaced.PeopleAffectedrows
                      ? item.sectors.displaced.PeopleAffectedrows.reduce(
                          (sum, value) =>
                            (sum + parseInt(value.number_displaced_by_gender_fhh ||0 )),
                          0
                        )
                      : 0
                  )
                }}
              </td>
              <td
                width="10%"
                colspan="2"
                style="font-weight:bold"
                class="right-align"
              >
                {{
                  numberWithCommas(
                    item.sectors.displaced.PeopleAffectedrows
                      ? item.sectors.displaced.PeopleAffectedrows.reduce(
                          (sum, value) =>
                            sum +
                            (parseInt(value.number_displaced_by_gender_fhh || 0) +
                              +parseInt(value.number_displaced_by_gender_mhh ||0 )),
                          0
                        )
                      : 0
                  )
                }}
              </td>
            </tr>

            <!-- end -->
            <tr>
              <td style="width:20%" colspan="5">
                <strong>Emergency Needs</strong>
              </td>
              <td colspan="12">
                <li style="margin-left:2%" class="qcont">
                  {{
                    !item.sectors.displaced.response_needed_emergency
                      ? "Not reported"
                      : item.sectors.displaced.response_needed_emergency
                          .map(item => {
                            if (item.specification) {
                              return `${item.name} (${item.specification})`;
                            } else {
                              return item.name;
                            }
                          })
                          .join(",")
                  }}
                </li>
              </td>
            </tr>
            <tr>
              <td style="width:20%" colspan="5">
                <strong>Non-Food Items Needs</strong>
              </td>
              <td colspan="12">
                <li style="margin-left:2%" class="qcont">
                  {{
                    !item.sectors.displaced.response_needed_non_food
                      ? "Not reported"
                      : item.sectors.displaced.response_needed_non_food
                          .map(item => {
                            if (item.specification) {
                              return `${item.name} (${item.specification})`;
                            } else {
                              return item.name;
                            }
                          })
                          .join(",")
                  }}
                </li>
              </td>
            </tr>
            <tr>
              <td style="width:20%" colspan="5">
                <strong>Response Needed</strong>
              </td>
              <td colspan="12" style="width:70%">
                <!-- <u>
                  <strong>URGENT</strong>
                </u>
                <br /> -->
                <!-- <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.displaced
                      ? item.sectors.displaced.urgent_response_needed
                        ? item.sectors.displaced.urgent_response_needed
                        : "N/A"
                      : "N/A"
                  }}
                </li>
                <hr style="margin:1%;" /> -->
                <u>
                  <strong>GENERAL</strong>
                </u>
                <br />
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.displaced
                      ? item.sectors.displaced.response_needed
                        ? item.sectors.displaced.response_needed
                        : "Not reported"
                      : "Not reported"
                  }}
                </li>
              </td>
            </tr>
          </table>
        </td>
      </tr>
      <!--=======================DISPLACED END HERE======================-->
      
      <!--=======================AGRICULTURE START HERE======================-->
      <tr
        v-if="hasAgricultureProperties(item)"
        style="margin:0 !important;padding:0 !important;border:0px !important;"
      >
        <td
          colspan="15"
          style="margin:0 !important;padding:0 !important;border:0px"
        >
          <table
            width="100%"
            style="margin:0 !important;padding:0 !important;border:0px"
          >
            <tr>
              <td
                width="9%"
                class="clearfix"
                :rowspan="
                  (item.sectors.agriculture.crops_damaged ? 1 : 1) +
                    (item.sectors.agriculture.crops_damaged
                      ? item.sectors.agriculture.crops_damaged.length >= 0
                        ? item.sectors.agriculture.crops_damaged.length + 2
                        : 1
                      : 1) +
                    (item.sectors.agriculture.impact_on_livestock
                      ? item.sectors.agriculture.impact_on_livestock.length > 0
                        ? 1
                        : 1
                      : 1) +
                    (item.sectors.agriculture.food_item_damage
                      ? item.sectors.agriculture.food_item_damage.length > 0
                        ? item.sectors.agriculture.food_item_damage.length + 1
                        : 1
                      : 1) +
                    (item.sectors.agriculture.livelihoods_affected
                      ? item.sectors.agriculture.livelihoods_affected.length > 0
                        ? item.sectors.agriculture.livelihoods_affected.length +
                          1
                        : 1
                      : 1) +
                    (item.sectors.agriculture.facilities_damaged
                      ? item.sectors.agriculture.facilities_damaged.length > 0
                        ? item.sectors.agriculture.facilities_damaged.length + 1
                        : 1
                      : 1) +
                    (item.sectors.agriculture.impact_on_crops
                      ? item.sectors.agriculture.impact_on_crops.length > 0
                        ? item.sectors.agriculture.impact_on_crops.length + 1
                        : 1
                      : 1) +
                    (item.sectors.agriculture.response_needed_crops
                      ? item.sectors.agriculture.response_needed_crops.length >
                        0
                        ? item.sectors.agriculture.response_needed_crops
                            .length + 1
                        : 1
                      : 1) +
                    8
                "
              >
                <center><img
                  src="../../../../static/other_cluster_agriculture_100px_bluebox.png"
                  height="150"
                /></center>
              </td>
            </tr>

            <tr
              v-if="
                item.sectors.agriculture.crops_damaged &&
                  item.sectors.agriculture.crops_damaged.length > 0
              "
            >
              <td
                v-bind:rowspan="
                  item.sectors.agriculture.crops_damaged
                    ? item.sectors.agriculture.crops_damaged.length + 1
                    : 1
                "
                
                width="20%"
              >
                <strong>Damage per crop (Hectares)</strong>
              </td>
              <td colspan="3" width="20%">
                <b>Name</b>
              </td>
              <td colspan="3" width="10%">
                <b>Submerged</b>
              </td>
              <td colspan="3" width="10%">
                <b>Washed away</b>
              </td>
              <td colspan="2" width="10%">
                <b>
                  <center>Total</center>
                </b>
              </td>
            </tr>
            <tr
              v-if="
                item.sectors.agriculture.crops_damaged &&
                  item.sectors.agriculture.crops_damaged.length > 0
              "
              v-for="crop in item.sectors.agriculture.crops_damaged"
            >
              <td colspan="3" width="40%">{{ crop.name }}</td>
              <td width="10%" colspan="3" class="right-align">
                {{ numberWithCommas(crop.hectares_submerged)  || "Not reported"}}
              </td>
              <td width="10%" colspan="3" class="right-align">
                {{ numberWithCommas(crop.hectares_washed_away)  || "Not reported"}}
              </td>
              <td width="10%" colspan="2" class="right-align">
                {{
                  numberWithCommas(
                    (crop.hectares_submerged
                      ? parseInt(crop.hectares_submerged)
                      : 0) +
                      (crop.hectares_washed_away
                        ? parseInt(crop.hectares_washed_away)
                        : 0)
                  )
                }}
              </td>
            </tr>

            <!--======================START==========================-->

            <tr
              v-if="
                item.sectors.agriculture.facilities_damaged &&
                  item.sectors.agriculture.facilities_damaged.length > 0
              "
            >
              <td
                :rowspan="
                  item.sectors.agriculture.facilities_damaged
                    ? item.sectors.agriculture.facilities_damaged.length + 1
                    : 1
                "
                width="20%"
              >
                <strong>Facilities damaged</strong>
              </td>
              <td colspan="3" width="10%">
                <b>Facility</b>
              </td>
              <td colspan="3" width="10%">
                <b>Partially</b>
              </td>
              <td colspan="3" width="10%">
                <b>Completely </b>
              </td>
              <td colspan="2" width="10%">
                <b>Total</b>
              </td>
            </tr>

            <tr
              v-if="
                item.sectors.agriculture.facilities_damaged &&
                  item.sectors.agriculture.facilities_damaged.length > 0
              "
              v-for="facilty in item.sectors.agriculture.facilities_damaged"
            >
              <td colspan="3" width="10%" class="right-left">
                {{
                  facilty.name == "Other"
                    ? "Other(" + facilty.other_facility_name + ")"
                    : facilty.name
                }}
              </td>
              <td width="10%" colspan="3" class="right-left">
                {{ facilty.partially_damaged  || 0}}
              </td>
              <td width="10%" colspan="3" class="right-left">
                {{ facilty.completely_damaged  || 0}}
              </td>
              <td width="10%" colspan="2" class="right-align">
                {{
                  numberWithCommas(
                    (facilty.partially_damaged
                      ? parseInt(facilty.partially_damaged)
                      : 0) +
                      (facilty.completely_damaged
                        ? parseInt(facilty.completely_damaged)
                        : 0)
                  )
                }}
              </td>
            </tr>
            <!--======================END============================-->

            <!--======================START==========================-->

            <tr
              v-if="
                item.sectors.agriculture.impact_on_crops &&
                  item.sectors.agriculture.impact_on_crops.length > 0
              "
            >
              <td
              width="20%"
              colspan="4"
                :rowspan="
                  item.sectors.agriculture.impact_on_crops
                    ? item.sectors.agriculture.impact_on_crops.length + 1
                    : 1
                "
                
              >
                <strong>Damage per crop (Households)</strong>
              </td>
              <td colspan="3">
                <b>Crop</b>
              </td>
              <td colspan="3">
                <b>HH affected</b>
              </td>
              <td colspan="2">
                <b>Hectares </b>
              </td>
            </tr>

            <tr
              v-if="
                item.sectors.agriculture.impact_on_crops &&
                  item.sectors.agriculture.impact_on_crops.length > 0
              "
              v-for="crop in item.sectors.agriculture.impact_on_crops"
            >
              <td colspan="3" class="right-left">
                {{
                  crop.name == "Other"
                    ? "Other(" + crop.other_crop_type + ")"
                    : crop.name
                }}
              </td>
              <td colspan="3" class="right-left">{{ crop.hh_affected  || 0}}</td>
              <td colspan="2" class="right-left">
                {{ crop.hectares_damaged  || 0}}
              </td>
            </tr>
            <!--======================END============================-->

            <!--======================START==========================-->

            <tr
              v-if="
                item.sectors.agriculture.impact_on_livestock &&
                  item.sectors.agriculture.impact_on_livestock.length > 0
              "
            >
              <td
                :rowspan="
                  item.sectors.agriculture.impact_on_livestock
                    ? item.sectors.agriculture.impact_on_livestock.length + 1
                    : 1
                "
                
                width="20%"
              >
                <strong>Livestock affected</strong>
              </td>
              <td colspan="3">
                <b>Livestock</b>
              </td>
              <td colspan="3">
                <b>HH affected</b>
              </td>
              <td colspan="3" width="10%">
                <b>Injured </b>
              </td>
              <td colspan="2" width="10%">
                <b>Dead </b>
              </td>
            </tr>

            <tr
              v-if="
                item.sectors.agriculture.impact_on_livestock &&
                  item.sectors.agriculture.impact_on_livestock.length > 0
              "
              v-for="livestock in item.sectors.agriculture.impact_on_livestock"
            >
              <td colspan="3" class="right-left">
                {{
                  livestock.name == "Other"
                    ? "Other(" + livestock.other_livestock_name + ")"
                    : livestock.name
                }}
              </td>
              <td colspan="3" class="right-left">
                {{ livestock.hh_affected_l  || 0}}
              </td>
              <td colspan="3" class="right-left">
                {{ livestock.livestock_deaths || 0}}
              </td>
              <td colspan="2" class="right-left">
                {{ livestock.livestock_injured || 0}}
              </td>
            </tr>
            <!--======================END============================-->

            <!--======================START==========================-->

            <tr
              v-if="
                item.sectors.agriculture.livelihoods_affected &&
                  item.sectors.agriculture.livelihoods_affected.length > 0
              "
            >
              <td
                v-bind:rowspan="
                  item.sectors.agriculture.livelihoods_affected
                    ? item.sectors.agriculture.livelihoods_affected.length + 1
                    : 1
                "
                width="20%"
              >
                <strong>Livelihoods Affected</strong>
              </td>

              <td colspan="2" width="10%">
                <b>Livelihood</b>
              </td>
              <td colspan="2" width="10%">
                <b>Population group</b>
              </td>
              <td colspan="2" width="10%">
                <b>Severity</b>
              </td>
              <td colspan="3" width="10%" class="text-center">
                <b>MHH</b>
              </td>
              <td colspan="2" width="10%" class="text-center">
                <b>FHH</b>
              </td>
              <!-- <td colspan="2" width="10%">
                        <b>
                          <center>Total</center>
                        </b>
                      </td> -->
            </tr>
            <tr
              v-if="
                item.sectors.agriculture.livelihoods_affected &&
                  item.sectors.agriculture.livelihoods_affected.length > 0
              "
              v-for="livelihoods in item.sectors.agriculture
                .livelihoods_affected"
            >
              <td colspan="2" width="10%" class="right-left">
                {{ livelihoods.livelihood_type }}
              </td>
              <td width="10%" colspan="2" class="right-left">
                {{
                  livelihoods.category == "Other"
                    ? "Other(" + livelihoods.other_category == "undefined"
                      ? "Other(Not reported"
                      : "Other(Not reported" + ")"
                    : livelihoods.category
                }}
              </td>
              <td width="10%" colspan="2" class="right-left">
                {{ livelihoods.severity }}
              </td>
              <td width="10%" colspan="3" class="right-align">
                {{
                  numberWithCommas(
                    livelihoods.male_HH ? parseInt(livelihoods.male_HH) : 0
                  )
                }}
              </td>

              <td width="10%" colspan="2" class="right-align">
                {{
                  numberWithCommas(
                    livelihoods.female_HH ? parseInt(livelihoods.female_HH) : 0
                  )
                }}
              </td>
            </tr>
            <!--======================END============================-->

            <tr
              v-if="
                item.sectors.agriculture.food_item_damage &&
                  item.sectors.agriculture.food_item_damage.length > 0
              "
            >
              <td
                v-bind:rowspan="
                  item.sectors.agriculture.food_item_damage.length + 1
                "
                colspan="5"
                width="20%"
              >
                <strong>Number of Food items damage (KGs)</strong>
              </td>
              <td colspan="7" width="60%">
                <b>Name</b>
              </td>
              <td colspan="2" width="10%">
                <b>in KGs</b>
              </td>
            </tr>
            <tr
              v-if="
                item.sectors.agriculture.food_item_damage &&
                  item.sectors.agriculture.food_item_damage.length > 0
              "
              v-for="crop in item.sectors.agriculture.food_item_damage"
            >
              <td colspan="7" width="60%">{{ crop.food_item_name }}</td>
              <td colspan="2" width="10%" class="right-align">
                {{ numberWithCommas(crop.number_of_kilos) }}
              </td>
            </tr>
            <tr>
              <td style="width:20%">
                <strong>Emergency Needs</strong>
              </td>
              <td colspan="10">
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.agriculture.emergent_needs == undefined
                      ? "Not reported"
                      : item.sectors.agriculture.emergent_needs.join(", ")
                  }}
                </li>
              </td>
            </tr>

            <tr>
              <td style="width:20%">
                <strong>Emergency response</strong>
              </td>
              <td colspan="10">
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.agriculture.emergency_response_needed ==
                    undefined
                      ? "Not reported"
                      : item.sectors.agriculture.emergency_response_needed.join(
                          ", "
                        )
                  }}
                </li>
              </td>
            </tr>

            <!--======================START==========================-->

            <tr
              v-if="
                item.sectors.agriculture.response_needed_crops &&
                  item.sectors.agriculture.response_needed_crops.length > 0
              "
            >
              <td
                :rowspan="
                  item.sectors.agriculture.response_needed_crops
                    ? item.sectors.agriculture.response_needed_crops.length + 1
                    : 1
                "
              >
                <strong>Support for crops</strong>
              </td>
              <td colspan="3">
                <b>Name</b>
              </td>
              <td colspan="3">
                <b>Area</b>
              </td>
              <td colspan="4">
                <b>Requirement</b>
              </td>
            </tr>

            <tr
              v-if="
                item.sectors.agriculture.response_needed_crops &&
                  item.sectors.agriculture.response_needed_crops.length > 0
              "
              v-for="facilty in item.sectors.agriculture.response_needed_crops"
            >
              <td colspan="3" class="right-left">
                {{
                  facilty.name == "Other"
                    ? "Other(" + facilty.other_crop_name + ")"
                    : facilty.name
                }}
              </td>
              <td colspan="3" class="right-left">{{ facilty.area }}</td>
              <td colspan="4" class="right-left">{{ facilty.requirement }}</td>
            </tr>
            <!--======================END============================-->

            <tr>
              <td  style="width:20%">
                <strong>Response Needed</strong>
              </td>
              <td colspan="10" style="width:70%">
                <!-- <u>
                  <strong>URGENT</strong>
                </u>
                <br />
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.agriculture
                      ? item.sectors.agriculture.urgent_response_needed
                        ? item.sectors.agriculture.urgent_response_needed
                        : "N/A"
                      : "N/A"
                  }}
                </li>
                <hr style="margin:1%;" /> -->
                <u>
                  <strong>GENERAL</strong>
                </u>
                <br />
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.agriculture
                      ? item.sectors.agriculture.response_needed
                        ? item.sectors.agriculture.response_needed
                        : "Not reported"
                      : "Not reported"
                  }}
                </li>
              </td>
            </tr>
          </table>
        </td>
      </tr>
      <!--=======================AGRICULTURE END HERE======================-->
    
      <!--=======================FOOD SECURITY START HERE======================-->
      <tr
        v-if="hasFoodProperties(item)"
        style="margin:0;padding:0 !important;border:0px !important;"
      >
        <td colspan="15" style="margin:0;padding:0 !important;border:0px">
          <table
            width="100%"
            style="margin:0;padding:0 !important;border:0px !important;"
          >
            <tr>
              <td
                :rowspan="
                  (item.sectors.food.food_stocks_avaliability
                    ? item.sectors.food.food_stocks_avaliability.length > 0
                      ? item.sectors.food.food_stocks_avaliability.length + 1
                      : 1
                    : 1) +
                    (item.sectors.food.food_access
                      ? item.sectors.food.food_access.length > 0
                        ? item.sectors.food.food_access.length + 1
                        : 1
                      : 1) +
                    (item.sectors.food.food_item_damage
                      ? item.sectors.food.food_item_damage.length > 0
                        ? item.sectors.food.food_item_damage.length + 1
                        : 1
                      : 1) +
                    (item.sectors.food.impact_on_price
                      ? item.sectors.food.impact_on_price.length > 0
                        ? item.sectors.food.impact_on_price.length + 1
                        : 1
                      : 1) +
                    4
                "
                width="10%"
                class="clearfix"
              >
                <center><img
                  src="../../../../static/cluster_food_security_100px_bluebox.png"
                  height="150"
                /></center>
              </td>
              <td
                v-if="
                  item.sectors.food.food_stocks_avaliability &&
                    item.sectors.food.food_stocks_avaliability.length > 0
                "
                v-bind:rowspan="
                  item.sectors.food.food_stocks_avaliability.length > 0
                    ? item.sectors.food.food_stocks_avaliability.length + 1
                    : 1
                "
                colspan="4"
                style="width:20%"
              >
                <strong>Food stocks availability (HH)</strong>
              </td>
              <td
                colspan="3"
                width="30%"
                v-if="
                  item.sectors.food.food_stocks_avaliability &&
                    item.sectors.food.food_stocks_avaliability.length > 0
                "
              >
                <b>Category</b>
              </td>
              <td
                colspan="2"
                width="10%"
                v-if="
                  item.sectors.food.food_stocks_avaliability &&
                    item.sectors.food.food_stocks_avaliability.length > 0
                "
              >
                <b>Population group</b>
              </td>
              <td
                width="10%"
                colspan="2"
                v-if="
                  item.sectors.food.food_stocks_avaliability &&
                    item.sectors.food.food_stocks_avaliability.length > 0
                "
              >
                <b>
                  <center>FHH</center>
                </b>
              </td>
              <td
                width="10%"
                colspan="2"
                v-if="
                  item.sectors.food.food_stocks_avaliability &&
                    item.sectors.food.food_stocks_avaliability.length > 0
                "
              >
                <b>
                  <center>MHH</center>
                </b>
              </td>

              <td
                width="10%"
                colspan="2"
                v-if="
                  item.sectors.food.food_stocks_avaliability &&
                    item.sectors.food.food_stocks_avaliability.length > 0
                "
              >
                <b>
                  <center>Total</center>
                </b>
              </td>
            </tr>

            <tr
              v-if="
                item.sectors.food.food_stocks_avaliability &&
                  item.sectors.food.food_stocks_avaliability.length > 0
              "
              v-for="crop in item.sectors.food.food_stocks_avaliability"
            >
              <td width="30%" colspan="3">
                {{ crop.availability_food_stocks || "Not reported" }}
              </td>
              <td width="10%" colspan="2">
                {{
                  crop.selectedAgeGroup == "Other"
                    ? "Other(" + crop.other_crop_type == "undefined"
                      ? "Other(Not reported"
                      : "Other(Not reported" + ")"
                    : crop.selectedAgeGroup
                }}
              </td>
              <td width="10%" colspan="2" class="right-align">
                {{ crop.female_HH || 0}}
              </td>
              <td width="10%" colspan="2" class="right-align">
                {{ crop.male_HH || 0}}
              </td>

              <td width="10%" colspan="2" class="right-align">
                {{
                  parseInt(crop.female_HH || 0) + parseInt(crop.male_HH || 0)
                }}
              </td>
            </tr>
            <!------------------work from dc starts----------------------------------------->
            <!---start---------------------->
            <tr v-if="item.sectors.food.food_item_damage">
              <td
                style="width:20%"
                :rowspan="
                  item.sectors.food.food_item_damage.length > 0
                    ? item.sectors.food.food_item_damage.length + 1
                    : 1
                "
                colspan="4"
              >
                <b>Food Items Damaged</b>
              </td>
              <td colspan="5" style="width:15%">
                <b>
                  Food Name
                </b>
              </td>
              <td colspan="5">
                <b>
                  <center>Mass(KGs)</center>
                </b>
              </td>
            </tr>
            <tr
              v-for="value in item.sectors.food.food_item_damage"
              :key="value"
            >
              <td colspan="5">
                {{ value.food_item_name }}
              </td>
              <td colspan="5" class="right-align">
                {{ numberWithCommas(value.number_of_kilos) || 0}}
              </td>
            </tr>
            <!----------------end----------->
            <!---food ---------------------->

            <!---food  accessibility---------------------->
            <tr
              v-if="
                item.sectors.food.food_access &&
                  item.sectors.food.food_access.length > 0
              "
            >
              <td
                style="width:20%"
                :rowspan="
                  item.sectors.food.food_access.length > 0
                    ? item.sectors.food.food_access.length + 1
                    : 1
                "
                colspan="3"
              >
                <b>Food accessibility</b>
              </td>
              <td colspan="7">
                <b>
                  Question
                </b>
              </td>
              <td colspan="5">
                <b>
                  Response
                </b>
              </td>
            </tr>
            <tr v-for="value in item.sectors.food.food_access" :key="value">
              <td colspan="7">
                {{ value.name }}
              </td>
              <td colspan="5">
                {{ value.response }}
              </td>
            </tr>
            <!----------------Food accessibility ends----------->

            <!---food  accessibility---------------------->
            <tr
              v-if="
                item.sectors.food.impact_on_price &&
                  item.sectors.food.impact_on_price.length > 0
              "
            >
              <td
                style="width:20%"
                :rowspan="
                  item.sectors.food.impact_on_price.length > 0
                    ? item.sectors.food.impact_on_price.length + 1
                    : 1
                "
                colspan="3"
              >
                <b>Food Prices (MWK)</b>
              </td>
              <td colspan="4">
                <b>
                  Item
                </b>
              </td>
              <td colspan="4">
                <b>
                  Before disaster
                </b>
              </td>
              <td colspan="3">
                <b>
                  After disaster
                </b>
              </td>
            </tr>
            <tr v-for="value in item.sectors.food.impact_on_price" :key="value">
              <td colspan="4">
                {{
                  value.name == "Other"
                    ? "Other(" + value.other_crop_type + ")"
                    : value.name
                }}
              </td>
              <td colspan="4" class="right-align">
                {{ numberWithCommas(value.price_before_disaster) || "Not recorded"}}
              </td>

              <td colspan="3" class="right-align">
                {{ numberWithCommas(value.price_after_disaster) || "Not recorded"}}
              </td>
            </tr>
            <!----------------work from dc finish---------------------------------->
            <tr>
              <td colspan="4" style="width:20%">
                <strong>Emergency Needs</strong>
              </td>
              <td colspan="11">
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.food.emergent_needs == undefined
                      ? "Not reported"
                      : item.sectors.food.emergent_needs.join(", ")
                  }}
                </li>
              </td>
            </tr>
            <tr>
              <td colspan="4" style="width:20%">
                <strong>Response Needed</strong>
              </td>
              <td colspan="11" style="width:80%">
                <!-- <u>
                  <strong>URGENT</strong>
                </u>
                <br />
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.food
                      ? item.sectors.food.urgent_response_needed
                        ? item.sectors.food.urgent_response_needed
                        : "N/A"
                      : "N/A"
                  }}
                </li>
                <hr style="margin:1%;" /> -->
                <u>
                  <strong>GENERAL</strong>
                </u>
                <br />
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.food
                      ? item.sectors.food.response_needed
                        ? item.sectors.food.response_needed
                        : "Not reported"
                      : "Not reported"
                  }}
                </li>
              </td>
            </tr>
          </table>
        </td>
      </tr>
      <!--=======================FOOD SECURITY END HERE======================-->
     
      <!--=======================EDUCATION START HERE======================-->
      <tr
        v-if="hasEducationProperties(item)"
        style="margin:0;padding:0 !important;border:0px !important;"
      >
        <td colspan="15" style="margin:0;padding:0 !important;border:0px">
          <table
            width="100%"
            style="margin:0;padding:0 !important;border:0px !important;"
          >
            <tr>
              <td
                :rowspan="
                  5 +
                    (item.sectors.education.impact_on_schools
                      ? item.sectors.education.impact_on_schools.length +
                        item.sectors.education.impact_on_schools.length
                      : 0) *
                      3
                "
                width="9%"
                class="clearfix"
              >
                <center>
                  <img
                  src="../../../../static/cluster_education_100px_bluebox.png"
                  height="150"
                />
                </center>
              </td>
              <td
                v-if="
                  item.sectors.education.impact_on_schools &&
                    item.sectors.education.impact_on_schools.length > 0
                "
                :rowspan="1 + item.sectors.education.impact_on_schools.length"
                colspan="4"
                style="20%"
              >
                <b>Impact on schools buildings</b>
              </td>
              <td
                v-if="
                  item.sectors.education.impact_on_schools &&
                    item.sectors.education.impact_on_schools.length > 0
                "
                width="10%"
              >
                <b>School type</b>
              </td>
              <td
                v-if="
                  item.sectors.education.impact_on_schools &&
                    item.sectors.education.impact_on_schools.length > 0
                "
                width="10%"
              >
                <b>Structure type</b>
              </td>
              <td
                v-if="
                  item.sectors.education.impact_on_schools &&
                    item.sectors.education.impact_on_schools.length > 0
                "
                width="10%"
              >
                <b>Name</b>
              </td>
              <td
                v-if="
                  item.sectors.education.impact_on_schools &&
                    item.sectors.education.impact_on_schools.length > 0
                "
                width="10%"
              >
                <b>Roof affected</b>
              </td>
              <td
                v-if="
                  item.sectors.education.impact_on_schools &&
                    item.sectors.education.impact_on_schools.length > 0
                "
                width="10%"
              >
                <b>Under water</b>
              </td>
              <td
                v-if="
                  item.sectors.education.impact_on_schools &&
                    item.sectors.education.impact_on_schools.length > 0
                "
                width="10%"
              >
                <b>Extent of damage</b>
              </td>

              <td
                v-if="
                  item.sectors.education.impact_on_schools &&
                    item.sectors.education.impact_on_schools.length > 0
                "
                width="10%"
              >
                <b>Condition</b>
              </td>
            </tr>

            <tr
              v-if="
                item.sectors.education.impact_on_schools &&
                  item.sectors.education.impact_on_schools.length > 0
              "
              v-for="row in item.sectors.education.impact_on_schools"
            >
              <td width="10%">
                {{ row.school_type + (" | " + row.public_or_private || "") }}
              </td>
              <td width="10%">{{ row.structure_type }}</td>
              <td width="10%">{{ row.school_name }}</td>
              <td width="10%">
                {{ row.roofs_affected || 0}}
              </td>
              <td width="10%">
                {{ row.underwater || 0}}
              </td>
              <td width="10%">
                {{ row.status }}
              </td>
              <td width="10%">
                {{ row.condition }}
              </td>
            </tr>

            <tr
              v-if="
                item.sectors.education.impact_on_schools &&
                  item.sectors.education.impact_on_schools.length > 0
              "
            >
              <td
                v-if="item.sectors.education.impact_on_schools"
                :rowspan="1 + item.sectors.education.impact_on_schools.length"
                colspan="4"
                width="20%"
              >
                <b>Impact on school goers</b>
              </td>
              <td
                v-if="item.sectors.education.impact_on_schools"
                colspan="3"
                width="10%"
              >
                <b>School type</b>
              </td>
              <td
                v-if="item.sectors.education.impact_on_schools"
                colspan="2"
                width="10%"
              >
                <b>Name</b>
              </td>
              <td v-if="item.sectors.education.impact_on_schools" width="10%">
                <b>Males out of school</b>
              </td>
              <td v-if="item.sectors.education.impact_on_schools" width="10%">
                <b>Females out of school</b>
              </td>
            </tr>
            <tr
              v-if="
                item.sectors.education.impact_on_schools &&
                  item.sectors.education.impact_on_schools.length > 0
              "
              v-for="row in item.sectors.education.impact_on_schools"
            >
              <td colspan="3" width="10%">
                {{ row.school_type + (" | " + row.public_or_private || "") }}
              </td>
              <td colspan="2" width="10%">{{ row.school_name }}</td>
              <td width="10%" class="right-align">
                {{ numberWithCommas(row.females_out_of_school) || 0}}
              </td>
              <td width="10%" class="right-align">
                {{ numberWithCommas(row.males_out_of_school) || 0}}
              </td>
            </tr>

            <tr
              v-if="
                item.sectors.education.impact_on_schools &&
                  item.sectors.education.impact_on_schools.length > 0
              "
            >
              <td
                v-if="item.sectors.education.impact_on_schools"
                :rowspan="1 + item.sectors.education.impact_on_schools.length"
                colspan="3"
                width="20%"
              >
                <b>Impact on teachers</b>
              </td>
              <td
                v-if="item.sectors.education.impact_on_schools"
                colspan="3"
                width="20%"
              >
                <b>School type</b>
              </td>
              <td
                v-if="item.sectors.education.impact_on_schools"
                colspan="2"
                width="10%"
              >
                <b>Name</b>
              </td>
              <td
                v-if="item.sectors.education.impact_on_schools"
                colspan="2"
                width="20%"
              >
                <b>Female</b>
              </td>

              <td
                v-if="item.sectors.education.impact_on_schools"
                colspan="3"
                width="20%"
              >
                <b>Male</b>
              </td>
            </tr>
            <tr
              v-if="
                item.sectors.education.impact_on_schools &&
                  item.sectors.education.impact_on_schools.length > 0
              "
              v-for="row in item.sectors.education.impact_on_schools"
            >
              <td colspan="3" width="20%">
                {{ row.school_type + (" | " + row.public_or_private || "") }}
              </td>
              <td colspan="2" width="10%">{{ row.school_name }}</td>

              <td colspan="2" width="20%" class="right-align">
                {{ numberWithCommas(row.female_teachers) || 0}}
              </td>
              <td colspan="3" width="20%" class="right-align">
                {{ numberWithCommas(row.male_teachers) || 0}}
              </td>
            </tr>

            <tr>
              <td colspan="2" style="width:20%">
                <strong>Emergency Needs</strong>
              </td>
              <td colspan="12">
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.education.emergent_needs == undefined
                      ? "Not reported"
                      : item.sectors.education.emergent_needs.join(", ")
                  }}
                </li>
              </td>
            </tr>
            <tr>
              <td colspan="2" style="width:20%">
                <strong>Response Needed</strong>
              </td>
              <td colspan="12">
                <!-- <u>
                  <strong>URGENT</strong>
                </u>
                <br />
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.education
                      ? item.sectors.education.urgent_response_needed
                        ? item.sectors.education.urgent_response_needed
                        : "N/A"
                      : "N/A"
                  }}
                </li>
                <hr style="margin:1%;" /> -->
                <u>
                  <strong>GENERAL</strong>
                </u>
                <br />
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.education
                      ? item.sectors.education.response_needed
                        ? item.sectors.education.response_needed
                        : "Not reported"
                      : "Not reported"
                  }}
                </li>
              </td>
            </tr>
          </table>
        </td>
      </tr>
      <!--=======================EDUCATION END HERE======================-->

      <!--=======================HEALTH START HERE======================-->
      <tr
        v-if="hasNutritionProperties(item)"
        style="margin:0;padding:0 !important;border:0px !important;"
      >
        <td colspan="15" style="margin:0;padding:0 !important;border:0px">
          <table
            width="100%"
            style="margin:0;padding:0 !important;border:0px !important;"
          >
            <tr>
              <td
              :rowspan="
                  (item.sectors.health.available_health_facilities
                    ? item.sectors.health.available_health_facilities.length
                    : 0) +
                    (item.sectors.health.risk_out_disease_outbreak
                      ? item.sectors.health.risk_out_disease_outbreak.length + 1
                      : 0) +
                    (item.sectors.health.available_health_medical
                      ? item.sectors.health.available_health_medical.length + 1
                      : 0) +
                    9
                "
                width="9%"
                class="clearfix"
              >
                <center>
                  <img
                  src="../../../../static/cluster_health_100px_bluebox.png"
                  height="150"
                />
                </center>
              </td>
              
              <td
                v-if="item.sectors.health.available_health_facilities"
                  :rowspan="
                    1 + item.sectors.health.available_health_facilities.length
                  "
                colspan="4"
              >
                <b>Facilities affected</b>
              </td>
              <td colspan="4" width="15%">
                <b>Type</b>
              </td>



              <td colspan="2" width="20%">
                <b>
                  <center>Name</center>
                </b>
              </td>
              <td colspan="2" width="15%">
                <b>
                  <b><center>Staff houses affected</center></b>
                </b>
              </td>
              <td colspan="2" width="20%">
                <b>
                  <b>Status</b>
                </b>
              </td>
            </tr>
            <tr
              v-if="
                  item.sectors.health.available_health_facilities &&
                    item.sectors.health.available_health_facilities.length > 0
                "
                v-for="row in item.sectors.health.available_health_facilities"
              >
              <td colspan="4" width="40%">
                {{ row.name }}
              </td>
              <td colspan="2" width="10%">
                {{ row.facility_name }}
              </td>
              <td colspan="2" width="10%" class="right-align">
                <center>{{ row.staff_houses_affected || 0}}</center>
              </td>
              <td colspan="2" width="10%">
                {{ row.status }}
              </td>
            </tr>
            <tr v-if="
                item.sectors.health.available_health_medical &&
                  item.sectors.health.available_health_medical.length > 0
              ">
              <td
                :rowspan="
                    1 + item.sectors.health.available_health_medical.length
                  "
                colspan="4"
                width="20%"
              >
                <strong>Availability of medical and health personnel</strong>
              </td>
              <td colspan="6" width="20%">
                <b>Service</b>
              </td>
              <td colspan="3" width="20%">
                <b>Status</b>
              </td>
            </tr>
            <tr
            v-if="
                item.sectors.health.available_health_medical &&
                  item.sectors.health.available_health_medical.length > 0
              "
              v-for="row in item.sectors.health.available_health_medical"
            >
              <td colspan="6">{{ row.name }}</td>
              <td colspan="3">{{ row.status }}</td>
            </tr>

            <tr v-if="
                item.sectors.health.risk_out_disease_outbreak &&
                  item.sectors.health.risk_out_disease_outbreak.length > 0
              ">
              <td
              :rowspan="
                  1 + item.sectors.health.risk_out_disease_outbreak.length
                "
                width="20%"
                colspan="4"
              >
                <strong>Risks of disease outbreak</strong>
              </td>
              <td colspan="6" width="40%">
                <b>Disease</b>
              </td>
              <td colspan="3" width="10%">
                <b>
                  <center>Risk</center>
                </b>
              </td>              
            </tr>

            <tr
            v-if="
                item.sectors.health.risk_out_disease_outbreak &&
                  item.sectors.health.risk_out_disease_outbreak.length > 0
              "
              v-for="row in item.sectors.health.risk_out_disease_outbreak"
            >
              <td colspan="6" width="40%">
                {{ row.name }}
              </td>

              <td colspan="3" width="40%">
                {{ row.risk }}
              </td>
              
            </tr>

            <tr>
              <td colspan="4" style="width:22%">
                <strong>Compromised services</strong>
              </td>
              <td colspan="9">
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.health.compromised_services == undefined
                      ? "Not reported"
                      : item.sectors.health.compromised_services.join(", ")
                  }}
                </li>
              </td>
            </tr>
            <tr>
              <td colspan="4" style="width:20%">
                <strong>Emergency Needs</strong>
              </td>
              <td colspan="9">
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.health.emergent_needs == undefined
                      ? "Not reported"
                      : item.sectors.health.emergent_needs.join(", ")
                  }}
                </li>
              </td>
            </tr>

            <tr>
              <td colspan="4" style="width:20%">
                <strong>Emergency Children Needs</strong>
              </td>
              <td colspan="9">
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.health.emergent_children_needs == undefined
                      ? "Not reported"
                      : item.sectors.health.emergent_children_needs.join(", ")
                  }}
                </li>
              </td>
            </tr>
           
            <tr>
              <td colspan="4" style="width:20%">
                <strong>Emergency Women Needs</strong>
              </td>
              <td colspan="9">
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.health.emergent_women_needs == undefined
                      ? "Not reported"
                      : item.sectors.health.emergent_women_needs.join(", ")
                  }}
                </li>
              </td>
            </tr>
            <tr>
              <td colspan="4" style="width:20%">
                <strong>Response Needed</strong>
              </td>
              <td colspan="9" style="width:70%">
                <!-- <u>
                  <strong>URGENT</strong>
                </u>
                <br />
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.health
                      ? item.sectors.health.urgent_response_needed
                        ? item.sectors.health.urgent_response_needed
                        : "N/A"
                      : "N/A"
                  }}
                </li>
                <hr style="margin:1%;" /> -->
                <u>
                  <strong>GENERAL</strong>
                </u>
                <br />
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.health
                      ? item.sectors.health.response_needed
                        ? item.sectors.health.response_needed
                        : "Not reported"
                      : "Not reported"
                  }}
                </li>
              </td>
            </tr>
          </table>
        </td>
      </tr>
      <!--=======================HEALTH END HERE======================-->

      <!--=======================NUTRITION START HERE======================-->
      <tr
        v-if="hasNutritionProperties(item)"
        style="margin:0;padding:0 !important;border:0px !important;"
      >
        <td colspan="15" style="margin:0;padding:0 !important;border:0px">
          <table
            width="100%"
            style="margin:0;padding:0 !important;border:0px !important;"
          >
            <tr>
              <td
                :rowspan="
                  4 +
                    (item.sectors.nutrition.affected_population
                      ? item.sectors.nutrition.affected_population.length
                      : 0) +
                    (item.sectors.nutrition.impact_on_nutrition_programmes
                      ? item.sectors.nutrition.impact_on_nutrition_programmes
                          .length
                      : 0) +
                    (item.sectors.nutrition.lostLivelihood
                      ? item.sectors.nutrition.lostLivelihood.length
                      : 0) +
                    1
                "
                width="9%"
                class="clearfix"
              >
                <center>
                  <img
                  src="../../../../static/cluster_nutrition_100px_bluebox.png"
                  height="150"
                />
                </center>
              </td>
              
              <td
                v-if="item.sectors.nutrition.affected_population"
                width="20%"
                :rowspan="1 + item.sectors.nutrition.affected_population.length"
                colspan="4"
              >
                <strong>Affected Population</strong>
              </td>
              <td colspan="4" width="40%">
                <b>Category</b>
              </td>
              <td colspan="2" width="10%">
                <b>
                  <center>Male</center>
                </b>
              </td>
              <td colspan="2" width="10%">
                <b>
                  <center>Female</center>
                </b>
              </td>
              <td colspan="2" width="10%">
                <b>
                  <center>Total</center>
                </b>
              </td>
            </tr>
            <tr
              v-if="item.sectors.nutrition.affected_population"
              v-for="row in item.sectors.nutrition.affected_population"
            >
              <td colspan="4" width="40%">
                {{
                  row.name == "Other"
                    ? "Other(" + row.other_affected_population + ")"
                    : row.name
                }}
              </td>
              <td colspan="2" width="10%" class="right-align">
                {{ numberWithCommas(row.affected_males) || 0}}
              </td>
              <td colspan="2" width="10%" class="right-align">
                {{ numberWithCommas(row.affected_females) || 0}}
              </td>
              <td colspan="2" width="10%" class="right-align">
                {{
                  numberWithCommas(
                    parseInt(row.affected_females || 0) +
                      parseInt(row.affected_males || 0)
                  )
                }}
              </td>
            </tr>

            <tr v-if="item.sectors.nutrition.impact_on_nutrition_programmes">
              <td
                :rowspan="
                  1 +
                    item.sectors.nutrition.impact_on_nutrition_programmes.length
                "
                colspan="4"
                width="20%"
              >
                <strong>Impact on nutrition programmes</strong>
              </td>
              <td colspan="4">
                <b>Category</b>
              </td>
              <td colspan="6">
                <b>Status</b>
              </td>
            </tr>
            <tr
              v-if="item.sectors.nutrition.impact_on_nutrition_programmes"
              v-for="row in item.sectors.nutrition
                .impact_on_nutrition_programmes"
            >
              <td colspan="4">{{ row.name }}</td>
              <td colspan="6">{{ row.status }}</td>
            </tr>

            <tr v-if="item.sectors.nutrition.lostLivelihood">
              <td
                v-if="item.sectors.nutrition.lostLivelihood"
                width="20%"
                :rowspan="1 + item.sectors.nutrition.lostLivelihood.length"
                colspan="4"
              >
                <strong>Lost livelihood</strong>
              </td>
              <td colspan="4" width="40%">
                <b>Population group</b>
              </td>
              <td colspan="2" width="10%">
                <b>
                  <center>Male</center>
                </b>
              </td>
              <td colspan="2" width="10%">
                <b>
                  <center>Female</center>
                </b>
              </td>
              <td colspan="2" width="10%">
                <b>
                  <center>Total</center>
                </b>
              </td>
            </tr>

            <tr
              v-if="item.sectors.nutrition.lostLivelihood"
              v-for="row in item.sectors.nutrition.lostLivelihood"
            >
              <td colspan="4" width="40%">
                {{
                  row.name == "Other"
                    ? "Other(" + row.other_category_name + ")"
                    : row.name
                }}
              </td>
              <td colspan="2" width="10%" class="right-align">
                {{ numberWithCommas(row.male_lost_livelihood) || 0}}
              </td>
              <td colspan="2" width="10%" class="right-align">
                {{ numberWithCommas(row.male_lost_livelihood) || 0}}
              </td>
              <td colspan="2" width="10%" class="right-align">
                {{
                  numberWithCommas(
                    parseInt(row.male_lost_livelihood || 0) +
                      parseInt(row.male_lost_livelihood || 0)
                  )
                }}
              </td>
            </tr>

            <tr>
              <td colspan="4" style="width:20%">
                <strong>Emergency Needs</strong>
              </td>
              <td colspan="10">
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.nutrition.emergent_needs == undefined
                      ? "Not reported"
                      : item.sectors.nutrition.emergent_needs.join(", ")
                  }}
                </li>
              </td>
            </tr>

            <tr v-if="item.sectors.nutrition.impact_on_nutrition_programmes">
              <td colspan="4" style="width:20%">
                <strong>Response Needed</strong>
              </td>
              <td colspan="10" style="width:70%">
                <!-- <u>
                  <strong>URGENT</strong>
                </u>
                <br />
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.nutrition
                      ? item.sectors.nutrition.urgent_response_needed
                        ? item.sectors.nutrition.urgent_response_needed
                        : "N/A"
                      : "N/A"
                  }}
                </li>
                <hr style="margin:1%;" /> -->
                <u>
                  <strong>GENERAL</strong>
                </u>
                <br />
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.nutrition
                      ? item.sectors.nutrition.response_needed
                        ? item.sectors.nutrition.response_needed
                        : "Not reported"
                      : "Not reported"
                  }}
                </li>
              </td>
            </tr>
          </table>
        </td>
      </tr>
      <!--=======================NUTRITION END HERE======================-->

      <!--=======================PROTECTION START HERE======================-->
      <tr
        v-if="hasProtectionProperties(item)"
        style="margin:0;padding:0 !important;border:0px !important;"
      >
        <td colspan="15" style="margin:0;padding:0 !important;border:0px">
          <table
            width="100%"
            style="margin:0;padding:0 !important;border:0px !important;"
          >
            <tr>
              <td
                :rowspan="
                  10 +
                    (item.sectors.protection.impact_on_vulnerable_persons
                      ? item.sectors.protection.impact_on_vulnerable_persons
                          .length + 1
                      : 0) +
                    (item.sectors.protection.protection_supplies
                      ? item.sectors.protection.protection_supplies.length + 1
                      : 0)
                "
                width="9%"
                class="clearfix"
              >
               <center>
                <img
                  src="../../../../static/cluster_protection_100px_bluebox.png"
                  height="150"
                />
               </center>
              </td>
              </tr>
              <tr v-if="item.sectors.protection.impact_on_vulnerable_persons">
                <td
                
                :rowspan="
                  1 +
                    item.sectors.protection
                .impact_on_vulnerable_persons.length
                "
                colspan="3"
                width="30%"
              >
                <strong>Impact on vulnerable population</strong> 
              </td>
              <td colspan="4" width="10%">
                <b>Population group</b>
              </td>
              <td colspan="3" width="10%">
                <b>
                  <center>Male</center>
                </b>
              </td>
              <td colspan="3" width="10%">
                <b>
                  <center>Female</center>
                </b>
              </td>
              <td colspan="3" width="10%">
                <b>
                  <center>Total</center>
                </b>
              </td>
                        
            </tr>
            <tr
              v-if="item.sectors.protection.impact_on_vulnerable_persons"
              v-for="row in item.sectors.protection
                .impact_on_vulnerable_persons"
            >
              <td colspan="4" width="30%">
                {{ row.name == "Other" ? row.other_population_type : row.name }}
              </td>
              <td colspan="3" width="10%" class="right-align">
                {{ numberWithCommas(row.impacted_males) || 0}}
              </td>
              <td colspan="3" width="10%" class="right-align">
                {{ numberWithCommas(row.impacted_females) || 0}}
              </td>
              <td colspan="3" width="10%" class="right-align">
                {{
                  numberWithCommas(
                    (row.impacted_females
                      ? parseInt(row.impacted_females)
                      : 0) +
                      (row.impacted_males ? parseInt(row.impacted_males) : 0)
                  )
                }}
              </td>
            </tr>
            <tr v-if="item.sectors.protection.protection_concerns">
              <td style="width:20%">
                <strong>Major Protection Concerns</strong>
              </td>
              <td colspan="13" style="width:70%">
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.protection.protection_concerns == undefined
                      ? "Not reported"
                      : item.sectors.protection.protection_concerns.join(", ")
                  }}
                </li>
              </td>
            </tr>
            <tr v-if="item.sectors.protection.protection_services">
              <td style="width:20%">
                <strong>Protection Service Available</strong>
              </td>
              <td colspan="13" style="width:70%">
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.protection.protection_services == undefined
                      ? "Not reported"
                      : item.sectors.protection.protection_services.join(", ")
                  }}
                </li>
              </td>
            </tr>
            <tr v-if="item.sectors.protection.protection_trainings">
              <td style="width:20%">
                <strong>Training of Protection Service Providers</strong>
              </td>
              <td colspan="13" style="width:70%">
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.protection.protection_trainings == undefined
                      ? "Not reported"
                      : item.sectors.protection.protection_trainings.join(", ")
                  }}
                </li>
              </td>
            </tr>
            <tr v-if="item.sectors.protection.protection_supplies">
              <td
                :rowspan="
                  1 + item.sectors.protection.protection_supplies.length
                "
                width="20%"
              >
                <strong>Protection Supplies</strong>
              </td>
              <td colspan="4" width="30%">
                <b>Name</b>
              </td>
              <td colspan="4" width="20%">
                <b>
                  <center>Quantity</center>
                </b>
              </td>
              <td colspan="5" width="20%">
                <b>
                  <center>Unit of Measure</center>
                </b>
              </td>
            </tr>
            <tr
              v-if="item.sectors.protection.protection_supplies"
              v-for="row in item.sectors.protection.protection_supplies"
            >
              <td colspan="4" width="30%">{{ row.name }}</td>
              <td colspan="4" width="20%" class="right-align">
                {{ numberWithCommas(row.quantity) }}
              </td>
              <td colspan="5" width="20%" class="right-align">
                {{ row.unit_of_measure }}
              </td>
            </tr>
            <tr v-if="item.sectors.protection.protection_awareness">
              <td style="width:20%">
                <strong>Protection Awareness</strong>
              </td>
              <td colspan="13" style="width:70%">
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.protection.protection_awareness == undefined
                      ? "Not reported"
                      : item.sectors.protection.protection_awareness.join(", ")
                  }}
                </li>
              </td>
            </tr>
            <tr v-if="item.sectors.protection.protection_assessments">
              <td style="width:20%">
                <strong>Protection Assessments</strong>
              </td>
              <td colspan="13" style="width:70%">
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.protection.protection_assessments == undefined
                      ? "Not reported"
                      : item.sectors.protection.protection_assessments.join(
                          ", "
                        )
                  }}
                </li>
              </td>
            </tr>
            <tr v-if="item.sectors.protection.protection_mainstreaming">
              <td style="width:20%">
                <strong>Gender Main Stream</strong>
              </td>
              <td colspan="13" style="width:70%">
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.protection.protection_mainstreaming ==
                    undefined
                      ? "Not reported"
                      : item.sectors.protection.protection_mainstreaming.join(
                          ", "
                        )
                  }}
                </li>
              </td>
            </tr>
            <tr v-if="item.sectors.protection.protection_needs">
              <td style="width:20%">
                <strong>Protection Needs</strong>
              </td>
              <td colspan="13" style="width:70%">
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.protection.protection_needs == undefined
                      ? "Not reported"
                      : item.sectors.protection.protection_needs.join(", ")
                  }}
                </li>
              </td>
            </tr>
            <tr v-if="item.sectors.protection.impact_on_vulnerable_persons">
              <td style="width:20%">
                <strong>Response Needed</strong>
              </td>
              <td colspan="13" style="width:70%">
                <u>
                  <strong>URGENT</strong>
                </u>
                <br />
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.protection
                      ? item.sectors.protection.urgent_response_needed
                        ? item.sectors.protection.urgent_response_needed
                        : "Not reported"
                      : "Not reported"
                  }}
                </li>
                <!-- <hr style="margin:1%;" />
                <u>
                  <strong>GENERAL</strong>
                </u>
                <br />
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.protection
                      ? item.sectors.protection.response_needed
                        ? item.sectors.protection.response_needed
                        : "N/A"
                      : "N/A"
                  }}
                </li> -->
              </td>
            </tr>
          </table>
        </td>
      </tr>
      <!--=======================PROTECTION END HERE======================-->

      <!--=======================WASH START HERE======================-->
      <tr
        v-if="hasWashProperties(item)"
        style="margin:0;padding:0 !important;border:0px !important;"
      >
        <td colspan="15" style="margin:0;padding:0 !important;border:0px">
          <table
            width="100%"
            style="margin:0;padding:0 !important;border:0px !important;"
          >
            <tr v-if="item.sectors.wash">
              <td
                :rowspan="
                  6 +
                    (item.sectors.wash.risk_of_water_contamination
                      ? item.sectors.wash.risk_of_water_contamination.length + 1
                      : 1) +
                    (item.sectors.wash.hh_affected_by_water_contamination
                      ? item.sectors.wash.hh_affected_by_water_contamination
                          .length + 1
                      : 1) +
                    (item.sectors.wash.accessToToilets
                      ? item.sectors.wash.accessToToilets.length + 1
                      : 1) +
                    (item.sectors.wash.hh_affected_by_water_contamination
                      ? item.sectors.wash.hh_affected_by_water_contamination
                          .length + 1
                      : 1) +
                    (item.sectors.wash.withoutSafeDrinkingWater
                      ? item.sectors.wash.withoutSafeDrinkingWater.length + 1
                      : 1) +
                    (item.sectors.wash.impact_on_water_sources
                      ? item.sectors.wash.impact_on_water_sources.length + 1
                      : 1) +
                    1
                "
                width="9%"
                class="clearfix"
              >
                <center>
                  <img
                  src="../../../../static/cluster_WASH_100px_bluebox.png"
                  height="150"
                />
                </center>
              </td>
              <td
                :rowspan="
                  item.sectors.wash.risk_of_water_contamination
                    ? item.sectors.wash.risk_of_water_contamination.length + 1
                    : 1
                "
                colspan="5"
                style="width:30%"
              >
                <b>Risk of contamination</b>
              </td>
              <td colspan="5">
                <b>Source</b>
              </td>
              <td colspan="5">
                <b>Risk</b>
              </td>
            </tr>
            <tr
              v-if="item.sectors.wash.risk_of_water_contamination"
              v-for="row in item.sectors.wash.risk_of_water_contamination"
            >
              <td colspan="5">
                
                {{
                  row.name == "Other"
                    ? "Other(" + row.other_source_name + ")"
                    : row.name
                }}
              </td>
              <td colspan="5">{{ row.risk }}</td>
            </tr>

            <tr v-if="item.sectors.wash.impact_on_water_sources">
              <td
                :rowspan="
                  item.sectors.wash.impact_on_water_sources
                    ? item.sectors.wash.impact_on_water_sources.length + 1
                    : 1
                "
                colspan="5"
                style="width:30%"
              >
                <b>Impact on water sources</b>
              </td>
              <td colspan="5">
                <b>Source</b>
              </td>
              <td colspan="5">
                <b>Impact</b>
              </td>
            </tr>
            <tr
              v-if="item.sectors.wash.impact_on_water_sources"
              v-for="row in item.sectors.wash.impact_on_water_sources"
            >
              <td colspan="5">
               
                {{
                  row.name == "Other"
                    ? "Other(" + row.other_structure_name + ")"
                    : row.name
                }}
              </td>
              <td colspan="5">{{ row.impact }}</td>
            </tr>

            <tr v-if="item.sectors.wash.accessToToilets">
              <td
                style="width:30%"
                :rowspan="item.sectors.wash.accessToToilets.length + 1"
                colspan="5"
              >
                <b>Households without access to toilets</b>
              </td>
              <td colspan="3">
                <b>
                  Household group
                </b>
              </td>
              <td colspan="3">
                <b>
                  <center>MHH</center>
                </b>
              </td>
              <td colspan="3">
                <b>
                  <center>FHH</center>
                </b>
              </td>
              <td colspan="3">
                <b>
                  <center>Total</center>
                </b>
              </td>
            </tr>
            <tr
              v-if="item.sectors.wash && item.sectors.wash.accessToToilets"
              v-for="row in item.sectors.wash.accessToToilets"
            >
              <td colspan="3">
               
                {{
                  row.name == "Other"
                    ? "Other(" + row.other_category_name + ")"
                    : row.name
                }}
              </td>
              <td colspan="3" class="right-align">
                {{ numberWithCommas(row.access_to_toilets_mhh) || 0}}
              </td>
              <td colspan="3" class="right-align">
                {{ numberWithCommas(row.access_to_toilets_fhh) || 0}}
              </td>
              <td colspan="3" class="right-align">
                {{
                  numberWithCommas(
                    parseInt(row.access_to_toilets_mhh) +
                      parseInt(row.access_to_toilets_fhh)
                  )
                }}
              </td>
            </tr>
            <!---start---------------------->
            <tr v-if="item.sectors.wash.withoutSafeDrinkingWater">
              <td
                style="width:20%"
                :rowspan="item.sectors.wash.withoutSafeDrinkingWater.length + 1"
                colspan="5"
              >
                <b>Households without access to safe drinking water</b>
              </td>
              <td colspan="3">
                <b>
                  Household group
                </b>
              </td>
             
              <td colspan="3">
                <b>
                  <center>MHH</center>
                </b>
              </td>
              <td colspan="3">
                <b>
                  <center>FHH</center>
                </b>
              </td>
              
              <td colspan="3">
                <b>
                  <center>Total</center>
                </b>
              </td>
            </tr>

            <tr
              v-if="item.sectors.wash.withoutSafeDrinkingWater"
              v-for="(row, index) in item.sectors.wash.withoutSafeDrinkingWater"
              :key="index"
            >
              <td colspan="3">
                {{
                  row.name == "Other"
                    ? "Other(" + row.other_category_name + ")"
                    : row.name
                }}
              </td>
              
              <td colspan="3" class="right-align">
                {{ numberWithCommas(row.with_safe_water_mhh) || 0}}
              </td>
              <td colspan="3" class="right-align">
                {{ numberWithCommas(row.with_safe_water_fhh) || 0}}
              </td>
              <td colspan="3" class="right-align">
                {{
                  numberWithCommas(
                    parseInt(row.with_safe_water_mhh || 0) +
                      parseInt(row.with_safe_water_fhh || 0)
                  )
                }}
              </td>
            </tr>

            <!----------------end----------->

            <tr v-if="item.sectors.wash.hh_affected_by_water_contamination">
              <td
                style="width:20%"
                :rowspan="
                  item.sectors.wash.hh_affected_by_water_contamination.length +
                    1
                "
                colspan="5"
              >
                <b>Households affected by water contamination</b>
              </td>
              <td colspan="3">
                <b>
                  Household group
                </b>
              </td>
              <td colspan="3">
                <b>
                  <center>MHH</center>
                </b>
              </td>
              <td colspan="3">
                <b>
                  <center>FHH</center>
                </b>
              </td>
              <td colspan="3">
                <b>
                  <center>Total</center>
                </b>
              </td>
            </tr>

            <tr
              v-if="item.sectors.wash.hh_affected_by_water_contamination"
              v-for="row in item.sectors.wash
                .hh_affected_by_water_contamination"
            >
              <td colspan="3">
                {{
                  row.name == "Other"
                    ? "Other(" + row.other_category_name + ")"
                    : row.name
                }}
              </td>
              <td colspan="3" class="right-align">
                {{ numberWithCommas(row.risk_contamination_mhh) || 0}}
              </td>
              <td colspan="3" class="right-align">
                {{ numberWithCommas(row.risk_contamination_fhh) || 0}}
              </td>
              <td colspan="3" class="right-align">
                {{
                  numberWithCommas(
                    parseInt(row.risk_contamination_mhh) +
                      parseInt(row.risk_contamination_fhh)
                  )
                }}
              </td>
            </tr>

            <tr v-if="item.sectors.wash.emergent_needs">
              <td style="width:20%">
                <strong>Wash Needs</strong>
              </td>
              <td colspan="14" style="width:70%">
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.wash.emergent_needs == undefined
                      ? "Not reported"
                      : item.sectors.wash.emergent_needs.join(", ")
                  }}
                </li>
              </td>
            </tr>

            <tr>
              <td colspan="6" style="width:20%">
                <strong>Response Needed</strong>
              </td>
              <td colspan="9">
                <!-- <u>
                  <strong>URGENT</strong>
                </u>
                <br />
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.wash
                      ? item.sectors.wash.urgent_response_needed
                        ? item.sectors.wash.urgent_response_needed
                        : "N/A"
                      : "N/A"
                  }}
                </li>
                <hr style="margin:1%;" /> -->
                <u>
                  <strong>GENERAL</strong>
                </u>
                <br />
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.wash
                      ? item.sectors.wash.response_needed
                        ? item.sectors.wash.response_needed
                        : "Not reported"
                      : "Not reported"
                  }}
                </li>
              </td>
            </tr>
          </table>
        </td>
      </tr>
      <!--=======================WASH END HERE======================-->

      <!--=======================LOGISTICS START HERE======================-->
      <tr
      v-if="hasLogisticsProperties(item)"
        style="margin:0;padding:0 !important;border:0px !important;"
      >
        <td colspan="15" style="margin:0;padding:0 !important;border:0px">
          <table
            width="100%"
            style="margin:0;padding:0 !important;border:0px !important;"
          >
            <tr>
              <td
                :rowspan="
                4 +
                (item.sectors.logistics.impacted_structures
                  ? item.sectors.logistics.impacted_structures.length
                  : 0) +
                (item.sectors.logistics.impacted_telecoms
                  ? item.sectors.logistics.impacted_telecoms.length
                  : 0)
            "
                width="9%"
                class="clearfix"
              >
                <center>
                  <img
                  src="../../../../static/cluster_logistics_100px_bluebox.png"
                  height="150"
                />
                </center>
              </td>
              <td
                v-if="item.sectors.logistics.impacted_structures"
                :rowspan="1 + item.sectors.logistics.impacted_structures.length"
               
                style="20%"
              >
                <b>Impact on Stractures</b>
              </td>
              <td
              v-if="item.sectors.logistics.impacted_structures"
                width="10%"
              >
                <b>Category</b>
              </td>
              <td
              v-if="item.sectors.logistics.impacted_structures"
                width="10%"
              >
                <b>Lat</b>
              </td>
              <td
              v-if="item.sectors.logistics.impacted_structures"
                width="10%"
              >
                <b>Long</b>
              </td>
              <td
              v-if="item.sectors.logistics.impacted_structures"
                width="10%"
              >
              <b>Road Length (m)</b>
              </td>
              <td
              v-if="item.sectors.logistics.impacted_structures"
                width="10%"
              >
              <b>Accessibility</b>
              </td>
              <td
              v-if="item.sectors.logistics.impacted_structures"
                width="10%"
              >
              <b>Road Condition</b>
              </td>

              <td
              v-if="item.sectors.logistics.impacted_structures"
                width="10%"
              >
              <b>Surface Condition</b>
              </td>
            </tr>

            <tr
            v-if="item.sectors.logistics.impacted_structures"
            v-for="row in item.sectors.logistics.impacted_structures"
            >
              <td width="10%">
                {{
                  row.name == "Other"
                    ? "Other(" + row.other_structure + ")"
                    : row.name
                }}
              </td>
              <td width="10%">{{ row.gps_lat || "Not recorded"}}</td>
              <td width="10%">{{ row.gps_lng || "Not recorded"}}</td>
              <td width="10%">
                {{ row.road_length || "Not recorded"}}
              </td>
              <td width="10%">
                {{ row.road_access }}
              </td>
              <td width="10%">
                {{ row.road_condition }}
              </td>
              <td width="10%">
                {{ row.surface_condition }}
              </td>
            </tr>

            <tr
              
            >
              <td
              v-if="item.sectors.logistics.impacted_telecoms"
              :rowspan="1 + item.sectors.logistics.impacted_telecoms.length"
              
                width="20%"
              >
                <b>Impact on Telecommunications</b>
              </td>
              <td
              v-if="item.sectors.logistics.impacted_telecoms"
                colspan="3"
                width="10%"
              >
                <b>Category</b>
              </td>
              <td
              v-if="item.sectors.logistics.impacted_telecoms"
                colspan="2"
                width="10%"
              >
                <b>Status</b>
              </td>
              <td v-if="item.sectors.logistics.impacted_telecoms" width="10%">
                <b>Lat</b>
              </td>
              <td v-if="item.sectors.logistics.impacted_telecoms" width="10%">
                <b>Long</b>
              </td>
            </tr>
            <tr
            v-if="item.sectors.logistics.impacted_telecoms"
            v-for="row in item.sectors.logistics.impacted_telecoms"
            >
              <td colspan="3" width="10%">
                {{
                  row.name == "Other"
                    ? "Other(" + row.other_telecommunications_type + ")"
                    : row.name
                }}
              </td>
              <td colspan="2" width="10%">{{ row.status }}</td>
              <td width="10%">
                {{ row.telecoms_gps_lat || "Not recorded"}}
              </td>
              <td width="10%">
                {{ row.telecoms_gps_long || "Not recorded"}}
              </td>
            </tr>

            <tr>
              <td style="width:20%">
                <strong>Emergency Needs</strong>
              </td>
              <td colspan="9">
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.logistics.emergent_needs == undefined
                      ? "Not reported"
                      : item.sectors.logistics.emergent_needs.join(", ")
                  }}
                </li>
              </td>
            </tr>
            <tr>
              <td style="width:20%">
                <strong>Response Needed</strong>
              </td>
              <td colspan="9">
                <!-- <u>
                  <strong>URGENT</strong>
                </u>
                <br />
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.logistics
                      ? item.sectors.logistics.urgent_response_needed
                        ? item.sectors.logistics.urgent_response_needed
                        : "N/A"
                      : "N/A"
                  }}
                </li>
                <hr style="margin:1%;" /> -->
                <u>
                  <strong>GENERAL</strong>
                </u>
                <br />
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.logistics
                      ? item.sectors.logistics.response_needed
                        ? item.sectors.logistics.response_needed
                        : "Not reported"
                      : "Not reported"
                  }}
                </li>
              </td>
            </tr>
          </table>
        </td>
      </tr>
      <!--=======================LOGISTICS END HERE======================-->

      <!--=======================ENVIRONMENT START HERE======================-->
      <tr
        v-if="hasEnvironmentProperties(item)"
        style="margin:0;padding:0 !important;border:0px !important;"
      >
        <td colspan="15" style="margin:0;padding:0 !important;border:0px">
          <table
            width="100%"
            style="margin:0;padding:0 !important;border:0px !important;"
          >
            <tr>
              <td
              style="width:9%"
                :rowspan="
                  3 +
                    (item.sectors.environment.impact_on_environment
                      ? item.sectors.environment.impact_on_environment.length
                      : 0)
                "
                
              >
                <center>
                  <img 
                      src="../../../../static/other_cluster_environment_100px_bluebox.png"
                      height="150"
                  />
                </center>
              </td>
              <td
                v-if="item.sectors.environment.impact_on_environment"
                :rowspan="
                  1 + item.sectors.environment.impact_on_environment.length
                "
                
                style="width:20%"
              >
                <strong>Impact on structures</strong>
              </td>
              <td
                v-if="item.sectors.environment.impact_on_environment"
                colspan="7"
              >
                <b>Question</b>
              </td>
              <td
                v-if="item.sectors.environment.impact_on_environment"
                colspan="3"
                width="10%"
              >
                <b>Response</b>
              </td>
            </tr>
            <tr
              v-if="item.sectors.environment.impact_on_environment"
              v-for="row in item.sectors.environment.impact_on_environment"
            >
              <td colspan="7" style="width:60%">
                {{ row.impact_question }}
              </td>
              <td colspan="3" width="10%">{{ row.response }}</td>
            </tr>




            <tr>
              <td style="width:20%">
                <strong>Emergency Needs</strong>
              </td>
              <td colspan="13">
                <li style="margin-left:2%; width:60%" class="qcont">
                  {{
                    item.sectors.environment.emergent_needs == undefined
                      ? "Not reported"
                      : item.sectors.environment.emergent_needs.join(", ")
                  }}
                </li>
              </td>
            </tr>




            <tr>
              <td style="width:20%">
                <strong>Response Needed</strong>
              </td>
              <td colspan="13">
                <b style="padding-bottom:20px">
                  <u>
                    <strong>URGENT</strong>
                  </u>
                </b>
                <br />
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.environment
                      ? item.sectors.environment.urgent_response_needed
                        ? item.sectors.environment.urgent_response_needed
                        : "Not reported"
                      : "Not reported"
                  }}
                </li>
                <hr style="margin:1%;" />
                <b style="padding-bottom:20px">
                  <u>
                    <strong>GENERAL</strong>
                  </u>
                </b>
                <br />
                <li style="margin-left:2%" class="qcont">
                  {{
                    item.sectors.environment
                      ? item.sectors.environment.response_needed
                        ? item.sectors.environment.response_needed
                        : "N/A"
                      : "N/A"
                  }}
                </li>
              </td>
            </tr>
          </table>
        </td>
      </tr>
      <!--=======================ENVIRONMENT END HERE======================-->
    </table>
  </div>
</template>
<script>
import { MongoReports } from "../api/MongoReports";
import { dinrforms } from "../api/forms/dinrforms.js";
//import { dinrforms } from "../api/dinrforms";
import { auth } from "../../../api/auth";
import swal from "sweetalert2";
import { signatures } from "../api/accounts/signatures";
import downloadexcel from "vue-json-excel";
import moment from "moment";
import axios from "axios";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import domtoimage from "dom-to-image";
var QRCode = require("qrcode");

export default {
  components: {
    downloadexcel,
    QRCode
  },

  data() {
    return {
      signature: {},
      numberOfTAs: "",
      dinrFormsData: {},
      villagesArray: [],
      campsArray: [],
      bufferDraFormsData: [],
      TAarray: [],
      draFormsData: [],
      download: [],
      downloadData: [],
      structureDisplacedAccommodatedArray: [],
      otherData: [],
      vhhousesvalue: "",
      filters: {
        Disaster: "all.dinrform.disaster",
        Country: "all.dinrform.district.admin0_name_en",
        "Country PCODE": "all.dinrform.district.admin0_pcode",
        District: "all.dinrform.district.admin2_name_en",
        Region: "all.dinrform.district.admin1_name_en",
        "TA PCODE": "all.admin3.admin3Pcode",
        TA: "all.admin3.admin3Name_en",
        "GVHS affected": "all.gvhsAffected",

        // "GVH affected": "gvhs.name",

        "Date of assessment ADRMC": "all.dinrform.doaAcpc",
        "Date of assessment DDRMC": "all.dinrform.doaDcpc",
        "Date of Disaster from": "all.dinrform.dodFrom",
        "Date of Disaster to": "all.dinrform.dodTo",

        //agriculture
        "food items damaged (KGs) (agriculture)": "all.food_item_damage",
        "number of crop hectares submerged  (agriculture)":
          "all.hectares_submerged",
        "number crop hectares washed off  (agriculture)":
          "all.hectares_washed_away",
        "number of households whose crops are impacted (agriculture)":
          "all.impact_on_crops_hh_affected",
        "number of crop hectares damaged in affected households  (agriculture)":
          "all.impact_on_crops_hectares_damaged",

        "hh affected per impacted livestock  (agriculture)":
          "all.impact_on_livestock_hh",
        "number of impacted livestock  (agriculture)":
          "all.impact_on_livestock_la",

        //displaced

        "number male HH accomadeted (displaced)":
          "all.displaced_households_accommodated_male",
        "number female HH accomadeted (displaced)":
          "all.displaced_households_accommodated_female",

        "number of males disaggregated (displaced)":
          "all.displaced_disagregated_male",
        "number of females disaggregated (displaced)":
          "all.displaced_disagregated_female",

        "number of males accomodated (displaced)":
          "all.displaced_individuals_accommodated_male",
        "number of females accomodated (displaced)":
          "all.displaced_individuals_accommodated_female",

        //education

        "number of school buildings functioning (education)":
          "all.education_building_functioning",
        "number of school buildings underwater (education)":
          "all.education_underwater",
        "number of school buildings completely damaged (education)":
          "all.education_completely_damaged",

        "number of school buildings partially functioning (education)":
          "all.education_building_partly_functioning",

        "number of school buildings closed  (education)":
          "all.education_closed_buildings",

        "males of out school (education)": "all.education_males_out_of_school",
        "females of out school (education)":
          "all.education_females_out_of_school",

        //food

        "is food available? (food)": "all.is_food_available_food",

        //health

        "number of facilities partially functioning (health)":
          "all.health_partially_functioning",
        "number of  facilities on verge of closing (health)":
          "all.health_verge_of_closing",
        "number  facilities closed (health)": "all.health_closed",

        "state of medical supply availability (health)":
          "all.medical_supply_availability",

        "state of health personnel availability (health)":
          "all.health_personel_availability",

        //livelihoods

        "number severely affected (livelihoods)":
          "all.livelihoods_severely_affected",

        "number slightly affected (livelihoods)":
          "all.livelihoods_slightly_affected",

        //protection

        "impacted females (protection)":
          "all.impact_on_vulnerable_persons_females",

        "impacted males (protection)": "all.impact_on_vulnerable_persons_males",

        //logistics

        "state of access to main roads (logistics)": "all.road_access",

        //nutrition

        "number of affected males (nutrition)":
          "all.nutrition_affected_pop_male",
        "number of affected females (nutrition)":
          "all.nutrition_affected_pop_female",

        //wash

        "FHH with safe water (wash)": "all.sectors.wash.with_safe_water_fhh",
        "MHH with safe water (wash)": "all.sectors.wash.with_safe_water_mhh",
        "FHH with toilet access (wash)":
          "all.sectors.wash.access_to_toilets_fhh",
        "MHH with toilet access (wash)":
          "all.sectors.wash.access_to_toilets_mhh",
        "FHH risking contamination (wash)":
          "all.sectors.wash.risk_contamination_fhh",
        "MHH risking contamination  (wash)":
          "all.sectors.wash.risk_contamination_mhh",

        //shelter

        "Houses partly damaged (shelter)": "all.sectors.shelter.partly_damaged",
        "Houses underwater (shelter)": "all.sectors.shelter.under_water",
        "Houses completely (shelter)": "all.sectors.shelter.completely_damaged",

        "number of males without shelter(shelter)":
          "all.shelter_without_shelter_male",
        "number of females without shelter (shelter)":
          "all.shelter_without_shelter_female",

        "number of female HH with houses damaged (shelter)":
          "all.shelter_fhh_affected",
        "number of male HH with houses damaged (shelter)":
          "all.shelter_mhh_affected",

        "number of injured females(shelter)":
          "all.shelter_people_injured_female",
        "number of injured males in category (shelter)":
          "all.shelter_people_injured_female",

        "number of dead females (shelter)": "all.shelter_people_dead_female",
        "number of dead males (shelter)": "all.shelter_people_dead_male",

        "number of displaced male (displaced)": "all.PeopleAffectedrows_male",
        "number of displaced females (displaced)":
          "all.PeopleAffectedrows_female",

        "No. of FHH with 1-2 month Food Availability (food)":
          "all.food_1_2_months",
        "No. of FHH with less than 1 month Food Availability (food)":
          "all.food_less_1_month",

        "No. of FHH with more than 2 months Food Availability (food)":
          "all.food_2_months",

        "No. of FHH who lost Food Stock (food)": "all.food_stock_lost",

        "No. of MHH with 1-2 month Food Availability (food)":
          "all.food_1_2_months_male",
        "No. of MHH with less than 1 month Food Availability (food)":
          "all.food_less_1_month_male",

        "No. of MHH with more than 2 months Food Availability (food)":
          "all.food_2_months_male",

        "No. of MHH who lost Food Stock (food)": "all.food_stock_lost_male",

        "Urgent response needed (agriculture)":
          "all.sectors.agriculture.response_needed",
        "Urgent response needed (displaced)":
          "all.sectors.displaced.urgent_response_needed",
        "Urgent needed (shelter)": "all.sectors.shelter.urgent_response_needed",
        "Urgent response needed (wash)":
          "all.sectors.wash.urgent_response_needed",
        "Urgent response needed (livelihoods)":
          "all.sectors.livelihoods.response_needed",
        "Urgent response needed (health)":
          "all.sectors.health.urgent_response_needed",
        "Urgent response needed (environment)":
          "all.sectors.environment.urgent_response_needed",
        "Urgent response needed (food)":
          "all.sectors.food.urgent_response_needed",
        "Urgent response needed (logistics)":
          "all.sectors.logistics.urgent_response_needed",
        "Urgent response needed (nutrition)":
          "all.sectors.nutrition.urgent_response_needed",
        "Urgent response needed (protection)":
          "all.sectors.protection.urgent_response_needed",

        "general response needed (agriculture)":
          "all.sectors.agriculture.response_needed",
        "general response needed (displaced)":
          "all.sectors.displaced.response_needed",
        "response needed (shelter)": "all.sectors.shelter.response_needed",
        "general response needed (wash)": "all.sectors.wash.response_needed",
        "general response needed (livelihoods)":
          "all.sectors.livelihoods.response_needed",
        "general response needed (health)":
          "all.sectors.health.response_needed",
        "general response needed (environment)":
          "all.sectors.environment.response_needed",
        "general response needed (food)": "all.sectors.food.response_needed",
        "general response needed (logistics)":
          "all.sectors.logistics.response_needed",
        "general response needed (nutrition)":
          "all.sectors.nutrition.response_needed",
        "general response needed (protection)":
          "all.sectors.protection.response_needed"
      }
    };
  },

  computeLogo() {
    return "../../../../static/logo.png";
  },
  computed: {
    signature_path: function() {
      if (this.signature && this.signature.signature)
        return (
          process.env.VUE_APP_ENGINE_URL +
          "/" +
          this.signature.signature.replace("\\", "/")
        );
    },
    hasPictures: function() {
      if ("disasterImages" in this.dinrFormsData) {
        return this.dinrFormsData.disasterImages.length > 0;
      }
      return false;
    }
  },
  updated() {
    delete this.signatureText.signature;
    delete this.signatureText.status;
    /*    console.log(this.signatureText)
    QRCode.toDataURL(JSON.stringify(this.signatureText),{width:"90",type: 'image/jpeg',
  quality: 1}, function(err, url) {
      if (error) console.error(error);
      console.log("success!");

      var img = document.getElementById('qr-code')
      img.src = url
    }); */

    var opts = {
      errorCorrectionLevel: "H",
      type: "image/jpeg",
      quality: 1,
      margin: 1
    };

    QRCode.toDataURL(
      JSON.stringify(this.signatureText || "not electronically signed by DC"),
      opts,
      function(err, url) {
        if (err) throw err;

        var img = document.getElementById("qr-code");
        img.src = url;
      }
    );
  },
  mounted() {
    //Get DINR Form

    MongoReports.getOneUnApprovedDinr(
      this.$route.params.uuid /* , this.$session.get("jwt") */
    ).then(response => {
      this.dinrFormsData = response.data;
      signatures.get(this.$session.get("jwt")).then(response => {
        //console.log(this.$session.get("user"))
        this.signature = this.dinrFormsData.approvalMetadata.signature[1];
      });

      this.signatureText = this.dinrFormsData.approvalMetadata
        ? Object.assign({}, this.dinrFormsData.approvalMetadata.signature)
        : "";
      this.signature = this.dinrFormsData.approvalMetadata
        ? Object.assign({}, this.dinrFormsData.approvalMetadata.signature)
        : "";

      //console.log(this.$route);

      //initialise number TAs in DNIR
      this.numberOfTAs = 0;

      let count = 0;
      //Get DRA Form

      let dradata = this.dinrFormsData.dra;
      this.bufferDraFormsData = dradata;
      this.draFormsData = [];
      //console.log(bufferDraFormsData);
      this.downloadData.dinrform = {};

      this.bufferDraFormsData.forEach(item => {
        //get camp details
        if (item.sectors.displaced !== undefined) {

          try {
          item.sectors.displaced.PeopleAffectedrows.sort((a, b)=>a.name.localeCompare(b.name))
        } catch (error) {
          
        }


        for (let i in item.sectors.displaced
          .displaced_households_accommodated) {
          this.structureDisplacedAccommodatedArray.push(
            item.sectors.displaced.displaced_households_accommodated[i]
          );
          for (let j in item.camps) {
            if (
              item.sectors.displaced.displaced_households_accommodated[i]
                .structure_name === item.camps[j].name &&
              item.sectors.displaced.displaced_households_accommodated[i]
                .name === "Camp"
            ) {
              var record = {
                accomodated_females_hh:
                  item.sectors.displaced.displaced_households_accommodated[i]
                    .accomodated_females_hh,
                accomodated_males_hh:
                  item.sectors.displaced.displaced_households_accommodated[i]
                    .accomodated_males_hh,
                structure_name:
                  item.sectors.displaced.displaced_households_accommodated[i]
                    .structure_name,
                name:
                  item.sectors.displaced.displaced_households_accommodated[i]
                    .name,
                square_meters: item.camps[j].square_meters,
                lat: item.camps[j].gps_lat,
                long: item.camps[j].gps_lng
              };
              this.structureDisplacedAccommodatedArray.pop();
              this.structureDisplacedAccommodatedArray.push(record);
            }
          }
        }
      }

      if (item.sectors.shelter !== undefined) {
        //sort peopleaffected from shelter by name 
        try {
          item.sectors.shelter.PeopleAffectedrows.sort((a, b)=>a.name.localeCompare(b.name))
        } catch (error) {
          
        }
        
      }
      
        this.processExcel(item);

        this.TAarray.push(item.admin3.admin3Name_en.trim());
        for (let i in item.villages) {
          this.villagesArray.push(item.villages[i].name);
        }
        for (let i in item.camps) {
          this.campsArray.push(item.camps[i].name);
        }
      });

      this.TAarray = this.TAarray.sort();

      this.villagesArray = this.villagesArray
        .join()
        .replace(/ /g, "")
        .split(",")
        .sort()
        .filter(function(item, pos, self) {
          return self.indexOf(item) == pos;
        });
      this.campsArray = this.campsArray
        .join()
        .replace(/ /g, "")
        .split(",")
        .sort()
        .filter(function(item, pos, self) {
          return self.indexOf(item) == pos;
        });
    });
  },

  methods: {
    hasAgricultureProperties(item) {
      const agricultureProperties = [
        'crops_damaged',
        'emergent_needs',
        'facilities_damaged',
        'impact_on_crops',
        'impact_on_livestock',
        'livelihoods_affected',
        'partially_damaged',
        'response_needed_crops',
        'response_needed_livestock',
      ];
      return (
        item &&
        item.sectors.agriculture &&
        agricultureProperties.some(prop => item.sectors.agriculture[prop] && item.sectors.agriculture[prop].length > 0)
      );
    },


    hasEducationProperties(item) {
      const educationProperties = [
        'emergent_needs',
        'impact_on_schools',
      ];
      return (
        item &&
        item.sectors.education &&
        educationProperties.some(prop => item.sectors.education[prop] && item.sectors.education[prop].length > 0)
      );
    },


    hasEnvironmentProperties(item) {
      const environmentProperties = [
        'impact_on_environment',
        // Add any new properties here
      ];

      return (
        item &&
        item.sectors.environment &&
        environmentProperties.some(prop => item.sectors.environment[prop] && item.sectors.environment[prop].length > 0)
      );
    },


    hasFoodProperties(item) {
      const foodProperties = [
        'emergent_needs',
        'food_access',
        'food_item_damage',
        'food_stocks_avaliability',
        'impact_on_price',
      ];

      return (
       item &&
       item.sectors.food &&
       foodProperties.some(prop => item.sectors.food[prop] && item.sectors.food[prop].length > 0)
       );
    },


    hasHealthProperties(item) {
      const healthProperties = [
        'available_health_facilities',
        'emergent_needs',
        'available_health_medical',
        'compromised_services',
        'emergent_children_needs',
        'risk_out_disease_outbreak',
      ];

      return (
       item &&
       item.sectors.health &&
       healthProperties.some(prop => item.sectors.health[prop] && item.sectors.health[prop].length > 0)
       );
    },

    hasLogisticsProperties(item) {
      const logisticsProperties = [
        // 'access_of_structures',
        'emergent_needs',
        'impacted_structures',
        'impacted_telecoms',
        // 'other_access_to_structures',
        // 'other_impacted_structures',
      ];
      return (
       item &&
       item.sectors.logistics &&
       logisticsProperties.some(prop => item.sectors.logistics[prop] && item.sectors.logistics[prop].length > 0)
       );
    },

    hasNutritionProperties(item) {
      const nutritionProperties = [
        'affected_population',
        // 'emergent_needs',
        // 'impact_on_nutrition_programmes',
        'lostLivelihood',
      ];
      return (
       item &&
       item.sectors.nutrition &&
       nutritionProperties.some(prop => item.sectors.nutrition[prop] && item.sectors.nutrition[prop].length > 0)
       );
    },

    hasProtectionProperties(item) {
      const protectionProperties = [
        'impact_on_vulnerable_persons',
        'protection_assessments',
        'protection_awareness',
        'protection_concerns',
        'protection_mainstreaming',
        'protection_needs',
        'protection_services',
        'protection_supplies',
        'protection_trainings',
      ];
      return (
       item &&
       item.sectors.protection &&
       protectionProperties.some(prop => item.sectors.protection[prop] && item.sectors.protection[prop].length > 0)
       );
    },

    hasShelterProperties(item) {
      const shelterProperties = [
        //'ImpactOnHouses',
        'PeopleAffectedrows',
        'PeopleDeadrows',
        'PeopleInjuredrows',
        'PeopleMissingrows',
        'camp_info',
        'other_structures_damaged',
        'people_without_shelter',
        'response_needed_emergency',
        'response_needed_non_food'
      ];

      return (

       item &&
       item.sectors.shelter &&
       shelterProperties.some(prop => item.sectors.shelter[prop] && item.sectors.shelter[prop].length > 0)
       );
    },

    hasWashProperties(item) {
      const washProperties = [
      'risk_of_water_contamination',
      'hh_affected_by_water_contamination',
         'accessToToilets',
        'withoutSafeDrinkingWater',
        'impact_on_water_sources',
        'emergent_needs',
        'number_of_facilities_affected',
        'other_water_sources_impacted',
        //'people_without_shelter',
        'risk_of_water_contamination_other_sources'

      ];
      return (
       item &&
       item.sectors.wash &&
       washProperties.some(prop => item.sectors.wash[prop] && item.sectors.wash[prop].length > 0)
       );
    },

    hasDisplacedProperties(item) {
      const displacedProperties = [
        'PeopleAffectedrows',
        // 'displaced_disaggregated',
        // 'displaced_households_accommodated',
        // 'displaced_individuals_accommodated',
        // 'hummanitarian_assistance',
        // 'response_needed_emergency',
        // 'response_needed_non_food'
      ];
      return (
       item &&
       item.sectors.displaced &&
       displacedProperties.some(prop => item.sectors.displaced[prop] && item.sectors.displaced[prop].length > 0)
       );
    },

    disasterImage(disasterId, filename) {
      const resource = process.env.VUE_APP_ENGINE_URL + "/forms/dinr";

      return `${resource}/${disasterId}/images/${filename}`;
    },

    reviewSummary() {
      this.$router.push({
        path: "/districtmanager/summaryreport/" + this.$route.params.uuid
      });
    },
    async handleRequest(data) {
      dinrforms.updateUnapproved(data).then(
        response => {
          swal.fire({
            title: "Done Succesfully",
            text: "Your decision has been saved on this disaster",
            type: "success",
            animation: false
          });

          this.$router.push({ name: "unapprovedReports" });
        },
        reason => {
          swal.fire({
            title: "Failed to submit form",
            text: "possible invalid data (" + reason + ")",
            type: "error",
            animation: false
          });
        }
      );
    },
    async handleApprove() {
      let self = this;
      var { value: password } = await swal({
        title: "Confirm Approval by entering your password",
        text: `I, ${this.$session.get("userObj").firstName} ${
          this.$session.get("userObj").lastName
        } (${this.$session.get("userObj").email}) - ${
          this.$session.get("user").admin2_name_en
        } DC, I am confirming that the filled data is correct and the best reflection of the disaster which occurred.`,
        input: "password",
        type: "warning",
        buttonsStyling: false,
        confirmButtonClass: "btn btn-success btn-fill",
        cancelButtonClass: "btn btn-danger btn-fill",
        confirmButtonText: "Yes, approve this!",
        showCancelButton: true,
        inputPlaceholder: "password",
        inputAttributes: {
          autocapitalize: "off",
          autocorrect: "off",
          required: true
        }
      }).then(result => {
        if (result.value != null) {
          auth
            .login({
              email: this.$session.get("userObj").email,
              password: result.value
            })
            .then(
              response => {
                this.handleRequest({
                  isApproved: true,
                  _id: this.$route.params.uuid,
                  approvalMetadata: { signature: this.signature }
                });
              },
              reason => {
                swal({
                  text:
                    "Failed submit for possible invalid password (  password : " +
                    reason +
                    " )",
                  type: "error",
                  toast: true,
                  position: "top-end",
                  confirmButtonClass: "btn btn-success btn-fill",
                  buttonsStyling: false
                });
              }
            );
        }
      });
    },
    async handleReject() {
      let self = this;
      var { value: password } = await swal({
        title: "Confirm Rejection",
        text: `I, ${this.$session.get("userObj").firstName} ${
          this.$session.get("userObj").lastName
        } (${this.$session.get("userObj").email}) - ${
          this.$session.get("user").admin2_name_en
        } DC, I am rejecting this disaster.`,
        html:
          '<textarea class="swal2-textarea" required id="comment" rows="4" placeholder="Why are you rejecting this?"></textarea><input id="password" required type="password" class="swal2-input" placeholder="password">',
        type: "warning",
        buttonsStyling: false,
        confirmButtonClass: "btn btn-success btn-fill",
        cancelButtonClass: "btn btn-danger btn-fill",
        confirmButtonText: "Yes, reject this!",
        showCancelButton: true,
        inputAttributes: {
          autocapitalize: "off",
          autocorrect: "off",
          required: true
        }
      }).then(result => {
        let password = document.getElementById("password").value;
        let comment = document.getElementById("comment").value;

        if (password != "" && comment != "") {
          auth
            .login({
              email: this.$session.get("userObj").email,
              password
            })
            .then(
              response => {
                this.handleRequest({
                  isRejected: true,
                  _id: this.$route.params.uuid,
                  approvalMetadata: { comment }
                });
              },
              reason => {
                swal({
                  text:
                    "Failed submit for possible invalid password (  password : " +
                    reason +
                    " )",
                  type: "error",
                  toast: true,
                  position: "top",
                  confirmButtonClass: "btn btn-success btn-fill",
                  buttonsStyling: false
                });
              }
            );
        } else {
          swal({
            text: "Fill in all fields",
            type: "error",
            toast: true,
            position: "top",
            confirmButtonClass: "btn btn-success btn-fill",
            buttonsStyling: false
          });
        }
      });
    },
    handleInfographics() {
      this.$router.push({
        path: "/districtmanager/infographics/" + this.$route.params.uuid
      });
    },
    numberWithCommas(number) {
      try {
        var parts = number.toString().split(".");
        parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        return parts.join(".");
      } catch (e) {}
    },
    comparator(key, order = "asc") {
      return function innerSort(a, b) {
        if (!a.hasOwnProperty(key) || !b.hasOwnProperty(key)) {
          return 0;
        }

        const varA = typeof a[key] === "string" ? a[key].toUpperCase() : a[key];
        const varB = typeof b[key] === "string" ? b[key].toUpperCase() : b[key];

        let comparison = 0;
        if (varA > varB) {
          comparison = 1;
        } else if (varA < varB) {
          comparison = -1;
        }
        return order === "desc" ? comparison * -1 : comparison;
      };
    },

    sortArrayByKey(arrayName) {
      return arrayName.slice().sort(this.comparator("name", "asc"));
    },
    downloadPDF() {
      var quotes = document.getElementById("section-to-print");
      html2canvas(quotes).then(canvas => {
        var imgData = canvas.toDataURL("image/png");

        var imgWidth = 210;
        var pageHeight = 295;
        var imgHeight = (canvas.height * imgWidth) / canvas.width;
        var heightLeft = imgHeight;

        var doc = new jsPDF("p", "mm", "A4");
        var position = 0;

        doc.addImage(imgData, "PNG", 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;

        while (heightLeft >= 0) {
          position = heightLeft - imgHeight;
          doc.addPage();
          doc.addImage(imgData, "PNG", 0, position, imgWidth, imgHeight);
          heightLeft -= pageHeight;
        }

        const disasterdistrict = document.getElementById("district").innerHTML;
        const disastertype = document.getElementById("disastertype").innerHTML;
        const disasterdate = document.getElementById("disasterstart").innerHTML;

        const filename = disasterdistrict + disastertype + disasterdate;

        doc.save(filename);
      });
    },
    printdiv(printpage) {
      var headstr = "<html><head><title></title></head><body>";
      var footstr = "</body>";
      var newstr = document.all.item(printpage).innerHTML;
      var oldstr = document.body.innerHTML;
      var disasterdistrict = document.getElementById("district").innerHTML;
      var disastertype = document.getElementById("disastertype").innerHTML;
      var disasterdate = document.getElementById("disasterstart").innerHTML;

      document.body.innerHTML = headstr + newstr + footstr;
      document.title =
        disasterdate + "_" + disasterdistrict + "_" + disastertype;
      window.print();
      document.body.innerHTML = oldstr;
      location.reload();
      return false;
    },
    dateFormate(dateData) {
      const cur_date = dateData;
      const formattedDate = moment(cur_date).format("DD/MM/YYYY");

      return formattedDate;
    },
    formatdate(data) {
      const cur_date = data;
      const formattedDate = moment(cur_date).format("DD/MM/YYYY hh:mm");

      return formattedDate;
    },

    formatedDate(data) {
      const cur_date = data;
      const formDate = moment(cur_date).format("YYYY/MM/DD");

      return formDate;
    },

    sumArrayValues(item, sector, array, key) {
      if (typeof item["sectors"][sector][array] !== "undefined") {
        return item["sectors"][sector][array]
          .map(function(item) {
            return parseInt(item[key]) ? parseInt(item[key]) : 0;
          })
          .reduce((sum, value) => sum + value, 0);
      } else if (typeof item[sector][array] === "undefined") {
        return 0;
      }
    },

    returnFieldvalue(item, sector, array, key) {
      if (typeof item["sectors"][sector][array] !== "undefined") {
        return item["sectors"][sector][array][0][key];
      } else if (typeof item["sectors"][sector][array] === "undefined") {
        return "NULL";
      }
    },

    returnFieldItemvalue(item, sector, array, key) {
      if (typeof item["sectors"][sector][array] !== "undefined") {
        for (let i in item["sectors"][sector][array]) {
          if (item["sectors"][sector][array][i].name === key) {
            return item["sectors"][sector][array][i].status;
          } else if (
            typeof item["sectors"][sector][array][i].key === "undefined"
          ) {
            return "NULL";
          }
        }
      }
    },

    sumArrayValuesNoAggregate(item, sector, array, key, filterBy, filterValue) {
      if (typeof item["sectors"][sector][array] !== "undefined") {
        return item["sectors"][sector][array]
          .filter(item => item[filterBy] === filterValue)
          .map(function(item) {
            return parseInt(item[key]) ? parseInt(item[key]) : 0;
          })
          .reduce((sum, value) => sum + value, 0);
      } else if (typeof item[sector][array] === "undefined") {
        return 0;
      }
    },

    processExcel(item) {
      this.downloadData.all = item;

      this.draFormsData.push(item);

      for (let i = 0; i < this.draFormsData.length; i++) {
        for (let a = 0; a < this.draFormsData[i].villages.length; a++) {
          this.villagesArray.push(this.draFormsData[i].villages[a].name);
        }

        for (let a = 0; a < this.draFormsData[i].camps.length; a++) {
          this.campsArray.push(this.draFormsData[i].camps[a].name);
        }
      }

      this.numberOfTAs++;

      let Gvharray = [];

      this.downloadData.dinrform = this.dinrFormsData;

      for (let i = 0; i < item.gvhs.length; i++) {
        let GVHname = item.gvhs[i].name;

        Gvharray.push(GVHname);
      }

      this.downloadData.all.dinrform = this.downloadData.dinrform;

      try {
        this.downloadData.all.dinrform.doaAcpc = this.formatedDate(
          this.downloadData.dinrform.doaAcpc
        );
      } catch (error) {
        this.downloadData.all.dinrform.doaAcpc = "NULL";
      }

      try {
        this.downloadData.all.dinrform.doaDcpc = this.formatedDate(
          this.downloadData.dinrform.doaDcpc
        );
      } catch (error) {
        this.downloadData.all.dinrform.doaDcpc = "NULL";
      }

      try {
        this.downloadData.all.dinrform.dodFrom = this.formatedDate(
          this.downloadData.dinrform.dodFrom
        );
      } catch (error) {
        this.downloadData.all.dinrform.dodFrom = "NULL";
      }

      try {
        this.downloadData.all.dinrform.dodTo = this.formatedDate(
          this.downloadData.dinrform.dodTo
        );
      } catch (error) {
        this.downloadData.all.dinrform.dodTo = "NULL";
      }

      try {
        this.downloadData.all.gvhsAffected = Gvharray.join();
      } catch (error) {
        this.downloadData.all.gvhsAffected = "NULL";
      }

      try {
        this.downloadData.all.is_food_available_food = this.returnFieldvalue(
          item,
          "food",
          "food_availability",
          "foodavailable"
        );
      } catch (error) {
        this.downloadData.all.is_food_available_food = "NULL";
      }

      try {
        this.downloadData.all.medical_supply_availability = this.returnFieldItemvalue(
          item,
          "health",
          "available_health_medical",
          "Medical supply"
        );
      } catch (error) {
        this.downloadData.all.medical_supply_availability = "NULL";
      }

      try {
        this.downloadData.all.health_personel_availability = this.returnFieldItemvalue(
          item,
          "health",
          "available_health_medical",
          "Health personel"
        );
      } catch (error) {
        this.downloadData.all.health_personel_availability = "NULL";
      }

      try {
        this.downloadData.all.road_access =
          item.sectors.logistics.access_of_structures[0].accessibility;
      } catch (error) {
        this.downloadData.all.road_access = "NULL";
      }

      try {
        this.downloadData.all.food_1_2_months = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "female_HH",
          "category",
          "No. of HH with 1-2 month Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_1_2_months = 0;
      }

      try {
        this.downloadData.all.food_2_months = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "female_HH",
          "category",
          "No. of HH with more than 2 months Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_2_months = 0;
      }

      try {
        this.downloadData.all.food_stock_lost = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "female_HH",
          "category",
          "No. of HH who lost Food Stock"
        );
      } catch (error) {
        this.downloadData.all.food_stock_lost = 0;
      }

      try {
        this.downloadData.all.food_less_1_month = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "female_HH",
          "category",
          "No. of HH with less than 1 month Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_less_1_month = 0;
      }

      try {
        this.downloadData.all.food_less_1_month_male = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "male_HH",
          "category",
          "No. of HH with less than 1 month Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_less_1_month_male = 0;
      }

      try {
        this.downloadData.all.food_1_2_months_male = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "male_HH",
          "category",
          "No. of HH with 1-2 month Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_1_2_months_male = 0;
      }

      try {
        this.downloadData.all.food_2_months_male = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "male_HH",
          "category",
          "No. of HH with more than 2 months Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_2_months_male = 0;
      }

      try {
        this.downloadData.all.food_stock_lost_male = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "male_HH",
          "category",
          "No. of HH who lost Food Stock"
        );
      } catch (error) {
        this.downloadData.all.food_stock_lost_male = 0;
      }

      try {
        this.downloadData.all.food_item_damage = this.sumArrayValues(
          item,
          "agriculture",
          "food_item_damage",
          "number_of_kilos"
        );
      } catch (error) {
        this.downloadData.all.food_item_damage = 0;
      }
      try {
        this.downloadData.all.PeopleAffectedrows_female = this.sumArrayValues(
          item,
          "displaced",
          "PeopleAffectedrows",
          "number_displaced_by_gender_fhh"
        );
      } catch (error) {
        this.downloadData.all.PeopleAffectedrows_female = 0;
      }

      try {
        this.downloadData.all.PeopleAffectedrows_male = this.sumArrayValues(
          item,
          "displaced",
          "PeopleAffectedrows",
          "number_displaced_by_gender_mhh"
        );
      } catch (error) {
        this.downloadData.all.PeopleAffectedrows_male = 0;
      }

      try {
        this.downloadData.all.hectares_submerged = this.sumArrayValues(
          item,
          "agriculture",
          "crops_damaged",
          "hectares_submerged"
        );
      } catch (error) {
        this.downloadData.all.hectares_submerged = 0;
      }

      try {
        this.downloadData.all.hectares_washed_away = this.sumArrayValues(
          item,
          "agriculture",
          "crops_damaged",
          "hectares_washed_away"
        );
      } catch (error) {
        this.downloadData.all.hectares_washed_away = 0;
      }

      try {
        this.downloadData.all.impact_on_crops_hh_affected = this.sumArrayValues(
          item,
          "agriculture",
          "impact_on_crops",
          "hh_affected"
        );
      } catch (error) {
        this.downloadData.all.impact_on_crops_hh_affected = 0;
      }

      try {
        this.downloadData.all.impact_on_crops_hectares_damaged = this.sumArrayValues(
          item,
          "agriculture",
          "impact_on_crops",
          "hectares_damaged"
        );
      } catch (error) {
        this.downloadData.all.impact_on_crops_hectares_damaged = 0;
      }

      try {
        this.downloadData.all.impact_on_livestock_hh = this.sumArrayValues(
          item,
          "agriculture",
          "impact_on_livestock",
          "hh_affected_l"
        );
      } catch (error) {
        this.downloadData.all.impact_on_livestock_hh = 0;
      }

      try {
        this.downloadData.all.impact_on_livestock_la = this.sumArrayValues(
          item,
          "agriculture",
          "impact_on_livestock",
          "livestock_affected"
        );
      } catch (error) {
        this.downloadData.all.impact_on_livestock_la = 0;
      }

      try {
        this.downloadData.all.displaced_households_accommodated_male = this.sumArrayValues(
          item,
          "displaced",
          "displaced_households_accommodated",
          "accomodated_males_hh"
        );
      } catch (error) {
        this.downloadData.all.displaced_households_accommodated_male = 0;
      }
      try {
        this.downloadData.all.displaced_households_accommodated_female = this.sumArrayValues(
          item,
          "displaced",
          "displaced_households_accommodated",
          "accomodated_females_hh"
        );
      } catch (error) {
        this.downloadData.all.displaced_households_accommodated_female = 0;
      }

      try {
        this.downloadData.all.displaced_disagregated_male = this.sumArrayValues(
          item,
          "displaced",
          "displaced_disaggregated",
          "displaced_males"
        );
      } catch (error) {
        this.downloadData.all.displaced_disagregated_male = 0;
      }

      try {
        this.downloadData.all.displaced_disagregated_female = this.sumArrayValues(
          item,
          "displaced",
          "displaced_disaggregated",
          "displaced_females"
        );
      } catch (error) {
        this.downloadData.all.displaced_disagregated_female = 0;
      }

      try {
        this.downloadData.all.displaced_individuals_accommodated_male = this.sumArrayValues(
          item,
          "displaced",
          "displaced_individuals_accommodated",
          "accomodated_males"
        );
      } catch (error) {
        this.downloadData.all.displaced_individuals_accommodated_male = 0;
      }

      try {
        this.downloadData.all.displaced_individuals_accommodated_female = this.sumArrayValues(
          item,
          "displaced",
          "displaced_individuals_accommodated",
          "accomodated_females"
        );
      } catch (error) {
        this.downloadData.all.displaced_individuals_accommodated_female = 0;
      }

      try {
        this.downloadData.all.education_building_functioning = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "functioning_buildings"
        );
      } catch (error) {
        this.downloadData.all.education_building_functioning = 0;
      }

      try {
        this.downloadData.all.education_building_partly_functioning = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "partially_functioning_buildings"
        );
      } catch (error) {
        this.downloadData.all.education_building_partly_functioning = 0;
      }

      try {
        this.downloadData.all.education_closed_buildings = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "closed_buildings"
        );
      } catch (error) {
        this.downloadData.all.education_closed_buildings = 0;
      }

      try {
        this.downloadData.all.education_females_out_of_school = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "females_out_of_school"
        );
      } catch (error) {
        this.downloadData.all.education_females_out_of_school = 0;
      }

      try {
        this.downloadData.all.education_males_out_of_school = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "males_out_of_school"
        );
      } catch (error) {
        this.downloadData.all.education_males_out_of_school = 0;
      }

      try {
        this.downloadData.all.education_underwater = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "underwater"
        );
      } catch (error) {
        this.downloadData.all.education_underwater = 0;
      }

      try {
        this.downloadData.all.education_completely_damaged = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "completely_damaged"
        );
      } catch (error) {
        this.downloadData.all.education_completely_damaged = 0;
      }

      try {
        this.downloadData.all.health_partially_functioning =
          this.sumArrayValues(
            item,
            "health",
            "available_health_facilities",
            "partially_functioning"
          ) +
          this.sumArrayValues(
            item,
            "health",
            "other_health_facilities",
            "partialy_functioning"
          );
      } catch (error) {
        this.downloadData.all.health_partially_functioning = 0;
      }

      try {
        this.downloadData.all.health_verge_of_closing =
          this.sumArrayValues(
            item,
            "health",
            "available_health_facilities",
            "verge_of_closing"
          ) +
          this.sumArrayValues(
            item,
            "health",
            "other_health_facilities",
            "verge_of_closing"
          );
      } catch (error) {
        this.downloadData.all.health_verge_of_closing = 0;
      }

      try {
        this.downloadData.all.health_closed =
          this.sumArrayValues(
            item,
            "health",
            "available_health_facilities",
            "closed"
          ) +
          this.sumArrayValues(
            item,
            "health",
            "other_health_facilities",
            "closed"
          );
      } catch (error) {
        this.downloadData.all.health_closed = 0;
      }

      try {
        this.downloadData.all.livelihoods_slightly_affected = this.sumArrayValues(
          item,
          "livelihoods",
          "livelihoods_affected",
          "severely_affected"
        );
      } catch (error) {
        this.downloadData.all.livelihoods_slightly_affected = 0;
      }
      try {
        this.downloadData.all.livelihoods_severely_affected = this.sumArrayValues(
          item,
          "livelihoods",
          "livelihoods_affected",
          "slightly_affected"
        );
      } catch (error) {
        this.downloadData.all.livelihoods_severely_affected = 0;
      }

      try {
        this.downloadData.all.impact_on_vulnerable_persons_males = this.sumArrayValues(
          item,
          "protection",
          "impact_on_vulnerable_persons",
          "impacted_males"
        );
      } catch (error) {
        this.downloadData.all.impact_on_vulnerable_persons_males = 0;
      }

      try {
        this.downloadData.all.impact_on_vulnerable_persons_females = this.sumArrayValues(
          item,
          "protection",
          "impact_on_vulnerable_persons",
          "impacted_females"
        );
      } catch (error) {
        this.downloadData.all.impact_on_vulnerable_persons_females = 0;
      }

      try {
        this.downloadData.all.nutrition_affected_pop_male = this.sumArrayValues(
          item,
          "nutrition",
          "affected_population",
          "affected_males"
        );
      } catch (error) {
        this.downloadData.all.nutrition_affected_pop_male = 0;
      }

      try {
        this.downloadData.all.nutrition_affected_pop_female = this.sumArrayValues(
          item,
          "nutrition",
          "affected_population",
          "affected_females"
        );
      } catch (error) {
        this.downloadData.all.nutrition_affected_pop_female = 0;
      }

      try {
        this.downloadData.all.shelter_without_shelter_male = this.sumArrayValues(
          item,
          "shelter",
          "people_without_shelter",
          "males"
        );
      } catch (error) {
        this.downloadData.all.shelter_without_shelter_male = 0;
      }

      try {
        this.downloadData.all.shelter_without_shelter_female = this.sumArrayValues(
          item,
          "shelter",
          "people_without_shelter",
          "females"
        );
      } catch (error) {
        this.downloadData.all.shelter_without_shelter_female = 0;
      }

      try {
        this.downloadData.all.shelter_people_dead_female = this.sumArrayValues(
          item,
          "shelter",
          "PeopleDeadrows",
          "females_dead"
        );
      } catch (error) {
        this.downloadData.all.shelter_people_dead_female = 0;
      }

      try {
        this.downloadData.all.shelter_people_dead_male = this.sumArrayValues(
          item,
          "shelter",
          "PeopleDeadrows",
          "males_dead"
        );
      } catch (error) {
        this.downloadData.all.shelter_people_dead_male = 0;
      }

      try {
        this.downloadData.all.shelter_people_injured_female = this.sumArrayValues(
          item,
          "shelter",
          "PeopleInjuredrows",
          "people_injured_females"
        );
      } catch (error) {
        this.downloadData.all.shelter_people_injured_female = 0;
      }

      try {
        this.downloadData.all.shelter_people_injured_male = this.sumArrayValues(
          item,
          "shelter",
          "PeopleInjuredrows",
          "people_injured_males"
        );
      } catch (error) {
        this.downloadData.all.shelter_people_injured_male = 0;
      }

      try {
        this.downloadData.all.shelter_fhh_affected = this.sumArrayValues(
          item,
          "shelter",
          "PeopleAffectedrows",
          "damaged_fhh"
        );
      } catch (error) {
        this.downloadData.all.shelter_fhh_affected = 0;
      }

      try {
        this.downloadData.all.shelter_mhh_affected = this.sumArrayValues(
          item,
          "shelter",
          "PeopleAffectedrows",
          "damaged_mhh"
        );
      } catch (error) {
        this.downloadData.all.shelter_mhh_affected = 0;
      }

      this.download.push({ all: this.downloadData.all });
    }
  }
};
</script>

<style scoped>
.modal-body .tags-input__wrapper {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
.el-tag.el-tag--primary {
  position: relative !important;
  display: inline;
}
.image-previewer,
.row {
  display: flex;
  flex-wrap: wrap;
  padding: 10px;
  justify-items: baseline;
}

.image-previewer .img,
.row .img {
  height: 300px;
  border-radius: 10px;
  cursor: pointer;
}
.image-previewer .image--wrapper {
  position: relative;
  margin: 10px;
}
.row .image--wrapper {
  position: relative;
  margin: 10px;
}
.image-previewer .row .image--wrapper {
  margin: 10px;
}
.image--wrapper .img--overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  z-index: 10;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}
.image--wrapper .img--overlay .btn {
  background: rgb(238, 5, 5);
  width: 100%;
  position: absolute;
  bottom: 0;
}
.custom-image {
  margin: 1px;
}

.progress,
.progress-bar {
  height: 30px;
  font-weight: bold;
}
.inner {
  overflow: hidden;
}
.inner img {
  transition: all 1.5s ease;
}
.inner:hover img {
  transform: scale(2);
  display: flex;
  flex-wrap: wrap;
}
table {
  border-collapse: collapse;
  width: 100%;
  margin: 0 auto;
}

table,
th,
td {
  border: 1px solid black;
  margin-top: 1%;
}

td {
  padding: 1%;
}



.noborder {
  border: none;
}

.vertical {
  writing-mode: vertical-rl;
}

.qcont:first-letter {
  text-transform: capitalize;
}

.right-align {
  text-align: right;
}

@media print {
  .section-not-to-print {
    visibility: hidden;
  }
  #section-to-print {
    visibility: visible;
  }
}
</style>
