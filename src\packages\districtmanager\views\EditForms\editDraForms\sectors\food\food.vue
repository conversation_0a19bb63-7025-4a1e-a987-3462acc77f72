<template>
  <div>
    <h2>FOOD</h2>
    <h3>
      *
      <small>Hint &nbsp;</small>
      <b>
        <font color="primary"
          >(HH: Households, FHH : Female Headed Households, MHH : Male Headed
          Households)</font
        >
      </b>
    </h3>
    <hr class="mt-3 mb-3" />
    <h2>
      <b class="alert-suc">Food availability</b>
    </h2>
    <form>
      <div
        class="row"
        v-for="(value, index) in food_availability"
        v-bind:key="index"
      >
        <div class="col-md">Is food available for everyone</div>
        <div class="col-md">
          <base-radio
            name="yes"
            class="mb-3"
            v-model="value.foodavailable"
            data-toggle="tooltip"
            data-placement="top"
            title="Yes"
            v-bind:value="'yes'"
            >Yes</base-radio
          >
        </div>

        <div class="col-md">
          <base-radio
            name="no"
            class="mb-3"
            v-model="value.foodavailable"
            data-toggle="tooltip"
            data-placement="top"
            title="No"
            v-bind:value="'no'"
            >No</base-radio
          >
        </div>

        <div v-if="value.foodavailable === 'no'" class="col-md-12">
          <base-input>
            <textarea
              class="form-control"
              id="exampleFormControlTextarea3"
              v-model="value.no_food_availability_explanation"
              data-toggle="tooltip"
              data-placement="top"
              title="Please provide explanation"
              placeholder="If no provide an explanation"
              rows="3"
            ></textarea>
          </base-input>
        </div>
      </div>
    </form>
    <hr />
    <h2>
      <b class="alert-suc">Food access</b>
    </h2>
    <form>
      <div class="row" v-for="(value, index) in food_access" v-bind:key="index">
        <div class="col-md">
          <base-input label="Please choose the question">
            <select
              class="form-control"
              v-model="value.question"
              data-toggle="tooltip"
              data-placement="top"
              title="Choose a access question"
            >
              <option
                v-for="question in food_access_questions"
                :value="question.name"
                >{{ question.name }}</option
              >
            </select>
          </base-input>
        </div>

        <div class="col-md pt-5">
          <base-radio
            name="yes"
            class="mb-3"
            v-bind:value="'yes'"
            data-toggle="tooltip"
            data-placement="top"
            title="Yes"
            v-model="value.response"
            >Yes</base-radio
          >
        </div>

        <div class="col-md pt-5">
          <base-radio
            name="no"
            class="mb-3"
            data-toggle="tooltip"
            data-placement="top"
            title="No"
            v-bind:value="'no'"
            v-model="value.response"
            >No</base-radio
          >
        </div>

        <div class="col-md pr-5 pt-5">
          <base-button
            size="sm"
            type="warning"
            class="btn-icon-only rounded-circle noprint"
            data-toggle="tooltip"
            data-placement="top"
            title="Remove food access question"
            v-if="food_access.length > 0"
            @click="
              removeItemRow(
                'food_access',
                food_access,
                food_access_questions,
                index,
                'question'
              )
            "
            >X</base-button
          >
        </div>
      </div>
    </form>
    <base-button
      size="md"
      type="info"
      class="btn-icon-only rounded-circle noprint"
      data-toggle="tooltip"
      data-placement="top"
      title="Add food access question"
      @click="
        addItemRow(
          'food_access',
          food_access,
          food_access_questions,
          'question'
        )
      "
    >
      <i class="ni ni-fat-add"></i>
    </base-button>
    <hr />

    <h2>
      <b class="alert-suc">Available Food Stocks</b>
    </h2>
    <form>
      <div
        class="row"
        v-for="(value, index) in food_stocks_avaliability"
        v-bind:key="index"
      >
        <div class="col-md">
          <base-input label="Please choose an option">
            <select
              class="form-control"
              v-model="value.category"
              data-toggle="tooltip"
              data-placement="top"
              title="Choose a category"
            >
              <option
                v-for="food_stock in food_stocks"
                :value="food_stock.name"
                >{{ food_stock.name }}</option
              >
            </select>
          </base-input>
        </div>
        <div class="col-md">
          <base-input
            label="MHH"
            type="number"
            placeholder="# of males"
            v-model.number="value.male_HH"
            oninput="validity.valid||(value='');"
            @input="init_totals('male_HH', food_stocks_avaliability, 'male_HH')"
            data-toggle="tooltip"
            data-placement="top"
            title="Enter number of male-headed households"
            min="0"
            :rules="[v => !!v || 'value is required']"
            v-bind:max="maxValue"
          />
        </div>
        <div class="col-md">
          <base-input
            label="FHH"
            type="number"
            placeholder="# of females"
            v-model.number="value.female_HH"
            oninput="validity.valid||(value='');"
            min="0"
            :rules="[v => !!v || 'value is required']"
            data-toggle="tooltip"
            data-placement="top"
            title="Enter number of female-headed households"
            @input="
              init_totals('female_HH', food_stocks_avaliability, 'female_HH')
            "
            v-bind:max="maxValue"
          />
        </div>
        <div class="col-md pt-5">
          <base-button
            size="sm"
            type="warning"
            class="btn-icon-only rounded-circle noprint"
            data-toggle="tooltip"
            data-placement="top"
            title="Remove food stock availability"
            v-if="food_stocks_avaliability.length > 0"
            @click="
              removeItemRow(
                'food_stocks_avaliability',
                food_stocks_avaliability,
                food_stocks,
                index,
                'category',
                ['male_HH', 'female_HH']
              )
            "
            >X</base-button
          >
        </div>
      </div>
    </form>
    <base-button
      size="md"
      type="info"
      class="btn-icon-only rounded-circle noprint"
      data-toggle="tooltip"
      data-placement="top"
      title="Add food stock availability"
      @click="
        addItemRow(
          'food_stocks_avaliability',
          food_stocks_avaliability,
          food_stocks,
          'category'
        )
      "
    >
      <i class="ni ni-fat-add"></i>
    </base-button>
    <hr />
    <div class="col-md">
      <h5 slot="header" class="mt-5">Total HH : {{ total_hh }}</h5>
    </div>
    <hr />
    <b>Response Needed for the Food Cluster</b>
    <hr class="mt-3 mb-3" />
    <base-input label="General Response needed for Food cluster">
      <textarea
        class="form-control"
        id="exampleFormControlTextarea3"
        data-toggle="tooltip"
        data-placement="top"
        title="Type the general response needed"
        placeholder="Type the response needed for the Food cluster"
        v-model="response_needed"
        rows="3"
      ></textarea>
    </base-input>
    <base-input label="Urgent Response needed for Food cluster">
      <textarea
        class="form-control"
        id="exampleFormControlTextarea3"
        data-toggle="tooltip"
        data-placement="top"
        title="Type the urgent response needed"
        placeholder="Type the urgent response needed for the Food cluster"
        v-model="urgent_response_needed"
        rows="3"
      ></textarea>
    </base-input>
    <base-button
      size="lg"
      type="primary"
      data-toggle="tooltip"
      data-placement="top"
      title="Save and goto next section"
      @click.stop="save"
      class="noprint"
      >Save & Continue</base-button
    >
  </div>
</template>
<script src="./index.js"/>
<style scoped>
@media print {
  .noprint {
    visibility: hidden;
  }
}
</style>
