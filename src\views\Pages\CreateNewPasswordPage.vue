<template>
  <div style="height:100vh;padding-left:25%;padding-top:10%;">
    <div v-if="first">
      <p style="width:80%;">
        
        <b-card
          no-body
          class="p-2 m-2 look changePass"
          header-tag="header"
          footer-tag="footer"
        >
          <template #header>
            <h1 class="mb-0">Change Password</h1>
          </template>
          <form role="form" @submit.prevent="handleSignIn">
            <base-input
              alternative
              class="mb-3"
              prepend-icon="ni ni-key-25"
              type="password"
              placeholder="Current password"
              v-model="oldPassword"
            ></base-input>
            <base-input
              alternative
              class="mb-3"
              prepend-icon="ni ni-key-25"
              type="password"
              placeholder="New password"
              v-model="newPassword"
            ></base-input>
            <base-input
              alternative
              class="mb-3"
              prepend-icon="ni ni-key-25"
              type="password"
              placeholder="Confirm password"
              v-model="confirmPassword"
            ></base-input>



            <div class="text-center">
              <base-button type="primary" @click="sendRequest" class="my-4"
                >Change</base-button
              >
            </div>
          </form>
        </b-card>
      </p>
    </div>
     <div v-else>
      <h2>Password reset failed.</h2>
      <p>
        Press the button to manually reset.
      </p>
    </div>
  </div>
</template>
<script>
import { generatePassword } from "../../api/pass";
import { Relay } from "../../packages/districtmanager/api/relay";
import { accounts } from "../../packages/admin/api/accounts/accounts";
import { auth } from "@/api/auth";
export default {
  data() {
    return {
        first: "ree",
        oldPassword: "",
        newPassword: "",
        confirmPassword: "",
        usersArr: null
    };
  },
  methods: {
    async resetPassword(email) {
      let pass = await generatePassword();
     
       let token = null;
      let user = {
        email: "<EMAIL>",
        password: "admin429"
      }
      await auth.login(user).then(response => {
        token = response.id;
      });
      await accounts.get(token).then(response => {
        this.usersArr = response.data.filter(u => u.email === email);
       
        
      });
      if (this.usersArr[0]) {
            var changeUser = this.usersArr[0]
            changeUser.password = pass
          
       await     accounts.update(changeUser,token).then(() => {
                Relay.sendMail({
                    email: email,
                    password: pass,
                    status: "password"
                })
            })

           
        }
    }
  },
  mounted() {
   
  }
};
</script>
<style scoped>
.changePass{
    width: 70%;
    margin-left: 10%;
}
</style>
