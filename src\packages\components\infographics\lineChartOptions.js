import { Colors } from './Colors'
const timeFormat = d3.timeFormat('%d-%m-%Y')

export const lineOptions = (d3, dc, series) => {
          series
    .chart(function (c) {
      return new dc.LineChart(c);
    })
   .height(450)
    .brushOn(false)
    .xAxisLabel("Month, Year")
    .clipPadding(10)
    .renderHorizontalGridLines(true)
    .seriesAccessor(function (d) {
       // console.log(d, "pooooooooooooooooooooooooooooooooo");
      return d.key[0];
    })
    .keyAccessor(function (d) {
      return d.key;
    })
    .valueAccessor(function (d) {
      return d.value;
    })
    .turnOnControls(true)
    .colorAccessor((d) => {
      return d.value;
    })
    .ordinalColors(["#bf6c00"])
    .title((d) => `${d.value} Households Affected`)
    .legend(dc.legend().x(350).y(500).itemHeight(13).gap(5).horizontal(1)
                  .legendWidth(120).itemWidth(60))

  series.margins().left += 20;
  series.margins().bottom += 20;
  series.margins().top += 20;

  series.clipPadding(10).elasticY(true);

  series.xAxis().tickFormat(d3.timeFormat("%b,%Y"));

  return series
}