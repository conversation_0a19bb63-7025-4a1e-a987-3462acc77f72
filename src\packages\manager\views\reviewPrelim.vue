`/* eslint-disable */`
<template>
  <div class="col-11 content" style="margin: 0 auto">

    <div class="row align-items-center py-4" style="padding-right:0px; padding-left:0">
      <div class="col-lg-6 col-6">
        <h6 class="h2 d-inline-block mb-0">Disaster</h6>
        <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
          <ol class="breadcrumb breadcrumb-links">
            <li class="breadcrumb-item">
              <router-link to="/manager/dashboard">
                <i class="fas fa-home"></i>
              </router-link>
            </li>
            <li class="breadcrumb-item">
              <a href="#">Preliminary Report</a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
              Detailed
            </li>
          </ol>
        </nav>
      </div>
      <div class="col-lg-6 col-6 text-right pr-3">
        <base-dropdown title-classes="btn btn-sm btn-primary mr-0" menu-on-right :has-toggle="false">
          <a slot="title" class="dropdown-toggle" type="neutral" size="sm">
            Options
          </a>

          <!-- <downloadexcel
              class="dropdown-item"
              :data="Array.from(download)"
              name="DisasterData.csv"
              type="csv"
              style="color:#26b6b2">
              EXPORT TO CSV
            </downloadexcel> -->

          <a class="dropdown-item" type="neutral" @click="printdiv('section-to-print')">
            <span class="btn-inner--icon">
              <!--  <i class="ni ni-fat-add"></i> -->
            </span>
            <span class="btn-inner--text" style="color:#26b6b2">Print</span>
          </a>

        </base-dropdown>
      </div>

      <div class="col-lg-6 col-5 text-right"></div>
    </div>

    <div id="section-to-print" class="">
      <card class="no-border-card" body-classes="px-0 pb-1" footer-classes="pb-2">
        <div style="color: #32325d">
          <div class="component-example__container">
            <!-- <DetailedReportHeader /> -->
            <div>
              <dl class="headings">
                <div class="row mx-4 mt-1">
                  <div class="col-6">
                    <img src="../../../../static/logo.png" height="90" />
                  </div>
                  <div class="col-6 text-right">
                    <img src="" id="qr-code" height="90" />
                  </div>
                </div>
                <dd class="text-center" style="font-size:22px; color: #32325d">
                  <p>
                    <b>
                      <b>
                        <strong style="font-size:22px;">Government of Malawi</strong>
                      </b>
                    </b>
                    <br />
                    <span>
                      <b>
                        <b>Department of Disaster Management Affairs (DoDMA)</b>
                      </b>
                    </span>
                  </p>

                  <p></p>
                  <p>
                    <span style="background:orange;width:100vh;padding-left:6px;padding-right:6px">
                      <b>
                        <b> Preliminary Disaster Report for {{ tableData.user.district.admin2_name_en}}

                          <br /></b>
                      </b>
                    </span>
                    <span id="TA"
                      style="font-weight: bold;font-size:22px;width:100vh;padding-left:6px;padding-right:6px">{{
                      tableData.TA

                      }}</span>
                  </p>
                  <div class="col-md-11">
                    <p
                      style="max-width:1500px; font-weight: bold; font-size: small; display:flex; flex-wrap: wrap; margin:5%; text-align: left;">
                      {{ tableData.covertext }}
                    </p>
                  </div>
                </dd>
              </dl>
              <div style="color: #32325d">
                <table class="col-md-11" v-for="item in tableData.impact">
                  <tr>
                    <td colspan="12" width="100%">
                      <center>
                        <h2>
                          <b style="">
                            GVH
                            <span id="gvh">{{
                            item.gvh.name

                            }}</span>

                          </b>
                        </h2>
                      </center>
                    </td>
                  </tr>
                  <tr>

                  <tr>
                    <td colspan="4" width="30%">
                      <b>Male HH</b>
                      :
                      <span id="mhh"> {{ item.mhh }} </span>
                    </td>
                    <td colspan="4">
                      <b>Female HH </b>
                      :
                      <span id="fhh"> {{ item.fhh }} </span>
                    </td>
                    <td colspan="4">
                      <b>Total HH </b>
                      :
                      <span id="hh"> {{ parseInt(item.mhh) + parseInt(item.fhh) }} </span>
                    </td>

                  </tr>
                  </tr>

                  <tr>
                    <td colspan="4">
                      <b>People Injured</b>
                      :
                      <span>{{
                      item.injured
                      }}</span>
                    </td>
                    <td colspan="3">
                      <b>People Missing</b>
                      :
                      <span>{{
                      item.missing
                      }}</span>
                    </td>
                    <td colspan="3">
                      <b>People Dead</b>
                      :
                      <span>{{
                      item.dead
                      }}</span>
                    </td>
                    <!-- <td colspan="4">
                      <b># of Properties damaged</b>
                      :
                      <span>{{
                      item.propertiesdamaged
                      }}</span>
                    </td> -->

                  </tr>
                  <tr>
                    <td colspan="4" width="30%">
                      <b>Pregnant</b>
                      :
                      <span>{{
                      item.pregnant
                      }}</span>
                    </td>
                    <td colspan="4">
                      <b>Lactating Women </b>
                      :
                      <span>{{
                      item.lactactingwomen
                      }}</span>
                    </td>
                    <td colspan="4">
                      <b>Under 5 Children </b>
                      :
                      <span>{{
                      item.underfive
                      }}</span>
                    </td>

                  </tr>
                  <!-- <tr>
                    <td colspan="12" width="100%">
                      <b>Type of Property damaged</b>
                      :
                      <span>{{
                      item.typeofdamage
                      }}</span>
                    </td>
                  </tr> -->

                  <tr>
                    <td colspan="12" width="100%">
                      <b>Comments</b>
                      :
                      <span>{{
                      tableData.comments
                      }}</span>
                    </td>
                  </tr>

                  <!-- <tr>
                    <td style="width:20%">
                      <strong>Response Needed</strong>
                    </td>
                    <td colspan="14">
                      <u>
                        <strong>Immediate Emergency/Priority needs</strong>
                      </u>
                      <br />
                      <li style="margin-left:2%" class="qcont">
                        <span>{{

                        item.needs.filter(Boolean)
                        .sort((a, b) => a.localeCompare(b))
                        .join(", ") || "Not reported"
                        }}</span>

                      </li>
                      <hr style="margin:1%;" />
                        <u>
                          <strong>Comments</strong>
                        </u>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          <span>{{
                           item.comments
                           }}</span>
                        </li>
                    </td>
                  </tr> -->
                </table>
                <br />
                <table class="col-md-11" width="100%" style="text-weight: bold; text-size: 120%">
                  <tr>
                    <td width="50%">
                      <strong>
                        Checked and Verified by
                        <span>
                          {{ this.officerSignatures[0].role }}
                        </span>
                      </strong>
                    </td>
                    <td>
                      <strong>
                        Approved by
                        <span>
                          {{ this.dcSignature[0].role }}
                        </span>
                      </strong>
                    </td>
                  </tr>
                  <tr>
                    <td width="50%">
                      <strong>Name :</strong>
                      <span v-if="this.officerSignatures">
                        {{
                        (this.officerSignatures[0].signatory_fname
                        ? this.officerSignatures[0].signatory_fname
                        : "") +
                        " " +
                        (this.officerSignatures[0].signatory_lname
                        ? this.officerSignatures[0].signatory_lname
                        : "")
                        }}
                      </span>
                    </td>
                    <td width="50%">
                      <strong>Name :</strong>
                      <span v-if="this.dcSignature">
                        {{
                        (this.dcSignature[0].signatory_fname
                        ? this.dcSignature[0].signatory_fname
                        : "") +
                        " " +
                        (this.dcSignature[0].signatory_lname
                        ? this.dcSignature[0].signatory_lname
                        : "")
                        }}
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td width="50%">
                      <strong>Signature :</strong>
                      <img v-if="this.officerSignatures[0].signature && officerpath" :src="officerpath"
                        style="box-shadow: 0 0 2px 2px white inset" class="mx-2" width="100" height="30" />
                    </td>
                    <td width="50%">
                      <strong>Signature :</strong>
                      <img v-if="this.dcSignature[0].signature &&  dcpath" :src="dcpath"
                        style="box-shadow: 0 0 2px 2px white inset" class="mx-2" width="100" height="30" />
                    </td>
                  </tr>

                  <tr>
                    <td width="50%">
                      <strong>Date :</strong>
                      <span v-if="this.officerSignatures">
                        {{ formatdate(this.officerSignatures[0].createdon) }}
                      </span>
                    </td>
                    <td width="50%">
                      <strong>Date :</strong>
                      <span v-if="this.dcSignature">
                        {{
                        tableData.createdon
                        ? formatdate(tableData.createdon)
                        : ""
                        }}
                      </span>
                    </td>
                  </tr>
                </table>
                <!-- <table
              class="col-md-11"

              width="100%"
              style="text-weight:bold;text-size:120%"
            >
              <tr>
                <td style="width:500px">
                  <strong>I have checked and verified that this report has been perfectly filled.</strong>
                </td>
                <td>

                    <div class="mx-3">
                      <strong>The DC will issue final approval here</strong>
                   </div>
                </td>
              </tr>
              <tr>
                <td>
                  <strong>Name :</strong>
                  {{
                    tableData.user.firstName +
                      " " +
                      tableData.user.lastName
                  }}
                </td>
                <td></td>
              </tr>
              <tr>
                <td>
                  <strong>Signature :</strong>
                 <span v-if="this.signature">
                        <base-button type="success" @click="handleApprove()"
                          >Sign</base-button
                        >
                        <base-button type="danger" @click="handleReject()"
                          >Reject</base-button
                        >
                        </span
                      >
                      <span class="text-danger" v-else> Create signature to be able to sign this report</span>
                </td>
                <td>
                  <div class="row"></div>
                </td>
              </tr>

              <tr>
                <td>
                  <strong>Date :</strong>
                  {{ formatdate(tableData.createdon) }}
                </td>
                <td></td>
              </tr>
     </table> -->
                <!-- <p style="text-align:center;font-size:120% ;">
                <b>ACRONYMS</b>
              </p> -->
                <!-- <table
                style="margin:0 auto;padding:0px !important; border:none !important;width:92%;"
              >
                <tr
                  style="margin:0px !important;padding:0px !important; border:none;"
                >
                  <td
                    style="margin:0px !important;padding:0px !important; border:none;"
                  >
                    <table style="margin:0 auto;border:1px solid;padding:0px">
                      <tr>
                        <td style="width:20%">
                          <b>ACPC</b>
                        </td>
                        <td>Area Civil Protection Committee</td>
                      </tr>
                      <tr>
                        <td>
                          <b>DCPC</b>
                        </td>
                        <td>District Civil Protection Committee</td>
                      </tr>
                      <tr>
                        <td>
                          <b>GVH</b>
                        </td>
                        <td>Group Village Headman</td>
                      </tr>
                      <tr>
                        <td>
                          <b>HH</b>
                        </td>
                        <td>Household</td>
                      </tr>
                      <tr>
                        <td>
                          <b>NR</b>
                        </td>
                        <td>Not reported</td>
                      </tr>
                    </table>
                  </td>
                  <td
                    style="margin:4px !important;padding:4px !important; border:none;"
                  ></td>
                  <td
                    style="margin:0px !important;padding:0px !important; border:none;"
                  >
                    <table style="margin:0 auto;border:1px solid;padding:0px">
                      <tr>
                        <td>
                          <b>N/A</b>
                        </td>
                        <td>Not Applicable</td>
                      </tr>

                      <tr>
                        <td>
                          <b>TA</b>
                        </td>
                        <td>Tradition Authority</td>
                      </tr>
                      <tr>
                        <td>
                          <b>T</b>
                        </td>
                        <td>Total</td>
                      </tr>
                      <tr>
                        <td>
                          <b>VCPC</b>
                        </td>
                        <td>Village Civil Protection Committee</td>
                      </tr>
                      <tr>
                        <td>
                          <b>VH</b>
                        </td>
                        <td>Village Headman</td>
                      </tr>
                    </table>
                  </td>
                </tr>
              </table> -->
                <br />
              </div>
            </div>
            <DetailedReportBody />
            <!-- <table
              class="col-md-11"
              v-if="numberOfTAs > 0"
              width="100%"
              style="text-weight: bold; text-size: 120%"
            >
              <tr>
                <td width="50%">
                  <strong>
                    Checked and Verified by
                    <span v-if="this.officerSignature">
                      {{ this.officerSignature.role }}
                    </span>
                  </strong>
                </td>
                <td>
                  <strong>
                    Approved by
                    <span v-if="this.dcSignature">
                      {{ this.dcSignature.role }}
                    </span>
                  </strong>
                </td>
              </tr>
              <tr>
                <td width="50%">
                  <strong>Name :</strong>
                  <span v-if="this.officerSignature">
                    {{
                      (this.officerSignature.signatory_fname
                        ? this.officerSignature.signatory_fname
                        : "") +
                        " " +
                        (this.officerSignature.signatory_lname
                          ? this.officerSignature.signatory_lname
                          : "")
                    }}
                  </span>
                </td>
                <td width="50%">
                  <strong>Name :</strong>
                  <span v-if="this.dcSignature">
                    {{
                      (this.dcSignature.signatory_fname
                        ? this.dcSignature.signatory_fname
                        : "") +
                        " " +
                        (this.dcSignature.signatory_lname
                          ? this.dcSignature.signatory_lname
                          : "")
                    }}
                  </span>
                </td>
              </tr>
              <tr>
                <td width="50%">
                  <strong>Signature :</strong>
                  <img
                    v-if="this.officerSignature && officerpath"
                    :src="officerpath"
                    style="box-shadow: 0 0 2px 2px white inset"
                    class="mx-2"
                    width="100"
                    height="30"
                  />
                </td>
                <td width="50%">
                  <strong>Signature :</strong>
                  <img
                    v-if="this.dcSignature && dcpath"
                    :src="dcpath"
                    style="box-shadow: 0 0 2px 2px white inset"
                    class="mx-2"
                    width="100"
                    height="30"
                  />
                </td>
              </tr>

              <tr>
                <td width="50%">
                  <strong>Date :</strong>
                  <span v-if="this.officerSignature">
                    {{ formatdate(this.officerSignature.createdon) }}
                  </span>
                </td>
                <td width="50%">
                  <strong>Date :</strong>
                  <span v-if="this.dcSignature">
                    {{
                      dinrFormsData.createdon
                        ? formatdate(dinrFormsData.createdon)
                        : ""
                    }}
                  </span>
                </td>
              </tr>
            </table> -->
            <br />
          </div>
        </div>
      </card>
    </div>
  </div>
</template>

<script>
import { MongoReports } from "../api/MongoReports";
import downloadexcel from "vue-json-excel";
import moment from "moment";
var QRCode = require("qrcode");
import { signatures } from "../api/accounts/signatures";
import dateformat from "dateformat";
import { mapGetters, mapActions } from "vuex";
import { prelimreports } from "../../districtmanager/api/forms/prelimreports.js";
import DetailedReportHeader from "../../dashboards/components/detailedReportHeader.vue";
import DetailedReportBody from "../../dashboards/components/detailedReportBody.vue";
import { Console } from "console";

export default {
  name: "ManagerDetailed",
  components: {
    downloadexcel,
    DetailedReportHeader,
    DetailedReportBody
  },
  data() {
    return {
      jwtuser: null,
      signature: "",
      signatureText: "",
      signature: {},
      officerSignatures: [],
      dcSignature: [],
      officerSignature: {},
      officerPath: "",
      dcPath: "",
      numberOfTAs: "",
      dinrFormsData: {},
      villagesArray: [],
      campsArray: [],
      bufferDraFormsData: [],
      TAarray: [],
      Gvharray: [],
      draFormsData: [],
      download: [],
      downloadData: [],
      otherData: [],
      tableData: [],
      vhhousesvalue: "",

    };
  },

  computeLogo() {
    return "../../../../static/logo.png";
  },

  updated() {
    delete this.signatureText.signature;
    delete this.signatureText.status;
    var opts = {
      errorCorrectionLevel: "H",
      type: "image/jpeg",
      quality: 1,
      margin: 1
    };

    QRCode.toDataURL(
      JSON.stringify(this.signatureText || "not electronically signed by DC"),
      opts,
      function (err, url) {
        if (err) throw err;

        var img = document.getElementById("qr-code");
        img.src = url;
      }
    );
  },

  computed: {
    officerpath: function () {
      if (this.officerSignatures && this.officerSignatures[0].signature)
        return (
          process.env.VUE_APP_ENGINE_URL +
          "/" +
          this.officerSignatures[0].signature.replace("\\", "/")
        );
    },
    dcpath: function () {
      if (this.dcSignature && this.dcSignature[0].signature)
        return (
          process.env.VUE_APP_ENGINE_URL +
          "/" +
          this.dcSignature[0].signature.replace("\\", "/")
        );
    },

  },
  beforeDestroy() { },

  created() {
    prelimreports.getReports().then((response) => {
      this.tableData = response.find(item => item._id == this.$route.params._id)
      this.officerSignatures.push(this.tableData.approvalMetadata.signature[0])
      this.dcSignature.push(this.tableData.approvalMetadata.signature[1])

    });


  },
  async mounted() {

    
    signatures.get(this.$session.get("jwt")).then(response => {
      //console.log(this.$session.get("user"))
      this.signature = response.data
        .filter(item => {
          return this.$session.get("user").admin2_name_en == item.district && item.signatory_email == this.$session.get("userObj").email
        })
        .filter(item => item.status == "Active")[0];
     
    });


    this.numberOfTAs = 0;

    let count = 0;

  },

  methods: {

    handleSignOut() {
      this.$session.destroy();
      this.$router.push({ name: "Login", params: { session: "lost" } });
    },

    formatedDatePlusOne(data) {
      const cur_date = this.addDays(data, 1);
      const finalDate = dateformat(cur_date, "dd-mm-yyyy");
      return finalDate;
    },

    addDays(date, days) {
      var result = new Date(date);
      result.setDate(result.getDate() + days);
      return result;
    },
    numberWithCommas(number) {
      try {
        var parts = number.toString().split(".");
        parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        return parts.join(".");
      } catch (e) { }
    },
    comparator(key, order = "asc") {
      return function innerSort(a, b) {
        if (!a.hasOwnProperty(key) || !b.hasOwnProperty(key)) {
          return 0;
        }

        const varA = typeof a[key] === "string" ? a[key].toUpperCase() : a[key];
        const varB = typeof b[key] === "string" ? b[key].toUpperCase() : b[key];

        let comparison = 0;
        if (varA > varB) {
          comparison = 1;
        } else if (varA < varB) {
          comparison = -1;
        }
        return order === "desc" ? comparison * -1 : comparison;
      };
    },

    sortArrayByKey(arrayName) {
      return arrayName.slice().sort(this.comparator("name", "asc"));
    },
    printdiv(printpage) {
      var headstr = "<html><head><title></title></head><body>";
      var footstr = "</body>";
      var newstr = document.all.item(printpage).innerHTML;
      var oldstr = document.body.innerHTML;


      document.body.innerHTML = headstr + newstr + footstr;

      window.print();
      document.body.innerHTML = oldstr;
      location.reload();
      return false;
    },
    dateFormate(dateData) {
      const cur_date = dateData;
      const formattedDate = moment(cur_date).format("DD/MM/YYYY");

      return formattedDate;
    },
    formatdate(data) {
      const cur_date = data;
      const formattedDate = moment(cur_date).format("DD/MM/YYYY");
      return formattedDate;
    },

    formatedDate(data) {
      const cur_date = data;
      const formDate = moment(cur_date).format("YYYY/MM/DD");

      return formDate;
    },

    sumArrayValues(item, sector, array, key) {
      if (typeof item["sectors"][sector][array] !== "undefined") {
        return item["sectors"][sector][array]
          .map(function (item) {
            return parseInt(item[key]) ? parseInt(item[key]) : 0;
          })
          .reduce((sum, value) => sum + value, 0);
      } else if (typeof item[sector][array] === "undefined") {
        return 0;
      }
    },

    returnFieldvalue(item, sector, array, key) {
      if (typeof item["sectors"][sector][array] !== "undefined") {
        return item["sectors"][sector][array][0][key];
      } else if (typeof item["sectors"][sector][array] === "undefined") {
        return "NULL";
      }
    },

    returnFieldItemvalue(item, sector, array, key) {
      if (typeof item["sectors"][sector][array] !== "undefined") {
        for (let i in item["sectors"][sector][array]) {
          if (item["sectors"][sector][array][i].name === key) {
            return item["sectors"][sector][array][i].status;
          } else if (
            typeof item["sectors"][sector][array][i].key === "undefined"
          ) {
            return "NULL";
          }
        }
      }
    },
  }
};
</script>

<style lang="stylus" scoped>
.modal-body .tags-input__wrapper {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.el-tag.el-tag--primary {
  position: relative !important;
  display: inline;
}

.image-previewer, .row {
  display: flex;
  flex-wrap: wrap;
  padding: 10px;
  justify-items: baseline;
}

.image-previewer .img, .row .img {
  height: 300px;
  border-radius: 10px;
  cursor: pointer;
}

.image-previewer .image--wrapper {
  position: relative;
  margin: 10px;
}

.row .image--wrapper {
  position: relative;
  margin: 10px;
}

.image-previewer .row .image--wrapper {
  margin: 10px;
}

.image--wrapper .img--overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  z-index: 10;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

.image--wrapper .img--overlay .btn {
  background: rgb(238, 5, 5);
  width: 100%;
  position: absolute;
  bottom: 0;
}

.progress, .progress-bar {
  height: 30px;
  font-weight: bold;
}

.inner {
  overflow: hidden;
}

.inner img {
  transition: all 1.5s ease;
}

.inner:hover img {
  transform: scale(2);
  display: flex;
  flex-wrap: wrap;
}

table {
  border-collapse: collapse;
  width: 100%;
  margin: 0 auto;
}

table, th, td {
  border: 1px solid black;
  margin-top: 1%;
}

td {
  padding: 1%;
}

.rotated {
  writing-mode: tb-rl;
  transform: rotate(-180deg);
  color: teal;
}

.noborder {
  border: none;
}

.vertical {
  writing-mode: vertical-rl;
}

.qcont:first-letter {
  text-transform: capitalize;
}

.right-align {
  text-align: right;
}

@media print {
  .section-not-to-print {
    visibility: hidden;
  }

  #section-to-print {
    visibility: visible;
  }
}
</style>
