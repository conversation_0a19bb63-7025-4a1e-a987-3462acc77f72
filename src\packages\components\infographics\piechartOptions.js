import { Colors } from './Colors'
export const pieOptions = (pie, dc) => {
  pie
    .ordinalColors(Colors)
    .renderTitle(true)
    .renderLabel(false)
    .ordering(function (d) {
      //console.log(d.key,"Honesty")
      return -d.value
    })

    .slicesCap(4)
    .innerRadius(80)
    //.externalLabels(50)
    .externalRadiusPadding(50)
    // .drawPaths(true)
    .turnOnControls(true)
    .radius(200)
    .legend(
      dc
        .legend()
        .x(10)
        .y(5)
        .itemHeight(10)
        .gap(5)
        .horizontal(1)
        .legendWidth(300)
        .itemWidth(200)
    )

  return pie
}
