<template>
  <div class="content">
    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">All</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">Accounts</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">All</li>
            </ol>
          </nav>
        </div>
        <div class="col-lg-6 col-5 text-right">
          <base-button size="sm" type="neutral" @click.native="handleRegister()">
            <span class="btn-inner--icon">
              <i class="ni ni-fat-add"></i>
            </span>
            <span class="btn-inner--text">Register</span>
          </base-button>

           <base-button size="sm" type="primary" @click="downloadExcel()">
            <span class="btn-inner--icon">
              <i class="text-black ni ni-cloud-download-95"></i>
            </span>
            <span class="btn-inner--text">EXPORT TO EXCEL</span>
          </base-button>
        </div>
      </div>
      <!-- Card stats -->
      <div class="row"></div>
    </base-header>
    <div class="container-fluid mt--6">
      <div>
        <card class="no-border-card" body-classes="px-0 pb-1" footer-classes="pb-2">
          <div>
            <div class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap">
              <el-select
                class="select-primary pagination-select"
                v-model="pagination.perPage"
                placeholder="Per page"
              >
                <el-option
                  class="select-primary"
                  v-for="item in pagination.perPageOptions"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>

              <div>
                <base-input
                  v-model="searchQuery"
                  prepend-icon="fas fa-search"
                  placeholder="Search..."
                >
                </base-input>
              </div>
            </div>
           <div>
              <b-table
              responsive
              sticky-header
              :striped="striped"
              :bordered="bordered"
              :borderless="borderless"
              :outlined="outlined"
              :small="small"
              :hover="hover"
              :dark="dark"
              :sort-icon= true
              :fixed="fixed"
              :foot-clone="footClone"
              :no-border-collapse="noCollapse"
              head-variant="light"
              :table-variant="tableVariant"
              :items="queriedData"
              :fields="tableColumns">

              <template #cell(roleName)="row">
               <span>
                     {{ row.value}}
              </span>
             </template>


            <template #cell(actions)="row">
              <b-button class="edit bg-primary"
                    type="primary"
                    size="sm"
                    icon @click="handleEdit(row.item, row.index, $event.target)">
              <i class="text-white ni ni-scissors"></i>
             </b-button>
              <b-button  @click="handleDelete(row.item, row.index, $event.target)"
                    class="remove bg-danger"
                    type="danger"
                    size="sm"
                    icon>
                    <i class="text-white ni ni-fat-remove"></i></b-button>

           </template>


              </b-table>
           </div>
          </div>
          <div
            slot="footer"
            class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
          >
            <div class>
              <p class="card-category">
                Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
                <span
                  v-if="selectedRows.length"
                >&nbsp; &nbsp; {{selectedRows.length}} rows selected</span>
              </p>
            </div>
            <base-pagination
              class="pagination-no-border"
              v-model="pagination.currentPage"
              :per-page="pagination.perPage"
              :total="total"
            ></base-pagination>
          </div>
        </card>
      </div>
    </div>
  </div>
</template>
<script>
import { Table, TableColumn, Select, Option } from "element-ui";
import RouteBreadCrumb from "@/components/Breadcrumb/RouteBreadcrumb";
import { BasePagination } from "@/components";
import clientPaginationMixin from "@/components/PaginatedTables/clientPaginationMixin";
import swal from "sweetalert2";
import { accounts } from "../../api/accounts/accounts";
import {
  generateExcel,
  generateTAGVHExcel,
  generateDisasterSummaryExcel,
} from "../../../../util/generateExcel";
// import { mapActions } from "vuex";

export default {
  mixins: [clientPaginationMixin],
  components: {
    BasePagination,
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn
  },
  data() {
    return {
      users: [],
      mydataCopy:[],
      propsToSearch: ["lastName", "email", "phone", "firstName", "roleName", "organisation"],
       fields: ['first_name', 'last_name', 'phone','email'],
      tableColumns: [

        {
          key: "firstName",
          label: "First Name",
          sortable: true
        },
        {
          key: "lastName",
          label: "Last Name",
          sortable: true
        },
        {
          key: "phone",
          label: "Phone",
          sortable: true
        },
          {
            key: "email",
            label: "Email",
            sortable: true
          },

           {
            key: "roleName",
            label: "Role",
            sortable: true
          },
           {
            key: "district.admin2_name_en",
            label: "District",
            sortable: true
          },

           {
            key: "organisation",
            label: "Organisation",
            sortable: true
          },

           { key: 'actions',
           label: 'Actions'
           }


      ],
      tableData: [],
      selectedRows: []
    };
  },

  methods: {

      downloadExcel() {


       var data = this.mydataCopy.map((item) => {
          return {
            Lastname: item.lastName,
            Firstname: item.firstName,
            Phone: item.phone,
            Email: item.email,
            Role: item.roleName,
            Organisation: item.organisation,

            sheet: "System users",
          };
        });

        this.users = data;

        generateTAGVHExcel(this.users);
    },

    handleRegister() {

      this.$router.push({ name: "AdminRegisterAccounts", params: {} });
    },
    handleEdit(index, row) {


     this.$router.push({ name: "AdminEditAccounts", params: { id: index.id } });
    },
    handleDelete(index, row) {
      swal({
        title: "Are you sure?",
        text: `You won't be able to revert this!`,
        type: "warning",
        showCancelButton: true,
        confirmButtonClass: "btn btn-success btn-fill",
        cancelButtonClass: "btn btn-danger btn-fill",
        confirmButtonText: "Yes, delete it!",
        buttonsStyling: false
      }).then(result => {
        if (result.value) {
          accounts.remove(index.id, this.$session.get("jwt")).then(
            response => {
              swal({
                title: "Deleted!",
                text: `You deleted ${index.firstName + " " + index.lastName}`,
                type: "success",
                confirmButtonClass: "btn btn-success btn-fill",
                buttonsStyling: false
              });
               this.deleteRow(index)
            },
            reason => {
              this.loading = false;
              this.$swal({
                title: "Failed to delete account",
                text: "possible invalid account (" + reason + ")",
                type: "error",
                confirmButtonClass: "btn btn-success btn-fill",
                buttonsStyling: false
              });
            }
          );
        }
      });
    },
    deleteRow(index) {
      let indexToDelete = this.tableData.findIndex(
        tableRow => tableRow.id === index.id
      );
      if (indexToDelete >= 0) {
        this.tableData.splice(indexToDelete, 1);
      }
    },
    selectionChange(selectedRows) {
      this.selectedRows = selectedRows;
    }
  },
  mounted() {
    accounts.get(this.$session.get("jwt")).then(response => {
    let mydata = {};
    this.mydataCopy = [...response.data];
    this.mydataCopy.push(mydata);
     this.tableData = response.data.filter(item => {

        return item.id !== this.$session.get("jwtuid");
      });
    });
   


    //accounts.get(this.$session.get("jwt")).then(response => {this.tableData   = response.data;})
  }
};
</script>
<style lang="scss" scoped>

.no-border-card .card-footer {
  border-top: 0;
}

.switch-holder {
    display: flex;
    padding: 5px 5px;
    border-radius: 10px;
    margin-bottom: 25px;
    margin-left: 35px;
    justify-content: flex-start;
    align-items: center;
}

.switch-label {
    width: 200px;
}

.switch-label i {
    margin-right: 5px;
}

.switch-toggle {
  // border:1px solid red;
    height: 40px;
}

.switch-toggle input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    z-index: -2;
}

.switch-toggle input[type="checkbox"] + label {
    position: relative;
    display: inline-block;
    width: 130px;
    height: 30px;
    border-radius: 1px;
    margin: 0;
    cursor: pointer;
    box-shadow: inset -8px -8px 15px rgba(255,255,255,.6),
                inset 10px 10px 10px rgba(0,0,0, .25);

}

.switch-toggle input[type="checkbox"] + label::before {
    position: absolute;
    content: 'DEACTIVE';
    font-size: 8px;
    text-align: center;
    line-height: 25px;
    top: 8px;
    left: 8px;
    width: 65px;
    height: 25px;
    border-radius: 20px;
    background-color: #d1dad3;
    box-shadow: -3px -3px 5px rgba(255,255,255,.5),
                3px 3px 5px rgba(0,0,0, .25);
    transition: .3s ease-in-out;
}

.switch-toggle input[type="checkbox"]:checked + label::before {
    left: 50%;
    content: 'ACTIVE';
    color: #fff;
    background-color: #2ec3a3;
    box-shadow: -3px -3px 5px rgba(255,255,255,.5),
                3px 3px 5px #2ec3a3;
}

</style>
