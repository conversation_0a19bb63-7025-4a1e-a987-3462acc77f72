
import axios from 'axios';

const resource = process.env.VUE_APP_ENGINE_URL + '/admin-3gvhs';

export class admin_3gvhs {

  static get(id = null, admin3Pcode = null) {
    if (id == null) {
      if (admin3Pcode == null) {
        return axios.get(resource).then(response => { return response });
      } else {
        return axios.get(resource + '?filter[where][admin3Pcode]=' + admin3Pcode).then(response => { return response });
      }
    }
    else {
      return axios.get(resource + '/' + id).then(response => { return response });
    }

  }
  static async create(data) {
    let response = await axios.post(resource, data).then(response => { return response });
    return response;
  }
  static async remove(id) {
    let response = await axios.delete(resource + '/' + id).then(response => { return response });
    return response;
  }
  static update(data) {
    return axios.patch(resource + '/' + data.id, data).then(response => { return response });
  }

}
