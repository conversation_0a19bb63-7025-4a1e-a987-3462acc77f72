<template>
  <div class="content">
    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Disaster</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">barTrends</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">All</li>
            </ol>
          </nav>
        </div>
        <div class="col-lg-6 col-5 text-right"></div>
      </div>
      <!-- Card stats -->
    </base-header>
    <div class="container-fluid mt--6">
      <div>
        <card
          class="no-border-card"
          body-classes="px-0 pb-1"
          footer-classes="pb-2"
        >
          <template slot="header">
            <h3 class="mb-0">TRENDS OF DISASTER REPORTS</h3>
          </template>
          <div>
            <div style="text-align:center">
              <h2>{{ filter.split("-")[1].toUpperCase() }}</h2>
            </div>
            <div
              class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
            >
              <div class="row col-12 mt-4">
                <div class="col-6">
                  <span style="margin-right:10px;">Trend :</span>
                  <span id="identifier-c"></span>
                </div>
                <div class="col-1"></div>
              </div>
            </div>
            <div style="width:100%;text-algin:center;" id="barTrends"></div>
            <div style="width:100%;text-algin:center;" id="lineTrends"></div>
          </div>
          <div
            slot="footer"
            class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
          ></div>
        </card>
      </div>
    </div>
  </div>
</template>
<script>
import { Table, TableColumn, Select, Option } from "element-ui";
import RouteBreadCrumb from "@/components/Breadcrumb/RouteBreadcrumb";
import * as d3 from "d3";
import * as dc from "dc";
import crossfilter from "crossfilter2";
var globalData = [];
var group = "trendCharts";
var parent = {};
import { MongoReports } from "../../districtmanager/api/MongoReports";
var moment = require("moment");

export default {
  components: {
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn
  },
  data() {
    return {
      filter: "6-Lifetime",
      data: [
       
      ],
      Graphtitle: ""
    };
  },
  computed: {
    drawGraph1() {
      ///tooltip
    },
    getData() {
      return this.data;
    }
  },
  async mounted() {
    parent = this;
    this.loadForms().then(response => {
      
        var ndx = crossfilter([...response.data.map(d => { 
        d.createdon = new Date(d.createdon.split("T")[0])
        return d
})]);
      var minDate = d3.min(ndx.all(), function(d) {
        return d.createdon;
      });
      var maxDate = d3.max(ndx.all(), function(d) {
        return d.createdon;
      });
      var xDateDimension = ndx.dimension(d => d3.timeMonth(d.createdon));
      var xDateDimensionGroup = xDateDimension.group();

   
     

       var series = new dc.CompositeChart("#lineTrends");

     
      series
        .compose([
            new dc.LineChart(series)
                .dimension(xDateDimension)
                .colors('red')
                .group(grp1, "Top Line")
                .dashStyle([2,2]),
            new dc.LineChart(series)
                .dimension(xDateDimensionvbv )
                .colors('blue')
                .group(grp2, "Bottom Line")
                .dashStyle([5,5])
            ])
        .height(900)
        .width(480)
        .brushOn(false)
        .xAxisLabel("Month, Year")
        .clipPadding(10)
        .renderHorizontalGridLines(true)
        .seriesAccessor(function(d) {
         
          return d.key[0];
        })
        .keyAccessor(function(d) {
           
          return d.key;
        })
        .valueAccessor(function(d) {
           
          return d.value;
        })
        .turnOnControls(true)
        .colorAccessor(d => {
          return d.value;
        })
        .ordinalColors(["#ff9f4a"])
        .title(d => `${d.value} disasters occured`);

      series.margins().left += 20;
      series.margins().bottom += 20;
      series.margins().top += 20;

      series.clipPadding(10).elasticY(true);

      series.xAxis().tickFormat(d3.timeFormat("%b,%Y"));
  dc.renderAll();
      //##################################
      // var xf = crossfilter(this.data);
      // var identifierDimension = xf.dimension(function(d) {
      //   return d.group;
      // });
     

      // new dc.SelectMenu("#identifier-c", group)
      //   .dimension(identifierDimension)
      //   .group(identifierDimension.group())

      //   .promptText("SELECT")
      //   .on("postRender", function() {
      //     d3.select(".dc-select-menu").attr(
      //       "class",
      //       "btn btn-outline-secondary border p-2 mr-4 text-left"
      //     );
      //   })
      //   .on("renderlet", chart => {
      //     d3.selectAll(".dc-select-option").text(function(d) {
      //       var key = d.key;
      //       return key.split("-")[1];
      //     });
      //   })
      //   .on("filtered", function(chart, filter) {
      //     parent.filter = filter;
      //     parent.drawGraph(filter);
      //   });

      //#################################################################################

      //dc.renderAll(group);
    });
  },
  methods: {
    async loadForms() {
      var response = await MongoReports.getUnapprovedDinrs().then(response => {
        let unsigneddata = response.data
          .filter(data =>
            data.district.admin2_name_en.includes(
              this.$session.get("user").admin2_name_en
            )
          )
          .filter(
            item =>
              !item ||
              ((!item.isApproved &&
                item.isApproved == false &&
                !item &&
                (!item.isRejected && item.isRejected == false)) ||
                (!item.approvalMetadata ||
                  !item.approvalMetadata.signature ||
                  item.approvalMetadata.signature.length == 0))
          )
          .map(obj => ({
            ...obj,
            hours: moment().diff(moment(obj.createdon), "hours"),
            date: moment(obj.createdon).format("DD-MM-YYYY")
          }));
        this.pushUnsigned(unsigneddata);

        let unapprovedData = response.data
          .filter(data =>
            data.district.admin2_name_en.includes(
              this.$session.get("user").admin2_name_en
            )
          )
          .filter(
            item =>
              (!item.isApproved || item.isApproved == false) &&
              (!item.isRejected || item.isRejected == false) &&
              item.approvalMetadata &&
              item.approvalMetadata.signature &&
              item.approvalMetadata.signature.length > 0
          )
          .map(obj => ({
            ...obj,
            hours: moment().diff(moment(obj.createdon), "hours"),
            date: moment(obj.createdon).format("DD-MM-YYYY")
          }));
        this.pushUnApproved(unapprovedData);
        return response;
      });
      var response2 = await MongoReports.getDinrs().then(response => {
        let submittedData = response.data.map(obj => ({
          ...obj,
          hours: moment().diff(moment(obj.createdon), "hours"),
          date: moment(obj.createdon).format("DD-MM-YYYY")
        }));
        let district = this.$session.get("user").admin2_name_en;
        this.pushSubmitted(submittedData);
        return response;
      });
      return response;
    },
    pushUnsigned(data) {
      var type = "1-Unsigned";
      data.forEach(form => {
        if (form.hours <= 24) {
          this.data.push({ ...form, type: type,group: "1-last 24 Hours" });
         
        }
        if (form.hours <= 168) {
          this.data
            .push({ ...form, type: type,group: "2-last 7 day" });
        }
        if (form.hours <= 720) {
          this.data.push({ ...form, type: type,group: "3-last 30 day" });
          
        }
        if (form.hours <= 2160) {
          this.data.push({ ...form, type: type,group: "4-last 90 days" });
          
        }
        if (form.hours <= 8760) {
           this.data.push({ ...form, type: type,group: "5-last 12 Months" });
          
        }
         this.data.push({ ...form, type: type,group: "6-Lifetime" });
      });
      return;
    },
    pushUnApproved(data) {
      var type = "2-UnApproved";
      data.forEach(form => {
        if (form.hours <= 24) {
          this.data.push({ ...form, type: type,group: "1-last 24 Hours" });
          
        }
        if (form.hours <= 168) {
          this.data.push({ ...form, type: type,group: "2-last 7 day" });
          
        }
        if (form.hours <= 720) {
          this.data.push({ ...form, type: type,group: "3-last 30 day" });
          
        }
        if (form.hours <= 2160) {
            this.data.push({ ...form, type: type,group: "4-last 90 days" });
         
        }
        if (form.hours <= 8760) {
          this.data.push({ ...form, type: type,group: "5-last 12 Months" });
         
        }
         this.data.push({ ...form, type: type,group: "6-Lifetime" });
        
      });
      return;
    },
    pushSubmitted(data) {
      var type = "3-Submitted";
      data.forEach(form => {
        if (form.hours <= 24) {
          this.data.push({ ...form, type: type,group: "1-last 24 Hours" });
         
        }
        if (form.hours <= 168) {
          this.data.push({ ...form, type: type,group: "2-last 7 day" });
         
        }
        if (form.hours <= 720) {
          this.data.push({ ...form, type: type,group: "3-last 30 day" });
         
        }
        if (form.hours <= 2160) {
           this.data.push({ ...form, type: type,group: "4-last 90 days" });
          
        }
        if (form.hours <= 8760) {
          this.data.push({ ...form, type: type,group: "5-last 12 Months" })
         
        }
        this.data.push({ ...form, type: type,group: "6-Lifetime" })
       
      });
      return;
    },
    drawGraph(filter) {
     // console.log("data",this.data)
     
    

     
//dc.renderAll(group);
 
    }
  }
};
</script>

<style>
#barTrends {
  font-family: Arial, Helvetica, sans-serif;
  border-collapse: collapse;
  width: 90%;
  margin-left: 5%;
}

#barTrends td,
#barTrends th {
  border: 1px solid #ddd;
  padding: 8px;
}

#barTrends tr:nth-child(even) {
  background-color: #f2f2f2;
}

#barTrends tr:hover {
  background-color: #ddd;
}

#barTrends th {
  padding-top: 12px;
  padding-bottom: 12px;
  text-align: left;
  background-color: #04aa6d;
  color: white;
}
#identifier-c select {
  padding-top: 12px !important;
  padding-bottom: 12px !important;
}
</style>
