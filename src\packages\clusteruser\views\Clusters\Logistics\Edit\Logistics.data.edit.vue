<template>
  <div>
    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Edit</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">Reports</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">Edit</li>
            </ol>
          </nav>
        </div>
        <div class="col-lg-6 col-5 text-right"></div>
      </div>
    </base-header>
    <div
      class="container-fluid mt--6"
      v-for="(item, index) in report"
      :key="index"
    >
      <div class="row justify-content-center">
        <div class="col-lg-8 card-wrapper">
          <!-- Grid system -->
          <card class="card">
            <!-- Card header -->
            <h3 slot="header" class="mb-0">Edit Report Form</h3>

            <form class="needs-validation" @submit.prevent="validate">
              <div class="row">
                <div class="col-lg-12">
                  <p class="mb-0 text-bold"><b>Disaster Details</b></p>
                </div>
              </div>

              <div class="form-row">
                <div class="col-md-6">
                  <base-input
                    label="Date of Occurence"
                    placeholder="Date of Occurence"
                    v-model="item.disasterdate"
                  ></base-input>
                </div>

                <div class="col-md-6">
                  <base-input
                    label="TA Affected"
                    placeholder="TA Affected"
                    disabled
                    v-model="item.TA.admin3_name_en"
                  ></base-input>
                </div>
              </div>

              <div class="row">
                <div class="col-lg-12">
                  <p class="mb-0 text-bold"><b>Structure Affected</b></p>
                </div>
              </div>

              <div class="form-row">
                <div class="col-md-3">
                  <base-input label="Structure Affected">
                    <select
                      class="form-control"
                      v-model="item.impact.structure_affected.response"
                    >
                      <option
                        v-for="item in structrures"
                        :key="item"
                        :value="item"
                        >{{ item }}</option
                      >
                    </select>
                  </base-input>
                </div>
                <div class="col-md-3">
                  <base-input
                    label="Structure Name"
                    placeholder="Structure Name"
                    v-model="item.impact.structure_affected.name"
                  ></base-input>
                </div>

                <div class="col-md-3">
                  <base-input
                    label="Link #"
                    v-model="item.impact.structure_affected.linknumber"
                    v-if="item.impact.structure_affected.response == 'Road'"
                    placeholder="Link #"
                  ></base-input>
                </div>
                <div class="col-md-3">
                  <base-input
                    label="Length of damage"
                    v-if="item.impact.structure_affected.response == 'Road'"
                    v-model="item.impact.structure_affected.length_of_damage"
                    placeholder="Length of damage"
                  ></base-input>
                </div>

                <div class="col-md-3">
                  <base-input
                    label="Other Structure Name"
                    type="number"
                    v-if="item.impact.structure_affected.response == 'Other'"
                    v-model="item.impact.structure_affected.other_name"
                    placeholder="Other Structure Name"
                  ></base-input>
                </div>
              </div>

              <div class="row">
                <div class="col-lg-12">
                  <p class="mb-0 text-bold"><b>Site Details</b></p>
                </div>
              </div>

              <div class="form-row">
                <div class="col-md-4">
                  <base-input label="Maximum Vehicle Size">
                    <select
                      class="form-control"
                      v-model="item.impact.vehicle_size.response"
                    >
                      <option
                        v-for="item in vehicle_size"
                        :key="item"
                        :value="item"
                        >{{ item }}</option
                      >
                    </select>
                  </base-input>
                </div>
                <div class="col-md-4">
                  <base-input label="Condition of Structure">
                    <select
                      class="form-control"
                      v-model="item.impact.condition_of_structrure.response"
                    >
                      <option
                        v-for="item in condition_of"
                        :key="item"
                        :value="item"
                        >{{ item }}</option
                      >
                    </select>
                  </base-input>
                </div>
                <div class="col-md-4">
                  <base-input label="Surface Condition of Site">
                    <select
                      class="form-control"
                      v-model="item.impact.condition_of_site.response"
                    >
                      <option
                        v-for="item in surface_condition"
                        :key="item"
                        :value="item"
                        >{{ item }}</option
                      >
                    </select>
                  </base-input>
                </div>
              </div>

              <div class="row">
                <div class="col-lg-12">
                  <p class="mb-0"><b>Telecom Infrastructure Affected</b></p>
                </div>
              </div>

              <div class="form-row">
                <div class="col-md-3">
                  <base-input label="Structure Affected">
                    <select
                      class="form-control"
                      v-model="item.impact.tele_infrastructure.response"
                    >
                      <option
                        v-for="item in infrastructure"
                        :key="item"
                        :value="item"
                        >{{ item }}</option
                      >
                    </select>
                  </base-input>
                </div>

                <div class="col-md-3">
                  <base-input
                    label="Service Provider"
                    placeholder="Service Provider"
                    v-model="item.impact.tele_infrastructure.service_provider"
                  ></base-input>
                </div>

                <div class="col-md-3">
                  <base-input label="Infrastructure State">
                    <select
                      class="form-control"
                      v-model="item.impact.tele_infrastructure.state"
                    >
                      <option
                        v-for="item in function_of"
                        :key="item"
                        :value="item"
                        >{{ item }}</option
                      >
                    </select>
                  </base-input>
                </div>

                <div class="col-md-3">
                  <base-input
                    label="Comments"
                    placeholder="Comments"
                    v-model="item.impact.tele_infrastructure.comments"
                  ></base-input>
                </div>
              </div>

              <div class="row">
                <div class="col-lg-12">
                  <p class="mb-0 text-bold"><b>Cordinates of site</b></p>
                </div>
              </div>

              <div class="form-row">
                <div class="col-md-6">
                  <base-input
                    label="GPS Latitude"
                    placeholder="GPS Latitude"
                    type="number"
                    v-model="item.impact.site_cordinates.gps_lat"
                  ></base-input>
                </div>

                <div class="col-md-6">
                  <base-input
                    label="GPS Longitude"
                    placeholder="GPS Longitude"
                    type="number"
                    v-model="item.impact.site_cordinates.gps_long"
                  ></base-input>
                </div>
              </div>

              <base-button type="primary" native-type="submit" @click="updateData(item, report)"
                >Update</base-button
              >
            </form>
          </card>
        </div>
      </div>
    </div>
  </div>
</template>

<script src="./Logistics.data.edit.js" />
