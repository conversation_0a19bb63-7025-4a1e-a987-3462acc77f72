
export default {
  $_veeValidate: {
    validator: 'new'
  },
  components: {},
  props: ['environment', 'max'],
  data () {
    return {
      maxValue: this.max,
      dataset: {},
      valid: false,
      impact_on_environment: [],
      response_needed: '',
      radio: {
        radio1: 'radio1',
        radio2: 'radio3'
      },
      questions: [
        { name: 'Were forests impacted?' },
        { name: 'Was wildlife impacted?' },
        { name: 'Was there soil erosion?' },
        { name: 'Were shrines impacted?' },
        { name: 'Were graveyards impacted?' }
      ]
    }
  },
  computed: {},

  methods: {
    addItemRow (member, array_name, static_data = [], key_value) {
      this.$emit(
        'addItemRow',
        'environment',
        member,
        array_name,
        static_data,
        key_value
      )
    },
    removeItemRow (member, array_name, static_data, index, key_value) {
      this.$emit(
        'removeItemRow',
        'environment',
        member,
        array_name,
        static_data,
        index,
        key_value
      )
    },
    save () {
      this.environment.response_needed = this.response_needed
      this.environment.urgent_response_needed = this.urgent_response_needed
      this.environment.impact_on_environment = this.impact_on_environment.filter(
        value => Object.keys(value).length !== 0
      )

      this.$emit('save', this.environment, 'environment')
    },
    autosave () {
      this.environment.response_needed = this.response_needed
      this.environment.urgent_response_needed = this.urgent_response_needed
      this.environment.impact_on_environment = this.impact_on_environment.filter(
        value => Object.keys(value).length !== 0
      )

      this.$emit('autosave', this.environment, 'environment')
    }
  },

  beforeMount () {
    typeof this.environment !== 'undefined' ? this.environment : {}

    setInterval(this.autosave, 1200)

    let dataset = JSON.parse(localStorage.getItem('environment').data)

    this.impact_on_environment =
      typeof dataset.impact_on_environment === 'undefined'
        ? this.impact_on_environment
        : dataset.impact_on_environment

    this.response_needed =
      typeof dataset.response_needed === 'undefined'
        ? this.response_needed
        : dataset.response_needed

    this.urgent_response_needed =
      typeof dataset.urgent_response_needed === 'undefined'
        ? this.urgent_response_needed
        : dataset.urgent_response_needed
  }
}
