var amqp = require("amqplib/callback_api");
import swal from "sweetalert2";
import Swal from "sweetalert2";
import { dinrforms } from "../../api/forms/dinrforms";
import { draforms } from "../../api/forms/draforms";
import { auth } from "@/api/auth";

export default {
  data() {
    return {
      /*excel test*/
      numberOfTAs: "",
      dinrFormsData: {},
      bufferDraFormsData: [],
      draFormsData: [],
      download: [],
      downloadData: [],
      otherData: [],

      propsToSearch: ["uuid", "disaster"],
      tableColumns: [
        {
          prop: "disaster",
          label: "Disaster",
          minWidth: 80,
          sortable: true
        }
      ],
      tableData: [],
      selectedRows: []
    };
  },
  methods: {
    async handleSubmit(row) {
      let self = this;
      var { value: password } = await swal({
        title: "Enter your password",
        text: `This is to endorse that you ${
          this.$session.get("user").firstName
        } ${this.$session.get("user").lastName} (${
          this.$session.get("user").email
        }) want to submit this form for approval.`,
        input: "password",
        type: "warning",
        buttonsStyling: false,
        confirmButtonClass: "btn btn-success btn-fill",
        cancelButtonClass: "btn btn-danger btn-fill",
        confirmButtonText: "Yes, submit it!",
        showCancelButton: true,
        inputPlaceholder: "password",
        inputAttributes: {
          autocapitalize: "off",
          autocorrect: "off",
          required: true
        }
      }).then(result => {
        if (result.value != null) {
          auth
            .login({
              email: this.$session.get("user").email,
              password: result.value
            })
            .then(
              response => {
                this.handleRequest(row);
              },
              reason => {
                swal({
                  text:
                    "Failed submit for possible invalid password (  password : " +
                    reason +
                    " )",
                  type: "error",
                  toast: true,
                  position: "top-end",
                  confirmButtonClass: "btn btn-success btn-fill",
                  buttonsStyling: false
                });
              }
            );
        }
      });
    },

    processRequest(id) {
      dinrforms.update({ status: "2", dra: this.draFormsData, _id: id }).then(
        response => {
          this.submitFormToQueue(id);
        },
        reason => {
          Swal.fire({
            title: "Failed to submit form",
            text: "possible invalid data (" + reason + ")",
            type: "error",
            animation: false
          });
        }
      );
    },

    submitFormToQueue(id) {
      let self = this;
      let RBMQ_URL = process.env.VUE_APP_RBMQ_URL;
      dinrforms.get(id).then(response => {
        amqp.connect(RBMQ_URL, function(error0, connection) {
          if (error0 === null) {
            connection.createChannel(function(error1, channel) {
              var queue = "unapproved_dinr_forms";

              channel.assertQueue(queue, {
                durable: true
              });

              var dinr = response.data;
              dinr.isApproved = false;
              dinr.isRejected = false;
              dinr.status = "1";
              channel.sendToQueue(queue, Buffer.from(JSON.stringify(dinr)));
              console.log(" [x] Sent");

              Swal.fire({
                title: "Succesfully submitted form for approval",
                text:
                  "disaster impact and needs report (DINR) form has been submitted for approval",
                type: "success",
                animation: false
              });
              self.loadData();
            });
          } else {
            Swal.fire({
              title: "Failed to submit form",
              text: "Possible network fail (Could not connect to queue server)",
              type: "error",
              animation: false
            });
          }
        });
      });
    },

    async handleRequest(row) {
      var { data } = await draforms.query({ dinrFormId: row._id });
      this.draFormsData = data;
      this.processRequest(row._id);
    },

    loadData() {
      dinrforms.get().then(response => {
        this.tableData = response.data.filter(
          data =>
            data.status.includes("1") &&
            data.account.email.includes(this.$session.get("user").email)
        )

      });
    },

    refresh(){
      MongoReports.getDinrs().then(response => {
        this.tableData = response.data.filter(
          data =>
            data.isApproved === true &&
            data.account.email.includes(this.$session.get("user").email)
        )
      });
    }
  }
};
