<template>
  <div>
    <!--Header-->
    <div class="header bg-success pt-5 pb-7">
      <div class="container">
        <div class="header-body">
          <div class="row align-items-center">
            <div class="col-lg-6">
              <div class="pr-5">
                <h1 class="display-2 text-white font-weight-bold mb-0">DRMIS</h1>
                <h2 class="display-4 text-white font-weight-light">Disaster Risk Management Information System</h2>
                <p class="text-white mt-4">A mordern disaster data collection system .</p>
                <div class="mt-5">
                  <router-link to="/login" class="btn btn-neutral my-2">Sign in</router-link>
                </div>
              </div>
            </div>
            <div class="col-lg-6">
              <div class="row pt-5">
                <div class="col-md-6">
                  <div class="card">
                    <div class="card-body">
                      <div class="icon icon-shape bg-gradient-red text-white rounded-circle shadow mb-4">
                        <i class="ni ni-active-40"></i>
                      </div>
                      <h5 class="h3">Disaster Data Collection</h5>
                      <p>Allows data collection of disaster data based on the DINR and DRA Forms.</p>
                    </div>
                  </div>
                  <div class="card">
                    <div class="card-body">
                      <div class="icon icon-shape bg-gradient-info text-white rounded-circle shadow mb-4">
                        <i class="ni ni-active-40"></i>
                      </div>
                      <h5 class="h3">Reports</h5>
                      <p>Auto generated reports .</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-6 pt-lg-5 pt-4">
                  <div class="card mb-4">
                    <div class="card-body">
                      <div class="icon icon-shape bg-gradient-success text-white rounded-circle shadow mb-4">
                        <i class="ni ni-active-40"></i>
                      </div>
                      <h5 class="h3">Inforgarphics</h5>
                      <p>Simple charts and infographics powered by Metabase.</p>
                    </div>
                  </div>
                  <div class="card mb-4">
                    <div class="card-body">
                      <div class="icon icon-shape bg-gradient-warning text-white rounded-circle shadow mb-4">
                        <i class="ni ni-active-40"></i>
                      </div>
                      <h5 class="h3">Powerful Database</h5>
                      <p>uses advanced database tools to manage backward and forward looking data</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <section class="py-7">
      <div class="container">
        <div class="row row-grid justify-content-center">
          <div class="col-lg-8 text-center">
            <div class="text-center">
              <h4 class="display-4 mb-5 mt-5">Technologies used</h4>
              <div class="row justify-content-center">
                <div class="col-md-2 col-3 my-2">
                  <el-tooltip placement="top" content="Bootstrap 4 - Most popular front-end component library">
                    <a href="https://www.creative-tim.com/product/argon-dashboard?ref=vadp-index-page" target="_blank">
                      <img src="https://s3.amazonaws.com/creativetim_bucket/tim_static_images/presentation-page/bootstrap.jpg" class="img-fluid rounded-circle shadow shadow-lg--hover">
                    </a>
                  </el-tooltip>
                </div>
                <div class="col-md-2 col-3 my-2">
                  <el-tooltip placement="top" content="Vue.js - The progressive javascript framework">
                    <a href="https://www.creative-tim.com/product/vue-argon-dashboard?ref=vadp-index-page" target="_blank">
                      <img src="https://s3.amazonaws.com/creativetim_bucket/tim_static_images/presentation-page/vue.jpg" class="img-fluid rounded-circle">
                    </a>
                  </el-tooltip>
                </div>
                <div class="col-md-2 col-3 my-2">
                  <el-tooltip placement="top" content="[Coming Soon] Sketch - Digital design toolkit">
                    <a href=" https://www.sketchapp.com/" target="_blank">
                      <img src="https://s3.amazonaws.com/creativetim_bucket/tim_static_images/presentation-page/sketch.jpg" class="img-fluid rounded-circle opacity-3">
                    </a>
                  </el-tooltip>
                </div>
                <div class="col-md-2 col-3 my-2">
                  <el-tooltip placement="top" content="[Coming Soon] Adobe Photoshop - Software for digital images manipulation">
                    <a href=" https://www.adobe.com/products/photoshop.html" target="_blank">
                      <img src="https://s3.amazonaws.com/creativetim_bucket/tim_static_images/presentation-page/ps.jpg" class="img-fluid rounded-circle opacity-3">
                    </a>
                  </el-tooltip>
                </div>
                <div class="col-md-2 col-3 my-2">
                  <el-tooltip placement="top" content="Angular - One framework. Mobile ">
                    <a href="https://www.creative-tim.com/product/argon-dashboard-angular?ref=vadp-index-page" target="_blank">
                      <img src="https://s3.amazonaws.com/creativetim_bucket/tim_static_images/presentation-page/angular.jpg" class="img-fluid rounded-circle opacity-3">
                    </a>
                  </el-tooltip>
                </div>
                <div class="col-md-2 col-3 my-2">
                  <el-tooltip placement="top" content="React - A JavaScript library for building user interfaces">
                    <a href="https://www.creative-tim.com/product/argon-dashboard-react?ref=vadp-index-page" target="_blank">
                      <img src="https://s3.amazonaws.com/creativetim_bucket/tim_static_images/presentation-page/react.jpg" class="img-fluid rounded-circle">
                    </a>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>
<script>
  import { Tooltip } from 'element-ui'
  export default {
    name: 'home-page',
    components: {
      [Tooltip.name]: Tooltip
    }
  };
</script>
