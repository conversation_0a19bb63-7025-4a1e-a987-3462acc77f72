import { flatten } from "./flatten";

export const loadFlatdras = async({ commit, rootGetters, state }, payload) => {
    if (state.flatdras.length == 0) {
        let dras = await rootGetters["dras/get"];
        let dinrs = await rootGetters["dinrs/get"];

        //console.log(dinrs, dras);
        //to add more parameters to flattener please add in flatten.js
        let data = await flatten(dinrs, dras, getSum);
        //console.log("flat dras",data)
        commit("loadFlatdras", data);
        return data;
    }

    return state.flatdras;
};

function getSum(total, num) {
    return total + (num ? +num : 0);
}
