<template>
  <div class="content">
    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Disaster</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/districtmanager/dashboard">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">PRELIMINARY REPORTS</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">All</li>
            </ol>
          </nav>
        </div>
        <div class="col-lg-6 col-6 text-right pr-3">
          <base-dropdown
            title-classes="btn btn-sm btn-primary mr-0"
            menu-on-right
            :has-toggle="false"
          >
            <a slot="title" class="dropdown-toggle" type="neutral" size="sm">
              <i class="text-black ni ni-cloud-download-95"></i> EXPORT TO EXCEL
            </a>
            <a class="dropdown-item" @click="downloadExcel('aggregated')"
              >Aggregated</a
            >
            <a class="dropdown-item" @click="downloadExcel('disaggregated')"
              >Disaggregated</a
            >
          </base-dropdown>
          <!-- <base-button title-classes="btn btn-sm btn-primary mr-0" size="sm" type="primary" @click="downloadExcel()">
            <i class="text-black ni ni-cloud-download-95"></i>
            <span class="btn-inner--text">EXPORT TO EXCEL</span>
            <span class="btn-inner--text"></span>
          </base-button> -->
        </div>
        <!-- <base-button size="sm" type="primary" @click="downloadExcel()">
            <span class="btn-inner--icon">
              <i class="text-black ni ni-cloud-download-95  text-right"></i>
            </span>
            <span class="btn-inner--text">EXPORT TO EXCEL</span>
          </base-bu.tton> -->
      </div>
      <!-- Card stats -->
    </base-header>
    <div class="container-fluid mt--6">
      <div>
        <card
          class="no-border-card"
          body-classes="px-0 pb-1"
          footer-classes="pb-2"
        >
          <template slot="header">
            <h3 class="mb-0">Preliminary Reports</h3>
          </template>

          <div>
            <div
              class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
            >
              <el-select
                class="select-primary pagination-select"
                v-model="pagination.perPage"
                placeholder="Per page"
              >
                <el-option
                  class="select-primary"
                  v-for="item in pagination.perPageOptions"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>
              <div>
                <el-select v-model="filter" placeholder="Select Time Range" @change="applyTimeFilter">
                  <el-option
                    v-for="range in dateranges"
                    :key="range"
                    :label="range"
                    :value="range"
                  ></el-option>
                </el-select>


              </div>
              <div>
                <base-input
                  v-model="searchQuery"
                  prepend-icon="fas fa-search"
                  placeholder="Search..."
                ></base-input>
              </div>


            </div>
            <div>

              <b-table
              responsive
              sticky-header
              :striped="striped"
              :bordered="bordered"
              :borderless="borderless"
              :outlined="outlined"
              :small="small"
              :hover="hover"
              :dark="dark"
              :sort-icon="true"
              :fixed="fixed"
              :foot-clone="footClone"
              :no-border-collapse="noCollapse"
              head-variant="light"
              :table-variant="tableVariant"
              :items="filteredItems"
              :fields="tableColumns"
            >
              <!-- Template for Status Cell -->
              <template #cell(status)="row">
                <b-badge variant="danger" v-if="row.item.status == 2">
                  <strong class="text-white">Not Approved</strong>
                </b-badge>
                <b-badge variant="success" v-if="row.item.status == 3">
                  <strong class="text-dark">Approved by DCPC</strong>
                </b-badge>
                <b-badge variant="warning" v-if="row.item.status == 4">
                  <strong class="text-dark">Approved by RRO</strong>
                </b-badge>
              </template>
              <template #cell(actions)="row">
                <base-button
                  class="btn btn-sm btn-info d-flex align-items-center"
                  @click="$router.push({ name: 'DetailPreliminary', params: { id: row.item.idval } })"
                >
                  <i class="ni ni-single-copy-04 mr-2"></i>
                  View Details
                </base-button>
              </template>

            </b-table>

            </div>
          </div>
          <div
            slot="footer"
            class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
          >
            <div class>
              <p class="card-category">
                Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
                <span v-if="selectedRows.length"
                  >&nbsp; &nbsp; {{ selectedRows.length }} rows selected</span
                >
              </p>
            </div>
            <base-pagination
              class="pagination-no-border"
              v-model="pagination.currentPage"
              :per-page="pagination.perPage"
              :total="total"
            ></base-pagination>

          </div>

        </card>
        <!-- <PreliDashboard /> -->
      </div>
    </div>
  </div>
</template>
<script>
import { Table, TableColumn, Select, Option, Button } from 'element-ui'
import RouteBreadCrumb from '@/components/Breadcrumb/RouteBreadcrumb'
import { BasePagination } from '@/components'
import clientPaginationMixin from '@/components/PaginatedTables/clientPaginationMixin'
import { admin2s } from '../api/location/admin2s'
import { flatten } from '../../../store/flatdinrs/flatten'

import {
  generateExcel,
  generateTAGVHExcel,
  generateDisasterSummaryExcel
} from '../../../util/generateExcel'
import { generateCSV } from '../../../util/generateCSV'
import utils from '../../../util/dashboard'
import { MongoReports } from '../api/MongoReports'
import downloadexcel from 'vue-json-excel'
import JSZip from 'jszip'
import FileSaver from 'file-saver'
import moment from 'moment'
import swal from 'sweetalert2'
import dateformat from 'dateformat'
import { mapGetters, mapActions } from 'vuex'

import { prelimreports } from '../../districtmanager/api/forms/prelimreports.js'
import { download } from '../../../util/download'
import { user } from '../../../api/user'

import globalmixin from '../../../mixins/globalmixin'

export default {
  mixins: [clientPaginationMixin, globalmixin],
  components: {
    BasePagination,
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    [Table.name]: Table,
    downloadexcel,
    [TableColumn.name]: TableColumn
  },
  data () {
    return {
      showModal: false,
      selectedRow: null,
      propsToSearch: ['ta', 'gvh', 'date'],
      searchQuery: '',
      filter: '',
      dateranges: ['24hrs', '48hrs', '1 Week', '2 Weeks', 'Lifetime'],
      images: [],
      dinrFormsData: {},
      villagesArray: [],
      campsArray: [],
      bufferDraFormsData: [],
      finalExcelData: [],
      TAarray: [],
      draFormsData: [],
      download: [],
      test: [],
      gvhDATA: [],
      allforms: [],
      districtsCopy: [],
      downloadData: [],
      testarray: [],
      TaData: [],
      premreports: [],
      admin2sData: [],
      AllDisasterSummaryExcel: [],
      disablebutton: false,

      gridOptions: {},
      tableColumns: [
        {
          key: 'district',
          label: 'District',
          sortable: false
        },
        {
          key: 'ta',
          label: 'TA',
          sortable: false
        },
        {
          key: 'gvh',
          label: 'GVH',
          sortable: false
        },

        {
          key: 'Date',

          label: 'Occured On & Submmited at ',
          sortable: false
        },
        {
          key: 'mhh',
          label: 'MHH',
          sortable: false
        },
        {
          key: 'fhh',
          label: 'FHH',
          sortable: false
        },
        // {
        //   key: 'injured',
        //   label: 'Injuries',
        //   sortable: false
        // },
        // {
        //   key: 'dead',
        //   label: 'Deaths',
        //   sortable: false
        // },
        // {
        //   key: 'missing',
        //   label: 'Missing',
        //   sortable: false
        // },
        // {
        //   key: 'pregnant',
        //   label: 'PW',
        //   sortable: false
        // },
        // {
        //   key: 'lactactingwomen',
        //   label: 'LW',
        //   sortable: false
        // },
        // {
        //   key: 'underfive',
        //   label: 'U5',
        //   sortable: false
        // },

        // {
        //   key: 'disabled',
        //   label: 'disabled',
        //   sortable: false
        // },

        // {
        //   key: 'numberofcamps',
        //   label: '# of Camps',
        //   sortable: true
        // },

        // {
        //   key: 'campnames',
        //   label: '# of Camps',
        //   sortable: true
        // },

        {
          key: 'actions',
          label: 'Actions'
        }
      ],
      tableData: [],
      selectedRows: [],
      totalinjured: 0,
      totaldeaths: 0,
      totalmissing: 0
    }
  },

  computed: {
    filteredItems() {
    // Apply base data from tableData
    let results = this.tableData;

    // Apply time filtering based on selected time range
    let hoursLimit = Infinity; // Default to 'Lifetime'
    if (this.filter === '24hrs') {
      hoursLimit = 24;
    } else if (this.filter === '48hrs') {
      hoursLimit = 48;
    } else if (this.filter === '72hrs') {
      hoursLimit = 72;
    } else if (this.filter === '1 Week') {
      hoursLimit = 168;
    } else if (this.filter === '2 Weeks') {
      hoursLimit = 336;
    }

    // Filter by time
    results = results.filter(item => {
      if (!item.dateof) return false; // Skip if date is missing
      const hoursDifference = moment().diff(moment(item.dateof), 'hours');
      return hoursDifference <= hoursLimit;
    });

    // Apply search filter
    if (this.searchQuery) {
      const searchLower = this.searchQuery.toLowerCase();
      results = results.filter(item =>
        this.propsToSearch.some(key =>
          String(item[key]).toLowerCase().includes(searchLower)
        )
      );
    }

    // Group records by idval and aggregate MHH and FHH
    const groupedResults = results.reduce((acc, item) => {
      if (!acc[item.idval]) {
        // Initialize group with the first record
        acc[item.idval] = { ...item };
      } else {
        // Aggregate MHH and FHH values
        acc[item.idval].mhh += item.mhh || 0;
        acc[item.idval].fhh += item.fhh || 0;
      }
      return acc;
    }, {});

    // Convert groupedResults back to array and apply pagination
    const groupedArray = Object.values(groupedResults);
    const start = (this.pagination.currentPage - 1) * this.pagination.perPage;
    const end = start + this.pagination.perPage;

    return groupedArray.slice(start, end);
  },

  // Update total to reflect the total number of grouped records
  total() {
    // Calculate the total count of unique idval records
    const groupedResults = this.tableData.reduce((acc, item) => {
      if (!acc[item.idval]) {
        acc[item.idval] = true;
      }
      return acc;
    }, {});

    return Object.keys(groupedResults).length;
  }
},

  methods: {
    downloadExcel (type) {
      if (type == 'aggregated') {
        // generate aggregated excel sheet
        prelimreports.getReportsLatest().then(response => {
          let district = this.$session.get('user').admin2_name_en
          let filterdData = []

          for (let i = 0; i < response.length; i++) {
            if (response[i].user.district.admin2_name_en == district) {
              filterdData.push(response[i])
            }
          }

          let summedValues = Object.values(
            filterdData.reduce(function (r, e) {
              let key = e._id.disasterdate + '|' + e._id.ta
              if (!r[key]) r[key] = e
              else {
                r[key].fhh += parseInt(Number(e.fhh))
                r[key].mhh += parseInt(Number(e.mhh))
                r[key].injured += parseInt(Number(e.injured))
                r[key].dead += parseInt(Number(e.dead))
                r[key].missing += parseInt(Number(e.missing))
                r[key].pregnant += parseInt(Number(e.pregnant))
                r[key].lactactingwomen += parseInt(Number(e.lactactingwomen))
                r[key].underfive += parseInt(Number(e.underfive))
                r[key].disabled += parseInt(Number(e.disabled))
              }
              return r
            }, {})
          )
          for (let i in summedValues) {
            this.premreports.push({
              District: summedValues[i].user.district.admin2_name_en,
              TA: summedValues[i]._id.ta,

              GVH: summedValues[i].gvh,

              Disaster: summedValues[i]._id.disaster,
              Date: moment(summedValues[i].disasterdate).format('DD-MM-YYYY'),
              MHH: summedValues[i].mhh,
              FHH: summedValues[i].fhh,
              Injuries: summedValues[i].injured,
              Deaths: summedValues[i].dead,
              Missing: summedValues[i].missing,

              PW: summedValues[i].pregnant,
              LW: summedValues[i].lactactingwomen,
              U5: summedValues[i].underfive,
              disabled: summedValues[i].disabled,
              // dcpcApprover: summedValues[i].dcpcApprover,
              //  dcApprover: summedValues[i].dcApprover,

              sheet: 'Preliminary reports'
            })
          }

          generateTAGVHExcel(this.premreports)
        })
      } else {
        // generate disagregated excel sheet
        var data = this.tableData.map(item => {
          return {
            District: item.user.district.admin2_name_en,

            TA: item.TA,
            GVH: item.gvh,
            Date: item.Date,
            MHH: item.mhh,
            FHH: item.fhh,
            Injuries: item.injured,
            Deaths: item.dead,
            Missing: item.missing,
            PW: item.pregnant,
            LW: item.lactactingwomen,
            U5: item.underfive,

            numberofcamps: item.numberofcamps,

            campnames: item.campnames,

            dcpcApprover: item.dcpcApprover,

            dcApprover: item.dcApprover,
            editby: item.editby,
            editon: moment(item.edittime).format('DD-MM-YYYY hh:mm:ss'),
            sheet: 'Preliminary reports'
          }
        })
        this.premreports = data
        generateTAGVHExcel(this.premreports)
      }
    },

    hasCoverText (index) {
      if (index.covertext === '') {
        return false
      } else {
        return true
      }
    },

    fetchDisaster (district) {
      return 0
    },

    editRecord (data) {
      this.$router.push({
        path: '/districtmanager/PrelimReports/' + data.item.id
      })
    },

    review (index) {
      this.$router.push({
        path: '/districtmanager/detailsprelimreport/' + index.id
      })
    },

    addcover (row) {
      this.selectedRow = row
      this.showModal = true
    },
    updateCoverText () {
      //console.log("selected", this.selectedRow)
      prelimreports
        .updateCoverText({
          _id: this.selectedRow.id,
          covertext: this.selectedRow.covertext
        })
        .then(
          () => {
            swal.fire({
              title: 'Done Succesfully',
              text: 'Your report cover has been saved',
              type: 'success',
              animation: false
            })

            this.showModal = false
          },
          reason => {
            swal.fire({
              title: 'Failed to submit cover',
              text: 'possible invalid data (' + reason + ')',
              type: 'error',
              animation: false
            })
          }
        )
    },

    async handleRequest (data) {
      prelimreports.addAttribute(data, 'approvalMetadata').then(
        () => {
          swal.fire({
            title: 'Approved Succesfully',
            text: 'Your decision has been saved on this disaster',
            type: 'success',
            animation: false
          })

          this.loadData()
        },
        reason => {
          swal.fire({
            title: 'Failed to submit form',
            text: 'possible invalid data (' + reason + ')',
            type: 'error',
            animation: false
          })
        }
      )
    },
    reviewSummary (row, index) {
      try {
        this.handleRequest({
          isApproved: false,
          status: '5',
          dcApprover:
            this.$session.get('userObj').firstName +
            ' ' +
            this.$session.get('userObj').lastName,
          _id: index.id
        })
      } finally {
        this.getAuthToken(row)
      }
    },

    formatedDate (data) {
      let finalDate = dateformat(data, 'dd-mm-yyyy')
      return finalDate
    },

    // loadData () {
    //   prelimreports.getReportsLatest().then(response => {
    //     let district = this.$session.get('user').admin2_name_en
    //     let impacts = []
    //     const rows = []
    //     const processedRows = []

    //     let filteredData = response.filter(
    //       data => data.user.district.admin2_name_en == district
    //     )

    //     for (const item of filteredData) {

    //         const row = {
    //           ...item,
    //           TA: item._id.ta,
    //           disaster: item._id.disaster,
    //           id: item._id,
    //           idval: item.idval,
    //           createdon: item.createdon,
    //           injured: item.injured == undefined ? 0 : parseInt(item.injured),
    //           gvh: item._id.gvh,

    //           dcpcApprover: item.dcpcApprover,
    //           images: item.images,

    //           numberofcamps: item.numberofcamps,

    //           campnames: item.campnames,

    //           dcApprover: item.dcApprover,
    //           lactactingwomen:
    //             item.lactactingwomen == undefined
    //               ? 0
    //               : parseInt(item.lactactingwomen),
    //           mhh: item.mhh == undefined ? 0 : parseInt(item.mhh),
    //           missing: item.missing == undefined ? 0 : parseInt(item.missing),
    //           dateof: item.disasterdate,
    //           editby: item.edits == undefined ? '' : item.edits.user,
    //           editon: item.edits == undefined ? '' : item.edits.edittime,
    //           timeof: new Date(item.createdon).toLocaleTimeString('it-IT'),
    //           underfive:
    //             item.underfive == undefined ? 0 : parseInt(item.underfive),
    //           pregnant:
    //             item.pregnant == undefined ? 0 : parseInt(item.pregnant),
    //           district: item.user.district.admin2_name_en,
    //           dead: item.dead == undefined ? 0 : parseInt(item.dead),
    //           disabled:
    //             item.disabled == undefined ? 0 : parseInt(item.disabled),
    //           fhh: item.fhh == undefined ? 0 : parseInt(item.fhh),
    //           disasterdate: moment(item.disasterdate).format('DD-MM-YYYY'),
    //           Date:
    //             moment(item.disasterdate).format('DD-MM-YYYY') +
    //             ' ' +
    //             new Date(item.createdon).toLocaleTimeString('it-IT'),
    //           status: item.status
    //         }

    //         impacts.push(row)
    //       }


    //     impacts = impacts.sort((a, b) => a.disaster.localeCompare(b.disaster))
    //     for (const impact of impacts) {
    //       const key = `${impact.TA}${impact.disaster}`
    //       if (!processedRows.includes(key)) {
    //         rows.push(impact)
    //         processedRows.push(key)
    //       } else {
    //         Object.assign(impact, {
    //           TA: '',
    //           district: '',
    //           Date: '',
    //           covertext: ''
    //         })

    //         rows.push(impact)
    //       }
    //     }
    //     this.tableData = rows.map((item, i) => {
    //       return {
    //         ...item,
    //         district: item.district,
    //         ta: item.TA,
    //         gvh: item.gvh,
    //         pregnant: item.pregnant,

    //         dcpcApprover: item.dcpcApprover,
    //         images: item.images,

    //         dcApprover: item.dcApprover,
    //         editby: item.edits == undefined ? '' : item.edits.user,
    //         editon: item.edits == undefined ? '' : item.edits.edittime,
    //         date: item.Date,
    //         id: item.idval,
    //         dateof: item.dateof,
    //         status: item.status,
    //         numberofcamps: item.numberofcamps,

    //         campnames: item.campnames
    //       }
    //     })
    //   })
    // }
    async loadData() {
  try {
    const response = await prelimreports.getReportsLatest();
    const district = this.$session.get("user").admin2_name_en; // Get the district from session

    // Filter data by district
    const filteredData = response.filter(
      (data) => data.user.district.admin2_name_en === district
    );

    let impacts = [];
    const rows = [];

    for (const item of filteredData) {
      const row = {
        ...item,
        TA: item._id.ta,
        disaster: item._id.disaster,
        id: item._id,
        idval: item.idval,
        createdon: item.createdon,
        injured: item.injured == undefined ? 0 : parseInt(item.injured),
        gvh: item._id.gvh,

        dcpcApprover: item.dcpcApprover,
        numberofcamps: item.numberofcamps,
        campnames: item.campnames,
        images: item.images,
        dcApprover: item.dcApprover,

        lactactingwomen:
          item.lactactingwomen == undefined
            ? 0
            : parseInt(item.lactactingwomen),
        mhh: item.mhh == undefined ? 0 : parseInt(item.mhh),
        missing: item.missing == undefined ? 0 : parseInt(item.missing),
        dateof: item.disasterdate,
        editby: item.edits == undefined ? "" : item.edits.user,
        editon: item.edits == undefined ? "" : item.edits.edittime,
        timeof: new Date(item.createdon).toLocaleTimeString("it-IT"),
        underfive: item.underfive == undefined ? 0 : parseInt(item.underfive),
        pregnant: item.pregnant == undefined ? 0 : parseInt(item.pregnant),
        district: item.user.district.admin2_name_en,
        dead: item.dead == undefined ? 0 : parseInt(item.dead),
        disabled: item.disabled == undefined ? 0 : parseInt(item.disabled),
        fhh: item.fhh == undefined ? 0 : parseInt(item.fhh),
        disasterdate: moment(item.disasterdate).format("DD-MM-YYYY"),
        Date:
          moment(item.disasterdate).format("DD-MM-YYYY") +
          " " +
          new Date(item.createdon).toLocaleTimeString("it-IT"),
        status: item.status,
      };

      impacts.push(row);
    }

    // Sort impacts by disaster date (descending)
    impacts = impacts.sort((a, b) => new Date(b.dateof) - new Date(a.dateof));

    // Push sorted impacts into rows
    for (const impact of impacts) {
      rows.push(impact);
    }

    // Map rows to table data
    this.tableData = rows.map((item) => ({
      ...item,
      district: item.district,
      ta: item.TA,
      gvh: item.gvh,
      pregnant: item.pregnant,
      date: item.Date,
      id: item.idval,
      dateof: item.dateof,
      status: item.status,
      numberofcamps: item.numberofcamps,
      campnames: item.campnames,
      dcpcApprover: item.dcpcApprover,
      dcApprover: item.dcApprover,
    }));
  } catch (error) {
    console.error("Error loading data:", error);
  }
}

  },
  created () {
    this.loadData()
  },
  async mounted () {
    admin2s.get().then(response => {
      this.admin2sData = response.data
    })
  }
}
</script>
<style>
.no-border-card .card-footer {
  border-top: 0;
}

button[disabled] {
  cursor: not-allowed;
}
</style>
