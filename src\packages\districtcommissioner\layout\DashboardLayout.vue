<template>
  <div class="wrapper">
    <notifications></notifications>
    <side-bar>
      <template slot-scope="props" slot="links">
        <sidebar-item
          :link="{
            name: 'Dashboard',
            icon: 'ni ni-shop text-default',
            path: '/districtcommissioner/dashboard'
          }"
        ></sidebar-item>
        <sidebar-item
          :link="{
            name: 'Signatures',
            icon: 'ni ni-key-25 text-default',
            path: '/districtcommissioner/signatures'
          }"
        ></sidebar-item>
        <sidebar-item
          :link="{
            name: 'Reports',
            icon: 'ni ni-chart-bar-32 text-default'
          }"
        >
        <sidebar-item
            :link="{
              name: 'Preliminary',
              path: '/districtcommissioner/PrelimReports',
            }"

            
          />
          <sidebar-item
            :link="{
              name: 'Unapproved',
              path: '/districtcommissioner/unapprovedreports'
            }"
          />
          <sidebar-item
            :link="{
              name: 'Rejected',
              path: '/districtcommissioner/rejectedreports'
            }"
          />
          <sidebar-item
            :link="{
              name: 'Approved',
              path: '/districtcommissioner/approvedreports'
            }"
          />
          <sidebar-item
            :link="{
              name: 'Submitted',
              path: '/districtcommissioner/reports'
              
            }"
          />
        </sidebar-item>
         <sidebar-item
          :link="{
            name: 'Status',
            icon: 'ni ni-badge text-default',
            path: '/districtcommissioner/reportStatus'
          }"
        ></sidebar-item>
      </template>

      <template slot="links-after">
        <hr class="my-3" />
        <h6 class="navbar-heading p-0 text-muted"><span style="color:#26b6b2;">Other Links</span></h6>

        <ul class="navbar-nav mb-md-3">
          <li class="nav-item">
            <sidebar-item
              :link="{
                name: 'About this system',
                icon: 'ni ni-ui-04 text-default',
                path: '/districtcommissioner/about'
              }"
              data-toggle="tooltip"
              data-placement="top"
              title="About this system"
            ></sidebar-item>
          </li>
         <li class="nav-item">
            <a class="nav-link" target="_blank" href="https://drive.google.com/file/d/1W0dPjmykx-_0myXy9CfbLFWElqoUXel8/view?usp=sharing" style="cursor: pointer">
              <i class="ni ni-cloud-download-95 text-default"></i>
              <span class="nav-link-text text-primary" 
                data-toggle="tooltip"
                data-placement="top"
                title="View user manual">User Manual</span>
            </a>
          </li>
        </ul>
      </template>
    </side-bar>
    <div class="main-content">
      <dashboard-navbar :type="$route.meta.navbarType"></dashboard-navbar>

      <div @click="$sidebar.displaySidebar(false)">
        <fade-transition :duration="200" origin="center top" mode="out-in">
          <!-- your content here -->
          <keep-alive include="DCDashboard">
            <router-view></router-view>
          </keep-alive>
        </fade-transition>
      </div>
      <content-footer v-if="!$route.meta.hideFooter"></content-footer>
    </div>
  </div>
</template>
<script>
/* eslint-disable no-new */
import PerfectScrollbar from "perfect-scrollbar";
import "perfect-scrollbar/css/perfect-scrollbar.css";
import NotificationBell from 'vue-notification-bell';

function hasElement(className) {
  return document.getElementsByClassName(className).length > 0;
}

function initScrollbar(className) {
  if (hasElement(className)) {
    new PerfectScrollbar(`.${className}`);
  } else {
    // try to init it later in case this component is loaded async
    setTimeout(() => {
      initScrollbar(className);
    }, 100);
  }
}

import DashboardNavbar from "./DashboardNavbar.vue";
import ContentFooter from "./ContentFooter.vue";
import DashboardContent from "./Content.vue";
import { FadeTransition } from "vue2-transitions";
import { mapGetters, mapActions } from "vuex";

export default {
  components: {
    DashboardNavbar,
    ContentFooter,
    DashboardContent,
    FadeTransition
  },
  methods: {

    // ...mapActions("prelim", {
    //   addprelimParameter: "addprelimParameter",
    // }),

    setpathvariable(){
      

     

    },


    initScrollbar() {
      let isWindows = navigator.platform.startsWith("Win");
      if (isWindows) {
        initScrollbar("scrollbar-inner");
      }
    },
    downloadManual() {
      var url = window.location.origin + "/DRMIS wep app manual.pdf";
      var filename = "DRMIS wep app manual.pdf";
      // console.log("location", window.location.origin)
      fetch(url).then(function (t) {
        return t.blob().then((b) => {
          var a = document.createElement("a");
          a.href = URL.createObjectURL(b);
          a.setAttribute("download", filename);
          a.click();
        });
      });
    },

  },
  mounted() {
    this.initScrollbar();
  }
};
</script>
<style lang="scss" scoped>
.text-primary {
    color: #606266 !important;
}
@import "../../../assets/style.css";
@import "../../../assets/ie7/ie7.css";
</style>
