<template>
  <div class="container">
    <!-- Disaster Summary and Needs Form Card -->
    <div class="card white-card">
      <div class="card-body">

        <!-- Disaster Summary Section -->
        <div class="summary-section">
          <div class="col-12 d-flex flex-column justify-content-center align-items-center text-center">
            <!-- Centered Image -->
            <img src="../../../../static/logo.png" height="90" />

            <!-- Centered Text -->
            <dl class="headings">
              <dd class="text-center" style="font-size:22px; color: #32325d; margin-top: 10px;">
                <p>
                  <b><strong>Government of Malawi</strong></b>
                  <br />
                  <span>
                    <b>Department of Disaster Management Affairs (DoDMA)</b>
                  </span>
                </p>
              </dd>
            </dl>
          </div>


        <br />
          <table class="summary-table">
            <tr>
              <td colspan="12" v-if="dinrFormsData.district">
                <center>
                  <h2>
                    <b style="text-transform: uppercase">DISASTER RESPONSE FORM FOR
                      <span id="district">{{ dinrFormsData.district.admin2_name_en }}</span>
                    </b>
                  </h2>
                </center>
              </td>
            </tr>

            <tr>
              <td colspan="6">
                <b>Type of disasters</b>: <span id="disastertype"> {{ dinrFormsData.disaster || "N/A" }} </span>
              </td>
              <td colspan="6">
                <b>Report submitted at</b>:
                {{ formatDate(dinrFormsData.createdon) || "N/A" }}
              </td>
            </tr>
            <tr>
              <td colspan="6">
                <b>Date Disaster started</b>:
                <span id="disasterstart">{{ formatDate(dinrFormsData.dodFrom) || "N/A" }}</span>
              </td>
              <td colspan="6">
                <b>Date Disaster ended</b>:
                {{ formatDate(dinrFormsData.dodTo) || "N/A" }}
              </td>
            </tr>
            <tr>
              <td colspan="4">
                <b>Date of assessment by ADRMC/VDRMC</b>:
                {{ formatDate(dinrFormsData.doaAcpc) || "N/A" }}
              </td>
              <td colspan="4">
                <b>Date of assessment/verification by DDRMC</b>:
                {{ formatDate(dinrFormsData.doaDcpc) || "N/A" }}
              </td>
              <td colspan="4">
                <b>Date reported to the DEC</b>:
                {{ formatDate(dinrFormsData.dateReported) || "N/A" }}
              </td>
            </tr>
          </table>
        </div>
        <hr class="mt-3 mb-3 text-uppercase" color="#d5d5d5" />
        <base-input label="General Response that is being provided for this disaster">
          <textarea
            class="form-control"
            data-toggle="tooltip"
            data-placement="top"
            title="Type the general response that is needed"
            id="exampleFormControlTextarea3"
            placeholder="Type the response needed for this disaster"
           v-model="needsFormData.comments"
            rows="6"

            @input="validateResponse"
            required
          ></textarea>
        </base-input>
        <p v-if="isInvalid" class="text-danger">{{ errorMessage }}</p>

        <el-button type="primary" @click="submitNeedsForm" :disabled="isInvalid">Submit</el-button>
        <!-- Needs Form Section -->
        <div class="needs-form-section">
          <!-- <h3 class="form-title">Enter Response Items that have been provided</h3>
          <el-form :model="needsFormData" label-width="100px" class="needs-form">
            <el-form-item>
              <el-input
                type="textarea"
                v-model="needsFormData.comments"
                placeholder="Enter Response Items"
                class="large-textarea"
              ></el-input> -->
            <!-- </el-form-item> -->
            <!-- <el-form-item>
              <div class="button-center">
                <el-button type="primary" @click="submitNeedsForm"  :disabled="isInvalid" >Submit</el-button>
              </div>
            </el-form-item> -->
          <!-- </el-form> -->
        </div>
      </div>
    </div>
  </div>
</template>


<script>
import axios from 'axios';
import { resource } from '../../../api/dinrs/index.js';


import { Form, FormItem, Input, Button } from "element-ui";
import moment from "moment";
import { mapGetters, mapActions } from "vuex";
import SummaryReportHeader from "../../dashboards/components/summaryReportHeader.vue";
import Swal from "sweetalert2";
export default {
  name: "ManagerSummaryWithNeedsForm",
  components: {
    [Form.name]: Form,
    [FormItem.name]: FormItem,
    [Input.name]: Input,
    [Button.name]: Button,
    SummaryReportHeader,
  },
  props: {
    id: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      needsFormData: {

        comments: ''

      },
      errorMessage: '',
      isInvalid: true,
      dinrFormsData: {},
      TAarray: [],
      villagesArray: [],
      officerSignature: null,
      dcSignature: null
    };
  },
  computed: {
    ...mapGetters({
      getDinrById: "dinrs/getById"

    }),
    userData() {
      const sessionData = sessionStorage.getItem('vue-session-key');
      return sessionData ? JSON.parse(sessionData).userObj : { firstName: 'N/A', lastName: 'N/A' };
    }
  },
  methods: {
    validateResponse() {
      // Validate minimum character length
      if (this.needsFormData.comments.trim().length < 20) {
        this.isInvalid = true;
        this.errorMessage = "Response must be at least 20 characters long.";
      } else {
        this.isInvalid = false;
        this.errorMessage = "";
      }
    },


    ...mapActions("dinrs", {
      fetchDinrData: "get",
      submitNeedsForm:"updateDinr"
    }),

    async submitNeedsForm() {
  this.loading = true; // Show loader
  const updateUrl = `${resource}/${this.id}`;
  const payload = {
    response: this.needsFormData.comments,
    actedonDate: new Date(),
    actedBy: `${this.userData.firstName} ${this.userData.lastName}`,
  };

  try {
    // First API request
    const response = await axios.patch(updateUrl, payload);
    console.log("Response data:", response.data);

    // Ensure dinrFormsData has the required fields
    if (!this.dinrFormsData || !this.dinrFormsData.district) {
      throw new Error("Missing required disaster information.");
    }

    // Prepare notification payload
    const notificationPayload = {
      district: this.dinrFormsData.district.admin2_name_en, // District name
      disaster: this.dinrFormsData.disaster,  // Disaster type
      endDate: this.formatDate(this.dinrFormsData.dodTo),  // Disaster end date
      startDate: this.formatDate(this.dinrFormsData.dodFrom), // Disaster start date
    };

    // Fire-and-forget for notification request
    axios.post(
      "http://localhost:3009/api/notifications/disaster-response",
      notificationPayload
    )
    .then(() => {
      console.log("Notification sent successfully");
    })
    .catch((error) => {
      console.error("Failed to send notification:", error);
    });

    // Show success message
    Swal.fire({
      title: "Response needs added successfully",
      icon: "success",
      animation: true,
    }).then(() => {
      this.$router.push("/manager/reports/");
      window.location.reload();
    });
  } catch (error) {
    console.error("Error:", error);
    Swal.fire({
      title: "Failed to add response needs",
      icon: "warning",
      animation: true,
    });
  } finally {
    this.loading = false; // Hide loader
  }
},


    // async submitNeedsForm() {
    //   const payload = {
    //     id: this.id,
    //     model: {
    //       response: this.needsFormData.comments,
    //       actedonDate: new Date(),
    //       actedBy: `${this.userData.firstName} ${this.userData.lastName}`
    //     }
    //   };

    //   try {
    //     await this.updateDinrData(payload);
    //     alert("Needs response submitted successfully!");
    //   } catch (error) {
    //     console.error("Failed to submit needs response:", error);
    //     alert("Update failed!");
    //   }
    // },
    formatDate(date) {
      return date ? moment(date).format("DD/MM/YYYY") : "N/A";
    }
  },
  async mounted() {
    await this.fetchDinrData(this.id);
    this.dinrFormsData = this.getDinrById(this.id);

    console.log("Retrieved dinrFormsData:", this.dinrFormsData);

    if (this.dinrFormsData) {
      this.TAarray = this.dinrFormsData.affectedTAs || [];
      this.villagesArray = this.dinrFormsData.affectedVillages || [];
      this.officerSignature = this.dinrFormsData.approvalMetadata && this.dinrFormsData.approvalMetadata.officerSignature || {};
      this.dcSignature = this.dinrFormsData.approvalMetadata && this.dinrFormsData.approvalMetadata.dcSignature || {};
    }
  }
};
</script>

<style scoped>
.container {
  padding-top: 20px;
}

.white-card {
  background-color: white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  margin-bottom: 20px;
}

.card-body {
  padding: 20px;
}

.summary-table {
  width: 100%;
  border-collapse: collapse;
  text-align: left;
  margin-top: 20px;
  color: black;
}

.summary-table td, .summary-table th {
  border: 1px solid black;
  padding: 10px;
}

.summary-table h2 {
  color: black;
  font-weight: bold;
  margin: 0;
}

.summary-table b {
  color: black;
}
.container {
  padding-top: 20px;
}

.white-card {
  background-color: white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  margin-bottom: 20px;
}

.card-body {
  padding: 20px;
}

.section-title {
  font-size: 1.5em;
  color: #32325d;
  margin-bottom: 10px;
}

.summary-section p {
  font-size: 1em;
  margin: 5px 0;
}

.summary-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

.summary-table td {
  padding: 10px;
}




.large-textarea {
  width: 100%; /* Ensures it fills the container */
  min-width: 600px; /* Sets a minimum width for the textarea */
  min-height: 250px; /* Sets a minimum height to improve visibility */
  resize: vertical; /* Allows the user to resize vertically */
  font-size: 16px; /* Makes the text more readable */
  border: 1px solid #ccc; /* Adds a light border for better visibility */
  box-sizing: border-box; /* Includes padding and border in the element's total width and height */
}

.needs-form {
  display: flex;
  flex-direction: column;
  align-items: center; /* Centers the form items horizontally */
  width: 100%; /* Makes the form full width of its container */
  max-width: 800px; /* Adjusts or remove as necessary for your design */
  margin: auto; /* Centers the form in the available horizontal space */
}

.needs-form-section {
  width: 100%; /* Ensures the section takes full width of its parent */
  padding: 20px; /* Adds padding around the form for spacing */
}

.button-center {
  width: 100%; /* Ensures button container spans the full width of the form */
  display: flex;
  justify-content: center; /* Centers the button horizontally */
  margin-top: 20px; /* Provides top margin for spacing */
}

.form-title {
  font-size: 1.5em;
  color: #32325d;
  margin-bottom: 15px;
  text-align: center;
}

.el-form-item {
  width: 100%;
}

.el-button {
  background-color: #26b6b2;
  border-color: #26b6b2;
  color: white;
}
</style>
