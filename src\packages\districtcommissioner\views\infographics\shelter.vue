<template>
  <div>
    <div style="background:#F4F4F4">
      <div class="row pt-4">
        <div class="col">
          <div class="card">
            <div class="card-header">
              <span style="color:teal;font-size:150%">
                <b>
                  SHELTER &#38; CAMP MGT [
                  <span style="color:red"
                    >{{ data.Disaster }} - {{ data.District }}</span
                  >
                  ]
                </b>
                <b class="pull-right"
                  >ASSESSMENT PERIOD :
                  {{
                    dateFormate(
                      new Date(data.Date_of_Disaster_from).toLocaleDateString(
                        "en-US"
                      )
                    )
                  }}
                  -
                  {{
                    dateFormate(
                      new Date(data.Date_of_Disaster_to).toLocaleDateString(
                        "en-US"
                      )
                    )
                  }}</b
                >
              </span>
              <hr />
              <div class="row">
                <!--   <base-input
                  label="TAs"
                  style="color:Lime;"
                  class="col-xl-5"
                  :value="data.TA"
                  disabled
                ></base-input>-->
                <base-input label="TA" class="col-xl-3">
                  <el-select
                    v-model="selected"
                    @change="getInfoGraphicsData(selected)"
                    placeholder="select"
                  >
                    <el-option
                      v-for="option in data.TAList"
                      :key="option.label"
                      :label="option.label"
                      :value="option.value + ',' + option.label"
                    ></el-option>
                  </el-select>
                </base-input>
                <base-input
                  label="GVHs"
                  class="col-xl-9"
                  :value="data.GVHS_affected"
                  readonly
                ></base-input>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-xl-3 col-md-6">
          <stats-card
            title="People affected"
            type="gradient-green"
            :sub-title="
              (
                parseInt(data.number_of_dead_females_shelter) +
                  parseInt(data.number_of_dead_males_shelter) +
                  parseInt(data.number_of_injured_males_in_category_shelter) +
                  parseInt(data.number_of_injured_females_shelter) +
                  parseInt(data.number_of_females_without_shelter_shelter) +
                  parseInt(data.number_of_males_without_shelter_shelter) +
                  parseInt(data.number_of_males_disaggregated_displaced) +
                  parseInt(data.number_of_females_disaggregated_displaced) || 0
              ).toString()
            "
            icon="icon-ocha-affected-population"
          >
            <template slot="footer">
              <span>
                <i class="icon-ocha-affected-man"></i>
                {{
                  parseInt(data.number_of_dead_females_shelter) +
                    parseInt(data.number_of_injured_females_shelter) +
                    parseInt(data.number_of_females_without_shelter_shelter) ||
                    0
                }}
              </span>
              <span class="pull-right">
                <i class="icon-ocha-affected-woman"></i>
                {{
                  parseInt(data.number_of_dead_males_shelter) +
                    parseInt(data.number_of_injured_males_in_category_shelter) +
                    parseInt(data.number_of_males_without_shelter_shelter) || 0
                }}
              </span>
            </template>
          </stats-card>
        </div>
        <div class="col-xl-3 col-md-6">
          <stats-card
            title="People Injured"
            type="gradient-green"
            :sub-title="
              (
                parseInt(data.number_of_injured_males_in_category_shelter) +
                  parseInt(data.number_of_injured_females_shelter) || 0
              ).toString()
            "
            icon="icon-ocha-affected-population"
          >
            <template slot="footer">
              <span>
                <i class="icon-ocha-affected-man"></i>
                {{ data.number_of_injured_males_in_category_shelter }}
              </span>
              <span class="pull-right">
                <i class="icon-ocha-affected-woman"></i>
                {{ data.number_of_injured_females_shelter }}
              </span>
            </template>
          </stats-card>
        </div>
        <div class="col-xl-3 col-md-6">
          <stats-card
            title="People dead"
            type="gradient-green"
            :sub-title="
              (
                parseInt(data.number_of_dead_females_shelter) +
                  parseInt(data.number_of_dead_males_shelter) || 0
              ).toString()
            "
            icon="icon-ocha-affected-population"
          >
            <template slot="footer">
              <span>
                <i class="icon-ocha-affected-man"></i>
                {{ data.number_of_dead_males_shelter }}
              </span>
              <span class="pull-right">
                <i class="icon-ocha-affected-woman"></i>
                {{ data.number_of_dead_females_shelter }}
              </span>
            </template>
          </stats-card>
        </div>
        <div class="col-xl-3 col-md-6">
          <stats-card
            title="without shelter"
            type="gradient-green"
            :sub-title="
              (
                parseInt(data.number_of_females_without_shelter_shelter) +
                  parseInt(data.number_of_males_without_shelter_shelter) || 0
              ).toString()
            "
            icon="icon-unhcr-locations-settlement"
          >
            <template slot="footer">
              <span>
                <i class="icon-ocha-affected-man"></i>
                {{ data.number_of_males_without_shelter_shelter }}
              </span>
              <span class="pull-right">
                <i class="icon-ocha-affected-woman"></i>
                {{ data.number_of_females_without_shelter_shelter }}
              </span>
            </template>
          </stats-card>
        </div>
      </div>
      <div class="row">
        <div class="col-xl-4">
          <div class="row">
            <div class="col">
              <div class="card" style="height:700px">
                <span class="text-left" style="padding:1%">
                  <b>{{ data.District }} District</b>
                </span>
                <div class="card-body pt-0">
                  <shelter-map
                    v-on:map-clicked="onMapClick"
                    :district="data.District"
                    v-if="data.District"
                  ></shelter-map>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-xl-8">
          <div class="row">
            <div class="col-xl-12">
              <card>
                <ul class="list-group list-group-flush list my--3">
                  <li
                    class="list-group-item px-0"
                    v-for="house in houses"
                    :key="house.id"
                  >
                    <div class="row align-items-center">
                      <div class="col ml--2">
                        <h4 class="mb-0">
                          <a href="#!">{{ house.name }} HH</a>
                        </h4>
                      </div>
                      <i class="icon-unhcr-locations-settlement"></i>
                      <div class="col-auto">{{ house.number }}</div>
                    </div>
                  </li>
                </ul>
              </card>
            </div>
          </div>

          <div class="row">
            <div class="col-xl-6">
              <card>
                <template slot="header">
                  <!-- Subtitle -->
                  <h6 class="surtitle">Extent of damaged of houses %</h6>
                  <!-- Title -->
                </template>
                <div>
                  <doughtNutChart
                    :data="doughnutChartData"
                    :options="doughnutChartOptions"
                  ></doughtNutChart>
                </div>
              </card>
            </div>
            <div class="col-xl-6">
              <card>
                <template slot="header">
                  <!-- Subtitle -->
                  <h6 class="surtitle">People affected</h6>
                </template>
                <div>
                  <barChart
                    :data="barChartData"
                    :options="barChartOptions"
                  ></barChart>
                </div>
              </card>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import flatPicker from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";

import RouteBreadCrumb from "@/components/Breadcrumb/RouteBreadcrumb";
import BaseHeader from "@/components/BaseHeader";
import StatsCard from "@/components/Cards/StatsCard";
import { Select, Option } from "element-ui";
import shelterMap from "./components/shelterMap";
import doughtNutChart from "./components/doughtNutChart";
import barChart from "./components/barChart";
import TagsInput from "@/components/Inputs/TagsInput";
import { metabase } from "../../api/metabase/metabase";
import axios from "axios";
import moment from "moment";

window.jQuery = require("jquery");

function randomScalingFactor() {
  return Math.round(Math.random() * 100);
}

export default {
  components: {
    StatsCard,
    BaseHeader,
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    flatPicker,
    shelterMap,
    doughtNutChart,
    barChart,
    TagsInput
  },
  props: ["data", "msg"],
  data() {
    return {
      tags: ["Floods", "2019-01-01 to 2019-12-31"],
      districts: [],
      TAList: [],
      selected: {},
      doughnutChartOptions: {
        hoverBorderWidth: 20
      },
      doughnutChartData: {
        hoverBackgroundColor: "red",
        hoverBorderWidth: 10,
        labels: ["Completely", "Underwater", "Partly"],
        datasets: [
          {
            backgroundColor: ["#00000", "teal", "#a97142"],
            data: [
              this.data.Houses_completely_shelter,
              this.data.Houses_underwater_shelter,
              this.data.Houses_partly_damaged_shelter
            ]
          }
        ]
      },
      barChartOptions: {
        hoverBorderWidth: 20
      },
      barChartData: {
        hoverBackgroundColor: "red",
        hoverBorderWidth: 10,
        labels: ["Without Shelter", "Injured", "Dead"],
        datasets: [
          {
            label: "people",
            backgroundColor: "teal",
            data: [
              parseInt(this.data.number_of_females_without_shelter_shelter) +
                parseInt(this.data.number_of_males_without_shelter_shelter),
              parseInt(this.data.number_of_injured_males_in_category_shelter) +
                parseInt(this.data.number_of_injured_females_shelter),
              parseInt(this.data.number_of_dead_females_shelter) +
                parseInt(this.data.number_of_dead_males_shelter)
            ]
          }
        ]
      },

      filter: {
        disasters: "Floods",
        range: "2019-01-01 to 2019-12-31"
      },
      disasters: [
        {
          name: "Heavy Rains"
        },
        {
          name: "Earth quake"
        },
        {
          name: "Floods"
        }
      ],
      districts: [
        {
          label: "Chikwawa",
          value: "Chikwawa",
          region: "South"
        },
        {
          label: "Mangochi",
          value: "Mangochi",
          region: "South"
        },
        {
          label: "Balaka",
          value: "Balaka",
          region: "North"
        },
        {
          label: "Phalombe",
          value: "Phalombe",
          region: "Central"
        }
      ],
      regions: [
        {
          region: "South"
        },
        {
          region: "Central"
        },
        {
          region: "North"
        },
        {
          region: "East"
        }
      ],
      houses: []
    };
  },
  methods: {
    getInfoGraphicsData(selectedTA) {
      var data = selectedTA.split(",");
      ///console.log(data);
      var TA = data[1];
      var ID = data[0];
      if (TA === "All") {
        this.$emit("Dinr", ID);
      } else {
        this.$emit("Dra", ID);
      }
    },
    dateFormate(dateData) {
      const cur_date = dateData;
      const formattedDate = moment(cur_date).format("DD/MM/YYYY");
      return formattedDate;
    },
    addRemoveValuesToTagsFromMap(district) {
      var found = this.tags.find(element => element === district);
      //console.log(found);
      if (found != undefined) {
        var index = this.tags.indexOf(district);
        if (index !== -1) this.tags.splice(index, 1);
      } else {
        this.tags.push(disnpmtrict);
      }
    },
    onMapClick: function(attr) {
      this.$notify({
        group: "map",
        title: "State clicked",
        text: `You clicked on state with id: ${attr.mapId} and title: ${attr.title}`
      });
    }
  },
  created() {
    //console.log(this.data);
    this.selected = this.data.TAname;
    this.houses.push(
      {
        id: 1,
        name: "Completely damaged",
        number: this.data.Houses_completely_shelter,
        males: 89,
        females: this.data.number_of_female_HH_with_houses_damaged_shelter
      },
      {
        id: 2,
        name: "Partly damaged",
        number: this.data.Houses_partly_damaged_shelter
      },
      {
        id: 3,
        name: "Underwater",
        number: this.data.Houses_underwater_shelter
      }
    );
  }
};
</script>
<style>
@import "../../../../assets/fonts/font-humanitarian.css";
</style>
