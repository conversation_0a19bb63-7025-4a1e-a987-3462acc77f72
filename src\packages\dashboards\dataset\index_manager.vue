<template>
  <div>
    <div class="row">
      <div class="col-12 m-1">
        <li class="list-group-item">
          <div class="row">
            <div class="col-8 text-left">
              <h2 class="text-justify m-0">
                <!-- The is filterable table which is listing about 80% of data which
                is captured by the DRMIS system. In the top of this page there
                is a button where you can export the data to csv. -->
                <b-form-radio-group
                  v-model="select"
                  :options="options"
                  :state="state"
                  name="radio-validation"
                ></b-form-radio-group>
              </h2>
            </div>
            <div class="text-right col-4">
              <button
                class="reset btn btn-primary btn-sm pull-right"
                @click="downloadCSV({ filename: 'dmis_disasters.csv' })"
              >
                <i class="text-black ni ni-cloud-download-95"></i> EXPORT TO CSV
              </button>
              <button
                class="reset btn btn-warning mr-2 btn-sm pull-right"
                @click="downloadExcel()"
              >
                <i class="text-black ni ni-cloud-download-95"></i> EXPORT TO
                EXCEL
              </button>
            </div>
          </div>
        </li>
      </div>
    </div>

    <div v-if="select === 'first'" class="p-lg m-1" style="full-width">
      <div class="row" style="justify-content: center">
        <div class="py-lg pr-2 col-12 rounded-corner">
          <b-card-group class style="max-width: 100%; height: 63vh">
            <b-card class="text-dark border flat mb-3">
              <b-card-header class="bg-transparent p-3">
                <div>
                  <h3 class="mb-0">UN-SELECTED INDICATOR(S)</h3>
                </div>
                <div class="row">
                  <div class="col-8 font-weight-bolder mt-3 text-primary">
                    <button
                      class="btn btn-sm btn btn-outline-primary"
                      @click="selectAll"
                    >
                      <b-icon icon="plus" shift-v="0" scale="3"></b-icon>
                    </button>
                    <b>SELECT ALL</b>
                  </div>
                  <div class="col-2"></div>
                  <div class="col-2"></div>
                </div>
              </b-card-header>
              <b-card-body class="py-1 px-0">
                <b-list-group>
                  <b-list-group-item
                    v-for="(list, index) in lists"
                    :key="list.num"
                    href="#"
                    class="border-0 p-2"
                    @click.native="selectHeader(index)"
                  >
                    <div class="row">
                      <div
                        style="font-size: 1.5rem"
                        class="col-1 pl-3 pr-0 primary"
                      >
                        <b-icon
                          icon="capslock-fill"
                          rotate="90"
                          variant="primary"
                          shift-h="-1"
                          shift-v="-1"
                        ></b-icon>
                      </div>
                      <div class="col-11 px-2">
                        <b-card-title class="mb-0 pt-2">
                          <h3 class="mb-0">{{ list.title }}</h3>
                          <p class="mb-0">{{ list.description }}</p>
                        </b-card-title>
                      </div>
                    </div>
                  </b-list-group-item>
                </b-list-group>
              </b-card-body>
            </b-card>
            <b-card class="text-dark border flat mb-3">
              <b-card-header class="bg-transparent p-3">
                <div>
                  <h3 class="mb-0">SELECTED INDICATOR(S)</h3>
                </div>
                <div class="row">
                  <div class="col-8 font-weight-bolder mt-3 text-primary">
                    <button
                      class="btn btn-sm btn btn-outline-primary"
                      @click="removeAll"
                    >
                      <b-icon icon="dash" shift-v="2" scale="3"></b-icon>
                    </button>
                    <b>DESELECT ALL</b>
                  </div>
                  <div class="col-2"></div>
                  <div class="col-2"></div>
                </div>
              </b-card-header>
              <b-card-body class="py-2 px-0">
                <b-list-group>
                  <b-list-group-item
                    v-for="(select, index) in selected"
                    :key="index"
                    @click="removeHeader(index)"
                    href="#"
                    class="border-0 p-2"
                  >
                    <div class="row">
                      <div
                        style="font-size: 1.5rem"
                        class="col-1 pl-3 pr-0 primary"
                      >
                        <b-icon
                          icon="capslock-fill"
                          rotate="270"
                          variant="warning"
                          shift-h="-1"
                          shift-v="-1"
                        ></b-icon>
                      </div>
                      <div class="col-11 px-2">
                        <b-card-title class="mb-0 pt-2">
                          <h3 class="mb-0">{{ select.title }}</h3>
                          <p class="mb-0">{{ select.description }}</p>
                        </b-card-title>
                      </div>
                    </div>
                  </b-list-group-item>
                </b-list-group>
              </b-card-body>
            </b-card>
          </b-card-group>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-12 m-1">
        <li class="list-group-item">
          <div class="row">
            <div class="col-12">
              <h3>KEY</h3>
            </div>
          </div>
          <div class="row">
            <div
              v-for="(key, index) in keys"
              :key="index"
              class="col-4 text-left"
            >
              <p>{{ key.text }}</p>
            </div>
          </div>
        </li>
      </div>
    </div>

    <div v-if="select === 'second'" class="ml-1 p-0">
      <vue-good-table
        ref="sr-table"
        :columns="columns"
        :rows="rows"
        :isLoading="isLoaded"
        :pagination-options="{ enabled: true, perPage: 10 }"
        :search-options="{ enabled: true }"
        max-height="800px"
        theme="polar-bear"
      ></vue-good-table>
    </div>
  </div>
</template>
<script>
import "vue-good-table/dist/vue-good-table.css";
import { VueGoodTable } from "vue-good-table";
import _ from "underscore";
import flatPickr from "flatpickr";
import "flatpickr/dist/flatpickr.css";
import "flatpickr/dist/themes/airbnb.css";
import dateformat from "dateformat";
import utils from "../../../util/dashboard";
import { admin2s } from "../../manager/api/location/admin2s";
import { mapActions, mapGetters } from "vuex";

export default {
  components: {
    VueGoodTable
  },
  data() {
    return {
      show: true,
      select: "first",
      tableData: [],
      downloadData: [],
      download: [],
      villagesArray: [],
      campsArray: [],
      Gvharray: [],
      isLoaded: false,
      aggregatedData: [],
      state: true,
      options: [
        { text: "Report Generator", value: "first" },
        { text: "Table", value: "second" }
      ],
      keys: [
        { text: "HH = Household" },
        { text: "MHH = Male Headed Household" },
        { text: "FHH = Female Headed Household" },
        { text: "T/A = Traditional Authority" },
        { text: "GVH = Group Village Headman" },
        { text: "DDRMC = District Disaster Risk Management Committee" },
        { text: "ADRMC = Area Disaster Risk Management Committee" }
      ],
      selected: [],
      lists: [
        {
          num: 72,
          title: "Country PCODE",
          header: "Country_PCODE",
          description: ""
        },
        {
          num: 71,
          title: "Country",
          header: "Country",
          description: ""
        },
        {
          num: 0,
          title: "Disaster ID",
          header: "dinr",
          description: ""
        },
        {
          num: 1,
          title: "DRA ID",
          header: "oid",
          description: ""
        },
        {
          num: 2,
          title: "Disaster",
          header: "Disaster",
          description: ""
        },
        {
          num: 3,
          title: "Region",
          header: "Region",
          description: ""
        },
        {
          num: 4,
          title: "District",
          header: "District",
          description: ""
        },
        {
          num: 5,
          title: "TA PCODE",
          header: "TA_PCODE",
          description: ""
        },
        {
          num: 6,
          title: "TA",
          header: "TA",
          description: ""
        },
        {
          num: 7,
          title: "GVHs Affected",
          header: "GVHS_affected",
          description: ""
        },
        {
          num: 8,
          title: "Date of assessment ACPC",
          header: "Date_of_assessment_ACPC",
          description: ""
        },
        {
          num: 9,
          title: "Date of assessment DCPC",
          header: "Date_of_assessment_DCPC",
          description: ""
        },
        {
          num: 10,
          title: "Date of Disaster from",
          header: "Date_of_Disaster_from",
          description: ""
        },
        {
          num: 11,
          title: "Date of Disaster to",
          header: "Date_of_Disaster_to",
          description: ""
        },
        {
          num: 12,
          title: "food items damaged KGs agriculture",
          header: "food_items_damaged_KGs_agriculture",
          description: ""
        },
        {
          num: 13,
          title: "Number of crop hectares submerged agriculture",
          header: "number_of_crop_hectares_submerged_agriculture",
          description: ""
        },
        {
          num: 14,
          title: "Number crop hectares washed off agriculture",
          header: "number_crop_hectares_washed_off_agriculture",
          description: ""
        },
        {
          num: 15,
          title: "Number of households whose crops are impacted agriculture",
          header: "number_of_households_whose_crops_are_impacted_agriculture",
          description: ""
        },
        {
          num: 16,
          title:
            "Number of crop hectares damaged in affected households agriculture",
          header: "of_crop_hectares_damaged_in_affected_households_agriculture",
          description: ""
        },

        {
          num: 17,
          title: "HH affected per impacted livestock agriculture",
          header: "hh_affected_per_impacted_livestock_agriculture",
          description: ""
        },
        {
          num: 18,
          title: "Number of impacted livestock agriculture",
          header: "number_of_impacted_livestock_agriculture",
          description: ""
        },
        {
          num: 19,
          title: "Number male HH accomadeted displaced",
          header: "number_male_HH_accomadeted_displaced",
          description: ""
        },

        {
          num: 20,
          title: "Number male HH accomadeted displaced",
          header: "number_male_HH_accomadeted_displaced",
          description: ""
        },
        {
          num: 21,
          title: "Number female HH accomadeted displaced",
          header: "number_female_HH_accomadeted_displaced",
          description: ""
        },

        {
          num: 22,
          title: "Number of males disaggregated displaced",
          header: "number_of_males_disaggregated_displaced",
          description: ""
        },
        {
          num: 23,
          title: "Number of females disaggregated displaced",
          header: "number_of_females_disaggregated_displaced",
          description: ""
        },
        {
          num: 24,
          title: "Number of males accomodated displaced",
          header: "number_of_males_accomodated_displaced",
          description: ""
        },
        {
          num: 25,
          title: "Number of females accomodated displaced",
          header: "number_of_females_accomodated_displaced",
          description: ""
        },
        {
          num: 26,
          title: "Number of school buildings functioning education",
          header: "number_of_school_buildings_functioning_education",
          description: ""
        },
        {
          num: 27,
          title: "Number of school buildings underwater education",
          header: "number_of_school_buildings_underwater_education",
          description: ""
        },
        {
          num: 28,
          title: "Number of school buildings completely damaged education",
          header: "number_of_school_buildings_completely_damaged_education",
          description: ""
        },
        {
          num: 29,
          title: "Number of school buildings partially functioning education",
          header: "number_of_school_buildings_partially_functioning_education",
          description: ""
        },

        {
          num: 30,
          title: "Number of school buildings closed education",
          header: "number_of_school_buildings_closed_education",
          description: ""
        },
        {
          num: 31,
          title: "Males of out school education",
          header: "males_of_out_school_education",
          description: ""
        },
        {
          num: 32,
          title: "Females of out school education",
          header: "females_of_out_school_education",
          description: ""
        },
        {
          num: 33,
          title: "Is food available food",
          header: "is_food_available_food",
          description: ""
        },
        {
          num: 34,
          title: "Number of facilities partially functioning health",
          header: "number_of_facilities_partially_functioning_health",
          description: ""
        },
        {
          num: 35,
          title: "Number of facilities on verge of closing health",
          header: "number_of_facilities_on_verge_of_closing_health",
          description: ""
        },
        {
          num: 36,
          title: "Number facilities closed health",
          header: "number_facilities_closed_health",
          description: ""
        },
        {
          num: 37,
          title: "State of medical supply availability health",
          header: "state_of_medical_supply_availability_health",
          description: ""
        },
        {
          num: 38,
          title: "State of health personnel availability health",
          header: "state_of_health_personnel_availability_health",
          description: ""
        },
        {
          num: 39,
          title: "Number severely affected livelihoods",
          header: "number_severely_affected_livelihoods",
          description: ""
        },
        {
          num: 40,
          title: "Number slightly affected livelihoods",
          header: "number_slightly_affected_livelihoods",
          description: ""
        },
        {
          num: 41,
          title: "Impacted females protection",
          header: "impacted_females_protection",
          description: ""
        },
        {
          num: 42,
          title: "Impacted males protection",
          header: "impacted_males_protection",
          description: ""
        },
        {
          num: 43,
          title: "State of access to main roads logistics",
          header: "state_of_access_to_main_roads_logistics",
          description: ""
        },
        {
          num: 44,
          title: "Number of affected males nutrition",
          header: "number_of_affected_males_nutrition",
          description: ""
        },
        {
          num: 45,
          title: "Number of affected females nutrition",
          header: "number_of_affected_females_nutrition",
          description: ""
        },
        {
          num: 46,
          title: "FHH with safe water wash",
          header: "FHH_with_safe_water_wash",
          description: ""
        },
        {
          num: 47,
          title: "MHH with safe water wash",
          header: "MHH_with_safe_water_wash",
          description: ""
        },
        {
          num: 48,
          title: "FHH with toilet access wash",
          header: "FHH_with_toilet_access_wash",
          description: ""
        },
        {
          num: 49,
          title: "MHH with toilet access wash",
          header: "MHH_with_toilet_access_wash",
          description: ""
        },

        {
          num: 50,
          title: "FHH risking contamination wash",
          header: "FHH_risking_contamination_wash",
          description: ""
        },
        {
          num: 51,
          title: "MHH risking contamination wash",
          header: "MHH_risking_contamination_wash",
          description: ""
        },
        {
          num: 52,
          title: "Houses partly damaged shelter",
          header: "Houses_partly_damaged_shelter",
          description: ""
        },
        {
          num: 53,
          title: "Houses underwater shelter",
          header: "Houses_underwater_shelter",
          description: ""
        },
        {
          num: 54,
          title: "Houses completely shelter",
          header: "Houses_completely_shelter",
          description: ""
        },
        {
          num: 55,
          title: "Number of males without shelter shelter",
          header: "number_of_males_without_shelter_shelter",
          description: ""
        },
        {
          num: 56,
          title: "Number of females without shelter shelter",
          header: "number_of_females_without_shelter_shelter",
          description: ""
        },
        {
          num: 57,
          title: "Number of female HH with houses damaged shelter",
          header: "number_of_female_HH_with_houses_damaged_shelter",
          description: ""
        },
        {
          num: 58,
          title: "Number of male HH with houses damaged shelter",
          header: "number_of_male_HH_with_houses_damaged_shelter",
          description: ""
        },
        {
          num: 59,
          title: "Number of injured females shelter",
          header: "number_of_injured_females_shelter",
          description: ""
        },
        {
          num: 60,
          title: "Number of injured males in category shelter",
          header: "number_of_injured_males_in_category_shelter",
          description: ""
        },

        {
          num: 61,
          title: "Number of dead females shelter",
          header: "number_of_dead_females_shelter",
          description: ""
        },
        {
          num: 62,
          title: "Number of dead males shelter",
          header: "number_of_dead_males_shelter",
          description: ""
        },
        {
          num: 63,
          title: "Number of displaced male displaced",
          header: "number_of_displaced_male_displaced",
          description: ""
        },
        {
          num: 64,
          title: "Number of displaced females displaced",
          header: "number_of_displaced_females_displaced",
          description: ""
        },
        {
          num: 65,
          title: "No of FHH with 1-2 month Food Availability food",
          header: "No_of_FHH_with_1_2_month_Food_Availability_food",
          description: ""
        },
        {
          num: 66,
          title: "No of FHH with less than 1 month Food Availability food",
          header: "No_of_FHH_with_less_than_1_month_Food_Availability_food",
          description: ""
        },
        {
          num: 67,
          title: "No of FHH with more than 2 months Food Availability food",
          header: "No_of_FHH_with_more_than_2_months_Food_Availability_food",
          description: ""
        },
        {
          num: 68,
          title: "No of FHH who lost Food Stock food",
          header: "No_of_FHH_who_lost_Food_Stock_food",
          description: ""
        },
        {
          num: 69,
          title: "No of MHH with 1-2 month Food Availability food",
          header: "No_of_MHH_with_1_2_month_Food_Availability_food",
          description: ""
        },
        {
          num: 70,
          title: "No of MHH with less than 1 month Food Availability food",
          header: "No_of_MHH_with_less_than_1_month_Food_Availability_food",
          description: ""
        },
        {
          num: 71,
          title: "No of MHH with more than 2 months Food Availability food",
          header: "No_of_MHH_with_more_than_2_months_Food_Availability_food",
          description: ""
        },
        {
          num: 72,
          title: "No of MHH who lost Food Stock food",
          header: "No_of_MHH_who_lost_Food_Stock_food",
          description: ""
        }
      ],
      columns: [
        {
          label: "Disaster ID",
          field: "dinr",
          filterOptions: {
            enabled: true,
            placeholder: "filter"
          }
        },
        {
          label: "DRA ID",
          field: "oid",
          filterOptions: {
            enabled: true,
            placeholder: "filter"
          }
        },
        {
          label: "Disaster",
          field: "Disaster",
          filterOptions: {
            enabled: true,
            filterFn: this.columnFilterFn, //custom filter function that
            trigger: "enter",
            placeholder: "Select",
            filterDropdownItems: [""]
          }
        },
        {
          label: "Region",
          field: "Region",
          filterOptions: {
            enabled: true,
            filterFn: this.columnFilterFn, //custom filter function that
            trigger: "enter",
            placeholder: "Select",
            filterDropdownItems: [""]
          }
        },
        {
          label: "District",
          field: "District",
          filterOptions: {
            enabled: true,
            filterFn: this.columnFilterFn, //custom filter function that
            trigger: "enter",
            placeholder: "Select",
            filterDropdownItems: [""]
          }
        },
        {
          label: "TA_PCODE",
          field: "TA_PCODE",
          filterOptions: {
            enabled: true
          }
        },

        {
          label: "TA",
          field: "TA",
          filterOptions: {
            enabled: true,
            filterFn: this.columnFilterFn, //custom filter function that
            trigger: "enter",
            placeholder: "Select",
            filterDropdownItems: [""]
          }
        },
        {
          label: "GVHs",
          field: "GVHS_affected",
          filterOptions: {
            enabled: true
          }
        },
        {
          label: "Date_of_assessment_ACPC",
          field: "Date_of_assessment_ACPC",
          type: "date",
          dateInputFormat: "yyyy-MM-dd",
          dateOutputFormat: "dd-MM-yyyy",
          filterOptions: {
            enabled: true,
            placeholder: "dateacpc",
            filterFn: this.dateRangeFilter
          }
        },
        {
          label: "Date_of_assessment_DCPC",
          field: "Date_of_assessment_DCPC",
          type: "date",
          dateInputFormat: "yyyy-MM-dd",
          dateOutputFormat: "dd-MM-yyyy",
          filterOptions: {
            enabled: true,
            placeholder: "datedcpc",
            filterFn: this.dateRangeFilter
          }
        },
        {
          label: "Date_of_Disaster_from",
          field: "Date_of_Disaster_from",
          type: "date",
          dateInputFormat: "yyyy-MM-dd",
          dateOutputFormat: "dd-MM-yyyy",
          filterOptions: {
            enabled: true,
            placeholder: "datefrom",
            filterFn: this.dateRangeFilter
          }
        },
        {
          label: "Date_of_Disaster_to",
          field: "Date_of_Disaster_to",
          type: "date",
          dateInputFormat: "yyyy-MM-dd",
          dateOutputFormat: "dd-MM-yyyy",
          filterOptions: {
            enabled: true,
            placeholder: "dateto",
            filterFn: this.dateRangeFilter
          }
        },
        {
          label: "food_items_damaged_KGs_agriculture",
          field: "food_items_damaged_KGs_agriculture",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_of_crop_hectares_submerged_agriculture",
          field: "number_of_crop_hectares_submerged_agriculture",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_crop_hectares_washed_off_agriculture",
          field: "number_crop_hectares_washed_off_agriculture",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_crop_hectares_washed_off_agriculture",
          field: "number_crop_hectares_washed_off_agriculture",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_crop_hectares_washed_off_agriculture",
          field: "number_crop_hectares_washed_off_agriculture",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_crop_hectares_washed_off_agriculture",
          field: "number_crop_hectares_washed_off_agriculture",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_crop_hectares_washed_off_agriculture",
          field: "number_crop_hectares_washed_off_agriculture",
          type: "number",
          filterOptions: { enabled: true }
        },

        {
          label: "number_of_households_whose_crops_are_impacted_agriculture",
          field: "number_of_households_whose_crops_are_impacted_agriculture",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "of_crop_hectares_damaged_in_affected_households_agriculture",
          field: "of_crop_hectares_damaged_in_affected_households_agriculture",
          type: "number",
          filterOptions: { enabled: true }
        },

        {
          label: "hh_affected_per_impacted_livestock_agriculture",
          field: "hh_affected_per_impacted_livestock_agriculture",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_of_impacted_livestock_agriculture",
          field: "number_of_impacted_livestock_agriculture",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_male_HH_accomadeted_displaced",
          field: "number_male_HH_accomadeted_displaced",
          type: "number",
          filterOptions: { enabled: true }
        },

        {
          label: "number_male_HH_accomadeted_displaced",
          field: "number_male_HH_accomadeted_displaced",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_female_HH_accomadeted_displaced",
          field: "number_female_HH_accomadeted_displaced",
          type: "number",
          filterOptions: { enabled: true }
        },

        {
          label: "number_of_males_disaggregated_displaced",
          field: "number_of_males_disaggregated_displaced",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_of_females_disaggregated_displaced",
          field: "number_of_females_disaggregated_displaced",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_of_males_accomodated_displaced",
          field: "number_of_males_accomodated_displaced",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_of_females_accomodated_displaced",
          field: "number_of_females_accomodated_displaced",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_of_school_buildings_functioning_education",
          field: "number_of_school_buildings_functioning_education",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_of_school_buildings_underwater_education",
          field: "number_of_school_buildings_underwater_education",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_of_school_buildings_completely_damaged_education",
          field: "number_of_school_buildings_completely_damaged_education",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_of_school_buildings_partially_functioning_education",
          field: "number_of_school_buildings_partially_functioning_education",
          type: "number",
          filterOptions: { enabled: true }
        },

        {
          label: "number_of_school_buildings_closed_education",
          field: "number_of_school_buildings_closed_education",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "males_of_out_school_education",
          field: "males_of_out_school_education",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "females_of_out_school_education",
          field: "females_of_out_school_education",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "is_food_available_food",
          field: "is_food_available_food",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_of_facilities_partially_functioning_health",
          field: "number_of_facilities_partially_functioning_health",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_of_facilities_on_verge_of_closing_health",
          field: "number_of_facilities_on_verge_of_closing_health",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_facilities_closed_health",
          field: "number_facilities_closed_health",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "state_of_medical_supply_availability_health",
          field: "state_of_medical_supply_availability_health",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "state_of_health_personnel_availability_health",
          field: "state_of_health_personnel_availability_health",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_severely_affected_livelihoods",
          field: "number_severely_affected_livelihoods",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_slightly_affected_livelihoods",
          field: "number_slightly_affected_livelihoods",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "impacted_females_protection",
          field: "impacted_females_protection",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "impacted_males_protection",
          field: "impacted_males_protection",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "state_of_access_to_main_roads_logistics",
          field: "state_of_access_to_main_roads_logistics",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_of_affected_males_nutrition",
          field: "number_of_affected_males_nutrition",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_of_affected_females_nutrition",
          field: "number_of_affected_females_nutrition",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "FHH_with_safe_water_wash",
          field: "FHH_with_safe_water_wash",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "MHH_with_safe_water_wash",
          field: "MHH_with_safe_water_wash",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "FHH_with_toilet_access_wash",
          field: "FHH_with_toilet_access_wash",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "MHH_with_toilet_access_wash",
          field: "MHH_with_toilet_access_wash",
          type: "number",
          filterOptions: { enabled: true }
        },

        {
          label: "FHH_risking_contamination_wash",
          field: "FHH_risking_contamination_wash",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "MHH_risking_contamination_wash",
          field: "MHH_risking_contamination_wash",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "Houses_partly_damaged_shelter",
          field: "Houses_partly_damaged_shelter",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "Houses_underwater_shelter",
          field: "Houses_underwater_shelter",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "Houses_completely_shelter",
          field: "Houses_completely_shelter",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_of_males_without_shelter_shelter",
          field: "number_of_males_without_shelter_shelter",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_of_females_without_shelter_shelter",
          field: "number_of_females_without_shelter_shelter",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_of_female_HH_with_houses_damaged_shelter",
          field: "number_of_female_HH_with_houses_damaged_shelter",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_of_male_HH_with_houses_damaged_shelter",
          field: "number_of_male_HH_with_houses_damaged_shelter",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_of_injured_females_shelter",
          field: "number_of_injured_females_shelter",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_of_injured_males_in_category_shelter",
          field: "number_of_injured_males_in_category_shelter",
          type: "number",
          filterOptions: { enabled: true }
        },

        {
          label: "number_of_dead_females_shelter",
          field: "number_of_dead_females_shelter",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_of_dead_males_shelter",
          field: "number_of_dead_males_shelter",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_of_displaced_male_displaced",
          field: "number_of_displaced_male_displaced",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "number_of_displaced_females_displaced",
          field: "number_of_displaced_females_displaced",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "No_of_FHH_with_1_2_month_Food_Availability_food",
          field: "No_of_FHH_with_1_2_month_Food_Availability_food",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "No_of_FHH_with_less_than_1_month_Food_Availability_food",
          field: "No_of_FHH_with_less_than_1_month_Food_Availability_food",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "No_of_FHH_with_more_than_2_months_Food_Availability_food",
          field: "No_of_FHH_with_more_than_2_months_Food_Availability_food",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "No_of_FHH_who_lost_Food_Stock_food",
          field: "No_of_FHH_who_lost_Food_Stock_food",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "No_of_MHH_with_1_2_month_Food_Availability_food",
          field: "No_of_MHH_with_1_2_month_Food_Availability_food",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "No_of_MHH_with_less_than_1_month_Food_Availability_food",
          field: "No_of_MHH_with_less_than_1_month_Food_Availability_food",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "No_of_MHH_with_more_than_2_months_Food_Availability_food",
          field: "No_of_MHH_with_more_than_2_months_Food_Availability_food",
          type: "number",
          filterOptions: { enabled: true }
        },
        {
          label: "No_of_MHH_who_lost_Food_Stock_food",
          field: "No_of_MHH_who_lost_Food_Stock_food",
          type: "number",
          filterOptions: { enabled: true }
        }
      ],
      rows: []
    };
  },
  methods: {
    ...mapActions({
      loadReports1: "flatdras/loadFlatdras",
      loadReports: "flatdinrs/loadFlatdinrs",
      loadReports2: "loadReports",
      getDrasAction: "dras/get",
      getDinrsAction: "dinrs/get"
    }),
    myColumnFilter: function(data, filterString) {
      //console.log("FILTER for: " + filterString);
    },
    dateRangeFilter(data, filterString) {
      let dateRange = filterString.split("to");
      let startDate = Date.parse(dateRange[0]);
      let endDate = Date.parse(dateRange[1]);
      return (data =
        Date.parse(data) >= startDate && Date.parse(data) <= endDate);
    },
    prepareData(downloadPayload, lists, selected) {
      let data = JSON.parse(JSON.stringify(downloadPayload));
      if (selected.length > 0) {
        lists.forEach((element, index) => {
          let header = element.header;
          data.forEach((el, i) => {
            delete el[header];
          });
        });
      }
      return data;
    },
    async downloadExcel() {
      if (this.show) {
        let data = [...this.prepareData(this.rows, this.lists, this.selected)];
        utils.generateExcel(data);
      } else {
      }
    },
    selectHeader(index) {
      this.selected.splice(index, 0, this.lists[index]);
      this.lists.splice(index, 1);
    },
    removeHeader(index) {
      this.selected[index].check = false;
      this.lists.splice(index, 0, this.selected[index]);
      this.selected.splice(index, 1);
    },
    selectAll() {
      this.lists.forEach((element, index) => {
        this.selected.splice(index, 0, this.lists[index]);
      });
      this.lists = [];
      this.check = false;
    },
    removeAll() {
      this.selected.forEach((element, index) => {
        this.lists.splice(index, 0, this.selected[index]);
      });
      this.selected = [];
      this.check1 = false;
    },

    convertArrayOfObjectsToCSV(args) {
      var result, ctr, keys, columnDelimiter, lineDelimiter, data;

      data = args.data || null;
      if (data == null || !data.length) {
        return null;
      }

      columnDelimiter = args.columnDelimiter || ",";
      lineDelimiter = args.lineDelimiter || "\n";

      keys = Object.keys(data[0]);

      result = "";
      result += keys.join(columnDelimiter);
      result += lineDelimiter;

      data.forEach(function(item) {
        ctr = 0;
        keys.forEach(function(key) {
          if (ctr > 0) result += columnDelimiter;

          result += item[key];
          ctr++;
        });
        result += lineDelimiter;
      });

      return result;
    },
    downloadCSV(args) {
      if (this.show) {
        let data = [...this.prepareData(this.rows, this.lists, this.selected)];

        utils.download(utils.generateCSV(data), "csv");
      } else {
        var data, filename, link;
        var csv = this.convertArrayOfObjectsToCSV({
          data: this.$refs["sr-table"].filteredRows[0].children
        });
        if (csv == null) return;

        filename = args.filename || "export.csv";

        if (!csv.match(/^data:text\/csv/i)) {
          csv = "data:text/csv;charset=utf-8," + csv;
        }
        data = encodeURI(csv);

        link = document.createElement("a");
        link.setAttribute("href", data);
        link.setAttribute("download", filename);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    },
    async loadData() {
      this.aggregatedData = await this.loadReports1();

      this.aggregatedData.forEach(dra => {
        //console.log("dra", dra);
        dra.Date_of_assessment_ACPC = dra.Date_of_assessment_ACPC.split("T")[0];
        dra.Date_of_assessment_DCPC = dra.Date_of_assessment_DCPC.split("T")[0];
        dra.Date_of_Disaster_from = dra.Date_of_Disaster_from.split("T")[0];
        dra.Date_of_Disaster_to = dra.Date_of_Disaster_to.split("T")[0];
        dra.GVHS_affected = dra.GVHS_affected.split(",").join("|");
        //console.log("Dreaaa", dra);
        this.rows.push(dra);
      });

      //console.log(this.rows, "this is it");
      this.$refs["sr-table"].typedColumns.slice(2, 7).forEach((item, index) => {
        if (item.field === "TA_PCODE") return;
        this.$refs["sr-table"].typedColumns[
          index + 2
        ].filterOptions.filterDropdownItems = _.uniq(
          _.map(this.rows, item.field)
        )
          .sort()
          .map(function(o) {
            if (o != "") {
              return {
                value: o,
                text: o
              };
            } else {
              return {
                value: o,
                text: "empty"
              };
            }
          });

        let inputs = [
          'input[placeholder="dateacpc"]',
          'input[placeholder="datedcpc"]',
          'input[placeholder="datefrom"]',
          'input[placeholder="dateto"]'
        ];
        inputs.forEach(function(input) {
          flatPickr(input, {
            dateFormat: "m-d-Y",
            mode: "range",
            allowInput: true
          });
        });
      });
      this.isLoaded = false;
    },
    formatedDate(data) {
      let finalDate = dateformat(data, "dd-mm-yyyy");
      return finalDate;
    },
    processExcel(item, dinformDataset) {
      this.downloadData.all = item;
      //console.log("IIIIIII", item,dinformDataset);
      this.downloadData.all.dinr = dinformDataset.dinr;
      this.downloadData.all.oid = item.oid;
      this.draFormsData.push(item);

      for (let i = 0; i < this.draFormsData.length; i++) {
        for (let a = 0; a < this.draFormsData[i].villages.length; a++) {
          this.villagesArray.push(this.draFormsData[i].villages[a].name);
        }

        for (let a = 0; a < this.draFormsData[i].camps.length; a++) {
          this.campsArray.push(this.draFormsData[i].camps[a].name);
        }
      }

      this.numberOfTAs++;

      let Gvharray = [];

      this.downloadData.dinrform = {
        ...this.downloadData.dinrform,
        ...dinformDataset
      };

      for (let i = 0; i < item.gvhs.length; i++) {
        let GVHname = item.gvhs[i].name;

        Gvharray.push(GVHname);
      }

      this.downloadData.all.dinrform = this.downloadData.dinrform;
      this.downloadData.all.oid = item._id;

      try {
        this.downloadData.all.dinrform.doaAcpc = this.formatedDate(
          this.downloadData.dinrform.doaAcpc
        );
      } catch (error) {
        this.downloadData.all.dinrform.doaAcpc = "";
      }

      try {
        this.downloadData.all.dinrform.doaDcpc = this.formatedDate(
          this.downloadData.dinrform.doaDcpc
        );
      } catch (error) {
        this.downloadData.all.dinrform.doaDcpc = "";
      }

      try {
        this.downloadData.all.dinrform.dodFrom = this.formatedDate(
          this.downloadData.dinrform.dodFrom
        );
      } catch (error) {
        this.downloadData.all.dinrform.dodFrom = "";
      }

      try {
        this.downloadData.all.dinrform.dodTo = this.formatedDate(
          this.downloadData.dinrform.dodTo
        );
      } catch (error) {
        this.downloadData.all.dinrform.dodTo = "";
      }

      try {
        this.downloadData.all.gvhsAffected = Gvharray.join();
      } catch (error) {
        this.downloadData.all.gvhsAffected = "";
      }

      try {
        this.downloadData.all.is_food_available_food = this.returnFieldvalue(
          item,
          "food",
          "food_availability",
          "foodavailable"
        );
      } catch (error) {
        this.downloadData.all.is_food_available_food = "";
      }

      try {
        this.downloadData.all.medical_supply_availability = this.returnFieldItemvalue(
          item,
          "health",
          "available_health_medical",
          "Medical supply"
        );
      } catch (error) {
        this.downloadData.all.medical_supply_availability = "";
      }

      try {
        this.downloadData.all.health_personel_availability = this.returnFieldItemvalue(
          item,
          "health",
          "available_health_medical",
          "Health personel"
        );
      } catch (error) {
        this.downloadData.all.health_personel_availability = "";
      }

      try {
        this.downloadData.all.road_access =
          item.sectors.logistics.access_of_structures[0].accessibility;
      } catch (error) {
        this.downloadData.all.road_access = "";
      }

      try {
        this.downloadData.all.food_1_2_months = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "female_HH",
          "category",
          "No. of HH with 1-2 month Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_1_2_months = 0;
      }

      try {
        this.downloadData.all.food_2_months = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "female_HH",
          "category",
          "No. of HH with more than 2 months Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_2_months = 0;
      }

      try {
        this.downloadData.all.food_stock_lost = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "female_HH",
          "category",
          "No. of HH who lost Food Stock"
        );
      } catch (error) {
        this.downloadData.all.food_stock_lost = 0;
      }

      try {
        this.downloadData.all.food_less_1_month = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "female_HH",
          "category",
          "No. of HH with less than 1 month Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_less_1_month = 0;
      }

      try {
        this.downloadData.all.food_less_1_month_male = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "male_HH",
          "category",
          "No. of HH with less than 1 month Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_less_1_month_male = 0;
      }

      try {
        this.downloadData.all.food_1_2_months_male = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "male_HH",
          "category",
          "No. of HH with 1-2 month Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_1_2_months_male = 0;
      }

      try {
        this.downloadData.all.food_2_months_male = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "male_HH",
          "category",
          "No. of HH with more than 2 months Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_2_months_male = 0;
      }

      try {
        this.downloadData.all.food_stock_lost_male = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "male_HH",
          "category",
          "No. of HH who lost Food Stock"
        );
      } catch (error) {
        this.downloadData.all.food_stock_lost_male = 0;
      }

      try {
        this.downloadData.all.food_item_damage = this.sumArrayValues(
          item,
          "agriculture",
          "food_item_damage",
          "number_of_kilos"
        );
      } catch (error) {
        this.downloadData.all.food_item_damage = 0;
      }
      try {
        this.downloadData.all.PeopleAffectedrows_female = this.sumArrayValues(
          item,
          "displaced",
          "PeopleAffectedrows",
          "number_displaced_by_gender_fhh"
        );
      } catch (error) {
        this.downloadData.all.PeopleAffectedrows_female = 0;
      }

      try {
        this.downloadData.all.PeopleAffectedrows_male = this.sumArrayValues(
          item,
          "displaced",
          "PeopleAffectedrows",
          "number_displaced_by_gender_mhh"
        );
      } catch (error) {
        this.downloadData.all.PeopleAffectedrows_male = 0;
      }

      try {
        this.downloadData.all.displaced_hh =
          parseInt(this.downloadData.all.PeopleAffectedrows_female) +
          parseInt(this.downloadData.all.PeopleAffectedrows_male);
      } catch (error) {
        this.downloadData.all.displaced_hh = 0;
      }
      try {
        this.downloadData.all.hectares_submerged = this.sumArrayValues(
          item,
          "agriculture",
          "crops_damaged",
          "hectares_submerged"
        );
      } catch (error) {
        this.downloadData.all.hectares_submerged = 0;
      }

      try {
        this.downloadData.all.hectares_washed_away = this.sumArrayValues(
          item,
          "agriculture",
          "crops_damaged",
          "hectares_washed_away"
        );
      } catch (error) {
        this.downloadData.all.hectares_washed_away = 0;
      }

      try {
        this.downloadData.all.impact_on_crops_hh_affected = this.sumArrayValues(
          item,
          "agriculture",
          "impact_on_crops",
          "hh_affected"
        );
      } catch (error) {
        this.downloadData.all.impact_on_crops_hh_affected = 0;
      }

      try {
        this.downloadData.all.impact_on_crops_hectares_damaged = this.sumArrayValues(
          item,
          "agriculture",
          "impact_on_crops",
          "hectares_damaged"
        );
      } catch (error) {
        this.downloadData.all.impact_on_crops_hectares_damaged = 0;
      }

      try {
        this.downloadData.all.impact_on_livestock_hh = this.sumArrayValues(
          item,
          "agriculture",
          "impact_on_livestock",
          "hh_affected_l"
        );
      } catch (error) {
        this.downloadData.all.impact_on_livestock_hh = 0;
      }

      try {
        this.downloadData.all.impact_on_livestock_la = this.sumArrayValues(
          item,
          "agriculture",
          "impact_on_livestock",
          "livestock_affected"
        );
      } catch (error) {
        this.downloadData.all.impact_on_livestock_la = 0;
      }

      try {
        this.downloadData.all.displaced_households_accommodated_male = this.sumArrayValues(
          item,
          "displaced",
          "displaced_households_accommodated",
          "accomodated_males_hh"
        );
      } catch (error) {
        this.downloadData.all.displaced_households_accommodated_male = 0;
      }
      try {
        this.downloadData.all.displaced_households_accommodated_female = this.sumArrayValues(
          item,
          "displaced",
          "displaced_households_accommodated",
          "accomodated_females_hh"
        );
      } catch (error) {
        this.downloadData.all.displaced_households_accommodated_female = 0;
      }

      try {
        this.downloadData.all.displaced_disagregated_male = this.sumArrayValues(
          item,
          "displaced",
          "displaced_disaggregated",
          "displaced_males"
        );
      } catch (error) {
        this.downloadData.all.displaced_disagregated_male = 0;
      }

      try {
        this.downloadData.all.displaced_disagregated_female = this.sumArrayValues(
          item,
          "displaced",
          "displaced_disaggregated",
          "displaced_females"
        );
      } catch (error) {
        this.downloadData.all.displaced_disagregated_female = 0;
      }

      try {
        this.downloadData.all.displaced_individuals_accommodated_male = this.sumArrayValues(
          item,
          "displaced",
          "displaced_individuals_accommodated",
          "accomodated_males"
        );
      } catch (error) {
        this.downloadData.all.displaced_individuals_accommodated_male = 0;
      }

      try {
        this.downloadData.all.displaced_individuals_accommodated_female = this.sumArrayValues(
          item,
          "displaced",
          "displaced_individuals_accommodated",
          "accomodated_females"
        );
      } catch (error) {
        this.downloadData.all.displaced_individuals_accommodated_female = 0;
      }

      try {
        this.downloadData.all.education_building_functioning = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "functioning_buildings"
        );
      } catch (error) {
        this.downloadData.all.education_building_functioning = 0;
      }

      try {
        this.downloadData.all.education_building_partly_functioning = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "partially_functioning_buildings"
        );
      } catch (error) {
        this.downloadData.all.education_building_partly_functioning = 0;
      }

      try {
        this.downloadData.all.education_closed_buildings = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "closed_buildings"
        );
      } catch (error) {
        this.downloadData.all.education_closed_buildings = 0;
      }

      try {
        this.downloadData.all.education_females_out_of_school = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "females_out_of_school"
        );
      } catch (error) {
        this.downloadData.all.education_females_out_of_school = 0;
      }

      try {
        this.downloadData.all.education_males_out_of_school = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "males_out_of_school"
        );
      } catch (error) {
        this.downloadData.all.education_males_out_of_school = 0;
      }

      try {
        this.downloadData.all.education_underwater = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "underwater"
        );
      } catch (error) {
        this.downloadData.all.education_underwater = 0;
      }

      try {
        this.downloadData.all.education_completely_damaged = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "completely_damaged"
        );
      } catch (error) {
        this.downloadData.all.education_completely_damaged = 0;
      }

      try {
        this.downloadData.all.health_partially_functioning =
          this.sumArrayValues(
            item,
            "health",
            "available_health_facilities",
            "partially_functioning"
          ) +
          this.sumArrayValues(
            item,
            "health",
            "other_health_facilities",
            "partialy_functioning"
          );
      } catch (error) {
        this.downloadData.all.health_partially_functioning = 0;
      }

      try {
        this.downloadData.all.health_verge_of_closing =
          this.sumArrayValues(
            item,
            "health",
            "available_health_facilities",
            "verge_of_closing"
          ) +
          this.sumArrayValues(
            item,
            "health",
            "other_health_facilities",
            "verge_of_closing"
          );
      } catch (error) {
        this.downloadData.all.health_verge_of_closing = 0;
      }

      try {
        this.downloadData.all.health_closed =
          this.sumArrayValues(
            item,
            "health",
            "available_health_facilities",
            "closed"
          ) +
          this.sumArrayValues(
            item,
            "health",
            "other_health_facilities",
            "closed"
          );
      } catch (error) {
        this.downloadData.all.health_closed = 0;
      }

      try {
        this.downloadData.all.livelihoods_slightly_affected = this.sumArrayValues(
          item,
          "livelihoods",
          "livelihoods_affected",
          "severely_affected"
        );
      } catch (error) {
        this.downloadData.all.livelihoods_slightly_affected = 0;
      }
      try {
        this.downloadData.all.livelihoods_severely_affected = this.sumArrayValues(
          item,
          "livelihoods",
          "livelihoods_affected",
          "slightly_affected"
        );
      } catch (error) {
        this.downloadData.all.livelihoods_severely_affected = 0;
      }

      try {
        this.downloadData.all.impact_on_vulnerable_persons_males = this.sumArrayValues(
          item,
          "protection",
          "impact_on_vulnerable_persons",
          "impacted_males"
        );
      } catch (error) {
        this.downloadData.all.impact_on_vulnerable_persons_males = 0;
      }

      try {
        this.downloadData.all.impact_on_vulnerable_persons_females = this.sumArrayValues(
          item,
          "protection",
          "impact_on_vulnerable_persons",
          "impacted_females"
        );
      } catch (error) {
        this.downloadData.all.impact_on_vulnerable_persons_females = 0;
      }

      try {
        this.downloadData.all.nutrition_affected_pop_male = this.sumArrayValues(
          item,
          "nutrition",
          "affected_population",
          "affected_males"
        );
      } catch (error) {
        this.downloadData.all.nutrition_affected_pop_male = 0;
      }

      try {
        this.downloadData.all.nutrition_affected_pop_female = this.sumArrayValues(
          item,
          "nutrition",
          "affected_population",
          "affected_females"
        );
      } catch (error) {
        this.downloadData.all.nutrition_affected_pop_female = 0;
      }

      try {
        this.downloadData.all.shelter_without_shelter_male = this.sumArrayValues(
          item,
          "shelter",
          "people_without_shelter",
          "males"
        );
      } catch (error) {
        this.downloadData.all.shelter_without_shelter_male = 0;
      }

      try {
        this.downloadData.all.shelter_without_shelter_female = this.sumArrayValues(
          item,
          "shelter",
          "people_without_shelter",
          "females"
        );
      } catch (error) {
        this.downloadData.all.shelter_without_shelter_female = 0;
      }

      try {
        this.downloadData.all.shelter_people_dead_female = this.sumArrayValues(
          item,
          "shelter",
          "PeopleDeadrows",
          "females_dead"
        );
      } catch (error) {
        this.downloadData.all.shelter_people_dead_female = 0;
      }

      try {
        this.downloadData.all.shelter_people_dead_male = this.sumArrayValues(
          item,
          "shelter",
          "PeopleDeadrows",
          "males_dead"
        );
      } catch (error) {
        this.downloadData.all.shelter_people_dead_male = 0;
      }

      try {
        this.downloadData.all.shelter_dead =
          parseInt(this.downloadData.all.shelter_people_dead_male) +
          parseInt(this.downloadData.all.shelter_people_dead_female);
      } catch (error) {
        this.downloadData.all.shelter_dead = 0;
      }

      try {
        this.downloadData.all.shelter_people_injured_female = this.sumArrayValues(
          item,
          "shelter",
          "PeopleInjuredrows",
          "people_injured_females"
        );
      } catch (error) {
        this.downloadData.all.shelter_people_injured_female = 0;
      }

      try {
        this.downloadData.all.shelter_people_injured_male = this.sumArrayValues(
          item,
          "shelter",
          "PeopleInjuredrows",
          "people_injured_males"
        );
      } catch (error) {
        this.downloadData.all.shelter_people_injured_male = 0;
      }

      try {
        this.downloadData.all.shelter_injured =
          parseInt(this.downloadData.all.shelter_people_injured_female) +
          parseInt(this.downloadData.all.shelter_people_injured_male);
      } catch (error) {
        this.downloadData.all.shelter_injured = 0;
      }

      try {
        this.downloadData.all.shelter_fhh_affected = this.sumArrayValues(
          item,
          "shelter",
          "PeopleAffectedrows",
          "damaged_fhh"
        );
      } catch (error) {
        this.downloadData.all.shelter_fhh_affected = 0;
      }

      try {
        this.downloadData.all.shelter_mhh_affected = this.sumArrayValues(
          item,
          "shelter",
          "PeopleAffectedrows",
          "damaged_mhh"
        );
      } catch (error) {
        this.downloadData.all.shelter_mhh_affected = 0;
      }

      try {
        this.downloadData.all.shelter_affected_hh =
          parseInt(this.downloadData.all.shelter_mhh_affected) +
          parseInt(this.downloadData.all.shelter_fhh_affected);
      } catch (error) {
        this.downloadData.all.shelter_affected_hh = "";
      }

      this.download.push({ ...this.downloadData.all });
      var dataset = this.downloadData.all;
      //console.log("DDDAta set ", dataset);
      this.rows.push({
        dinr: dataset.dinr,
        oid: dataset.oid,
        Disaster: dataset.dinrform.disaster,
        Region: "",
        District: dataset.dinrform.district,
        TA_PCODE: dataset.admin3.admin3Pcode,
        TA: dataset.admin3.admin3Name_en,
        GVHS_affected: dataset.gvhs.map(v => v.name).join('|'),
        Date_of_assessment_ACPC: dataset.dinrform.doaAcpc,
        Date_of_assessment_DCPC: dataset.dinrform.doaDcpc,
        Date_of_Disaster_from: dataset.dinrform.dodFrom,
        Date_of_Disaster_to: dataset.dinrform.dodTo,
        food_items_damaged_KGs_agriculture: dataset.food_item_damage,
        number_of_crop_hectares_submerged_agriculture: dataset.hectares_submerged,
        number_crop_hectares_washed_off_agriculture: dataset.hectares_washed_away,
        number_of_households_whose_crops_are_impacted_agriculture: dataset.impact_on_crops_hh_affected,
        //
        of_crop_hectares_damaged_in_affected_households_agriculture:dataset.food_item_damage,
        hh_affected_per_impacted_livestock_agriculture: dataset.impact_on_livestock_hh,
        number_of_impacted_livestock_agriculture: dataset.impact_on_livestock_la,
        number_male_HH_accomadeted_displaced: dataset.displaced_households_accommodated_male,
        number_female_HH_accomadeted_displaced: dataset.displaced_households_accommodated_female,
        number_of_males_disaggregated_displaced: dataset.displaced_disagregated_male,
        number_of_females_disaggregated_displaced: dataset.displaced_disagregated_female,
        number_of_males_accomodated_displaced: dataset.displaced_individuals_accommodated_male,
        number_of_females_accomodated_displaced: dataset.displaced_individuals_accommodated_female,
        number_of_school_buildings_functioning_education: dataset.education_building_functioning,
        number_of_school_buildings_underwater_education:dataset.education_underwater,
        number_of_school_buildings_completely_damaged_education:dataset.education_completely_damaged,
        number_of_school_buildings_partially_functioning_education:dataset.education_building_partly_functioning,
        number_of_school_buildings_closed_education: dataset.education_closed_buildings,
        males_of_out_school_education: dataset.education_males_out_of_school,
        females_of_out_school_education: dataset.education_females_out_of_school,
        is_food_available_food: dataset.is_food_available_food,
        number_of_facilities_partially_functioning_health: dataset.health_partially_functioning,
        number_of_facilities_on_verge_of_closing_health: dataset.health_verge_of_closing,
        number_facilities_closed_health: dataset.health_closed,
        state_of_medical_supply_availability_health: dataset.medical_supply_availability,
        state_of_health_personnel_availability_health: dataset.health_personel_availability,
        number_severely_affected_livelihoods: dataset.livelihoods_severely_affected,
        number_slightly_affected_livelihoods: dataset.livelihoods_slightly_affected,
        //
        impacted_females_protection: dataset.medical_supply_availability,
        impacted_males_protection: dataset.medical_supply_availability,
        state_of_access_to_main_roads_logistics: dataset.road_access,
        number_of_affected_males_nutrition: dataset.nutrition_affected_pop_male,
        number_of_affected_females_nutrition: dataset.nutrition_affected_pop_female,
        //
        FHH_with_safe_water_wash: dataset.medical_supply_availability,
        MHH_with_safe_water_wash: dataset.medical_supply_availability,
        FHH_with_toilet_access_wash: dataset.medical_supply_availability,
        MHH_with_toilet_access_wash: dataset.medical_supply_availability,
        FHH_risking_contamination_wash:dataset.medical_supply_availability,
        MHH_risking_contamination_wash: dataset.medical_supply_availability,

        Houses_partly_damaged_shelter: dataset.medical_supply_availability,
        Houses_underwater_shelter: dataset.medical_supply_availability,
        Houses_completely_shelter: dataset.medical_supply_availability,
        //
        number_of_males_without_shelter_shelter: dataset.medical_supply_availability,
        number_of_females_without_shelter_shelter:dataset.medical_supply_availability,
        number_of_female_HH_with_houses_damaged_shelter: dataset.medical_supply_availability,
        //
        number_of_male_HH_with_houses_damaged_shelter: dataset.shelter_fhh_affected,
        number_of_injured_females_shelter: dataset.shelter_people_injured_female,
        number_of_injured_males_in_category_shelter:dataset.shelter_people_injured_male,
        number_of_dead_females_shelter: dataset.shelter_people_dead_female,
        number_of_dead_males_shelter: dataset.shelter_people_dead_male,

        number_of_displaced_male_displaced: dataset.medical_supply_availability,
        number_of_displaced_females_displaced: dataset.medical_supply_availability,

        No_of_FHH_with_1_2_month_Food_Availability_food: dataset.food_1_2_months,
        No_of_FHH_with_less_than_1_month_Food_Availability_food: dataset.food_less_1_month,
        No_of_FHH_with_more_than_2_months_Food_Availability_food: dataset.food_2_months,
        No_of_FHH_who_lost_Food_Stock_food: dataset.food_stock_lost,
        No_of_MHH_with_1_2_month_Food_Availability_food: dataset.food_1_2_months_male,
        No_of_MHH_with_less_than_1_month_Food_Availability_food: dataset.food_less_1_month_male,
        No_of_MHH_with_more_than_2_months_Food_Availability_food: dataset.food_2_months_male,
        No_of_MHH_who_lost_Food_Stock_food: dataset.food_stock_lost_male
      });
    },
    processGVHExcel() {
      let gvhdata = [];
      for (let k in this.download) {
        try {
          this.download[k].all.sectors.shelter =
            this.download[k].all.sectors.shelter === undefined
              ? (this.download[k].all.sectors.shelter = [
                  {
                    damaged_fhh: "",
                    damaged_mhh: "",
                    fully_blown_roof: "",
                    partly_blown_roof: "",
                    burnt: ""
                  }
                ])
              : this.download[k].all.sectors.shelter;

          this.download[k].all.sectors.displaced =
            this.download[k].all.sectors.displaced === undefined
              ? (this.download[k].all.sectors.displaced = [
                  {
                    number_displaced_by_gender_fhh: "",
                    number_displaced_by_gender_mhh: ""
                  }
                ])
              : this.download[k].all.sectors.displaced;

          if (
            typeof this.download[k].all.sectors.shelter.PeopleInjuredrows !==
              "undefined" ||
            typeof this.download[k].all.sectors.shelter.PeopleAffectedrows !==
              "undefined" ||
            typeof this.download[k].all.sectors.shelter.PeopleDeadrows !==
              "undefined" ||
            typeof this.download[k].all.sectors.displaced.PeopleAffectedrows !==
              "undefined"
          ) {
            let districtdata = {
              DistasterID: this.download[k].all.dinrform.dinr,
              DisasterName: this.download[k].all.dinrform.disaster,
              country: this.download[k].all.dinrform.country,
              country_pcode: this.download[k].all.dinrform.country_pcode,

              district: this.download[k].all.dinrform.district,
              TAname: this.download[k].all.admin3.admin3Name_en,
              TApcode: this.download[k].all.admin3.admin3Pcode,
              dstart: this.download[k].all.dinrform.dodFrom,
              dend: this.download[k].all.dinrform.dodTo,
              dacpc: this.download[k].all.dinrform.doaAcpc,
              ddcpc: this.download[k].all.dinrform.doaDcpc,
              submited: this.download[k].all.dinrform.date,
              gvhs: this.download[k].all.gvhs,
              shelter_peopleaffected:
                this.download[k].all.sectors.shelter.PeopleAffectedrows ===
                undefined
                  ? (this.download[
                      k
                    ].all.sectors.shelter.PeopleAffectedrows = [])
                  : this.download[k].all.sectors.shelter.PeopleAffectedrows,

              shelter_peopleinjured: this.download[k].all.sectors.shelter
                .PeopleInjuredrows,
              shelter_peopledead: this.download[k].all.sectors.shelter
                .PeopleDeadrows,

              shelter_urgent_response: this.download[k].all.sectors.shelter
                .urgent_response_needed,
              shelter_longterm_response: this.download[k].all.sectors.shelter
                .response_needed,

              displaced_peopleaffected:
                this.download[k].all.sectors.displaced.PeopleAffectedrows ===
                undefined
                  ? (this.download[
                      k
                    ].all.sectors.displaced.PeopleAffectedrows = [])
                  : this.download[k].all.sectors.displaced.PeopleAffectedrows,

              displaced_urgent_response:
                this.download[k].all.sectors.displaced.urgent_response_needed ==
                undefined
                  ? ""
                  : this.download[k].all.sectors.displaced
                      .urgent_response_needed,
              displaced_longterm_response:
                this.download[k].all.sectors.displaced.response_needed ==
                undefined
                  ? ""
                  : this.download[k].all.sectors.displaced.response_needed
            };

            for (let i in districtdata.gvhs) {
              let gvhname = districtdata.gvhs[i].name;

              let femalesInjured = 0;
              let malesInjured = 0;

              let femalesDead = 0;
              let malesDead = 0;

              let diplaced_fhh = "";
              let diplaced_mhh = "";

              let total_injured = 0;

              try {
                if (
                  typeof districtdata.shelter_peopleinjured == "undefined" ||
                  typeof districtdata.shelter_peopledead == "undefined"
                ) {
                  femalesInjured = 0;
                  malesInjured = 0;
                  femalesDead = 0;
                  malesDead = 0;

                  diplaced_fhh = "";
                  diplaced_mhh = "";

                  total_injured = 0;
                } else {
                  femalesInjured = districtdata.shelter_peopleinjured.find(
                    item => item.name == gvhname
                  ).people_injured_females;

                  malesInjured = districtdata.shelter_peopleinjured.find(
                    item => item.name == gvhname
                  ).people_injured_males;

                  total_injured =
                    parseInt(femalesInjured || 0) + parseInt(malesInjured || 0);
                  femalesDead = districtdata.shelter_peopledead.find(
                    item => item.name == gvhname
                  ).females_dead;

                  malesDead = districtdata.shelter_peopledead.find(
                    item => item.name == gvhname
                  ).males_dead;

                  diplaced_fhh = districtdata.displaced_peopleaffected.find(
                    item => item.name == gvhname
                  ).number_displaced_by_gender_fhh;

                  diplaced_mhh = districtdata.displaced_peopleaffected.find(
                    item => item.name == gvhname
                  ).number_displaced_by_gender_mhh;
                }
                gvhdata.push({
                  sheet: "GVH-BASED DATA",

                  DistasterID: districtdata.DistasterID,
                  Disaster: districtdata.DisasterName,
                  TA: districtdata.TAname,
                  "TA PCODE": districtdata.TApcode,
                  District: districtdata.district,
                  GVH: gvhname,

                  "Disaster Start Date": districtdata.dstart,
                  "Disaster End Date": districtdata.dend,
                  "ACPC Assessment Date": districtdata.dacpc,
                  "DCPC Assessement Date": districtdata.ddcpc,
                  "Submission Date": districtdata.submited,

                  "Female Households affected (shelter)": districtdata.shelter_peopleaffected.find(
                    item => item.name == gvhname
                  ).damaged_fhh,

                  "Male Households affected (shelter)": districtdata.shelter_peopleaffected.find(
                    item => item.name == gvhname
                  ).damaged_mhh,

                  "Total Households affected (shelter)":
                    parseInt(
                      districtdata.shelter_peopleaffected.find(
                        item => item.name == gvhname
                      ).damaged_mhh || 0
                    ) +
                    parseInt(
                      districtdata.shelter_peopleaffected.find(
                        item => item.name == gvhname
                      ).damaged_mhh || 0
                    ),

                  "Houses With fully blown roofs (shelter)": districtdata.shelter_peopleaffected.find(
                    item => item.name == gvhname
                  ).fully_blown_roof,

                  "Houses with partly blown roofs (shelter)": districtdata.shelter_peopleaffected.find(
                    item => item.name == gvhname
                  ).partly_blown_roof,

                  "Houses with walls damaged (shelter)": districtdata.shelter_peopleaffected.find(
                    item => item.name == gvhname
                  ).wall_damaged,

                  "Houses burnt (shelter)": districtdata.shelter_peopleaffected.find(
                    item => item.name == gvhname
                  ).burnt,

                  "Number of females injured (shelter)": femalesInjured,
                  "Number of males injured (shelter)": malesInjured,

                  "Total people injured (shelter)": total_injured,

                  "Number of females dead (shelter)": femalesDead,

                  "Number of males dead (shelter)": malesDead,

                  "Total people dead (shelter)":
                    parseInt(malesDead || 0) + parseInt(femalesDead || 0),

                  "Urgent needed (shelter)":
                    districtdata.shelter_urgent_response,
                  "General response needed (shelter)":
                    districtdata.shelter_longterm_response,

                  "Female Households affected (displaced)": diplaced_fhh,

                  "Male Households affected (displaced)": diplaced_mhh,

                  "Total Households affected (displaced)":
                    parseInt(diplaced_fhh || 0) + parseInt(diplaced_mhh || 0),

                  "Urgent response needed (displaced)":
                    districtdata.displaced_urgent_response,
                  "Long-term response needed (displaced)":
                    districtdata.displaced_longterm_response
                });
              } catch (error) {
                // console.log(error);
              }
            }
          } else {
            this.download[k].all.sectors.shelter.PeopleAffectedrows = [];
            this.download[k].all.sectors.shelter.PeopleDeadrows = [];
            this.download[k].all.sectors.shelter.PeopleMissingrows = [];
            this.download[k].all.sectors.shelter.PeopleInjuredrows = [];

            this.download[k].all.sectors.shelter.urgent_response_needed = "";
            this.download[k].all.sectors.shelter.response_needed = "";
            this.download[k].all.sectors.displaced.urgent_response_needed = "";
            this.download[k].all.sectors.displaced.response_needed = "";
          }

          this.gvhDATA = gvhdata;
        } catch (error) {
          // console.log(error);
        }
      }
    }
  },
  async mounted() {
    try {
      this.isLoaded = true; // Set loading state

      //this.loadData();
      let dinrs = [...(await this.getDinrsAction())];
      let dras = [...(await this.getDrasAction())];

      let DINRData = await dinrs;
      let DRASData = await dras;

      // Initialize arrays if they don't exist
      if (!Array.isArray(DINRData)) {
        console.warn('DINRData is not an array:', DINRData);
        DINRData = [];
      }
      if (!Array.isArray(DRASData)) {
        console.warn('DRASData is not an array:', DRASData);
        DRASData = [];
      }

      this.allforms = [...DINRData];

      this.allforms.forEach((dinr, index) => {
        if (!dinr || !dinr._id) {
          console.warn('Invalid dinr object at index', index, dinr);
          return;
        }

        var draarr = [];
        DRASData.forEach((dra, point) => {
          if (dra && dinr._id == dra.dinrFormId) {
            draarr.push({ ...dra });
          }
        });
        dinr.dra = [...draarr];
      });

      // Sort data before processing
      DINRData.sort(function(a, b) {
        return new Date(b.createdon) - new Date(a.createdon);
      });

      DINRData.forEach((dinr, index) => {
        if (!dinr || !dinr._id || !dinr.district || !dinr.account) {
          console.warn('Invalid dinr data at index', index, dinr);
          return;
        }

        try {
          this.tableData.push({
            id: index + 1,
            disaster: dinr.disaster || 'Unknown',
            district: dinr.district.admin2_name_en || 'Unknown',
            date: this.formatedDate(dinr.createdon),
            officer: (dinr.account.firstName || '') + " " + (dinr.account.lastName || ''),
            dinr: dinr._id,
            status: dinr.status || 'Unknown'
          });
        } catch (error) {
          console.error('Error processing dinr data at index', index, error, dinr);
        }

      let dinformDataset = {
        disaster: DINRData[index].disaster,
        district: DINRData[index].district.admin2_name_en,
        date: this.formatedDate(DINRData[index].createdon),
        doaAcpc: DINRData[index].doaAcpc,
        doaDcpc: DINRData[index].doaDcpc,
        dodFrom: DINRData[index].dodFrom,
        dodTo: DINRData[index].dodTo,
        officer:
          DINRData[index].account.firstName +
          " " +
          DINRData[index].account.lastName,
        dinr: DINRData[index]._id,
        country: "MALAWI",
        country_pcode: "MWI"
        //status:  DINRData[index].status
      };

      let dra = dras.filter(dra => dra.dinrFormId == row);

      this.draFormsData = [];

      this.downloadData.dinrform = {};

      dra.forEach((item, index, array) => {
        this.processExcel(item, dinformDataset);
      });
    });

    this.processGVHExcel();

    admin2s.get().then(response => {
      //console.log(response.data);
      this.admin2sData = response.data;
    });
  },
  computed: {
    ...mapGetters({
      getFlatdinrs: "getFlatdinrs"
    })
  }
};
</script>
<style scoped>
.no-border-card .card-footer {
  border-top: 0;
}
.card-body {
  padding: 2px;
}
.list-group {
  max-height: 57vh;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
}
::-webkit-scrollbar {
  width: 20px;
}
::-webkit-scrollbar-track {
  background-color: transparent;
}
::-webkit-scrollbar-thumb {
  background-color: #d6dee1;
  border-radius: 20px;
  border: 6px solid transparent;
  background-clip: content-box;
}
</style>
