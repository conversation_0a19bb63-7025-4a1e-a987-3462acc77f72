
.vgt-input, .vgt-select{
  width: 100%;
  height: 32px;
  line-height: 1;
  display: block;
  font-size: 14px;
  font-weight: normal;
  padding: 6px 12px;
  color: $text-color;
  border-radius: 4px;
  box-sizing: border-box;
  background-image: none;
  background-color: #fff;
  border: 1px solid $input-border-color;
  transition: border-color .2s cubic-bezier(.645,.045,.355,1);
  &::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
    color: $text-color;
    opacity: 0.3; /* Firefox */
  }
  &:focus{
    outline: none;
    border-color: $link-color;
  }
}

