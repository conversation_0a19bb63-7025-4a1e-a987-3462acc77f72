import Fuse from 'fuse.js';
import dateformat from "dateformat";
export default {
  computed: {
    /***
     * Returns a page from the searched data or the whole data. Search is performed in the watch section below
     */
    queriedData() {
      let result = this.tableData;
      if (this.searchedData.length > 0) {
        result = this.searchedData;
      } else {
        if (this.searchQuery) {
          result = []
        }
      }
      return result.slice(this.from, this.to);
    },
    to() {
      let highBound = this.from + this.pagination.perPage;
      if (this.total < highBound) {
        highBound = this.total;
      }
      return highBound;
    },
    from() {
      return this.pagination.perPage * (this.pagination.currentPage - 1);
    },
    total() {
      return this.searchedData.length > 0
        ? this.searchedData.length
        : this.tableData.length;
    }
  },
  data() {
    return {
      pagination: {
        perPage: 10,
        currentPage: 1,
        perPageOptions: [5, 10, 25, 50],
        total: 0
      },
      searchQuery: '',
      searchedData: [],
      fuseSearch: null
    }
  },
  methods: {
    switchReportByDateSubmited(submitedAt,cutDate=new Date('2022-06-01'),type="details",id){
      let submittedDate=new Date(submitedAt)
      if (submittedDate > cutDate) {
        this.$router.push({
          path: "/manager/"+type+"report/" + id
        });
      }
      else {
        this.$router.push({
          path: "/manager/"+type+"reportOld/" + id
        });
      }
    },
    formatedDate(data) {
      let finalDate = dateformat(data, "dd-mm-yyyy");
      return finalDate;
    },
    sortChange({ prop, order }) {
      if (prop) {
        this.tableData.sort((a, b) => {
          let aVal = a[prop]
          let bVal = b[prop]
          if (order === 'ascending') {
            return aVal > bVal ? 1 : -1
          }
          return bVal - aVal ? 1 : -1
        })
      } else {
        this.tableData.sort((a, b) => {
          return a.id - b.id
        })
      }
    }
  },
  mounted() {
    // Fuse search initialization.
    this.fuseSearch = new Fuse(this.tableData, {
      keys: ['name', 'email'],
      threshold: 0.3
    });
  },
  watch: {
    /**
     * Searches through the table data by a given query.
     * NOTE: If you have a lot of data, it's recommended to do the search on the Server Side and only display the results here.
     * @param value of the query
     */
    searchQuery(value) {
      if (!value) {
        this.searchedData = this.tableData
        return
      }

      if (this.propsToSearch.length > 0) {
        const data = this.tableData.filter(item => {
          const matches = []

          for (const filter of this.propsToSearch) {
            if (item[filter]) {
              if (item[filter].toLowerCase().includes(value.toLowerCase())) {
                matches.push(item)
              }
            }
          }

          if (matches.length > 0) {
            return item
          }
        })

        this.searchedData = data
        return
      }

      const result = this.fuseSearch.search(this.searchQuery);
      return result
      // let result = this.tableData;
      // if (value !== '') {
      //   if (this)
      //   // result = this.fuseSearch.search(this.searchQuery);
      //   console.log(value, this.propsToSearch)
      // }
      // this.searchedData = result;
    }
  }
}
