[33mce85d818[m[33m ([m[1;36mHEAD -> [m[1;32mmika[m[33m)[m HEAD@{0}: reset: moving to HEAD
[33mce85d818[m[33m ([m[1;36mHEAD -> [m[1;32mmika[m[33m)[m HEAD@{1}: commit: changes
[33m34cf93d9[m HEAD@{2}: commit: added shalter and displayed
[33m8003334e[m HEAD@{3}: commit: shelter report part fiexed
[33m73049bae[m HEAD@{4}: commit: fixing detailed report
[33mf7ccfebd[m[33m ([m[1;31morigin/mika_extension[m[33m)[m HEAD@{5}: pull origin mika_extension: Fast-forward
[33m9a9dd58c[m HEAD@{6}: pull: Fast-forward
[33mc43aef3d[m HEAD@{7}: reset: moving to HEAD
[33mc43aef3d[m HEAD@{8}: pull: Fast-forward
[33m8661e47d[m HEAD@{9}: reset: moving to HEAD
[33m8661e47d[m HEAD@{10}: pull origin notifications: Fast-forward
[33m2ebc01ad[m HEAD@{11}: commit: cleaned trends chanrts
[33m9d76a808[m HEAD@{12}: commit: improving drmis dashbaord
[33mcc9f03a3[m HEAD@{13}: commit: changed icon for food
[33m4de9cefb[m HEAD@{14}: commit (merge): improved ui and dashbaords
[33m4ad497f3[m HEAD@{15}: commit (merge): fixed conflicts
[33mf70fc727[m HEAD@{16}: commit: changes
[33mb748f5f5[m HEAD@{17}: commit: removed spacing and border
[33m8ef91a5a[m HEAD@{18}: commit: fixed button and scrollbar on login window
[33me38d7654[m[33m ([m[1;32mmaster[m[33m)[m HEAD@{19}: checkout: moving from master to mika
[33me38d7654[m[33m ([m[1;32mmaster[m[33m)[m HEAD@{20}: commit: login page
[33m450a87d7[m[33m ([m[1;31morigin/master[m[33m, [m[1;31morigin/HEAD[m[33m)[m HEAD@{21}: commit (merge): working on login
[33m772f7313[m HEAD@{22}: pull origin notifications: Merge made by the 'recursive' strategy.
[33mba17e079[m HEAD@{23}: commit (merge): fixed preliminary list
[33m1314e5f4[m HEAD@{24}: commit (merge): fix conflict
[33m2e62f86c[m HEAD@{25}: commit (merge): 'CHANGES'
[33mfe80cf24[m HEAD@{26}: checkout: moving from notifications to master
[33maf63881d[m[33m ([m[1;32mnotifications[m[33m)[m HEAD@{27}: checkout: moving from master to notifications
[33mfe80cf24[m HEAD@{28}: commit (merge): merged
[33m80866fb2[m HEAD@{29}: pull: Fast-forward
[33m6c712cce[m HEAD@{30}: commit (merge): merge
[33m5f30bfca[m HEAD@{31}: clone: from https://git-codecommit.us-east-2.amazonaws.com/v1/repos/DRMIS_WEB_APP
