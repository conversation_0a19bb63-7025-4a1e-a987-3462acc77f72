<template>
  <div class="container mt-4">
    <h2 class="mb-3">Detailed Preliminary Report</h2>
    <b-card v-if="reportDetails">
      <b-card-header>
        <h4>{{ reportDetails.district }} - {{ reportDetails.TA }}</h4>
        <p><strong>Date:</strong> {{ reportDetails.date }}</p>
      </b-card-header>

      <b-card-body>
        <p><strong>Disaster Type:</strong> {{ reportDetails.disaster }}</p>
        <p><strong>GVH:</strong> {{ reportDetails.gvh }}</p>
        <p><strong>Male-Headed Households (MHH):</strong> {{ reportDetails.mhh }}</p>
        <p><strong>Female-Headed Households (FHH):</strong> {{ reportDetails.fhh }}</p>
        <p><strong>Injuries:</strong> {{ reportDetails.injured }}</p>
        <p><strong>Deaths:</strong> {{ reportDetails.dead }}</p>
        <p><strong>Missing:</strong> {{ reportDetails.missing }}</p>
        <p><strong>Pregnant Women (PW):</strong> {{ reportDetails.pregnant }}</p>
        <p><strong>Lactating Women (LW):</strong> {{ reportDetails.lactactingwomen }}</p>
        <p><strong>Under Five (U5):</strong> {{ reportDetails.underfive }}</p>
        <p><strong>Disabled (PLWD):</strong> {{ reportDetails.disabled }}</p>
        <p><strong>Number of Camps:</strong> {{ reportDetails.numberofcamps }}</p>
        <p><strong>Camp Names:</strong> {{ reportDetails.campnames }}</p>
      </b-card-body>

      <b-card-footer>
        <b-button variant="primary" @click="$router.push('/manager/dashboard')">
          Back to Dashboard
        </b-button>
      </b-card-footer>
    </b-card>

    <b-alert v-else variant="warning" show>
      No detailed information found for the selected report.
    </b-alert>
  </div>
</template>

<script>
import { BCard, BCardHeader, BCardBody, BCardFooter, BAlert, BButton } from 'bootstrap-vue';
import { prelimreports } from '../../districtmanager/api/forms/prelimreports.js';

export default {
  name: 'DetailedPrelim',
  components: {
    BCard,
    BCardHeader,
    BCardBody,
    BCardFooter,
    BAlert,
    BButton
  },
  props: {
    idval: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      reportDetails: null
    }
  },
  created() {
    this.fetchReportDetails();
  },
  methods: {
    async fetchReportDetails() {
      try {
        const response = await prelimreports.getReportsLatest();
        // Find the report with the matching idval
        const report = response.find(item => item.idval === this.idval);
        if (report) {
          this.reportDetails = report;
        } else {
          this.reportDetails = null;
        }
      } catch (error) {
        console.error("Failed to fetch report details:", error);
        this.reportDetails = null;
      }
    }
  }
}
</script>

<style scoped>
.container {
  margin-top: 20px;
}
</style>
