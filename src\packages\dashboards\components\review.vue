`/* eslint-disable */`
<template>
  <div class="col-md-11" style="margin:0 auto">
    <base-header class="pb-1 col-12" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-6">
          <h6 class="h2 d-inline-block mb-0">Disaster </h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              
            </ol>
          </nav>
        </div>

        <div class="col-lg-6 col-6 text-right">
          <downloadexcel
            class="btn"
            :data="Array.from(download)"
            :fields="filters"
            name="DisasterData.csv"
            type="csv"
          >
            <base-button size="sm" type="neutral">
              <i class="text-primary ni ni-cloud-download-95"></i> EXPORT TO CSV
            </base-button>
          </downloadexcel>
          <base-button size="sm" type="neutral" @click="printdiv('section-to-print')">
            <span class="btn-inner--icon">
              <!--  <i class="ni ni-fat-add"></i> -->
            </span>
            <span class="btn-inner--text">Print</span>
          </base-button>
          <base-button size="sm" type="neutral" @click="downloadPDF()">
            <span class="btn-inner--icon">
              <!--  <i class="ni ni-fat-add"></i> -->
            </span>Download PDF
            <span class="btn-inner--text"></span>
          </base-button>
        </div>
      </div>
    </base-header>
    <div ref="content" id="section-to-print">
      <card class="no-border-card" body-classes="px-0 pb-1" footer-classes="pb-2">
        <div>
          <div class="component-example__container">
            <dl class="headings">
              <div class="row mx-4 mt-1">
                <div class="col-6">
                  <img src="../../../../static/logo.png" height="90" />
                </div>
                <div class="col-6 text-right">
                  <img src id="qr-code" height="90" />
                </div>
              </div>
              <dd class="text-center" style="font-size:22px; color: #32325d">
                <p>
                  <b>
                    <b>
                      <strong style="font-size:22px;">Government of Malawi</strong>
                    </b>
                  </b>
                  <br />
                  <span>
                    <b>
                      <b>Department of Disaster Management Affairs (DoDMA)</b>
                    </b>
                  </span>
                </p>

                <p></p>
                <p>
                  <span style="background:orange;width:100vh;padding-left:6px;padding-right:6px">
                    <b>
                      <b>Disaster Impact and Needs Reporting Form</b>
                    </b>
                  </span>
                </p>
              </dd>
            </dl>
            <div>
              <br />
              <p style="text-align:center;font-size:120%">
                <b>ACRONYMS</b>
              </p>
             



              <table class="col-md-11">
                <tr>
                  <td colspan="9">
                    <center>
                      <h2>
                        <b style="text-transform:uppercase"
                          >DETAILED REPORT FOR
                          <span id="district">{{
                            dinrFormsData.district.admin2_name_en
                          }}</span>
                        </b>
                      </h2>
                    </center>
                  </td>
                </tr>
                <tr>
                  <td colspan="6" width="50%">
                    <b>Affected TAs</b>
                    :
                    <span>
                      {{ TAarray.join() == "" ? "Not reported" : TAarray.join(", ") }}
                    </span>
                  </td>
          
                  <td colspan="6" width="50%">
                    <b>Affected Villages</b>
                    :
                    <span>
                      {{
                        villagesArray
                          .filter(Boolean)
                          .sort((a, b) => a.localeCompare(b))
                          .join(", ") || "Not reported"
                      }}
                    </span>
                  </td>
                </tr>
                <tr>
                  <td colspan="6" width="50%" style="text-transform:capitalize">
                    <b>Type of disasters</b>
                    : <span id="disastertype"> {{ dinrFormsData.disaster }} </span>
                  </td>
                  <td colspan="6" width="50%">
                    <b>Report submitted at</b>
                    :
                    {{
                      dateFormate(
                        new Date(dinrFormsData.createdon).toLocaleDateString("en-US")
                      )
                    }}
                  </td>
                </tr>
                <tr>
                  <td colspan="6" width="50%">
                    <b>Date Disaster started</b>
                    :
                    <span id="disasterstart">
                      {{
                        dateFormate(
                          new Date(dinrFormsData.dodFrom).toLocaleDateString("en-US")
                        )
                      }}
                    </span>
                  </td>
                  <td colspan="6" width="50%">
                    <b>Date Disaster ended</b>
                    :
                    {{
                      dateFormate(
                        new Date(dinrFormsData.dodTo).toLocaleDateString("en-US")
                      )
                    }}
                  </td>
                </tr>
          
                <tr>
                  <td colspan="4">
                    <b>Date of assessment by ADRMC/VDRMC</b>
                    :
                    {{
                      dateFormate(
                        new Date(dinrFormsData.doaAcpc).toLocaleDateString("en-US")
                      )
                    }}
                  </td>
                  <td colspan="4">
                    <b>Date of assessment/verification by DDRMC</b>
                    :
                    {{
                      dateFormate(
                        new Date(dinrFormsData.doaDcpc).toLocaleDateString("en-US")
                      )
                    }}
                  </td>
                  <td colspan="4">
                    <b>Date reported to the DEC</b>
                    :
                    {{
                      dinrFormsData.dateReported
                        ? dateFormate(
                            new Date(dinrFormsData.dateReported).toLocaleDateString(
                              "en-US"
                            )
                          )
                        : ""
                    }}
                  </td>
                </tr>
              </table>
          
              <table
                v-for="(item, index) in draFormsData"
                v-bind:key="index"
                class="col-md-11"
              >
                <!-- display ta name -->
                <tr>
                  <td colspan="15">
                    <h3 class="text-center" style="text-transform:uppercase">
                      {{ item.admin3.admin3Name_en }}
                    </h3>
                  </td>
                </tr>
                <!-- end display ta name -->
          
                <tr>
                  <td colspan="6" style="width:30%">
                    <b>
                      <b>GVH</b>
                    </b>
                    :
                    <span class="text-nowrap">{{
                      sortArrayByKey(item.gvhs)
                        .map(e => e.name.replace(/(\r\n|\n|\r)/gm, ""))
                        .join(", ") || "Not reported"
                    }}</span>
                  </td>
                  <td colspan="5" style="width:35%">
                    <b>Camps</b>
                    :
                    <span class="text-nowrap">{{
                      item.camps
                        .map(i => i.name)
                        .filter(e => (e ? e.replace(/(\r\n|\n|\r)/gm, "") : ""))
                        .sort((a, b) => a.localeCompare(b))
                        .join(", ") || "Not reported"
                    }}</span>
                  </td>
          
                  <td colspan="4" style="width:35%">
                    <b>Affected Villages</b>
                    :
                    <span>{{
                      item.villages
                        .map(i => i.name)
                        .sort((a, b) => a.localeCompare(b))
                        .join(", ") || "Not reported"
                    }}</span>
                  </td>
                </tr>
                <!-- general -->
                <tr
                  v-if="item.sectors.shelter"
                  style="margin-botton:9padding:0 !important;border:0px !important;"
                >
                  <td
                    colspan="15"
                    style="margin:0 !important;padding:0 !important;border:0px"
                  >
                    <table
                      width="100%"
                      style="margin:0;padding:0 !important;border:0px !important;"
                    >
                      <tr
                        v-if="
                          (item.sectors.shelter.PeopleInjuredrows && ((
                            item.sectors.shelter.PeopleInjuredrows.length > 0) ||
                            (item.sectors.shelter.PeopleDeadrows &&
                              item.sectors.shelter.PeopleDeadrows.length > 0)))
                        "
                      >
                        <td colspan="13" class="text-center">
                          <span><b>GENERAL INFORMATION </b></span>
                        </td>
                      </tr>
                      <!-- people injured start -->
                      <tr
                        v-if="
                          item.sectors.shelter.PeopleInjuredrows &&
                            item.sectors.shelter.PeopleInjuredrows.length > 0
                        "
                      >
                        <td
                          width="20%"
                          :rowspan="
                            item.sectors.shelter.PeopleInjuredrows.length > 0
                              ? item.sectors.shelter.PeopleInjuredrows.length + 2
                              : item.sectors.shelter.PeopleInjuredrows.length + 1
                          "
                        >
                          <strong>People Injured</strong>
                        </td>
                        <td colspan="2" width="20%">
                          <b>GVH</b>
                        </td>
                        <td colspan="4" width="20%">
                          <b>Population group</b>
                        </td>
                        <td colspan="2" width="10%">
                          <b>
                            <center>Male</center>
                          </b>
                        </td>
                        <td colspan="2" width="10%">
                          <b>
                            <center>Female</center>
                          </b>
                        </td>
                        <td colspan="2" width="10%" style="font-weight:bold">
                          <b>
                            <center>Total</center>
                          </b>
                        </td>
                      </tr>
                      <tr
                        v-if="
                          item.sectors.shelter&&
                          item.sectors.shelter.PeopleInjuredrows &&
                            item.sectors.shelter.PeopleInjuredrows.length > 0
                        "
                        v-for="(row, index) in item.sectors.shelter.PeopleInjuredrows"
                      >
                        <td
                          colspan="2"
                          v-if="
                            index ==
                              item.sectors.shelter.PeopleInjuredrows.findIndex(
                                x => x.name == row.name
                              )
                          "
                          :rowspan="
                            item.sectors.shelter.PeopleInjuredrows.filter(
                              x => x.name == row.name
                            ).length
                          "
                        >
                          {{ row.name }}
                        </td>
                        <td width="30%" colspan="4">
                          {{
                            row.category == "Other"
                              ? "Other(" + row.other_category_name + ")"
                              : row.category
                          }}
                        </td>
                        <td width="10%" colspan="2" class="right-align">
                          {{ numberWithCommas(row.males_injured || 0) }}
                        </td>
                        <td width="10%" colspan="2" class="right-align">
                          {{ numberWithCommas(row.females_injured || 0) }}
                        </td>
                        <td
                          style="font-weight:bold"
                          width="10%"
                          colspan="2"
                          class="right-align"
                        >
                          {{
                            numberWithCommas(
                              parseInt(row.males_injured || 0) +
                                parseInt(row.females_injured || 0)
                            )
                          }}
                        </td>
                      </tr>
                      <tr
                        v-if="
                          item.sectors.shelter&&item.sectors.shelter.PeopleInjuredrows &&
                            item.sectors.shelter.PeopleInjuredrows.length > 0
                        "
                      >
                        <td width="30%" style="font-weight:bold" colspan="5">
                          Total
                        </td>
                        <td
                          width="10%"
                          colspan="2"
                          style="font-weight:bold;"
                          class="right-align"
                        >
                          {{
                            numberWithCommas(
                              item.sectors.shelter.PeopleInjuredrows
                                ? item.sectors.shelter.PeopleInjuredrows.reduce(
                                    (sum, value) =>
                                      sum + +parseInt(value.males_injured || 0),
                                    0
                                  )
                                : 0
                            )
                          }}
                        </td>
                        <td
                          width="10%"
                          colspan="2"
                          style="font-weight:bold"
                          class="right-align"
                        >
                          {{
                            numberWithCommas(
                              item.sectors.shelter.PeopleInjuredrows
                                ? item.sectors.shelter.PeopleInjuredrows.reduce(
                                    (sum, value) =>
                                      sum + +parseInt(value.females_injured || 0),
                                    0
                                  )
                                : 0
                            )
                          }}
                        </td>
                        <td
                          width="10%"
                          colspan="2"
                          style="font-weight:bold"
                          class="right-align"
                        >
                          {{
                            numberWithCommas(
                              item.sectors.shelter.PeopleInjuredrows
                                ? item.sectors.shelter.PeopleInjuredrows.reduce(
                                    (sum, value) =>
                                      sum +
                                      (+parseInt(value.males_injured || 0) +
                                        parseInt(value.females_injured || 0)),
                                    0
                                  )
                                : 0
                            )
                          }}
                        </td>
                      </tr>
                      <!-- people injured end -->
                      <!-- people dead start -->
                      <tr
                        v-if="
                          item.sectors.shelter&&item.sectors.shelter.PeopleDeadrows &&
                            item.sectors.shelter.PeopleDeadrows.length > 0
                        "
                      >
                        <td
                          width="20%"
                          :rowspan="
                            item.sectors.shelter.PeopleDeadrows.length > 0
                              ? item.sectors.shelter.PeopleDeadrows.length + 2
                              : item.sectors.shelter.PeopleDeadrows + 1
                          "
                        >
                          <b>People Dead</b>
                        </td>
                        <td width="20%" colspan="2">
                          <b>GVH</b>
                        </td>
                        <td width="20%" colspan="3">
                          <b>Population Group</b>
                        </td>
                        <td colspan="2" width="10%">
                          <b>
                            <center>Male</center>
                          </b>
                        </td>
                        <td colspan="2" width="10%">
                          <b>
                            <center>Female</center>
                          </b>
                        </td>
                        <td colspan="2" width="10%">
                          <b>
                            <center>Total</center>
                          </b>
                        </td>
                      </tr>
                      <tr
                        v-if="
                          item.sectors.shelter&&item.sectors.shelter.PeopleDeadrows &&
                            item.sectors.shelter.PeopleDeadrows.length > 0
                        "
                        v-for="(row, index) in item.sectors.shelter.PeopleDeadrows"
                      >
                        <td
                          width="20%"
                          colspan="2"
                          v-if="
                            index ==
                              item.sectors.shelter.PeopleDeadrows.findIndex(
                                x => x.name == row.name
                              )
                          "
                          :rowspan="
                            item.sectors.shelter.PeopleDeadrows.filter(
                              x => x.name == row.name
                            ).length
                          "
                        >
                          {{ row.name }}
                        </td>
                        <td width="20%" colspan="3">
                          {{
                            row.category == "Other"
                              ? "Other(" + row.other_category_name + ")"
                              : row.category
                          }}
                        </td>
                        <td width="10%" colspan="2" class="right-align">
                          {{ row.males_dead || 0 }}
                        </td>
                        <td width="10%" colspan="2" class="right-align">
                          {{ row.females_dead || 0 }}
                        </td>
                        <td
                          style="font-weight:bold"
                          width="10%"
                          colspan="2"
                          class="right-align"
                        >
                          {{
                            parseInt(row.males_dead || 0) +
                              parseInt(row.females_dead || 0)
                          }}
                        </td>
                      </tr>
                      <tr
                        v-if="
                          item.sectors.shelter&&item.sectors.shelter.PeopleDeadrows &&
                            item.sectors.shelter.PeopleDeadrows.length > 0
                        "
                      >
                        <td width="30%" style="font-weight:bold" colspan="5">
                          Total
                        </td>
                        <td
                          width="10%"
                          colspan="2"
                          style="font-weight:bold;"
                          class="right-align"
                        >
                          {{
                            numberWithCommas(
                              item.sectors.shelter.PeopleDeadrows
                                ? item.sectors.shelter.PeopleDeadrows.reduce(
                                    (sum, value) =>
                                      sum + +parseInt(value.males_dead || 0),
                                    0
                                  )
                                : 0
                            )
                          }}
                        </td>
                        <td
                          width="10%"
                          colspan="2"
                          style="font-weight:bold"
                          class="right-align"
                        >
                          {{
                            numberWithCommas(
                              item.sectors.shelter.PeopleDeadrows
                                ? item.sectors.shelter.PeopleDeadrows.reduce(
                                    (sum, value) =>
                                      sum + +parseInt(value.females_dead || 0),
                                    0
                                  )
                                : 0
                            )
                          }}
                        </td>
                        <td
                          width="10%"
                          colspan="2"
                          style="font-weight:bold"
                          class="right-align"
                        >
                          {{
                            numberWithCommas(
                              item.sectors.shelter.PeopleDeadrows
                                ? item.sectors.shelter.PeopleDeadrows.reduce(
                                    (sum, value) =>
                                      sum +
                                      (+parseInt(value.males_dead || 0) +
                                        parseInt(value.females_dead || 0)),
                                    0
                                  )
                                : 0
                            )
                          }}
                        </td>
                      </tr>
                      <!-- people dead end -->
                      <!-- people missing start -->
                      <tr
                        v-if="
                          item.sectors.shelter.PeopleMissingrows &&
                            item.sectors.shelter.PeopleMissingrows.length > 0
                        "
                      >
                        <td
                          width="20%"
                          :rowspan="
                            item.sectors.shelter.PeopleMissingrows.length > 0
                              ? item.sectors.shelter.PeopleMissingrows.length + 2
                              : item.sectors.shelter.PeopleMissingrows.length + 1
                          "
                        >
                          <strong>People Missing</strong>
                        </td>
                        <td
                          colspan="2
                                "
                          width="20%"
                        >
                          <b>GVH</b>
                        </td>
                        <td width="20%" colspan="3">
                          <b>Population Group</b>
                        </td>
                        <td colspan="2" width="10%">
                          <b>
                            <center>Male</center>
                          </b>
                        </td>
                        <td colspan="2" width="10%">
                          <b>
                            <center>Female</center>
                          </b>
                        </td>
                        <td colspan="2" width="10%" style="font-weight:bold">
                          <b>
                            <center>Total</center>
                          </b>
                        </td>
                      </tr>
                      <tr
                        v-if="
                          item.sectors.shelter.PeopleMissingrows &&
                            item.sectors.shelter.PeopleMissingrows.length > 0
                        "
                        v-for="(row, index) in item.sectors.shelter.PeopleMissingrows"
                      >
                        <td
                          width="20%"
                          colspan="2"
                          v-if="
                            index ==
                              item.sectors.shelter.PeopleMissingrows.findIndex(
                                x => x.name == row.name
                              )
                          "
                          :rowspan="
                            item.sectors.shelter.PeopleMissingrows.filter(
                              x => x.name == row.name
                            ).length
                          "
                        >
                          {{ row.name }}
                        </td>
                        <td width="10%" colspan="3">
                          {{
                            row.category == "Other"
                              ? "Other(" + row.other_category_name + ")"
                              : row.category
                          }}
                        </td>
                        <td width="10%" colspan="2" class="right-align">
                          {{ parseInt(row.males_missing || 0) }}
                        </td>
                        <td width="10%" colspan="2" class="right-align">
                          {{ parseInt(row.females_missing || 0) }}
                        </td>
                        <td
                          style="font-weight:bold"
                          width="10%"
                          colspan="2"
                          class="right-align"
                        >
                          {{
                            (row.males_missing ? parseInt(row.males_missing) : 0) +
                              (row.females_missing ? parseInt(row.females_missing) : 0)
                          }}
                        </td>
                      </tr>
          
                      <tr
                        v-if="
                          item.sectors.shelter.PeopleMissingrows &&
                            item.sectors.shelter.PeopleMissingrows.length > 0
                        "
                      >
                        <td width="30%" style="font-weight:bold" colspan="5">
                          Total
                        </td>
                        <td
                          width="10%"
                          colspan="2"
                          style="font-weight:bold;"
                          class="right-align"
                        >
                          {{
                            numberWithCommas(
                              item.sectors.shelter.PeopleMissingrows
                                ? item.sectors.shelter.PeopleMissingrows.reduce(
                                    (sum, value) =>
                                      sum + (+parseInt(value.males_missing) || 0),
                                    0
                                  )
                                : 0
                            )
                          }}
                        </td>
                        <td
                          width="10%"
                          colspan="2"
                          style="font-weight:bold"
                          class="right-align"
                        >
                          {{
                            numberWithCommas(
                              item.sectors.shelter.PeopleMissingrows
                                ? item.sectors.shelter.PeopleMissingrows.reduce(
                                    (sum, value) =>
                                      sum + (+parseInt(value.females_missing) || 0),
                                    0
                                  )
                                : 0
                            )
                          }}
                        </td>
                        <td
                          width="10%"
                          colspan="2"
                          style="font-weight:bold"
                          class="right-align"
                        >
                          {{
                            numberWithCommas(
                              item.sectors.shelter.PeopleMissingrows
                                ? item.sectors.shelter.PeopleMissingrows.reduce(
                                    (sum, value) =>
                                      sum +
                                      (+parseInt(value.females_missing || 0) +
                                        parseInt(value.males_missing || 0)),
                                    0
                                  )
                                : 0
                            )
                          }}
                        </td>
                      </tr>
          
                      <!-- people missing end -->
                    </table>
                  </td>
                </tr>
                <tr
                  style="border:none"
                  v-if="item.sectors.shelter&&((item.sectors.shelter.PeopleInjuredrows &&
                      item.sectors.shelter.PeopleInjuredrows.length > 0) ||
                      (item.sectors.shelter.PeopleDeadrows &&
                        item.sectors.shelter.PeopleDeadrows.length > 0))
                  "
                >
                  <td class="col-md-12" colspan="16"></td>
                </tr>
                <!-- end general -->
                <!-- shelter -->
                <tr
                  v-if="item.sectors.shelter"
                  style="margin:0 !important;padding:0 !important;border:0px !important;"
                >
                  <td
                    colspan="15"
                    style="margin:0 !important;padding:0 !important;border:0px"
                  >
                    <table
                      width="100%"
                      style="margin:0;padding:0 !important;border:0px !important;"
                    >
                      <tr>
                        <td style="text-align:left;">
                          <b>CLUSTER</b>
                        </td>
                        <td colspan="13" class="text-center">
                          <b>IMPACT ASSESSMENT</b>
                        </td>
                      </tr>
                      <tr>
                        <td
                          width="9%"
                          class="clearfix"
                          :rowspan="
                            (item.sectors.shelter.people_without_shelter
                              ? item.sectors.shelter.people_without_shelter.length > 0
                                ? item.sectors.shelter.people_without_shelter.length + 1
                                : 1
                              : 1) +
                              (item.sectors.shelter.PeopleAffectedrows
                                ? item.sectors.shelter.PeopleAffectedrows.length > 0
                                  ? item.sectors.shelter.PeopleAffectedrows.length + 1
                                  : 1
                                : 1) +
                              (item.sectors.shelter.other_structures_damaged
                                ? item.sectors.shelter.other_structures_damaged.length > 0
                                  ? item.sectors.shelter.other_structures_damaged.length *
                                    17
                                  : item.sectors.shelter.other_structures_damaged.length *
                                    17
                                : 1) +
                              11
                          "
                        >
                          <span class="rotated" style="font-weight: bold;"></span>
                          <img
                            src="../../../../static/cluster_shelter_100px_bluebox.png"
                            height="150"
                          />
                        </td>
                      </tr>
                      <tr v-if="item.sectors.shelter.people_without_shelter">
                        <td width="20%">
                          <b>Indicators</b>
                        </td>
                        <td colspan="4" width="40%">
                          <b>Population group</b>
                        </td>
                        <td width="10%" colspan="2">
                          <b>
                            <center>Male</center>
                          </b>
                        </td>
                        <td width="10%" colspan="3">
                          <b>
                            <center>Female</center>
                          </b>
                        </td>
                        <td width="10%" colspan="3">
                          <b>
                            <center>Total</center>
                          </b>
                        </td>
                      </tr>
          
                      <tr v-if="item.sectors.shelter.people_without_shelter">
                        <td
                          width="25%"
                          :rowspan="
                            item.sectors.shelter.people_without_shelter.length > 0
                              ? item.sectors.shelter.people_without_shelter.length + 2
                              : item.sectors.shelter.people_without_shelter.length + 1
                          "
                        >
                          <strong>People without shelter</strong>
                        </td>
                      </tr>
                      <tr
                        v-for="row in item.sectors.shelter.people_without_shelter"
                        v-if="item.sectors.shelter.people_without_shelter"
                        style="border:1px"
                      >
                        <td width="35%" colspan="4">
                          {{
                            row.name == "Other"
                              ? "Other(" + row.other_category_name + ")"
                              : row.name || "Not reported"
                          }}
                        </td>
                        <td width="10%" colspan="2" class="right-align">
                          {{ numberWithCommas(row.without_shelter_males || 0) }}
                        </td>
                        <td width="10%" colspan="3" class="right-align">
                          {{ numberWithCommas(row.without_shelter_females || 0) }}
                        </td>
                        <td
                          width="10%"
                          colspan="3"
                          style="font-weight:bold"
                          class="right-align"
                        >
                          {{
                            numberWithCommas(
                              parseInt(row.without_shelter_females || 0) +
                                parseInt(row.without_shelter_males || 0)
                            )
                          }}
                        </td>
                      </tr>
          
                      <tr
                        v-if="
                          item.sectors.shelter.people_without_shelter &&
                            item.sectors.shelter.people_without_shelter.length > 0
                        "
                      >
                        <td width="35%" style="font-weight:bold" colspan="4">
                          Total
                        </td>
          
                        <td
                          width="10%"
                          colspan="2"
                          style="font-weight:bold;"
                          class="right-align"
                        >
                          {{
                            numberWithCommas(
                              item.sectors.shelter.people_without_shelter
                                ? item.sectors.shelter.people_without_shelter.reduce(
                                    (sum, value) =>
                                      sum + +parseInt(value.without_shelter_males || 0),
                                    0
                                  )
                                : 0
                            )
                          }}
                        </td>
                        <td
                          width="10%"
                          colspan="3"
                          style="font-weight:bold"
                          class="right-align"
                        >
                          {{
                            numberWithCommas(
                              item.sectors.shelter.people_without_shelter
                                ? item.sectors.shelter.people_without_shelter.reduce(
                                    (sum, value) =>
                                      sum + +parseInt(value.without_shelter_females || 0),
                                    0
                                  )
                                : 0
                            )
                          }}
                        </td>
                        <td
                          width="10%"
                          colspan="3"
                          style="font-weight:bold"
                          class="right-align"
                        >
                          {{
                            numberWithCommas(
                              item.sectors.shelter.people_without_shelter
                                ? item.sectors.shelter.people_without_shelter.reduce(
                                    (sum, value) =>
                                      sum +
                                      (+parseInt(value.without_shelter_females || 0) +
                                        +parseInt(value.without_shelter_males || 0)),
                                    0
                                  )
                                : 0
                            )
                          }}
                        </td>
                      </tr>
          
                      <!-- start -->
                      <tr v-if="item.sectors.shelter.PeopleAffectedrows">
                        <td
                          width="20%"
                          :rowspan="
                            item.sectors.shelter.PeopleAffectedrows.length > 0
                              ? item.sectors.shelter.PeopleAffectedrows.length + 2
                              : item.sectors.shelter.PeopleAffectedrows.length + 1
                          "
                        >
                          <strong>Households Affected</strong>
                        </td>
          
                        <td colspan="2">
                          <b>GVH</b>
                        </td>
                        <td colspan="3" width="15%">
                          <b>Population group</b>
                        </td>
                        <td width="10%" colspan="2">
                          <b>
                            <center>Male</center>
                          </b>
                        </td>
                        <td width="10%" colspan="2">
                          <b>
                            <center>Female</center>
                          </b>
                        </td>
                        <td width="10%" colspan="2">
                          <b>
                            <center>Total</center>
                          </b>
                        </td>
                      </tr>
                      <tr
                        v-if="
                          item.sectors.shelter.PeopleAffectedrows &&
                            item.sectors.shelter.PeopleAffectedrows.length > 0
                        "
                        v-for="(row, index) in item.sectors.shelter.PeopleAffectedrows"
                      >
                        <td
                          width="10%"
                          colspan="2"
                          v-if="
                            index ==
                              item.sectors.shelter.PeopleAffectedrows.findIndex(
                                x => x.name == row.name
                              )
                          "
                          :rowspan="
                            item.sectors.shelter.PeopleAffectedrows.filter(
                              x => x.name == row.name
                            ).length
                          "
                        >
                          {{ row.name }}
                        </td>
                        <td width="14%" colspan="3">
                          {{
                            row.category == "Other"
                              ? "Other(" + row.other_category_name + ")"
                              : row.category || "Not reported"
                          }}
                        </td>
                        <td width="10%" colspan="2" class="right-align">
                          {{ numberWithCommas(row.damaged_mhh || 0) }}
                        </td>
                        <td width="10%" colspan="2" class="right-align">
                          {{ numberWithCommas(row.damaged_fhh || 0) }}
                        </td>
                        <td
                          style="font-weight:bold"
                          width="10%"
                          colspan="2"
                          class="right-align"
                        >
                          {{
                            numberWithCommas(
                              (row.damaged_mhh ? parseInt(row.damaged_mhh) : 0) +
                                (row.damaged_fhh ? parseInt(row.damaged_fhh) : 0)
                            )
                          }}
                        </td>
                      </tr>
                      <tr
                        v-if="
                          item.sectors.shelter.PeopleAffectedrows &&
                            item.sectors.shelter.PeopleAffectedrows.length > 0
                        "
                      >
                        <td width="40%" style="font-weight:bold" colspan="5">
                          Total
                        </td>
                        <td
                          width="10%"
                          colspan="2"
                          style="font-weight:bold;"
                          class="right-align"
                        >
                          {{
                            numberWithCommas(
                              item.sectors.shelter.PeopleAffectedrows
                                ? item.sectors.shelter.PeopleAffectedrows.reduce(
                                    (sum, value) => sum + +value.damaged_mhh,
                                    0
                                  )
                                : 0
                            )
                          }}
                        </td>
                        <td
                          width="10%"
                          colspan="2"
                          style="font-weight:bold"
                          class="right-align"
                        >
                          {{
                            numberWithCommas(
                              item.sectors.shelter.PeopleAffectedrows
                                ? item.sectors.shelter.PeopleAffectedrows.reduce(
                                    (sum, value) => sum + +value.damaged_fhh,
                                    0
                                  )
                                : 0
                            )
                          }}
                        </td>
                        <td
                          width="10%"
                          colspan="2"
                          style="font-weight:bold"
                          class="right-align"
                        >
                          {{
                            numberWithCommas(
                              item.sectors.shelter.PeopleAffectedrows
                                ? item.sectors.shelter.PeopleAffectedrows.reduce(
                                    (sum, value) =>
                                      sum + (+value.damaged_mhh + +value.damaged_fhh),
                                    0
                                  )
                                : 0
                            )
                          }}
                        </td>
                      </tr>
          
                      <!--  -->
                      <tr v-if="item.sectors.shelter.ImpactOnHouses">
                        <td
                          width="20%"
                          :rowspan="
                            item.sectors.shelter.ImpactOnHouses.length > 0
                              ? item.sectors.shelter.ImpactOnHouses.length + 2
                              : item.sectors.shelter.ImpactOnHouses.length + 1
                          "
                        >
                          <strong>Impact on Houses</strong>
                        </td>
                      </tr>
                      <tr v-if="item.sectors.shelter.ImpactOnHouses">
                        <td width="40%" colspan="4">
                          <b>GVH</b>
                        </td>
                        <td width="10%" colspan="2" style>
                          <b>
                            <center>Partly blown roof</center>
                          </b>
                        </td>
                        <td width="10%" colspan="2" style>
                          <b>
                            <center>Wall damaged</center>
                          </b>
                        </td>
                        <td width="10%" colspan="2" style>
                          <b>
                            <center>Fully blown roof</center>
                          </b>
                        </td>
                        <td width="10%" colspan="2" style>
                          <b>
                            <center>Burnt</center>
                          </b>
                        </td>
                      </tr>
                      <tr
                        v-if="item.sectors.shelter.ImpactOnHouses"
                        v-for="row in item.sectors.shelter.ImpactOnHouses"
                      >
                        <td width="40%" colspan="4">
                          {{ row.name }}
                        </td>
                        <td width="14%" colspan="2" class="right-align">
                          {{ row.partly_blown_roof }}
                        </td>
                        <td width="10%" colspan="2" class="right-align">
                          {{ row.wall_damaged }}
                        </td>
                        <td width="10%" colspan="2" class="right-align">
                          {{ row.fully_blown_roof }}
                        </td>
          
                        <td width="10%" colspan="2" class="right-align">
                          {{ row.burnt }}
                        </td>
                      </tr>
                      <tr v-if="item.sectors.shelter.other_structures_damaged">
                        <td
                          width="20%"
                          :rowspan="
                            item.sectors.shelter.other_structures_damaged.length > 0
                              ? item.sectors.shelter.other_structures_damaged.length *
                                  13 +
                                1
                              : item.sectors.shelter.other_structures_damaged.length *
                                  13 +
                                1
                          "
                        >
                          <strong>Structures Damaged</strong>
                        </td>
                        <td width="30%" colspan="4">
                          <b>Structure</b>
                        </td>
                        <td width="40%" colspan="12" style>
                          <b>
                            <center>Impact</center>
                          </b>
                        </td>
                      </tr>
          
                      <template
              v-if="item.sectors.shelter.other_structures_damaged != 0"
              v-for="(row, index) in item.sectors.shelter.other_structures_damaged"
            >
              <tr :key="index">
                <td width="30%" colspan="4" rowspan="4">
                  {{ row.name }}
                </td>

                <td width="10%" colspan="4" class="left-align">
                  Partly damaged
                </td>

                <td width="10%" colspan="4" class="left-align">
                  {{ row.partly_damaged || 0 }}
                </td>
              </tr>

              <tr>
                <td width="10%" colspan="4" class="left-align">
                  Under Water
                </td>

                <td width="10%" colspan="4" class="left-align">
                  {{ row.underwater || 0 }}
                </td>
              </tr>

              <tr>
                <td width="10%" colspan="4" class="left-align">
                  Completely Damaged
                </td>

                <td width="10%" colspan="4" class="left-align">
                  {{ row.completely_damaged || 0 }}
                </td>
              </tr>

              <tr>
                <td width="10%" colspan="4" class="left-align">
                  Roofs Affected
                </td>

                <td width="10%" colspan="4" class="left-align">
                  {{ row.roofs_affected || 0 }}
                </td>
              </tr>

              <!-- <tr>
                <td width="10%" colspan="4" class="left-align">
                  Bricks Partly Damaged  
                </td>

                <td width="10%" colspan="2" class="left-align">
                  {{ row.bricks_partly_damaged || 0 }}
                </td>
              </tr>

              <tr>
                <td width="10%" colspan="4" class="left-align">
                  Cement Partly Damaged
                </td>

                <td width="10%" colspan="2" class="left-align">
                  {{ row.cement_partly_damaged || 0 }}
                </td>
              </tr>

              <tr>
                <td width="10%" colspan="4" class="left-align">
                  Iron Sheets Partly Damaged
                </td>

                <td width="10%" colspan="2" class="left-align">
                  {{ row.iron_sheets_partly_damaged || 0 }}
                </td>
              </tr>

              <tr>
                <td width="10%" colspan="4" class="left-align">
                  Nails Partly Damaged
                </td>

                <td width="10%" colspan="2" class="left-align">
                  {{ row.nails_partly_damaged || 0 }}
                </td>
              </tr>

              <tr>
                <td width="10%" colspan="4" class="left-align">
                  Poles Partly Damaged
                </td>

                <td width="10%" colspan="2" class="left-align">
                  {{ row.poles_partly_damaged || 0 }}
                </td>
              </tr>

              <tr>
                <td width="10%" colspan="4" class="left-align">
                  Sand Aggregates Partly Damaged
                </td>

                <td width="10%" colspan="2" class="left-align">
                  {{ row.sand_aggregates_partly_damaged || 0 }}
                </td>
              </tr>

              <tr>
                <td width="10%" colspan="4" class="left-align">
                  Cement Roofs Affected
                </td>

                <td width="10%" colspan="2" class="left-align">
                  {{ row.cement_roofs_affected || 0 }}
                </td>
              </tr> -->

              <!-- <tr>
                <td width="10%" colspan="4" class="left-align">
                  Iron Sheets Roofs Affected
                </td>

                <td width="10%" colspan="2" class="left-align">
                  {{ row.iron_sheets_roofs_affected || 0 }}
                </td>
              </tr>

              <tr>
                <td width="10%" colspan="4" class="left-align">
                  Poles Roofs Affected
                </td>

                <td width="10%" colspan="2" class="left-align">
                  {{ row.poles_roofs_affected || 0 }}
                </td>
              </tr> -->

              <!-- <tr>
                <td width="10%" colspan="4" class="left-align">
                  Sand Aggregates Roofs Affected
                </td>

                <td width="10%" colspan="2" class="left-align">
                  {{ row.sand_aggregates_roofs_affected || 0 }}
                </td>
              </tr> -->
            </template>
          
                      <tr>
                        <td width="40%" colspan="4" rowspan="2">
                          <b>Extent Of Damage (HH)</b>
                        </td>
                        <td width="20%" colspan="3" style>
                          <b>
                            <center>Partly Damaged</center>
                          </b>
                        </td>
                        <td width="20%" colspan="3" style>
                          <b>
                            <center>Under Water/Submerged</center>
                          </b>
                        </td>
                        <td width="20%" colspan="3" style>
                          <b>
                            <center>Completely Damaged</center>
                          </b>
                        </td>
                      </tr>
                      <tr>
                        <td width="20%" colspan="3" class="right-align">
                          {{ item.sectors.shelter.partly_damaged }}
                        </td>
                        <td width="20%" colspan="3" class="right-align">
                          {{ item.sectors.shelter.under_water }}
                        </td>
                        <td width="20%" colspan="3" class="right-align">
                          {{ item.sectors.shelter.completely_damaged }}
                        </td>
                      </tr>
          
                      <tr>
                        <td style="width:20%">
                          <strong>Emergency Needs</strong>
                        </td>
                        <td colspan="14">
                          <li style="margin-left:2%" class="qcont">
                            {{
                              !item.sectors.shelter.response_needed_emergency
                                ? "Not reported"
                                : item.sectors.shelter.response_needed_emergency
                                    .map(item => {
                                      if (item.specification) {
                                        return `${item.name} (${item.specification})`;
                                      } else {
                                        return item.name;
                                      }
                                    })
                                    .join(",")
                            }}
                          </li>
                        </td>
                      </tr>
                      <tr>
                        <td style="width:20%">
                          <strong>Non-Food Items Needs</strong>
                        </td>
                        <td colspan="14">
                          <li style="margin-left:2%" class="qcont">
                            {{
                              !item.sectors.shelter.response_needed_non_food
                                ? "Not reported"
                                : item.sectors.shelter.response_needed_non_food
                                    .map(item => {
                                      if (item.specification) {
                                        return `${item.name} (${item.specification})`;
                                      } else {
                                        return item.name;
                                      }
                                    })
                                    .join(",")
                            }}
                          </li>
                        </td>
                      </tr>
          
                      <tr>
                        <td style="width:20%">
                          <strong>Response Needed</strong>
                        </td>
                        <td colspan="14">
                          <u>
                            <strong>URGENT</strong>
                          </u>
                          <br />
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.shelter
                                ? item.sectors.shelter.urgent_response_needed
                                  ? item.sectors.shelter.urgent_response_needed
                                  : "N/A"
                                : "N/A"
                            }}
                          </li>
                          <hr style="margin:1%;" />
                          <u>
                            <strong>GENERAL</strong>
                          </u>
                          <br />
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.shelter
                                ? item.sectors.shelter.response_needed
                                  ? item.sectors.shelter.response_needed
                                  : "N/A"
                                : "N/A"
                            }}
                          </li>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
                <!-- end shelter -->
                <!--  displaced -->
                <tr
                  v-if="item.sectors.displaced"
                  style="margin:0 !important;padding:0 !important;border:0px !important;"
                >
                  <td
                    colspan="15"
                    style="margin:0 !important;padding:0 !important;border:0px"
                  >
                    <table
                      width="100%"
                      style="margin:0;padding:0 !important;border:0px !important;"
                    >
                      <tr>
                        <td
                          width="10%"
                          class="clearfix"
                          :rowspan="
                            (item.sectors.displaced.displaced_households_accommodated
                              ? item.sectors.displaced.displaced_households_accommodated
                                  .length > 0
                                ? item.sectors.displaced.displaced_households_accommodated
                                    .length + 2
                                : 1
                              : 1) +
                              (item.sectors.displaced.PeopleAffectedrows
                                ? item.sectors.displaced.PeopleAffectedrows.length > 0
                                  ? item.sectors.displaced.PeopleAffectedrows.length + 2
                                  : 1
                                : 1) +
                              (item.sectors.displaced.displaced_individuals_accommodated
                                ? item.sectors.displaced
                                    .displaced_individuals_accommodated.length > 0
                                  ? item.sectors.displaced
                                      .displaced_individuals_accommodated.length + 2
                                  : 1
                                : 1) +
                              (item.sectors.displaced.displaced_disaggregated
                                ? item.sectors.displaced.displaced_disaggregated.length >
                                  0
                                  ? item.sectors.displaced.displaced_disaggregated
                                      .length + 2
                                  : 1
                                : 1) +
                              4
                          "
                        >
                          <span class="rotated" style="font-weight: bold;"
                            ></span
                          >
                          <img
                            src="../../../../static/crisis_population_displacement_100px_bluebox.png"
                            height="150"
                          />
                        </td>
          
                        <td
                          v-if="item.sectors.displaced.displaced_households_accommodated"
                          :rowspan="
                            1 +
                              item.sectors.displaced.displaced_households_accommodated
                                .length
                          "
                          colspan="4"
                          style="width:20%"
                        >
                          <strong
                            >Number of HH accomodated in different structures</strong
                          >
                        </td>
                        <td
                          colspan="3"
                          v-if="item.sectors.displaced.displaced_households_accommodated"
                          style="width:15%"
                        >
                          <b>Category</b>
                        </td>
                        <td
                          colspan="2"
                          v-if="item.sectors.displaced.displaced_households_accommodated"
                          style="width:15%"
                        >
                          <b>Name</b>
                        </td>
                        <td
                          v-if="item.sectors.displaced.displaced_households_accommodated"
                          colspan="2"
                        >
                          <b>Lat</b>
                        </td>
                        <td
                          v-if="item.sectors.displaced.displaced_households_accommodated"
                          colspan="2"
                        >
                          <b>Long</b>
                        </td>
                        <td
                          v-if="item.sectors.displaced.displaced_households_accommodated"
                          colspan="2"
                          style="width:10%"
                        >
                          <b>Male</b>
                        </td>
                        <td
                          v-if="item.sectors.displaced.displaced_households_accommodated"
                          colspan="2"
                          style="width:10%"
                        >
                          <b>Female</b>
                        </td>
                      </tr>
          
                      <tr
                        v-if="item.sectors.displaced.displaced_households_accommodated"
                        v-for="row in structureDisplacedAccommodatedArray"
                      >
                        <td colspan="3">{{ row.name }}</td>
                        <td colspan="2">{{ row.structure_name }}</td>
                        <td colspan="2">{{ row.lat }}</td>
                        <td colspan="2">{{ row.long }}</td>
                        <td colspan="2">{{ row.accomodated_males_hh }}</td>
                        <td colspan="2">{{ row.accomodated_females_hh }}</td>
                      </tr>
                      <!-- e -->
                      <tr
                        v-if="
                          item.sectors.displaced.displaced_disaggregated &&
                            item.sectors.displaced.displaced_disaggregated.length > 0
                        "
                      >
                        <td
                          colspan="8"
                          width="20%"
                          :rowspan="
                            item.sectors.displaced.displaced_disaggregated &&
                            item.sectors.displaced.displaced_disaggregated.length > 0
                              ? item.sectors.displaced.displaced_disaggregated.length + 2
                              : 1
                          "
                        >
                          <strong>Number of persons displaced by gender</strong>
                        </td>
                        <td colspan="3" width="15%">
                          <b>
                            Population group
                          </b>
                        </td>
                        <td colspan="2" width="15%">
                          <b>
                            <center>Male</center>
                          </b>
                        </td>
                        <td colspan="2" width="15%">
                          <b>
                            <center>Female</center>
                          </b>
                        </td>
                        <td colspan="2" width="15%">
                          <b>
                            <center>Total</center>
                          </b>
                        </td>
                      </tr>
          
                      <tr
                        v-if="
                          item.sectors.displaced.displaced_disaggregated &&
                            item.sectors.displaced.displaced_disaggregated.length > 0
                        "
                        v-for="(row, index) in item.sectors.displaced
                          .displaced_disaggregated"
                      >
                        <td
                          width="10%"
                          colspan="3"
                          v-if="
                            index ==
                              item.sectors.displaced.displaced_disaggregated.findIndex(
                                x => x.name == row.name
                              )
                          "
                          :rowspan="
                            item.sectors.displaced.displaced_disaggregated.filter(
                              x => x.name == row.name
                            ).length
                          "
                        >
                          {{
                            row.name == "Other"
                              ? "Other(" + row.other_displaced_persons_category + ")"
                              : row.name
                          }}
                        </td>
                        <td width="10%" colspan="2" class="right-align">
                          {{ numberWithCommas(row.displaced_males || 0) }}
                        </td>
                        <td width="10%" colspan="2" class="right-align">
                          {{ numberWithCommas(row.displaced_females || 0) }}
                        </td>
                        <td
                          style="font-weight:bold"
                          width="10%"
                          colspan="2"
                          class="right-align"
                        >
                          {{
                            numberWithCommas(
                              parseInt(row.displaced_females || 0) +
                                parseInt(row.displaced_males || 0)
                            )
                          }}
                        </td>
                      </tr>
          
                      <tr
                        v-if="
                          item.sectors.displaced.displaced_disaggregated &&
                            item.sectors.displaced.displaced_disaggregated.length > 0
                        "
                      >
                        <td width="15%" style="font-weight:bold" colspan="3">
                          Total
                        </td>
                        <td
                          width="10%"
                          colspan="2"
                          style="font-weight:bold;"
                          class="right-align"
                        >
                          {{
                            numberWithCommas(
                              item.sectors.displaced.displaced_disaggregated
                                ? item.sectors.displaced.displaced_disaggregated.reduce(
                                    (sum, value) =>
                                      sum + +parseInt(value.displaced_males || 0),
                                    0
                                  )
                                : 0
                            )
                          }}
                        </td>
                        <td
                          width="10%"
                          colspan="2"
                          style="font-weight:bold"
                          class="right-align"
                        >
                          {{
                            numberWithCommas(
                              item.sectors.displaced.displaced_disaggregated
                                ? item.sectors.displaced.displaced_disaggregated.reduce(
                                    (sum, value) =>
                                      sum + +parseInt(value.displaced_females || 0),
                                    0
                                  )
                                : 0
                            )
                          }}
                        </td>
                        <td
                          width="10%"
                          colspan="2"
                          style="font-weight:bold"
                          class="right-align"
                        >
                          {{
                            numberWithCommas(
                              item.sectors.displaced.displaced_disaggregated
                                ? item.sectors.displaced.displaced_disaggregated.reduce(
                                    (sum, value) =>
                                      sum +
                                      (+parseInt(value.displaced_females || 0) +
                                        +parseInt(value.displaced_males || 0)),
                                    0
                                  )
                                : 0
                            )
                          }}
                        </td>
                      </tr>
                      <!--  -->
                      <tr
                        v-if="
                          item.sectors.displaced.displaced_individuals_accommodated &&
                            item.sectors.displaced.displaced_individuals_accommodated
                              .length > 0
                        "
                      >
                        <td
                          colspan="8"
                          width="20%"
                          :rowspan="
                            item.sectors.displaced.displaced_individuals_accommodated
                              ? item.sectors.displaced.displaced_individuals_accommodated
                                  .length > 0
                                ? 2
                                : 1
                              : 1
                          "
                        >
                          <strong
                            >Number of People accomodated in difference structures</strong
                          >
                        </td>
                        <td colspan="2" width="15%">
                          <b>
                            <center>Male</center>
                          </b>
                        </td>
                        <td colspan="2" width="15%">
                          <b>
                            <center>Female</center>
                          </b>
                        </td>
                        <td colspan="2" width="15%">
                          <b>
                            <center>Total</center>
                          </b>
                        </td>
                      </tr>
          
                      <tr
                        v-if="
                          item.sectors.displaced.displaced_individuals_accommodated &&
                            item.sectors.displaced.displaced_individuals_accommodated
                              .length > 0
                        "
                      >
                        <td width="15%" colspan="2" class="right-align">
                          {{
                            numberWithCommas(
                              item.sectors.displaced.displaced_individuals_accommodated
                                ? item.sectors.displaced.displaced_individuals_accommodated
                                    .map(function(item) {
                                      return item.accomodated_males
                                        ? parseInt(item.accomodated_males)
                                        : 0;
                                    })
                                    .reduce((sum, value) => sum + value)
                                : 0
                            )
                          }}
                        </td>
                        <td width="15%" colspan="2" class="right-align">
                          {{
                            numberWithCommas(
                              item.sectors.displaced.displaced_individuals_accommodated
                                ? item.sectors.displaced.displaced_individuals_accommodated
                                    .map(function(item) {
                                      return item.accomodated_females
                                        ? parseInt(item.accomodated_females)
                                        : 0;
                                    })
                                    .reduce((sum, value) => sum + value)
                                : 0
                            )
                          }}
                        </td>
                        <td width="15%" colspan="2" class="right-align">
                          {{
                            numberWithCommas(
                              parseInt(
                                item.sectors.displaced.displaced_individuals_accommodated
                                  ? item.sectors.displaced.displaced_individuals_accommodated
                                      .map(function(item) {
                                        return item.accomodated_males
                                          ? parseInt(item.accomodated_males)
                                          : 0;
                                      })
                                      .reduce((sum, value) => sum + value)
                                  : 0
                              ) +
                                parseInt(
                                  item.sectors.displaced
                                    .displaced_individuals_accommodated
                                    ? item.sectors.displaced.displaced_individuals_accommodated
                                        .map(function(item) {
                                          return item.accomodated_females
                                            ? parseInt(item.accomodated_females)
                                            : 0;
                                        })
                                        .reduce((sum, value) => sum + value)
                                    : 0
                                )
                            )
                          }}
                        </td>
                      </tr>
                      <!-- start -->
          
                      <!-- start -->
          
                      <tr
                        v-if="
                          item.sectors.displaced.PeopleAffectedrows &&
                            item.sectors.displaced.PeopleAffectedrows.length > 0
                        "
                      >
                        <td
                          width="20%"
                          v-bind:rowspan="
                            item.sectors.displaced.PeopleAffectedrows.length
                              ? item.sectors.displaced.PeopleAffectedrows.length > 0
                                ? item.sectors.displaced.PeopleAffectedrows.length + 2
                                : 1
                              : 1
                          "
                          colspan="5"
                        >
                          <strong>Number of Households displaced</strong>
                        </td>
                        <td colspan="3">
                          <b>GVH</b>
                        </td>
                        <td colspan="3" width="10%">
                          <b>Household group</b>
                        </td>
                        <td width="10%" colspan="2">
                          <b>
                            <center>Male</center>
                          </b>
                        </td>
                        <td width="10%" colspan="2">
                          <b>
                            <center>Female</center>
                          </b>
                        </td>
                        <td width="10%" colspan="2">
                          <b>
                            <center>Total</center>
                          </b>
                        </td>
                      </tr>
                      <tr
                        v-if="
                          item.sectors.displaced.PeopleAffectedrows &&
                            item.sectors.displaced.PeopleAffectedrows.length > 0
                        "
                        v-for="(row, index) in item.sectors.displaced.PeopleAffectedrows"
                      >
                        <td
                          width="10%"
                          colspan="3"
                          v-if="
                            index ==
                              item.sectors.displaced.PeopleAffectedrows.findIndex(
                                x => x.name == row.name
                              )
                          "
                          :rowspan="
                            item.sectors.displaced.PeopleAffectedrows.filter(
                              x => x.name == row.name
                            ).length
                          "
                        >
                          {{ row.name }}
                        </td>
                        <td width="10%" colspan="3">
                          {{
                            row.category == "Other"
                              ? "Other(" + row.other_category_name + ")"
                              : row.category
                          }}
                        </td>
                        <td width="10%" colspan="2" class="right-align">
                          {{ numberWithCommas(row.number_displaced_by_gender_mhh || 0) }}
                        </td>
                        <td width="10%" colspan="2" class="right-align">
                          {{ numberWithCommas(row.number_displaced_by_gender_fhh || 0) }}
                        </td>
                        <td
                          style="font-weight:bold"
                          width="10%"
                          colspan="2"
                          class="right-align"
                        >
                          {{
                            numberWithCommas(
                              (row.number_displaced_by_gender_fhh
                                ? parseInt(row.number_displaced_by_gender_fhh)
                                : 0) +
                                (row.number_displaced_by_gender_mhh
                                  ? parseInt(row.number_displaced_by_gender_mhh)
                                  : 0)
                            )
                          }}
                        </td>
                      </tr>
                      <tr
                        v-if="
                          item.sectors.displaced.PeopleAffectedrows &&
                            item.sectors.displaced.PeopleAffectedrows.length > 0
                        "
                      >
                        <td width="20%" style="font-weight:bold" colspan="6">
                          Total
                        </td>
                        <td
                          width="10%"
                          colspan="2"
                          style="font-weight:bold;"
                          class="right-align"
                        >
                          {{
                            numberWithCommas(
                              item.sectors.displaced.PeopleAffectedrows
                                ? item.sectors.displaced.PeopleAffectedrows.reduce(
                                    (sum, value) =>
                                      sum + +value.number_displaced_by_gender_mhh,
                                    0
                                  )
                                : 0
                            )
                          }}
                        </td>
                        <td
                          width="10%"
                          colspan="2"
                          style="font-weight:bold"
                          class="right-align"
                        >
                          {{
                            numberWithCommas(
                              item.sectors.displaced.PeopleAffectedrows
                                ? item.sectors.displaced.PeopleAffectedrows.reduce(
                                    (sum, value) =>
                                      sum + +value.number_displaced_by_gender_fhh,
                                    0
                                  )
                                : 0
                            )
                          }}
                        </td>
                        <td
                          width="10%"
                          colspan="2"
                          style="font-weight:bold"
                          class="right-align"
                        >
                          {{
                            numberWithCommas(
                              item.sectors.displaced.PeopleAffectedrows
                                ? item.sectors.displaced.PeopleAffectedrows.reduce(
                                    (sum, value) =>
                                      sum +
                                      (+value.number_displaced_by_gender_fhh +
                                        +value.number_displaced_by_gender_mhh),
                                    0
                                  )
                                : 0
                            )
                          }}
                        </td>
                      </tr>
          
                      <!-- end -->
                      <tr>
                        <td style="width:20%" colspan="5">
                          <strong>Emergency Needs</strong>
                        </td>
                        <td colspan="12">
                          <li style="margin-left:2%" class="qcont">
                            {{
                              !item.sectors.displaced.response_needed_emergency
                                ? "Not reported"
                                : item.sectors.displaced.response_needed_emergency
                                    .map(item => {
                                      if (item.specification) {
                                        return `${item.name} (${item.specification})`;
                                      } else {
                                        return item.name;
                                      }
                                    })
                                    .join(",")
                            }}
                          </li>
                        </td>
                      </tr>
                      <tr>
                        <td style="width:20%" colspan="5">
                          <strong>Non-Food Items Needs</strong>
                        </td>
                        <td colspan="12">
                          <li style="margin-left:2%" class="qcont">
                            {{
                              !item.sectors.displaced.response_needed_non_food
                                ? "Not reported"
                                : item.sectors.displaced.response_needed_non_food
                                    .map(item => {
                                      if (item.specification) {
                                        return `${item.name} (${item.specification})`;
                                      } else {
                                        return item.name;
                                      }
                                    })
                                    .join(",")
                            }}
                          </li>
                        </td>
                      </tr>
                      <tr>
                        <td style="width:20%" colspan="5">
                          <strong>Response Needed</strong>
                        </td>
                        <td colspan="12" style="width:70%">
                          <u>
                            <strong>URGENT</strong>
                          </u>
                          <br />
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.displaced
                                ? item.sectors.displaced.urgent_response_needed
                                  ? item.sectors.displaced.urgent_response_needed
                                  : "N/A"
                                : "N/A"
                            }}
                          </li>
                          <hr style="margin:1%;" />
                          <u>
                            <strong>GENERAL</strong>
                          </u>
                          <br />
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.displaced
                                ? item.sectors.displaced.response_needed
                                  ? item.sectors.displaced.response_needed
                                  : "N/A"
                                : "N/A"
                            }}
                          </li>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
                <!-- end displaced -->
                <!--  agricultutre -->
                <tr
                  v-if="item.sectors.agriculture"
                  style="margin:0 !important;padding:0 !important;border:0px !important;"
                >
                  <td
                    colspan="15"
                    style="margin:0 !important;padding:0 !important;border:0px"
                  >
                    <table
                      width="100%"
                      style="margin:0 !important;padding:0 !important;border:0px"
                    >
                      <tr>
                        <td
                          width="9%"
                          class="clearfix"
                          :rowspan="
                            (item.sectors.agriculture.crops_damaged ? 1 : 1) +
                              (item.sectors.agriculture.crops_damaged
                                ? item.sectors.agriculture.crops_damaged.length >= 0
                                  ? item.sectors.agriculture.crops_damaged.length + 2
                                  : 1
                                : 1) +
                              (item.sectors.agriculture.impact_on_livestock
                                ? item.sectors.agriculture.impact_on_livestock.length > 0
                                  ? 1
                                  : 1
                                : 1) +
                              (item.sectors.agriculture.food_item_damage
                                ? item.sectors.agriculture.food_item_damage.length > 0
                                  ? item.sectors.agriculture.food_item_damage.length + 1
                                  : 1
                                : 1) +
                              (item.sectors.agriculture.livelihoods_affected
                                ? item.sectors.agriculture.livelihoods_affected.length > 0
                                  ? item.sectors.agriculture.livelihoods_affected.length +
                                    1
                                  : 1
                                : 1) +
                              (item.sectors.agriculture.facilities_damaged
                                ? item.sectors.agriculture.facilities_damaged.length > 0
                                  ? item.sectors.agriculture.facilities_damaged.length + 1
                                  : 1
                                : 1) +
                              (item.sectors.agriculture.impact_on_crops
                                ? item.sectors.agriculture.impact_on_crops.length > 0
                                  ? item.sectors.agriculture.impact_on_crops.length + 1
                                  : 1
                                : 1) +
                              (item.sectors.agriculture.response_needed_crops
                                ? item.sectors.agriculture.response_needed_crops.length >
                                  0
                                  ? item.sectors.agriculture.response_needed_crops
                                      .length + 1
                                  : 1
                                : 1) +
                              8
                          "
                        >
                          <span class="rotated" style="font-weight: bold;"
                            ></span
                          >
                          <img
                            src="../../../../static/other_cluster_agriculture_100px_bluebox.png"
                            height="150"
                          />
                        </td>
                      </tr>
          
                      <tr
                        v-if="
                          item.sectors.agriculture.crops_damaged &&
                            item.sectors.agriculture.crops_damaged.length > 0
                        "
                      >
                        <td
                          v-bind:rowspan="
                            item.sectors.agriculture.crops_damaged
                              ? item.sectors.agriculture.crops_damaged.length + 1
                              : 1
                          "
                          colspan="5"
                          width="20%"
                        >
                          <strong>Damage per crop (Hectares)</strong>
                        </td>
                        <td colspan="3" width="40%">
                          <b>Name</b>
                        </td>
                        <td colspan="2" width="10%">
                          <b>Submerged</b>
                        </td>
                        <td colspan="2" width="10%">
                          <b>Washed away</b>
                        </td>
                        <td colspan="2" width="10%">
                          <b>
                            <center>Total</center>
                          </b>
                        </td>
                      </tr>
                      <tr
                        v-if="
                          item.sectors.agriculture.crops_damaged &&
                            item.sectors.agriculture.crops_damaged.length > 0
                        "
                        v-for="crop in item.sectors.agriculture.crops_damaged"
                      >
                        <td colspan="3" width="40%">{{ crop.name }}</td>
                        <td width="10%" colspan="2" class="right-align">
                          {{ numberWithCommas(crop.hectares_submerged) }}
                        </td>
                        <td width="10%" colspan="2" class="right-align">
                          {{ numberWithCommas(crop.hectares_washed_away) }}
                        </td>
                        <td width="10%" colspan="2" class="right-align">
                          {{
                            numberWithCommas(
                              (crop.hectares_submerged
                                ? parseInt(crop.hectares_submerged)
                                : 0) +
                                (crop.hectares_washed_away
                                  ? parseInt(crop.hectares_washed_away)
                                  : 0)
                            )
                          }}
                        </td>
                      </tr>
          
                      <!--======================START==========================-->
          
                      <tr
                        v-if="
                          item.sectors.agriculture.facilities_damaged &&
                            item.sectors.agriculture.facilities_damaged.length > 0
                        "
                      >
                        <td
                          :rowspan="
                            item.sectors.agriculture.facilities_damaged
                              ? item.sectors.agriculture.facilities_damaged.length + 1
                              : 1
                          "
                          colspan="4"
                          width="20%"
                        >
                          <strong>Facilities damaged</strong>
                        </td>
                        <td colspan="3" width="10%">
                          <b>Facility</b>
                        </td>
                        <td colspan="2" width="10%">
                          <b>Partially</b>
                        </td>
                        <td colspan="2" width="10%">
                          <b>Completely </b>
                        </td>
                        <td colspan="2" width="10%">
                          <b>Total</b>
                        </td>
                      </tr>
          
                      <tr
                        v-if="
                          item.sectors.agriculture.facilities_damaged &&
                            item.sectors.agriculture.facilities_damaged.length > 0
                        "
                        v-for="facilty in item.sectors.agriculture.facilities_damaged"
                      >
                        <td colspan="3" width="10%" class="right-left">
                          {{
                            facilty.name == "Other"
                              ? "Other(" + facilty.other_facility_name + ")"
                              : facilty.name
                          }}
                        </td>
                        <td width="10%" colspan="2" class="right-left">
                          {{ facilty.partially_damaged }}
                        </td>
                        <td width="10%" colspan="2" class="right-left">
                          {{ facilty.completely_damaged }}
                        </td>
                        <td width="10%" colspan="2" class="right-align">
                          {{
                            numberWithCommas(
                              (facilty.partially_damaged
                                ? parseInt(facilty.partially_damaged)
                                : 0) +
                                (facilty.completely_damaged
                                  ? parseInt(facilty.completely_damaged)
                                  : 0)
                            )
                          }}
                        </td>
                      </tr>
                      <!--======================END============================-->
          
                      <!--======================START==========================-->
          
                      <tr
                        v-if="
                          item.sectors.agriculture.impact_on_crops &&
                            item.sectors.agriculture.impact_on_crops.length > 0
                        "
                      >
                        <td
                          :rowspan="
                            item.sectors.agriculture.impact_on_crops
                              ? item.sectors.agriculture.impact_on_crops.length + 1
                              : 1
                          "
                          colspan="5"
                        >
                          <strong>Damage per crop (Households)</strong>
                        </td>
                        <td colspan="3">
                          <b>Crop</b>
                        </td>
                        <td colspan="3">
                          <b>HH affected</b>
                        </td>
                        <td colspan="2">
                          <b>Hectares </b>
                        </td>
                      </tr>
          
                      <tr
                        v-if="
                          item.sectors.agriculture.impact_on_crops &&
                            item.sectors.agriculture.impact_on_crops.length > 0
                        "
                        v-for="crop in item.sectors.agriculture.impact_on_crops"
                      >
                        <td colspan="3" class="right-left">
                          {{
                            crop.name == "Other"
                              ? "Other(" + crop.other_crop_type + ")"
                              : crop.name
                          }}
                        </td>
                        <td colspan="3" class="right-left">{{ crop.hh_affected }}</td>
                        <td colspan="2" class="right-left">
                          {{ crop.hectares_damaged }}
                        </td>
                      </tr>
                      <!--======================END============================-->
          
                      <!--======================START==========================-->
          
                      <tr
                        v-if="
                          item.sectors.agriculture.impact_on_livestock &&
                            item.sectors.agriculture.impact_on_livestock.length > 0
                        "
                      >
                        <td
                          :rowspan="
                            item.sectors.agriculture.impact_on_livestock
                              ? item.sectors.agriculture.impact_on_livestock.length + 1
                              : 1
                          "
                          colspan="4"
                        >
                          <strong>Livestock affected</strong>
                        </td>
                        <td colspan="2">
                          <b>Livestock</b>
                        </td>
                        <td colspan="3">
                          <b>HH affected</b>
                        </td>
                        <td colspan="2">
                          <b>Injured </b>
                        </td>
                        <td colspan="2">
                          <b>Dead </b>
                        </td>
                      </tr>
          
                      <tr
                        v-if="
                          item.sectors.agriculture.impact_on_livestock &&
                            item.sectors.agriculture.impact_on_livestock.length > 0
                        "
                        v-for="livestock in item.sectors.agriculture.impact_on_livestock"
                      >
                        <td colspan="2" class="right-left">
                          {{
                            livestock.name == "Other"
                              ? "Other(" + livestock.other_livestock_name + ")"
                              : livestock.name
                          }}
                        </td>
                        <td colspan="3" class="right-left">
                          {{ livestock.hh_affected_l }}
                        </td>
                        <td colspan="2" class="right-left">
                          {{ livestock.livestock_deaths }}
                        </td>
                        <td colspan="2" class="right-left">
                          {{ livestock.livestock_injured }}
                        </td>
                      </tr>
                      <!--======================END============================-->
          
                      <!--======================START==========================-->
          
                      <tr
                        v-if="
                          item.sectors.agriculture.livelihoods_affected &&
                            item.sectors.agriculture.livelihoods_affected.length > 0
                        "
                      >
                        <td
                          v-bind:rowspan="
                            item.sectors.agriculture.livelihoods_affected
                              ? item.sectors.agriculture.livelihoods_affected.length + 1
                              : 1
                          "
                          colspan="3"
                          width="20%"
                        >
                          <strong>Livelihoods Affected</strong>
                        </td>
          
                        <td colspan="2" width="10%">
                          <b>Livelihood</b>
                        </td>
                        <td colspan="2" width="10%">
                          <b>Population group</b>
                        </td>
                        <td colspan="2" width="10%">
                          <b>Severity</b>
                        </td>
                        <td colspan="2" width="10%" class="text-center">
                          <b>MHH</b>
                        </td>
                        <td colspan="2" width="10%" class="text-center">
                          <b>FHH</b>
                        </td>
                        <!-- <td colspan="2" width="10%">
                                  <b>
                                    <center>Total</center>
                                  </b>
                                </td> -->
                      </tr>
                      <tr
                        v-if="
                          item.sectors.agriculture.livelihoods_affected &&
                            item.sectors.agriculture.livelihoods_affected.length > 0
                        "
                        v-for="livelihoods in item.sectors.agriculture
                          .livelihoods_affected"
                      >
                        <td colspan="2" width="10%" class="right-left">
                          {{ livelihoods.livelihood_type }}
                        </td>
                        <td width="10%" colspan="2" class="right-left">
                          {{
                            livelihoods.category == "Other"
                              ? "Other(" + livelihoods.other_category == "undefined"
                                ? "Other(Not reported"
                                : "Other(Not reported" + ")"
                              : livelihoods.category
                          }}
                        </td>
                        <td width="10%" colspan="2" class="right-left">
                          {{ livelihoods.severity }}
                        </td>
                        <td width="10%" colspan="2" class="right-align">
                          {{
                            numberWithCommas(
                              livelihoods.male_HH ? parseInt(livelihoods.male_HH) : 0
                            )
                          }}
                        </td>
          
                        <td width="10%" colspan="2" class="right-align">
                          {{
                            numberWithCommas(
                              livelihoods.female_HH ? parseInt(livelihoods.female_HH) : 0
                            )
                          }}
                        </td>
                      </tr>
                      <!--======================END============================-->
          
                      <tr
                        v-if="
                          item.sectors.agriculture.food_item_damage &&
                            item.sectors.agriculture.food_item_damage.length > 0
                        "
                      >
                        <td
                          v-bind:rowspan="
                            item.sectors.agriculture.food_item_damage.length + 1
                          "
                          colspan="5"
                          width="20%"
                        >
                          <strong>Number of Food items damage (KGs)</strong>
                        </td>
                        <td colspan="7" width="60%">
                          <b>Name</b>
                        </td>
                        <td colspan="2" width="10%">
                          <b>in KGs</b>
                        </td>
                      </tr>
                      <tr
                        v-if="
                          item.sectors.agriculture.food_item_damage &&
                            item.sectors.agriculture.food_item_damage.length > 0
                        "
                        v-for="crop in item.sectors.agriculture.food_item_damage"
                      >
                        <td colspan="7" width="60%">{{ crop.food_item_name }}</td>
                        <td colspan="2" width="10%" class="right-align">
                          {{ numberWithCommas(crop.number_of_kilos) }}
                        </td>
                      </tr>
                      <tr>
                        <td colspan="5" style="width:20%">
                          <strong>Emergency Needs</strong>
                        </td>
                        <td colspan="9">
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.agriculture.emergent_needs == undefined
                                ? "Not reported"
                                : item.sectors.agriculture.emergent_needs.join(", ")
                            }}
                          </li>
                        </td>
                      </tr>
          
                      <tr>
                        <td colspan="5" style="width:20%">
                          <strong>Emergency response</strong>
                        </td>
                        <td colspan="9">
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.agriculture.emergency_response_needed ==
                              undefined
                                ? "Not reported"
                                : item.sectors.agriculture.emergency_response_needed.join(
                                    ", "
                                  )
                            }}
                          </li>
                        </td>
                      </tr>
          
                      <!--======================START==========================-->
          
                      <tr
                        v-if="
                          item.sectors.agriculture.response_needed_crops &&
                            item.sectors.agriculture.response_needed_crops.length > 0
                        "
                      >
                        <td
                          :rowspan="
                            item.sectors.agriculture.response_needed_crops
                              ? item.sectors.agriculture.response_needed_crops.length + 1
                              : 1
                          "
                          colspan="4"
                        >
                          <strong>Support for crops</strong>
                        </td>
                        <td colspan="3">
                          <b>Name</b>
                        </td>
                        <td colspan="3">
                          <b>Area</b>
                        </td>
                        <td colspan="4">
                          <b>Requirement</b>
                        </td>
                      </tr>
          
                      <tr
                        v-if="
                          item.sectors.agriculture.response_needed_crops &&
                            item.sectors.agriculture.response_needed_crops.length > 0
                        "
                        v-for="facilty in item.sectors.agriculture.response_needed_crops"
                      >
                        <td colspan="3" class="right-left">
                          {{
                            facilty.name == "Other"
                              ? "Other(" + facilty.other_crop_name + ")"
                              : facilty.name
                          }}
                        </td>
                        <td colspan="3" class="right-left">{{ facilty.area }}</td>
                        <td colspan="4" class="right-left">{{ facilty.requirement }}</td>
                      </tr>
                      <!--======================END============================-->
          
                      <tr>
                        <td colspan="5" style="width:20%">
                          <strong>Response Needed</strong>
                        </td>
                        <td colspan="9" style="width:70%">
                          <u>
                            <strong>URGENT</strong>
                          </u>
                          <br />
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.agriculture
                                ? item.sectors.agriculture.urgent_response_needed
                                  ? item.sectors.agriculture.urgent_response_needed
                                  : "N/A"
                                : "N/A"
                            }}
                          </li>
                          <hr style="margin:1%;" />
                          <u>
                            <strong>GENERAL</strong>
                          </u>
                          <br />
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.agriculture
                                ? item.sectors.agriculture.response_needed
                                  ? item.sectors.agriculture.response_needed
                                  : "N/A"
                                : "N/A"
                            }}
                          </li>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
                <!-- end agricultutre -->
                <!--  Food security -->
                <tr
                  v-if="item.sectors.food"
                  style="margin:0;padding:0 !important;border:0px !important;"
                >
                  <td colspan="15" style="margin:0;padding:0 !important;border:0px">
                    <table
                      width="100%"
                      style="margin:0;padding:0 !important;border:0px !important;"
                    >
                      <tr>
                        <td
                          :rowspan="
                            (item.sectors.food.food_stocks_avaliability
                              ? item.sectors.food.food_stocks_avaliability.length > 0
                                ? item.sectors.food.food_stocks_avaliability.length + 1
                                : 1
                              : 1) +
                              (item.sectors.food.food_access
                                ? item.sectors.food.food_access.length > 0
                                  ? item.sectors.food.food_access.length + 1
                                  : 1
                                : 1) +
                              (item.sectors.food.food_item_damage
                                ? item.sectors.food.food_item_damage.length > 0
                                  ? item.sectors.food.food_item_damage.length + 1
                                  : 1
                                : 1) +
                              (item.sectors.food.impact_on_price
                                ? item.sectors.food.impact_on_price.length > 0
                                  ? item.sectors.food.impact_on_price.length + 1
                                  : 1
                                : 1) +
                              4
                          "
                          width="10%"
                          class="clearfix"
                        >
                          <span class="rotated" style="font-weight: bold;"
                            ></span
                          >
                          <img
                            src="../../../../static/cluster_food_security_100px_bluebox.png"
                            height="150"
                          />
                        </td>
                        <td
                          v-if="
                            item.sectors.food.food_stocks_avaliability &&
                              item.sectors.food.food_stocks_avaliability.length > 0
                          "
                          v-bind:rowspan="
                            item.sectors.food.food_stocks_avaliability.length > 0
                              ? item.sectors.food.food_stocks_avaliability.length + 1
                              : 1
                          "
                          colspan="4"
                          style="width:20%"
                        >
                          <strong>Food stocks availability (HH)</strong>
                        </td>
                        <td
                          colspan="3"
                          width="30%"
                          v-if="
                            item.sectors.food.food_stocks_avaliability &&
                              item.sectors.food.food_stocks_avaliability.length > 0
                          "
                        >
                          <b>Category</b>
                        </td>
                        <td
                          colspan="2"
                          width="10%"
                          v-if="
                            item.sectors.food.food_stocks_avaliability &&
                              item.sectors.food.food_stocks_avaliability.length > 0
                          "
                        >
                          <b>Population group</b>
                        </td>
                        <td
                          width="10%"
                          colspan="2"
                          v-if="
                            item.sectors.food.food_stocks_avaliability &&
                              item.sectors.food.food_stocks_avaliability.length > 0
                          "
                        >
                          <b>
                            <center>Female</center>
                          </b>
                        </td>
                        <td
                          width="10%"
                          colspan="2"
                          v-if="
                            item.sectors.food.food_stocks_avaliability &&
                              item.sectors.food.food_stocks_avaliability.length > 0
                          "
                        >
                          <b>
                            <center>Male</center>
                          </b>
                        </td>
          
                        <td
                          width="10%"
                          colspan="2"
                          v-if="
                            item.sectors.food.food_stocks_avaliability &&
                              item.sectors.food.food_stocks_avaliability.length > 0
                          "
                        >
                          <b>
                            <center>Total</center>
                          </b>
                        </td>
                      </tr>
          
                      <tr
                        v-if="
                          item.sectors.food.food_stocks_avaliability &&
                            item.sectors.food.food_stocks_avaliability.length > 0
                        "
                        v-for="crop in item.sectors.food.food_stocks_avaliability"
                      >
                        <td width="30%" colspan="3">
                          {{ crop.availability_food_stocks || "Not reported" }}
                        </td>
                        <td width="10%" colspan="2">
                          {{
                            crop.selectedAgeGroup == "Other"
                              ? "Other(" + crop.other_crop_type == "undefined"
                                ? "Other(Not reported"
                                : "Other(Not reported" + ")"
                              : crop.selectedAgeGroup
                          }}
                        </td>
                        <td width="10%" colspan="2" class="right-align">
                          {{ crop.female_HH }}
                        </td>
                        <td width="10%" colspan="2" class="right-align">
                          {{ crop.male_HH }}
                        </td>
          
                        <td width="10%" colspan="2" class="right-align">
                          {{
                            parseInt(crop.female_HH || 0) + parseInt(crop.male_HH || 0)
                          }}
                        </td>
                      </tr>
                      <!------------------work from dc starts----------------------------------------->
                      <!---start---------------------->
                      <tr v-if="item.sectors.food.food_item_damage">
                        <td
                          style="width:20%"
                          :rowspan="
                            item.sectors.food.food_item_damage.length > 0
                              ? item.sectors.food.food_item_damage.length + 1
                              : 1
                          "
                          colspan="4"
                        >
                          <b>Food Items Damaged</b>
                        </td>
                        <td colspan="5" style="width:15%">
                          <b>
                            Food Name
                          </b>
                        </td>
                        <td colspan="5">
                          <b>
                            <center>Mass(KGs)</center>
                          </b>
                        </td>
                      </tr>
                      <tr
                        v-for="value in item.sectors.food.food_item_damage"
                        :key="value"
                      >
                        <td colspan="5">
                          {{ value.food_item_name }}
                        </td>
                        <td colspan="5" class="right-align">
                          {{ numberWithCommas(value.number_of_kilos) }}
                        </td>
                      </tr>
                      <!----------------end----------->
                      <!---food ---------------------->
          
                      <!---food  accessibility---------------------->
                      <tr
                        v-if="
                          item.sectors.food.food_access &&
                            item.sectors.food.food_access.length > 0
                        "
                      >
                        <td
                          style="width:20%"
                          :rowspan="
                            item.sectors.food.food_access.length > 0
                              ? item.sectors.food.food_access.length + 1
                              : 1
                          "
                          colspan="3"
                        >
                          <b>Food accessibility</b>
                        </td>
                        <td colspan="7">
                          <b>
                            Question
                          </b>
                        </td>
                        <td colspan="5">
                          <b>
                            Response
                          </b>
                        </td>
                      </tr>
                      <tr v-for="value in item.sectors.food.food_access" :key="value">
                        <td colspan="7">
                          {{ value.name }}
                        </td>
                        <td colspan="5">
                          {{ value.response }}
                        </td>
                      </tr>
                      <!----------------Food accessibility ends----------->
          
                      <!---food  accessibility---------------------->
                      <tr
                        v-if="
                          item.sectors.food.impact_on_price &&
                            item.sectors.food.impact_on_price.length > 0
                        "
                      >
                        <td
                          style="width:20%"
                          :rowspan="
                            item.sectors.food.impact_on_price.length > 0
                              ? item.sectors.food.impact_on_price.length + 1
                              : 1
                          "
                          colspan="3"
                        >
                          <b>Food Prices (MWK)</b>
                        </td>
                        <td colspan="4">
                          <b>
                            Item
                          </b>
                        </td>
                        <td colspan="4">
                          <b>
                            Before disaster
                          </b>
                        </td>
                        <td colspan="3">
                          <b>
                            After disaster
                          </b>
                        </td>
                      </tr>
                      <tr v-for="value in item.sectors.food.impact_on_price" :key="value">
                        <td colspan="4">
                          {{
                            value.name == "Other"
                              ? "Other(" + value.other_crop_type + ")"
                              : value.name
                          }}
                        </td>
                        <td colspan="4" class="right-align">
                          {{ numberWithCommas(value.price_before_disaster) }}
                        </td>
          
                        <td colspan="3" class="right-align">
                          {{ numberWithCommas(value.price_after_disaster) }}
                        </td>
                      </tr>
                      <!----------------work from dc finish---------------------------------->
                      <tr>
                        <td colspan="4" style="width:20%">
                          <strong>Emergency Needs</strong>
                        </td>
                        <td colspan="11">
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.food.emergent_needs == undefined
                                ? "Not reported"
                                : item.sectors.food.emergent_needs.join(", ")
                            }}
                          </li>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="4" style="width:20%">
                          <strong>Response Needed</strong>
                        </td>
                        <td colspan="11" style="width:80%">
                          <u>
                            <strong>URGENT</strong>
                          </u>
                          <br />
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.food
                                ? item.sectors.food.urgent_response_needed
                                  ? item.sectors.food.urgent_response_needed
                                  : "N/A"
                                : "N/A"
                            }}
                          </li>
                          <hr style="margin:1%;" />
                          <u>
                            <strong>GENERAL</strong>
                          </u>
                          <br />
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.food
                                ? item.sectors.food.response_needed
                                  ? item.sectors.food.response_needed
                                  : "N/A"
                                : "N/A"
                            }}
                          </li>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
                <!-- end Food security -->
                <!--  Education-->
                <tr
                  v-if="item.sectors.education"
                  style="margin:0;padding:0 !important;border:0px !important;"
                >
                  <td colspan="15" style="margin:0;padding:0 !important;border:0px">
                    <table
                      width="100%"
                      style="margin:0;padding:0 !important;border:0px !important;"
                    >
                      <tr>
                        <td
                          :rowspan="
                            5 +
                              (item.sectors.education.impact_on_schools
                                ? item.sectors.education.impact_on_schools.length +
                                  item.sectors.education.impact_on_schools.length
                                : 0) *
                                3
                          "
                          width="9%"
                          class="clearfix"
                        >
                          <span class="rotated" style="font-weight: bold;"
                            ></span
                          >
                          <img
                            src="../../../../static/cluster_education_100px_bluebox.png"
                            height="150"
                          />
                        </td>
                        <td
                          v-if="
                            item.sectors.education.impact_on_schools &&
                              item.sectors.education.impact_on_schools.length > 0
                          "
                          :rowspan="1 + item.sectors.education.impact_on_schools.length"
                          colspan="4"
                          style="20%"
                        >
                          <b>Impact on schools buildings</b>
                        </td>
                        <td
                          v-if="
                            item.sectors.education.impact_on_schools &&
                              item.sectors.education.impact_on_schools.length > 0
                          "
                          width="10%"
                        >
                          <b>School type</b>
                        </td>
                        <td
                          v-if="
                            item.sectors.education.impact_on_schools &&
                              item.sectors.education.impact_on_schools.length > 0
                          "
                          width="10%"
                        >
                          <b>Structure type</b>
                        </td>
                        <td
                          v-if="
                            item.sectors.education.impact_on_schools &&
                              item.sectors.education.impact_on_schools.length > 0
                          "
                          width="10%"
                        >
                          <b>Name</b>
                        </td>
                        <td
                          v-if="
                            item.sectors.education.impact_on_schools &&
                              item.sectors.education.impact_on_schools.length > 0
                          "
                          width="10%"
                        >
                          <b>Roof affected</b>
                        </td>
                        <td
                          v-if="
                            item.sectors.education.impact_on_schools &&
                              item.sectors.education.impact_on_schools.length > 0
                          "
                          width="10%"
                        >
                          <b>Under water</b>
                        </td>
                        <td
                          v-if="
                            item.sectors.education.impact_on_schools &&
                              item.sectors.education.impact_on_schools.length > 0
                          "
                          width="10%"
                        >
                          <b>Extent of damage</b>
                        </td>
          
                        <td
                          v-if="
                            item.sectors.education.impact_on_schools &&
                              item.sectors.education.impact_on_schools.length > 0
                          "
                          width="10%"
                        >
                          <b>Condition</b>
                        </td>
                      </tr>
          
                      <tr
                        v-if="
                          item.sectors.education.impact_on_schools &&
                            item.sectors.education.impact_on_schools.length > 0
                        "
                        v-for="row in item.sectors.education.impact_on_schools"
                      >
                        <td width="10%">
                          {{ row.school_type + (" | " + row.public_or_private || "") }}
                        </td>
                        <td width="10%">{{ row.structure_type }}</td>
                        <td width="10%">{{ row.school_name }}</td>
                        <td width="10%">
                          {{ row.roofs_affected }}
                        </td>
                        <td width="10%">
                          {{ row.underwater }}
                        </td>
                        <td width="10%">
                          {{ row.status }}
                        </td>
                        <td width="10%">
                          {{ row.condition }}
                        </td>
                      </tr>
          
                      <tr
                        v-if="
                          item.sectors.education.impact_on_schools &&
                            item.sectors.education.impact_on_schools.length > 0
                        "
                      >
                        <td
                          v-if="item.sectors.education.impact_on_schools"
                          :rowspan="1 + item.sectors.education.impact_on_schools.length"
                          colspan="4"
                          width="20%"
                        >
                          <b>Impact on school goers</b>
                        </td>
                        <td
                          v-if="item.sectors.education.impact_on_schools"
                          colspan="3"
                          width="10%"
                        >
                          <b>School type</b>
                        </td>
                        <td
                          v-if="item.sectors.education.impact_on_schools"
                          colspan="2"
                          width="10%"
                        >
                          <b>Name</b>
                        </td>
                        <td v-if="item.sectors.education.impact_on_schools" width="10%">
                          <b>Males out of school</b>
                        </td>
                        <td v-if="item.sectors.education.impact_on_schools" width="10%">
                          <b>Females out of school</b>
                        </td>
                      </tr>
                      <tr
                        v-if="
                          item.sectors.education.impact_on_schools &&
                            item.sectors.education.impact_on_schools.length > 0
                        "
                        v-for="row in item.sectors.education.impact_on_schools"
                      >
                        <td colspan="3" width="10%">
                          {{ row.school_type + (" | " + row.public_or_private || "") }}
                        </td>
                        <td colspan="2" width="10%">{{ row.school_name }}</td>
                        <td width="10%" class="right-align">
                          {{ numberWithCommas(row.females_out_of_school) }}
                        </td>
                        <td width="10%" class="right-align">
                          {{ numberWithCommas(row.males_out_of_school) }}
                        </td>
                      </tr>
          
                      <tr
                        v-if="
                          item.sectors.education.impact_on_schools &&
                            item.sectors.education.impact_on_schools.length > 0
                        "
                      >
                        <td
                          v-if="item.sectors.education.impact_on_schools"
                          :rowspan="1 + item.sectors.education.impact_on_schools.length"
                          colspan="3"
                          width="20%"
                        >
                          <b>Impact on teachers</b>
                        </td>
                        <td
                          v-if="item.sectors.education.impact_on_schools"
                          colspan="3"
                          width="20%"
                        >
                          <b>School type</b>
                        </td>
                        <td
                          v-if="item.sectors.education.impact_on_schools"
                          colspan="2"
                          width="10%"
                        >
                          <b>Name</b>
                        </td>
                        <td
                          v-if="item.sectors.education.impact_on_schools"
                          colspan="2"
                          width="20%"
                        >
                          <b>Female</b>
                        </td>
          
                        <td
                          v-if="item.sectors.education.impact_on_schools"
                          colspan="3"
                          width="20%"
                        >
                          <b>Male</b>
                        </td>
                      </tr>
                      <tr
                        v-if="
                          item.sectors.education.impact_on_schools &&
                            item.sectors.education.impact_on_schools.length > 0
                        "
                        v-for="row in item.sectors.education.impact_on_schools"
                      >
                        <td colspan="3" width="20%">
                          {{ row.school_type + (" | " + row.public_or_private || "") }}
                        </td>
                        <td colspan="2" width="10%">{{ row.school_name }}</td>
          
                        <td colspan="2" width="20%" class="right-align">
                          {{ numberWithCommas(row.female_teachers) }}
                        </td>
                        <td colspan="3" width="20%" class="right-align">
                          {{ numberWithCommas(row.male_teachers) }}
                        </td>
                      </tr>
          
                      <tr>
                        <td colspan="2" style="width:20%">
                          <strong>Emergency Needs</strong>
                        </td>
                        <td colspan="12">
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.education.emergent_needs == undefined
                                ? "Not reported"
                                : item.sectors.education.emergent_needs.join(", ")
                            }}
                          </li>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="2" style="width:20%">
                          <strong>Response Needed</strong>
                        </td>
                        <td colspan="12">
                          <u>
                            <strong>URGENT</strong>
                          </u>
                          <br />
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.education
                                ? item.sectors.education.urgent_response_needed
                                  ? item.sectors.education.urgent_response_needed
                                  : "N/A"
                                : "N/A"
                            }}
                          </li>
                          <hr style="margin:1%;" />
                          <u>
                            <strong>GENERAL</strong>
                          </u>
                          <br />
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.education
                                ? item.sectors.education.response_needed
                                  ? item.sectors.education.response_needed
                                  : "N/A"
                                : "N/A"
                            }}
                          </li>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
                <!-- end Education -->
          
                <!--  Healthy -->
                <tr
                  v-if="item.sectors.health"
                  style="margin:0;padding:0 !importat;border:0px !important;"
                >
                  <td colspan="15" style="margin:0;padding:0 !important;border:0px">
                    <table
                      width="100%"
                      style="margin:0;padding:0 !important;border:0px !important;"
                    >
                      <tr>
                        <td
                          :rowspan="
                            (item.sectors.health.available_health_facilities
                              ? item.sectors.health.available_health_facilities.length
                              : 0) +
                              (item.sectors.health.risk_out_disease_outbreak
                                ? item.sectors.health.risk_out_disease_outbreak.length + 1
                                : 0) +
                              (item.sectors.health.available_health_medical
                                ? item.sectors.health.available_health_medical.length + 1
                                : 0) +
                              9
                          "
                          width="10%"
                          class="clearfix"
                        >
                          <span class="rotated" style="font-weight: bold;"
                            ></span
                          >
                          <img
                            src="../../../../static/cluster_health_100px_bluebox.png"
                            height="150"
                          />
                        </td>
                        <!--------------------->
                      </tr>
          
                      <tr
                        v-if="
                          item.sectors.health.available_health_facilities &&
                            item.sectors.health.available_health_facilities.length > 0
                        "
                      >
                        <td
                          v-if="item.sectors.health.available_health_facilities"
                          :rowspan="
                            1 + item.sectors.health.available_health_facilities.length
                          "
                          colspan="3"
                          width="20%"
                        >
                          <b>Facilities affected</b>
                        </td>
          
                        <td
                          v-if="item.sectors.health.available_health_facilities"
                          colspan="3"
                          width="10%"
                        >
                          <b>Type</b>
                        </td>
                        <td
                          v-if="item.sectors.health.available_health_facilities"
                          colspan="2"
                          width="10%"
                        >
                          <b>Name</b>
                        </td>
                        <td
                          v-if="item.sectors.health.available_health_facilities"
                          colspan="4"
                          width="10%"
                        >
                          <b>Staff houses affected</b>
                        </td>
                        <td
                          v-if="item.sectors.health.available_health_facilities"
                          colspan="3"
                          width="10%"
                        >
                          <b>Status</b>
                        </td>
                      </tr>
          
                      <tr
                        v-if="
                          item.sectors.health.available_health_facilities &&
                            item.sectors.health.available_health_facilities.length > 0
                        "
                        v-for="row in item.sectors.health.available_health_facilities"
                      >
                        <td colspan="3" width="10%">{{ row.name }}</td>
                        <td colspan="2" width="10%">{{ row.facility_name }}</td>
                        <td colspan="4" class="right-align" width="10%">
                          {{ row.staff_houses_affected }}
                        </td>
                        <td colspan="3" width="10%">{{ row.status }}</td>
                      </tr>
          
                      <tr
                        v-if="
                          item.sectors.health.available_health_medical &&
                            item.sectors.health.available_health_medical.length > 0
                        "
                      >
                        <td
                          :rowspan="
                            1 + item.sectors.health.available_health_medical.length
                          "
                          colspan="5"
                          style="width:22%"
                        >
                          <strong>Availability of medical and health personnel</strong>
                        </td>
                        <td colspan="2" style="width:40%">
                          <b>Service</b>
                        </td>
                        <td colspan="6" style="width:30%">
                          <b>Status</b>
                        </td>
                      </tr>
                      <tr
                        v-if="
                          item.sectors.health.available_health_medical &&
                            item.sectors.health.available_health_medical.length > 0
                        "
                        v-for="row in item.sectors.health.available_health_medical"
                      >
                        <td style="width:40%" colspan="2">{{ row.name }}</td>
                        <td style="width:30%" colspan="6">{{ row.status }}</td>
                      </tr>
                      <!------------end------------------>
                      <tr
                        v-if="
                          item.sectors.health.risk_out_disease_outbreak &&
                            item.sectors.health.risk_out_disease_outbreak.length > 0
                        "
                      >
                        <td
                          :rowspan="
                            1 + item.sectors.health.risk_out_disease_outbreak.length
                          "
                          colspan="5"
                          style="width:22%"
                        >
                          <strong>Risks of disease outbreak</strong>
                        </td>
                        <td colspan="2" style="width:40%">
                          <b>Disease</b>
                        </td>
                        <td colspan="6" style="width:30%">
                          <b>Risk</b>
                        </td>
                      </tr>
                      <tr
                        v-if="
                          item.sectors.health.risk_out_disease_outbreak &&
                            item.sectors.health.risk_out_disease_outbreak.length > 0
                        "
                        v-for="row in item.sectors.health.risk_out_disease_outbreak"
                      >
                        <td style="width:40%" colspan="2">{{ row.name }}</td>
                        <td style="width:30%" colspan="6">{{ row.risk }}</td>
                      </tr>
          
                      <tr>
                        <td colspan="5" style="width:22%">
                          <strong>Compromised services</strong>
                        </td>
                        <td colspan="8">
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.health.compromised_services == undefined
                                ? "Not reported"
                                : item.sectors.health.compromised_services.join(", ")
                            }}
                          </li>
                        </td>
                      </tr>
          
                      <tr>
                        <td colspan="5" style="width:22%">
                          <strong>Emergency Needs</strong>
                        </td>
                        <td colspan="8">
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.health.emergent_needs == undefined
                                ? "Not reported"
                                : item.sectors.health.emergent_needs.join(", ")
                            }}
                          </li>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="6" style="width:20%">
                          <strong>Emergency Children Needs</strong>
                        </td>
                        <td colspan="8">
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.health.emergent_children_needs == undefined
                                ? "Not reported"
                                : item.sectors.health.emergent_children_needs.join(", ")
                            }}
                          </li>
                        </td>
                      </tr>
                      <tr></tr>
                      <tr>
                        <td colspan="6" style="width:20%">
                          <strong>Emergency Women Needs</strong>
                        </td>
                        <td colspan="8">
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.health.emergent_women_needs == undefined
                                ? "Not reported"
                                : item.sectors.health.emergent_women_needs.join(", ")
                            }}
                          </li>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="6" style="width:20%">
                          <strong>Response Needed</strong>
                        </td>
                        <td colspan="8" style="width:70%">
                          <u>
                            <strong>URGENT</strong>
                          </u>
                          <br />
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.health
                                ? item.sectors.health.urgent_response_needed
                                  ? item.sectors.health.urgent_response_needed
                                  : "N/A"
                                : "N/A"
                            }}
                          </li>
                          <hr style="margin:1%;" />
                          <u>
                            <strong>GENERAL</strong>
                          </u>
                          <br />
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.health
                                ? item.sectors.health.response_needed
                                  ? item.sectors.health.response_needed
                                  : "N/A"
                                : "N/A"
                            }}
                          </li>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
                <!-- end Healthy -->
          
                <!-- Food livelihoods -->
                <!-- <tr
                          v-if="item.sectors.livelihoods"
                          style="margin:0;padding:0 !important;border:0px !important;"
                        >
                          <td
                            colspan="15"
                            style="margin:0;padding:0 !important;border:0px"
                          >
                            <table
                              width="100%"
                              style="margin:0;padding:0 !important;border:0px !important;"
                            >
                              <tr>
                                <td
                                  width="10%"
                                  class="clearfix"
          
                                  :rowspan="
                                    item.sectors.livelihoods.livelihoods_affected
                                      ? item.sectors.livelihoods.livelihoods_affected
                                          .length + 2
                                      : 2
                                  "
                                >
                                  <span class="rotated" style="font-weight: bold;"
                                    >Livelihoods</span
                                  >
                                  <img
                                    src="../../../../static/socioeconomic_livelihood_100px_bluebox.png"
                                    height="40"
                                  />
                                </td>
                                <td
                                  v-if="
                                    item.sectors.livelihoods.livelihoods_affected &&
                                      item.sectors.livelihoods.livelihoods_affected
                                        .length > 0
                                  "
                                  :rowspan="
                                    1 +
                                      item.sectors.livelihoods.livelihoods_affected.length
                                  "
                                  style="width:20%"
                                >
                                  <strong>Livelihoods affected (HH)</strong>
                                </td>
                                <td
                                  width="50%"
                                  v-if="
                                    item.sectors.livelihoods.livelihoods_affected &&
                                      item.sectors.livelihoods.livelihoods_affected
                                        .length > 0
                                  "
                                  colspan="8"
                                >
                                  <b>Category</b>
                                </td>
                                <td
                                  width="10%"
                                  v-if="
                                    item.sectors.livelihoods.livelihoods_affected &&
                                      item.sectors.livelihoods.livelihoods_affected
                                        .length > 0
                                  "
                                  colspan="2"
                                >
                                  <b>Severely affected</b>
                                </td>
                                <td
                                  width="10%"
                                  v-if="
                                    item.sectors.livelihoods.livelihoods_affected &&
                                      item.sectors.livelihoods.livelihoods_affected
                                        .length > 0
                                  "
                                  colspan="2"
                                >
                                  <b>Slightly affected</b>
                                </td>
                              </tr>
                              <tr
                                v-if="
                                  item.sectors.livelihoods.livelihoods_affected &&
                                    item.sectors.livelihoods.livelihoods_affected.length >
                                      0
                                "
                                v-for="row in item.sectors.livelihoods
                                  .livelihoods_affected"
                              >
                                <td width="50%" colspan="8">{{ row.name }}</td>
                                <td width="10%" colspan="2" class="right-align">
                                  {{ numberWithCommas(row.severely_affected) }}
                                </td>
                                <td width="10%" colspan="2" class="right-align">
                                  {{ numberWithCommas(row.slightly_affected) }}
                                </td>
                              </tr>
                              <tr>
                                <td colspan="1" style="width:20%">
                                  <strong>Response Needed</strong>
                                </td>
                                <td colspan="13" style="width:70%">
                                  <u>
                                    <strong>URGENT</strong>
                                  </u>
                                  <br />
                                  <li style="margin-left:2%" class="qcont">
                                    {{
                                      item.sectors.livelihoods
                                        ? item.sectors.livelihoods.urgent_response_needed
                                          ? item.sectors.livelihoods
                                              .urgent_response_needed
                                          : "N/A"
                                        : "N/A"
                                    }}
                                  </li>
                                  <hr style="margin:1%;" />
                                  <u>
                                    <strong>GENERAL</strong>
                                  </u>
                                  <br />
                                  <li style="margin-left:2%" class="qcont">
                                    {{
                                      item.sectors.livelihoods
                                        ? item.sectors.livelihoods.response_needed
                                          ? item.sectors.livelihoods.response_needed
                                          : "N/A"
                                        : "N/A"
                                    }}
                                  </li>
                                </td>
                              </tr>
                            </table>
                          </td>
                        </tr> -->
                <!-- end livelihoods -->
                <!--  Nutrition -->
                <tr
                  v-if="item.sectors.nutrition"
                  style="margin:0;padding:0 !important;border:0px !important;"
                >
                  <td colspan="15" style="margin:0;padding:0 !important;border:0px">
                    <table
                      width="100%"
                      style="margin:0;padding:0 !important;border:0px !important;"
                    >
                      <tr>
                        <td
                          :rowspan="
                            4 +
                              (item.sectors.nutrition.affected_population
                                ? item.sectors.nutrition.affected_population.length
                                : 0) +
                              (item.sectors.nutrition.impact_on_nutrition_programmes
                                ? item.sectors.nutrition.impact_on_nutrition_programmes
                                    .length
                                : 0) +
                              (item.sectors.nutrition.lostLivelihood
                                ? item.sectors.nutrition.lostLivelihood.length
                                : 0) +
                              1
                          "
                          width="9%"
                          class="clearfix"
                        >
                          <span class="rotated" style="font-weight: bold;"
                            ></span
                          >
                          <img
                            src="../../../../static/cluster_nutrition_100px_bluebox.png"
                            height="150"
                          />
                        </td>
                        <td
                          v-if="item.sectors.nutrition.affected_population"
                          width="20%"
                          :rowspan="1 + item.sectors.nutrition.affected_population.length"
                          colspan="4"
                        >
                          <strong>Affected Population</strong>
                        </td>
                        <td colspan="4" width="40%">
                          <b>Category</b>
                        </td>
                        <td colspan="2" width="10%">
                          <b>
                            <center>Male</center>
                          </b>
                        </td>
                        <td colspan="2" width="10%">
                          <b>
                            <center>Female</center>
                          </b>
                        </td>
                        <td colspan="2" width="10%">
                          <b>
                            <center>Total</center>
                          </b>
                        </td>
                      </tr>
                      <tr
                        v-if="item.sectors.nutrition.affected_population"
                        v-for="row in item.sectors.nutrition.affected_population"
                      >
                        <td colspan="4" width="40%">
                          {{
                            row.name == "Other"
                              ? "Other(" + row.other_affected_population + ")"
                              : row.name
                          }}
                        </td>
                        <td colspan="2" width="10%" class="right-align">
                          {{ numberWithCommas(row.affected_males) }}
                        </td>
                        <td colspan="2" width="10%" class="right-align">
                          {{ numberWithCommas(row.affected_females) }}
                        </td>
                        <td colspan="2" width="10%" class="right-align">
                          {{
                            numberWithCommas(
                              parseInt(row.affected_females || 0) +
                                parseInt(row.affected_males || 0)
                            )
                          }}
                        </td>
                      </tr>
          
                      <tr v-if="item.sectors.nutrition.impact_on_nutrition_programmes">
                        <td
                          :rowspan="
                            1 +
                              item.sectors.nutrition.impact_on_nutrition_programmes.length
                          "
                          colspan="4"
                          width="20%"
                        >
                          <strong>Impact on nutrition programmes</strong>
                        </td>
                        <td colspan="4">
                          <b>Category</b>
                        </td>
                        <td colspan="6">
                          <b>Status</b>
                        </td>
                      </tr>
                      <tr
                        v-if="item.sectors.nutrition.impact_on_nutrition_programmes"
                        v-for="row in item.sectors.nutrition
                          .impact_on_nutrition_programmes"
                      >
                        <td colspan="4">{{ row.name }}</td>
                        <td colspan="6">{{ row.status }}</td>
                      </tr>
          
                      <tr v-if="item.sectors.nutrition.lostLivelihood">
                        <td
                          v-if="item.sectors.nutrition.lostLivelihood"
                          width="20%"
                          :rowspan="1 + item.sectors.nutrition.lostLivelihood.length"
                          colspan="4"
                        >
                          <strong>Lost livelihood</strong>
                        </td>
                        <td colspan="4" width="40%">
                          <b>Population group</b>
                        </td>
                        <td colspan="2" width="10%">
                          <b>
                            <center>Male</center>
                          </b>
                        </td>
                        <td colspan="2" width="10%">
                          <b>
                            <center>Female</center>
                          </b>
                        </td>
                        <td colspan="2" width="10%">
                          <b>
                            <center>Total</center>
                          </b>
                        </td>
                      </tr>
          
                      <tr
                        v-if="item.sectors.nutrition.lostLivelihood"
                        v-for="row in item.sectors.nutrition.lostLivelihood"
                      >
                        <td colspan="4" width="40%">
                          {{
                            row.name == "Other"
                              ? "Other(" + row.other_category_name + ")"
                              : row.name
                          }}
                        </td>
                        <td colspan="2" width="10%" class="right-align">
                          {{ numberWithCommas(row.male_lost_livelihood) }}
                        </td>
                        <td colspan="2" width="10%" class="right-align">
                          {{ numberWithCommas(row.male_lost_livelihood) }}
                        </td>
                        <td colspan="2" width="10%" class="right-align">
                          {{
                            numberWithCommas(
                              parseInt(row.male_lost_livelihood || 0) +
                                parseInt(row.male_lost_livelihood || 0)
                            )
                          }}
                        </td>
                      </tr>
          
                      <tr>
                        <td colspan="4" style="width:20%">
                          <strong>Emergency Needs</strong>
                        </td>
                        <td colspan="10">
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.nutrition.emergent_needs == undefined
                                ? "Not reported"
                                : item.sectors.nutrition.emergent_needs.join(", ")
                            }}
                          </li>
                        </td>
                      </tr>
          
                      <tr v-if="item.sectors.nutrition.impact_on_nutrition_programmes">
                        <td colspan="4" style="width:20%">
                          <strong>Response Needed</strong>
                        </td>
                        <td colspan="10" style="width:70%">
                          <u>
                            <strong>URGENT</strong>
                          </u>
                          <br />
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.nutrition
                                ? item.sectors.nutrition.urgent_response_needed
                                  ? item.sectors.nutrition.urgent_response_needed
                                  : "N/A"
                                : "N/A"
                            }}
                          </li>
                          <hr style="margin:1%;" />
                          <u>
                            <strong>GENERAL</strong>
                          </u>
                          <br />
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.nutrition
                                ? item.sectors.nutrition.response_needed
                                  ? item.sectors.nutrition.response_needed
                                  : "N/A"
                                : "N/A"
                            }}
                          </li>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
                <!-- end Nutrition -->
                <!--  Protection -->
                <tr
                  v-if="item.sectors.protection"
                  style="margin:0;padding:0 !important;border:0px !important;"
                >
                  <td colspan="15" style="margin:0;padding:0 !important;border:0px">
                    <table
                      width="100%"
                      style="margin:0;padding:0 !important;border:0px !important;"
                    >
                      <tr v-if="item.sectors.protection.impact_on_vulnerable_persons">
                        <td
                          :rowspan="
                            10 +
                              (item.sectors.protection.impact_on_vulnerable_persons
                                ? item.sectors.protection.impact_on_vulnerable_persons
                                    .length + 1
                                : 0) +
                              (item.sectors.protection.protection_supplies
                                ? item.sectors.protection.protection_supplies.length + 1
                                : 0)
                          "
                          width="9%"
                          class="clearfix"
                        >
                          <span class="rotated" style="font-weight: bold;"
                            ></span
                          >
                          <img
                            src="../../../../static/cluster_protection_100px_bluebox.png"
                            height="150"
                          />
                        </td>
                        <td
                          :rowspan="
                            1 +
                              item.sectors.protection.impact_on_vulnerable_persons.length
                          "
                          width="30%"
                        >
                          <strong>Impact on vulnerable population</strong>
                        </td>
                        <td colspan="4" width="30%">
                          <b>Population group</b>
                        </td>
                        <td colspan="3" width="10%">
                          <b>
                            <center>Male</center>
                          </b>
                        </td>
                        <td colspan="3" width="10%">
                          <b>
                            <center>Female</center>
                          </b>
                        </td>
                        <td colspan="3" width="10%">
                          <b>
                            <center>Total</center>
                          </b>
                        </td>
                      </tr>
                      <tr
                        v-if="item.sectors.protection.impact_on_vulnerable_persons"
                        v-for="row in item.sectors.protection
                          .impact_on_vulnerable_persons"
                      >
                        <td colspan="4" width="30%">
                          {{ row.name == "Other" ? row.other_population_type : row.name }}
                        </td>
                        <td colspan="3" width="10%" class="right-align">
                          {{ numberWithCommas(row.impacted_males) }}
                        </td>
                        <td colspan="3" width="10%" class="right-align">
                          {{ numberWithCommas(row.impacted_females) }}
                        </td>
                        <td colspan="3" width="10%" class="right-align">
                          {{
                            numberWithCommas(
                              (row.impacted_females
                                ? parseInt(row.impacted_females)
                                : 0) +
                                (row.impacted_males ? parseInt(row.impacted_males) : 0)
                            )
                          }}
                        </td>
                      </tr>
                      <tr v-if="item.sectors.protection.protection_concerns">
                        <td style="width:20%">
                          <strong>Major Protection Concerns</strong>
                        </td>
                        <td colspan="13" style="width:70%">
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.protection.protection_concerns == undefined
                                ? "Not reported"
                                : item.sectors.protection.protection_concerns.join(", ")
                            }}
                          </li>
                        </td>
                      </tr>
                      <tr v-if="item.sectors.protection.protection_services">
                        <td style="width:20%">
                          <strong>Protection Service Available</strong>
                        </td>
                        <td colspan="13" style="width:70%">
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.protection.protection_services == undefined
                                ? "Not reported"
                                : item.sectors.protection.protection_services.join(", ")
                            }}
                          </li>
                        </td>
                      </tr>
                      <tr v-if="item.sectors.protection.protection_trainings">
                        <td style="width:20%">
                          <strong>Training of Protection Service Providers</strong>
                        </td>
                        <td colspan="13" style="width:70%">
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.protection.protection_trainings == undefined
                                ? "Not reported"
                                : item.sectors.protection.protection_trainings.join(", ")
                            }}
                          </li>
                        </td>
                      </tr>
                      <tr v-if="item.sectors.protection.protection_supplies">
                        <td
                          :rowspan="
                            1 + item.sectors.protection.protection_supplies.length
                          "
                          width="20%"
                        >
                          <strong>Protection Supplies</strong>
                        </td>
                        <td colspan="4" width="30%">
                          <b>Name</b>
                        </td>
                        <td colspan="4" width="20%">
                          <b>
                            <center>Quantity</center>
                          </b>
                        </td>
                        <td colspan="4" width="20%">
                          <b>
                            Unit of Measure
                          </b>
                        </td>
                      </tr>
                      <tr
                        v-if="item.sectors.protection.protection_supplies"
                        v-for="row in item.sectors.protection.protection_supplies"
                      >
                        <td colspan="4" width="30%">{{ row.name }}</td>
                        <td colspan="4" width="20%" class="right-align">
                          {{ numberWithCommas(row.quantity) }}
                        </td>
                        <td colspan="4" width="20%">
                          {{ row.unit_of_measure }}
                        </td>
                      </tr>
                      <tr v-if="item.sectors.protection.protection_awareness">
                        <td style="width:20%">
                          <strong>Protection Awareness</strong>
                        </td>
                        <td colspan="13" style="width:70%">
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.protection.protection_awareness == undefined
                                ? "Not reported"
                                : item.sectors.protection.protection_awareness.join(", ")
                            }}
                          </li>
                        </td>
                      </tr>
                      <tr v-if="item.sectors.protection.protection_assessments">
                        <td style="width:20%">
                          <strong>Protection Assessments</strong>
                        </td>
                        <td colspan="13" style="width:70%">
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.protection.protection_assessments == undefined
                                ? "Not reported"
                                : item.sectors.protection.protection_assessments.join(
                                    ", "
                                  )
                            }}
                          </li>
                        </td>
                      </tr>
                      <tr v-if="item.sectors.protection.protection_mainstreaming">
                        <td style="width:20%">
                          <strong>Gender Main Stream</strong>
                        </td>
                        <td colspan="13" style="width:70%">
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.protection.protection_mainstreaming ==
                              undefined
                                ? "Not reported"
                                : item.sectors.protection.protection_mainstreaming.join(
                                    ", "
                                  )
                            }}
                          </li>
                        </td>
                      </tr>
                      <tr v-if="item.sectors.protection.protection_needs">
                        <td style="width:20%">
                          <strong>Protection Needs</strong>
                        </td>
                        <td colspan="13" style="width:70%">
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.protection.protection_needs == undefined
                                ? "Not reported"
                                : item.sectors.protection.protection_needs.join(", ")
                            }}
                          </li>
                        </td>
                      </tr>
                      <tr v-if="item.sectors.protection.impact_on_vulnerable_persons">
                        <td style="width:20%">
                          <strong>Response Needed</strong>
                        </td>
                        <td colspan="13" style="width:70%">
                          <u>
                            <strong>URGENT</strong>
                          </u>
                          <br />
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.protection
                                ? item.sectors.protection.urgent_response_needed
                                  ? item.sectors.protection.urgent_response_needed
                                  : "N/A"
                                : "N/A"
                            }}
                          </li>
                          <hr style="margin:1%;" />
                          <u>
                            <strong>GENERAL</strong>
                          </u>
                          <br />
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.protection
                                ? item.sectors.protection.response_needed
                                  ? item.sectors.protection.response_needed
                                  : "N/A"
                                : "N/A"
                            }}
                          </li>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
                <!-- end Protection -->
                <!--  Wash -->
                <tr
                  v-if="item.sectors.wash"
                  style="margin:0;padding:0 !important;border:0px !important;"
                >
                  <td colspan="15" style="margin:0;padding:0 !important;border:0px">
                    <table
                      width="100%"
                      style="margin:0;padding:0 !important;border:0px !important;"
                    >
                      <tr v-if="item.sectors.wash">
                        <td
                          :rowspan="
                            6 +
                              (item.sectors.wash.risk_of_water_contamination
                                ? item.sectors.wash.risk_of_water_contamination.length + 1
                                : 1) +
                              (item.sectors.wash.hh_affected_by_water_contamination
                                ? item.sectors.wash.hh_affected_by_water_contamination
                                    .length + 1
                                : 1) +
                              (item.sectors.wash.accessToToilets
                                ? item.sectors.wash.accessToToilets.length + 1
                                : 1) +
                              (item.sectors.wash.hh_affected_by_water_contamination
                                ? item.sectors.wash.hh_affected_by_water_contamination
                                    .length + 1
                                : 1) +
                              (item.sectors.wash.withoutSafeDrinkingWater
                                ? item.sectors.wash.withoutSafeDrinkingWater.length + 1
                                : 1) +
                              (item.sectors.wash.impact_on_water_sources
                                ? item.sectors.wash.impact_on_water_sources.length + 1
                                : 1) +
                              1
                          "
                          width="9%"
                          class="clearfix"
                        >
                          <span class="rotated" style="font-weight: bold;"></span>
                          <img
                            src="../../../../static/cluster_WASH_100px_bluebox.png"
                            height="150"
                          />
                        </td>
                        <td
                          :rowspan="
                            item.sectors.wash.risk_of_water_contamination
                              ? item.sectors.wash.risk_of_water_contamination.length + 1
                              : 1
                          "
                          colspan="5"
                          style="width:30%"
                        >
                          <b>Risk of contamination</b>
                        </td>
                        <td colspan="5">
                          <b>Source</b>
                        </td>
                        <td colspan="5">
                          <b>Risk</b>
                        </td>
                      </tr>
                      <tr
                        v-if="item.sectors.wash.risk_of_water_contamination"
                        v-for="row in item.sectors.wash.risk_of_water_contamination"
                      >
                        <td colspan="5">
                          {{
                            row.name == "Other"
                              ? "Other(" + row.other_source_name + ")"
                              : row.name
                          }}
                        </td>
                        <td colspan="5">{{ row.risk }}</td>
                      </tr>
          
                      <tr v-if="item.sectors.wash.impact_on_water_sources">
                        <td
                          :rowspan="
                            item.sectors.wash.impact_on_water_sources
                              ? item.sectors.wash.impact_on_water_sources.length + 1
                              : 1
                          "
                          colspan="5"
                          style="width:30%"
                        >
                          <b>Impact on water sources</b>
                        </td>
                        <td colspan="5">
                          <b>Source</b>
                        </td>
                        <td colspan="5">
                          <b>Impact</b>
                        </td>
                      </tr>
                      <tr
                        v-if="item.sectors.wash.impact_on_water_sources"
                        v-for="row in item.sectors.wash.impact_on_water_sources"
                      >
                        <td colspan="5">
                          {{
                            row.name == "Other"
                              ? "Other(" + row.other_structure_name + ")"
                              : row.name
                          }}
                        </td>
                        <td colspan="5">{{ row.impact }}</td>
                      </tr>
          
                      <tr v-if="item.sectors.wash.accessToToilets">
                        <td
                          style="width:30%"
                          :rowspan="item.sectors.wash.accessToToilets.length + 1"
                          colspan="5"
                        >
                          <b>Households without access to toilets</b>
                        </td>
                        <td colspan="3">
                          <b>
                            Household group
                          </b>
                        </td>
                        <td colspan="3">
                          <b>
                            <center>Male</center>
                          </b>
                        </td>
                        <td colspan="3">
                          <b>
                            <center>Female</center>
                          </b>
                        </td>
                        <td colspan="3">
                          <b>
                            <center>Total</center>
                          </b>
                        </td>
                      </tr>
                      <tr
                        v-if="item.sectors.wash && item.sectors.wash.accessToToilets"
                        v-for="row in item.sectors.wash.accessToToilets"
                      >
                        <td colspan="3">
                          {{
                            row.name == "Other"
                              ? "Other(" + row.other_category_name + ")"
                              : row.name
                          }}
                        </td>
                        <td colspan="3" class="right-align">
                          {{ numberWithCommas(row.access_to_toilets_mhh) }}
                        </td>
                        <td colspan="3" class="right-align">
                          {{ numberWithCommas(row.access_to_toilets_fhh) }}
                        </td>
                        <td colspan="3" class="right-align">
                          {{
                            numberWithCommas(
                              parseInt(row.access_to_toilets_mhh) +
                                parseInt(row.access_to_toilets_fhh)
                            )
                          }}
                        </td>
                      </tr>
                      <!---start---------------------->
                      <tr v-if="item.sectors.wash.withoutSafeDrinkingWater">
                        <td
                          style="width:20%"
                          :rowspan="item.sectors.wash.withoutSafeDrinkingWater.length + 1"
                          colspan="5"
                        >
                          <b>Households without access to safe drinking water</b>
                        </td>
                        <td colspan="3">
                          <b>
                            Household group
                          </b>
                        </td>
                        <td colspan="3">
                          <b>
                            <center>Male</center>
                          </b>
                        </td>
                        <td colspan="3">
                          <b>
                            <center>Female</center>
                          </b>
                        </td>
                        <td colspan="3">
                          <b>
                            <center>Total</center>
                          </b>
                        </td>
                      </tr>
          
                      <tr
                        v-if="item.sectors.wash.withoutSafeDrinkingWater"
                        v-for="(row, index) in item.sectors.wash.withoutSafeDrinkingWater"
                        :key="index"
                      >
                        <td colspan="3">
                          {{
                            row.name == "Other"
                              ? "Other(" + row.other_category_name + ")"
                              : row.name
                          }}
                        </td>
                        <td colspan="3" class="right-align">
                          {{ numberWithCommas(row.with_safe_water_fhh) }}
                        </td>
                        <td colspan="3" class="right-align">
                          {{ numberWithCommas(row.with_safe_water_mhh) }}
                        </td>
                        <td colspan="3" class="right-align">
                          {{
                            numberWithCommas(
                              parseInt(row.with_safe_water_mhh || 0) +
                                parseInt(row.with_safe_water_fhh || 0)
                            )
                          }}
                        </td>
                      </tr>
          
                      <!----------------end----------->
          
                      <tr v-if="item.sectors.wash.hh_affected_by_water_contamination">
                        <td
                          style="width:20%"
                          :rowspan="
                            item.sectors.wash.hh_affected_by_water_contamination.length +
                              1
                          "
                          colspan="5"
                        >
                          <b>Households affected by water contamination</b>
                        </td>
                        <td colspan="3">
                          <b>
                            Household group
                          </b>
                        </td>
                        <td colspan="3">
                          <b>
                            <center>Male</center>
                          </b>
                        </td>
                        <td colspan="3">
                          <b>
                            <center>Female</center>
                          </b>
                        </td>
                        <td colspan="3">
                          <b>
                            <center>Total</center>
                          </b>
                        </td>
                      </tr>
          
                      <tr
                        v-if="item.sectors.wash.hh_affected_by_water_contamination"
                        v-for="row in item.sectors.wash
                          .hh_affected_by_water_contamination"
                      >
                        <td colspan="3">
                          {{
                            row.name == "Other"
                              ? "Other(" + row.other_category_name + ")"
                              : row.name
                          }}
                        </td>
                        <td colspan="3" class="right-align">
                          {{ numberWithCommas(row.risk_contamination_mhh) }}
                        </td>
                        <td colspan="3" class="right-align">
                          {{ numberWithCommas(row.risk_contamination_fhh) }}
                        </td>
                        <td colspan="3" class="right-align">
                          {{
                            numberWithCommas(
                              parseInt(row.risk_contamination_mhh) +
                                parseInt(row.risk_contamination_fhh)
                            )
                          }}
                        </td>
                      </tr>
          
                      <tr v-if="item.sectors.wash.emergent_needs">
                        <td style="width:20%">
                          <strong>Wash Needs</strong>
                        </td>
                        <td colspan="14" style="width:70%">
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.wash.emergent_needs == undefined
                                ? "Not reported"
                                : item.sectors.wash.emergent_needs.join(", ")
                            }}
                          </li>
                        </td>
                      </tr>
          
                      <tr>
                        <td colspan="6" style="width:20%">
                          <strong>Response Needed</strong>
                        </td>
                        <td colspan="9">
                          <u>
                            <strong>URGENT</strong>
                          </u>
                          <br />
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.wash
                                ? item.sectors.wash.urgent_response_needed
                                  ? item.sectors.wash.urgent_response_needed
                                  : "N/A"
                                : "N/A"
                            }}
                          </li>
                          <hr style="margin:1%;" />
                          <u>
                            <strong>GENERAL</strong>
                          </u>
                          <br />
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.wash
                                ? item.sectors.wash.response_needed
                                  ? item.sectors.wash.response_needed
                                  : "N/A"
                                : "N/A"
                            }}
                          </li>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
                <!-- end Wash -->
                <!--  Logisticts -->
                <tr
                  v-if="item.sectors.logistics"
                  style="margin:0;padding:0 !important;border:0px !important;"
                >
                  <td colspan="15" style="margin:0;padding:0 !important;border:0px">
                    <table
                      width="100%"
                      style="margin:0;padding:0 !important;border:0px !important;"
                    >
                      <tr>
                        <td
                          :rowspan="
                            4 +
                              (item.sectors.logistics.impacted_structures
                                ? item.sectors.logistics.impacted_structures.length
                                : 0) +
                              (item.sectors.logistics.impacted_telecoms
                                ? item.sectors.logistics.impacted_telecoms.length
                                : 0)
                          "
                          width="12%"
                          class="clearfix"
                        >
                          <span class="rotated" style="font-weight: bold;"
                            ></span
                          >
                          <img
                            src="../../../../static/cluster_logistics_100px_bluebox.png"
                            height="150"
                          />
                        </td>
                        <!-- structures start -->
                        <td
                          v-if="item.sectors.logistics.impacted_structures"
                          :rowspan="1 + item.sectors.logistics.impacted_structures.length"
                          colspan="4"
                          style="width:20%"
                        >
                          <strong>Impact on Stractures</strong>
                        </td>
                        <td
                          colspan="2"
                          v-if="item.sectors.logistics.impacted_structures"
                          style="width:20%"
                        >
                          <b>Category</b>
                        </td>
                        <td
                          v-if="item.sectors.logistics.impacted_structures"
                          colspan="3"
                          style="width:15%"
                        >
                          <b>Lat</b>
                        </td>
                        <td
                          v-if="item.sectors.logistics.impacted_structures"
                          colspan="3"
                          style="width:15%"
                        >
                          <b>Long</b>
                        </td>
                        <td
                          colspan="3"
                          v-if="item.sectors.logistics.impacted_structures"
                          style="width:20%"
                        >
                          <b>Road Length (m)</b>
                        </td>
                        <td
                          colspan="3"
                          v-if="item.sectors.logistics.impacted_structures"
                          style="width:20%"
                        >
                          <b>Accessibility</b>
                        </td>
                        <td
                          colspan="3"
                          v-if="item.sectors.logistics.impacted_structures"
                          style="width:20%"
                        >
                          <b>Road Condition</b>
                        </td>
                        <td
                          colspan="3"
                          v-if="item.sectors.logistics.impacted_structures"
                          style="width:20%"
                        >
                          <b>Surface Condition</b>
                        </td>
                      </tr>
                      <tr
                        v-if="item.sectors.logistics.impacted_structures"
                        v-for="row in item.sectors.logistics.impacted_structures"
                      >
                        <td colspan="2" style="width:20%">
                          {{
                            row.name == "Other"
                              ? "Other(" + row.other_structure + ")"
                              : row.name
                          }}
                        </td>
                        <td colspan="3" style="width:15%">{{ row.gps_lat }}</td>
                        <td colspan="3" style="width:15%">{{ row.gps_lng }}</td>
                        <td colspan="3" style="width:20%">{{ row.road_length }}</td>
                        <td colspan="3" style="width:20%">{{ row.road_access }}</td>
                        <td colspan="3" style="width:20%">{{ row.road_condition }}</td>
                        <td colspan="3" style="width:20%">{{ row.surface_condition }}</td>
                      </tr>
          
                      <!--impacted telecoms-->
                      <tr>
                        <td
                          v-if="item.sectors.logistics.impacted_telecoms"
                          :rowspan="1 + item.sectors.logistics.impacted_telecoms.length"
                          colspan="4"
                          style="width:20%"
                        >
                          <strong>Impact on Telecommunications</strong>
                        </td>
                        <td
                          colspan="4"
                          v-if="item.sectors.logistics.impacted_telecoms"
                          style="width:20%"
                        >
                          <b>Category</b>
                        </td>
                        <td
                          colspan="4"
                          v-if="item.sectors.logistics.impacted_telecoms"
                          style="width:20%"
                        >
                          <b>Status</b>
                        </td>
                        <td
                          colspan="5"
                          v-if="item.sectors.logistics.impacted_telecoms"
                          style="width:20%"
                        >
                          <b>Lat</b>
                        </td>
                        <td
                          colspan="5"
                          v-if="item.sectors.logistics.impacted_telecoms"
                          style="width:20%"
                        >
                          <b>Long</b>
                        </td>
                      </tr>
                      <tr
                        v-if="item.sectors.logistics.impacted_telecoms"
                        v-for="row in item.sectors.logistics.impacted_telecoms"
                      >
                        <td colspan="4" style="width:20%">
                          {{
                            row.name == "Other"
                              ? "Other(" + row.other_telecommunications_type + ")"
                              : row.name
                          }}
                        </td>
                        <td colspan="4" style="width:20%">{{ row.status }}</td>
                        <td colspan="5" style="width:20%">{{ row.telecoms_gps_lat }}</td>
                        <td colspan="5" style="width:20%">{{ row.telecoms_gps_long }}</td>
                      </tr>
          
                      <!-- structures end  -->
                      <tr>
                        <td colspan="4" style="width:20%">
                          <strong>Emergency Needs</strong>
                        </td>
                        <td colspan="20">
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.logistics.emergent_needs == undefined
                                ? "Not reported"
                                : item.sectors.logistics.emergent_needs.join(", ")
                            }}
                          </li>
                        </td>
                      </tr>
                      <tr>
                        <td colspan="4" style="width:20%">
                          <strong>Response Needed</strong>
                        </td>
                        <td colspan="20" style="width:70%">
                          <u>
                            <strong>URGENT</strong>
                          </u>
                          <br />
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.logistics
                                ? item.sectors.logistics.urgent_response_needed
                                  ? item.sectors.logistics.urgent_response_needed
                                  : "N/A"
                                : "N/A"
                            }}
                          </li>
                          <hr style="margin:1%;" />
                          <u>
                            <strong>GENERAL</strong>
                          </u>
                          <br />
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.logistics
                                ? item.sectors.logistics.response_needed
                                  ? item.sectors.logistics.response_needed
                                  : "N/A"
                                : "N/A"
                            }}
                          </li>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
                <!-- end Logisticts -->
                <!--  Environment -->
                <tr
                  v-if="item.sectors.environment"
                  style="margin:0;padding:0 !important;border:0px !important;"
                >
                  <td colspan="15" style="margin:0;padding:0 !important;border:0px">
                    <table
                      width="100%"
                      style="margin:0;padding:0 !important;border:0px !important;"
                    >
                      <tr>
                        <td
                          :rowspan="
                            3 +
                              (item.sectors.environment.impact_on_environment
                                ? item.sectors.environment.impact_on_environment.length
                                : 0)
                          "
                          width="10%"
                          class="clearfix"
                        >
                          <span class="rotated" style="font-weight: bold;"
                            ></span
                          >
                          <img
                            src="../../../../static/other_cluster_environment_100px_bluebox.png"
                            height="150"
                          />
                        </td>
                        <td
                          v-if="item.sectors.environment.impact_on_environment"
                          :rowspan="
                            1 + item.sectors.environment.impact_on_environment.length
                          "
                          colspan="3"
                          style="width:20%"
                        >
                          <strong>Impact on structures</strong>
                        </td>
                        <td
                          v-if="item.sectors.environment.impact_on_environment"
                          colspan="9"
                          style="width:60%"
                        >
                          <b>Question</b>
                        </td>
                        <td
                          v-if="item.sectors.environment.impact_on_environment"
                          colspan="2"
                          width="10%"
                        >
                          <b>Response</b>
                        </td>
                      </tr>
                      <tr
                        v-if="item.sectors.environment.impact_on_environment"
                        v-for="row in item.sectors.environment.impact_on_environment"
                      >
                        <td colspan="9" style="width:60%">
                          {{ row.impact_question }}
                        </td>
                        <td colspan="2" width="10%">{{ row.response }}</td>
                      </tr>
                      <tr>
                        <td colspan="3" style="width:20%">
                          <strong>Response Needed</strong>
                        </td>
                        <td colspan="11">
                          <b style="padding-bottom:20px">
                            <u>
                              <strong>URGENT</strong>
                            </u>
                          </b>
                          <br />
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.environment
                                ? item.sectors.environment.urgent_response_needed
                                  ? item.sectors.environment.urgent_response_needed
                                  : "N/A"
                                : "N/A"
                            }}
                          </li>
                          <hr style="margin:1%;" />
                          <b style="padding-bottom:20px">
                            <u>
                              <strong>GENERAL</strong>
                            </u>
                          </b>
                          <br />
                          <li style="margin-left:2%" class="qcont">
                            {{
                              item.sectors.environment
                                ? item.sectors.environment.response_needed
                                  ? item.sectors.environment.response_needed
                                  : "N/A"
                                : "N/A"
                            }}
                          </li>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
                <!-- end Environment -->
              </table>

              <table
              class="col-md-11"
              v-if="numberOfTAs > 0"
              width="100%"
              style="text-weight: bold; text-size: 120%"
            >
              <tr>
                <td width="50%">
                  <strong>
                    Checked and Verified by
                    <span v-if="this.officerSignature">
                      {{ this.officerSignature.role }}
                    </span>
                  </strong>
                </td>
                <td>
                  <strong>
                    Approved by
                    <span v-if="this.dcSignature">
                      {{ this.dcSignature.role }}
                    </span>
                  </strong>
                </td>
              </tr>
              <tr>
                <td width="50%">
                  <strong>Name :</strong>
                  <span v-if="this.officerSignature">
                    {{
                      (this.officerSignature.signatory_fname
                        ? this.officerSignature.signatory_fname
                        : "") +
                        " " +
                        (this.officerSignature.signatory_lname
                          ? this.officerSignature.signatory_lname
                          : "")
                    }}
                  </span>
                </td>
                <td width="50%">
                  <strong>Name :</strong>
                  <span v-if="this.dcSignature">
                    {{
                      (this.dcSignature.signatory_fname
                        ? this.dcSignature.signatory_fname
                        : "") +
                        " " +
                        (this.dcSignature.signatory_lname
                          ? this.dcSignature.signatory_lname
                          : "")
                    }}
                  </span>
                </td>
              </tr>
              <tr>
                <td width="50%">
                  <strong>Signature :</strong>
                  <img
                    v-if="this.officerSignature && officerpath"
                    :src="officerpath"
                    style="box-shadow: 0 0 2px 2px white inset"
                    class="mx-2"
                    width="100"
                    height="30"
                  />
                </td>
                <td width="50%">
                  <strong>Signature :</strong>
                  <img
                    v-if="this.dcSignature && dcpath"
                    :src="dcpath"
                    style="box-shadow: 0 0 2px 2px white inset"
                    class="mx-2"
                    width="100"
                    height="30"
                  />
                </td>
              </tr>

              <tr>
                <td width="50%">
                  <strong>Date :</strong>
                  <span v-if="this.officerSignature">
                    {{ formatdate(this.officerSignature.createdon) }}
                  </span>
                </td>
                <td width="50%">
                  <strong>Date :</strong>
                  <span v-if="this.dcSignature">
                    {{
                      dinrFormsData.createdon
                        ? formatdate(dinrFormsData.createdon)
                        : ""
                    }}
                  </span>
                </td>
              </tr>
            </table>

            <br />
            <table
              class="col-md-11"
              v-if="hasPictures"
              width="100%"
              style="text-weight: bold; text-size: 120%"
            >
              <tr>
                <td style="text-align: center">
                  <strong>Disaster Photos</strong>
                </td>
              </tr>
              <tr>
                <td colspan="2">
                  <div class="row">
                    <div class="col-md-12">
                      <div class="row" style="">
                        <div class="row">
                          <!--images  -->

                          <div
                            class="image-wrapper col-md-4"
                            v-for="(image, i) in dinrFormsData.disasterImages"
                            :key="i"
                          >
                            <div class="inner">
                              <img
                                :src="
                                  disasterImage(
                                    dinrFormsData._id,
                                    image.filename
                                  )
                                "
                                style="margin: 8px; height: 340px; width: 340px"
                                class="img-responsive img-thumbnail"
                              />
                            </div>
                            <div class="caption" style="margin-left: 12px">
                              {{ image.caption }}
                            </div>
                          </div>
                          <!-- endImage -->
                        </div>
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
            </table>

            <br />
            <br />
            <br />
            <p v-if="numberOfTAs <= 0">
              <center>
                <h3>
                  <strong>NO DATA</strong>
                </h3>
              </center>
            </p>
          </div>
        </div>
      </div>
      </card>
    </div>
  </div>
</template>

<script>
import downloadexcel from "vue-json-excel";
import moment from "moment";
var QRCode = require("qrcode");
import { dinrforms } from '../../districtmanager/api/forms/dinrforms.js'
import EventBus from "../../../helpers/event-bus"

import dateformat from "dateformat";
import { mapGetters, mapActions } from "vuex";
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import html2pdf from "html2pdf.js"

//var domToPdf = require('dom-to-pdf');

export default {
  props: ["data"],
  components: {
    downloadexcel,
  },

  data() {
    return {
      signatureText: "",
      signature: {},
      dcSignature: {},
      officerSignature: {},
      officerPath: "",
      dcPath: "",
      numberOfTAs: "",
      dinrFormsData: {},
      villagesArray: [],
      campsArray: [],
      bufferDraFormsData: [],
      TAarray: [],
      draFormsData: [],
      download: [],
      downloadData: [],
      otherData: [],
      vhhousesvalue: "",
      filters: {
        Disaster: "all.dinrform.disaster",
        Country: "all.dinrform.district.admin0_name_en",
        "Country PCODE": "all.dinrform.district.admin0_pcode",
        District: "all.dinrform.district.admin2_name_en",
        Region: "all.dinrform.district.admin1_name_en",
        "TA PCODE": "all.admin3.admin3Pcode",
        TA: "all.admin3.admin3Name_en",
        "GVHS affected": "all.gvhsAffected",

        // "GVH affected": "gvhs.name",

        "Date of assessment ADRMC": "all.dinrform.doaAcpc",
        "Date of assessment DDRMC": "all.dinrform.doaDcpc",
        "Date of Disaster from": "all.dinrform.dodFrom",
        "Date of Disaster to": "all.dinrform.dodTo",
        "Date of submission": "all.dinrform.submited",

        //shelter

        "Houses partly damaged (shelter)": "all.sectors.shelter.partly_damaged",
        "Houses underwater (shelter)": "all.sectors.shelter.under_water",
        "Houses completely (shelter)": "all.sectors.shelter.completely_damaged",

        "number of males without shelter(shelter)":
          "all.shelter_without_shelter_male",
        "number of females without shelter (shelter)":
          "all.shelter_without_shelter_female",

        "number of female HH affected (shelter)": "all.shelter_fhh_affected",
        "number of male HH affected(shelter)": "all.shelter_mhh_affected",

        "Total Households affected (shelter)": "all.shelter_affected_hh",

        "number of injured females(shelter)":
          "all.shelter_people_injured_female",
        "number of injured males in category (shelter)":
          "all.shelter_people_injured_female",

        "Total people injured (shelter)": "all.shelter_injured",

        "number of dead females (shelter)": "all.shelter_people_dead_female",
        "number of dead males (shelter)": "all.shelter_people_dead_male",

        "Total people dead (shelter)": "all.shelter_dead",

        "Urgent needed (shelter)": "all.sectors.shelter.urgent_response_needed",
        "General response needed (shelter)":
          "all.sectors.shelter.response_needed",

        //displaced

        "number of displaced male Households (displaced)":
          "all.PeopleAffectedrows_male",
        "number of displaced females Households (displaced)":
          "all.PeopleAffectedrows_female",

        "Total displaced Households (shelter)": "all.displaced_hh",

        "number of male HH accomodated (displaced)":
          "all.displaced_households_accommodated_male",
        "number of female HH accomodated (displaced)":
          "all.displaced_households_accommodated_female",

        "number of males disaggregated (displaced)":
          "all.displaced_disagregated_male",
        "number of females disaggregated (displaced)":
          "all.displaced_disagregated_female",

        "number of males accomodated (displaced)":
          "all.displaced_individuals_accommodated_male",
        "number of females accomodated (displaced)":
          "all.displaced_individuals_accommodated_female",

        "Urgent response needed (displaced)":
          "all.sectors.displaced.urgent_response_needed",
        "general response needed (displaced)":
          "all.sectors.displaced.response_needed",

        //wash

        "FHH with safe water (wash)": "all.sectors.wash.with_safe_water_fhh",
        "MHH with safe water (wash)": "all.sectors.wash.with_safe_water_mhh",
        "FHH with toilet access (wash)":
          "all.sectors.wash.access_to_toilets_fhh",
        "MHH with toilet access (wash)":
          "all.sectors.wash.access_to_toilets_mhh",
        "FHH risking contamination (wash)":
          "all.sectors.wash.risk_contamination_fhh",
        "MHH risking contamination  (wash)":
          "all.sectors.wash.risk_contamination_mhh",

        "Urgent response needed (wash)":
          "all.sectors.wash.urgent_response_needed",

        "general response needed (wash)": "all.sectors.wash.response_needed",

        //health

        "number of facilities partially functioning (health)":
          "all.health_partially_functioning",
        "number of  facilities on verge of closing (health)":
          "all.health_verge_of_closing",
        "number  facilities closed (health)": "all.health_closed",

        "state of medical supply availability (health)":
          "all.medical_supply_availability",

        "state of health personnel availability (health)":
          "all.health_personel_availability",
        "Urgent response needed (health)":
          "all.sectors.health.urgent_response_needed",

        "general response needed (health)":
          "all.sectors.health.response_needed",

        //food

        "is food available? (food)": "all.is_food_available_food",

        "No. of FHH with 1-2 month Food Availability (food)":
          "all.food_1_2_months",
        "No. of FHH with less than 1 month Food Availability (food)":
          "all.food_less_1_month",

        "No. of FHH with more than 2 months Food Availability (food)":
          "all.food_2_months",

        "No. of FHH who lost Food Stock (food)": "all.food_stock_lost",

        "No. of MHH with 1-2 month Food Availability (food)":
          "all.food_1_2_months_male",
        "No. of MHH with less than 1 month Food Availability (food)":
          "all.food_less_1_month_male",

        "No. of MHH with more than 2 months Food Availability (food)":
          "all.food_2_months_male",

        "No. of MHH who lost Food Stock (food)": "all.food_stock_lost_male",

        "Urgent response needed (food)":
          "all.sectors.food.urgent_response_needed",
        "general response needed (food)": "all.sectors.food.response_needed",

        //logistics

        "state of access to main roads (logistics)": "all.road_access",

        "Urgent response needed (logistics)":
          "all.sectors.logistics.urgent_response_needed",

        "general response needed (logistics)":
          "all.sectors.logistics.response_needed",

        //agriculture
        "food items damaged (KGs) (agriculture)": "all.food_item_damage",
        "number of crop hectares submerged  (agriculture)":
          "all.hectares_submerged",
        "number crop hectares washed off  (agriculture)":
          "all.hectares_washed_away",
        "number of households whose crops are impacted (agriculture)":
          "all.impact_on_crops_hh_affected",
        "number of crop hectares damaged in affected households  (agriculture)":
          "all.impact_on_crops_hectares_damaged",

        "hh affected per impacted livestock  (agriculture)":
          "all.impact_on_livestock_hh",
        "number of impacted livestock  (agriculture)":
          "all.impact_on_livestock_la",

        "Urgent response needed (agriculture)":
          "all.sectors.agriculture.response_needed",

        "general response needed (agriculture)":
          "all.sectors.agriculture.response_needed",

        //protection

        "impacted females (protection)":
          "all.impact_on_vulnerable_persons_females",

        "impacted males (protection)": "all.impact_on_vulnerable_persons_males",

        "Urgent response needed (protection)":
          "all.sectors.protection.urgent_response_needed",

        "general response needed (protection)":
          "all.sectors.protection.response_needed",

        //nutrition

        "number of affected males (nutrition)":
          "all.nutrition_affected_pop_male",
        "number of affected females (nutrition)":
          "all.nutrition_affected_pop_female",

        "general response needed (nutrition)":
          "all.sectors.nutrition.response_needed",

        //education

        "number of school buildings functioning (education)":
          "all.education_building_functioning",
        "number of school buildings underwater (education)":
          "all.education_underwater",
        "number of school buildings completely damaged (education)":
          "all.education_completely_damaged",

        "number of school buildings partially functioning (education)":
          "all.education_building_partly_functioning",

        "number of school buildings closed  (education)":
          "all.education_closed_buildings",

        "males of out school (education)": "all.education_males_out_of_school",
        "females of out school (education)":
          "all.education_females_out_of_school",

        //livelihoods

        "number severely affected (livelihoods)":
          "all.livelihoods_severely_affected",

        "number slightly affected (livelihoods)":
          "all.livelihoods_slightly_affected",

        "Urgent response needed (livelihoods)":
          "all.sectors.livelihoods.response_needed",

        "Urgent response needed (environment)":
          "all.sectors.environment.urgent_response_needed",

        "general response needed (livelihoods)":
          "all.sectors.livelihoods.response_needed",

        "general response needed (environment)":
          "all.sectors.environment.response_needed",
      },
    };
  },

  computeLogo() {
    return "../../../../static/logo.png";
  },

  updated() {
    delete this.signatureText.signature;
    delete this.signatureText.status;
    var opts = {
      errorCorrectionLevel: "H",
      type: "image/jpeg",
      quality: 1,
      margin: 1,
    };

    QRCode.toDataURL(
      JSON.stringify(this.signatureText || "not electronically signed by DC"),
      opts,
      function (err, url) {
        if (err) throw err;

        var img = document.getElementById("qr-code");
        img.src = url;
      }
    );
  },

  computed: {
    ...mapGetters({
      getDinrById: "dinrs/getById",
    }),
    ...mapGetters({
      getDraById: "dras/getById",
    }),
    officerpath: function () {
      if (this.officerSignature && this.officerSignature.signature)
        return (
          process.env.VUE_APP_ENGINE_URL +
          "/" +
          this.officerSignature.signature.replace("\\", "/")
        );
    },
    dcpath: function () {
      if (this.dcSignature && this.dcSignature.signature)
        return (
          process.env.VUE_APP_ENGINE_URL +
          "/" +
          this.dcSignature.signature.replace("\\", "/")
        );
    },
    hasPictures: function() {
      if ('disasterImages' in this.dinrFormsData) {
        return this.dinrFormsData.disasterImages.length > 0
      }
      return false
    }
  },

  async mounted() {
    await this.getDinrsAction();
    await this.getDrasAction();

    EventBus.$on("printPDF",async (self)=>{
      var element = document.getElementById('section-to-print');
      var disasterdistrict = document.getElementById('district').innerHTML;
      var disastertype = document.getElementById('disastertype').innerHTML;
      var disasterdate = document.getElementById('disasterstart').innerHTML;

  
      var opt = {
            margin: 0,
            filename: disasterdate + "_" + disasterdistrict + "_"+ disastertype,
            pagebreak: { mode: ['avoid-all', 'css', 'img'] },
            image: { type: 'jpeg', quality: 1 },
            html2canvas: { scale: 2,useCORS: true,allowTaint:true },
            jsPDF: { unit: 'in', format: 'a4', orientation: 'p' }
        };

      html2pdf().set(opt).from(element).save();
      self.modalShow=false;
    });

    //Get DINR Form
    let response = this.getDinrById(
      this.data /* , this.$session.get("jwt") */
      // this.$route.params.uuid /* , this.$session.get("jwt") */
    );

    this.dinrFormsData = response;
    this.signatureText = this.dinrFormsData.approvalMetadata
      ? [...this.dinrFormsData.approvalMetadata.signature]
      : "";
    this.signature = this.dinrFormsData.approvalMetadata
      ? [...this.dinrFormsData.approvalMetadata.signature]
      : [];

    this.dcSignature = this.signature.filter(
      (item) => item && item.role === "district commissioner"
    )[0];
    this.officerSignature = this.signature.filter(
      (item) => item && item.role !== "district commissioner"
    )[0];

    //console.log(this.$route);

    //initialise number TAs in DNIR
    this.numberOfTAs = 0;

    let count = 0;
    //Get DRA Form

    response = this.getDraById(
      this.data /* , this.$session.get("jwt") */
      // this.$route.params.uuid /* , this.$session.get("jwt") */
    );
    this.bufferDraFormsData = response;
    this.draFormsData = [];
    //console.log(bufferDraFormsData);
    this.downloadData.dinrform = {};

    this.bufferDraFormsData.forEach((item) => {
      this.processExcel(item);

      this.TAarray.push(item.admin3.admin3Name_en.trim());
      for (let i in item.villages) {
        this.villagesArray.push(item.villages[i].name);
      }
      for (let i in item.camps) {
        this.campsArray.push(item.camps[i].name);
      }

      this.TAarray = this.TAarray.sort();

      this.villagesArray = this.villagesArray
        .join()
        .replace(/ /g, "")
        .split(",")
        .sort()
        .filter(function (item, pos, self) {
          return self.indexOf(item) == pos;
        });
      this.campsArray = this.campsArray
        .join()
        .replace(/ /g, "")
        .split(",")
        .sort()
        .filter(function (item, pos, self) {
          return self.indexOf(item) == pos;
        });
    });
  },

  methods: {
     setShadowStyle(el, shadowStyle) {
        el.style.boxShadow = shadowStyle;
      },
     disasterImage(disasterId, filename) {
      const resource = process.env.VUE_APP_ENGINE_URL + '/forms/dinr'
     
      return `${resource}/${disasterId}/images/${filename}`
    },

    ...mapActions("dinrs", {
      getDinrsAction: "get",
    }),
    ...mapActions("dras", {
      getDrasAction: "get",
    }),
    formatedDatePlusOne(data) {
      const cur_date = this.addDays(data, 1);
      const finalDate = dateformat(cur_date, "dd-mm-yyyy");
      return finalDate;
    },

    addDays(date, days) {
      var result = new Date(date);
      result.setDate(result.getDate() + days);
      return result;
    },
    numberWithCommas(number) {
      try {
        var parts = number.toString().split(".");
        parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        return parts.join(".");
      } catch (e) {}
    },
    comparator(key, order = "asc") {
      return function innerSort(a, b) {
        if (!a.hasOwnProperty(key) || !b.hasOwnProperty(key)) {
          return 0;
        }

        const varA = typeof a[key] === "string" ? a[key].toUpperCase() : a[key];
        const varB = typeof b[key] === "string" ? b[key].toUpperCase() : b[key];

        let comparison = 0;
        if (varA > varB) {
          comparison = 1;
        } else if (varA < varB) {
          comparison = -1;
        }
        return order === "desc" ? comparison * -1 : comparison;
      };
    },

    sortArrayByKey(arrayName) {
      return arrayName.slice().sort(this.comparator("name", "asc"));
    },
downloadPDF(){
  var element = document.getElementById('section-to-print');
      var disasterdistrict = document.getElementById('district').innerHTML;
      var disastertype = document.getElementById('disastertype').innerHTML;
      var disasterdate = document.getElementById('disasterstart').innerHTML;

  
      var opt = {
            margin: 0,
            filename: disasterdate + "_" + disasterdistrict + "_"+ disastertype,
            pagebreak: { mode: ['avoid-all', 'css', 'img'] },
            image: { type: 'jpeg', quality: 1 },
            html2canvas: { scale: 2,useCORS: true,allowTaint:true },
            jsPDF: { unit: 'in', format: 'a4', orientation: 'p' }
        };

      html2pdf().set(opt).from(element).save();
      self.modalShow=false;
},
printdiv(printpage) {

            var headstr = "<html><head><title></title></head><body>";
            var footstr = "</body>";
            var newstr = document.all.item(printpage).innerHTML;
            var oldstr = document.body.innerHTML;
            var disasterdistrict = document.getElementById('district').innerHTML;
            var disastertype = document.getElementById('disastertype').innerHTML;
            var disasterdate = document.getElementById('disasterstart').innerHTML;

            document.body.innerHTML = headstr + newstr + footstr;
            document.title = disasterdate + "_" + disasterdistrict + "_"+ disastertype;
            window.print();
            document.body.innerHTML = oldstr;
            location.reload();
            return false;
},
    dateFormate(dateData) {
      const cur_date = dateData;
      const formattedDate = moment(cur_date).format("DD/MM/YYYY");

      return formattedDate;
    },
    formatdate(data) {
      const cur_date = data;
      const formattedDate = moment(cur_date).format("DD/MM/YYYY hh:mm");

      return formattedDate;
    },

    formatedDate(data) {
      const cur_date = data;
      const formDate = moment(cur_date).format("YYYY/MM/DD");

      return formDate;
    },

    sumArrayValues(item, sector, array, key) {
      if (typeof item["sectors"][sector][array] !== "undefined") {
        return item["sectors"][sector][array]
          .map(function (item) {
            return parseInt(item[key]) ? parseInt(item[key]) : 0;
          })
          .reduce((sum, value) => sum + value, 0);
      } else if (typeof item[sector][array] === "undefined") {
        return 0;
      }
    },

    returnFieldvalue(item, sector, array, key) {
      if (typeof item["sectors"][sector][array] !== "undefined") {
        return item["sectors"][sector][array][0][key];
      } else if (typeof item["sectors"][sector][array] === "undefined") {
        return "NULL";
      }
    },

    returnFieldItemvalue(item, sector, array, key) {
      if (typeof item["sectors"][sector][array] !== "undefined") {
        for (let i in item["sectors"][sector][array]) {
          if (item["sectors"][sector][array][i].name === key) {
            return item["sectors"][sector][array][i].status;
          } else if (
            typeof item["sectors"][sector][array][i].key === "undefined"
          ) {
            return "NULL";
          }
        }
      }
    },

    sumArrayValuesNoAggregate(item, sector, array, key, filterBy, filterValue) {
      if (typeof item["sectors"][sector][array] !== "undefined") {
        return item["sectors"][sector][array]
          .filter((item) => item[filterBy] === filterValue)
          .map(function (item) {
            return parseInt(item[key]) ? parseInt(item[key]) : 0;
          })
          .reduce((sum, value) => sum + value, 0);
      } else if (typeof item[sector][array] === "undefined") {
        return 0;
      }
    },

    processExcel(item) {
      this.downloadData.all = item;

      this.draFormsData.push(item);

      for (let i = 0; i < this.draFormsData.length; i++) {
        for (let a = 0; a < this.draFormsData[i].villages.length; a++) {
          this.villagesArray.push(this.draFormsData[i].villages[a].name);
        }

        for (let a = 0; a < this.draFormsData[i].camps.length; a++) {
          this.campsArray.push(this.draFormsData[i].camps[a].name);
        }
      }

      this.numberOfTAs++;

      let Gvharray = [];

      this.downloadData.dinrform = this.dinrFormsData;

      for (let i = 0; i < item.gvhs.length; i++) {
        let GVHname = item.gvhs[i].name;

        Gvharray.push(GVHname);
      }

      this.downloadData.all.dinrform = this.downloadData.dinrform;

      try {
        this.downloadData.all.dinrform.doaAcpc = this.formatedDate(
          this.downloadData.dinrform.doaAcpc
        );
      } catch (error) {
        this.downloadData.all.dinrform.doaAcpc = "NULL";
      }

      try {
        this.downloadData.all.dinrform.submited = this.formatedDatePlusOne(
          this.downloadData.all.dinrform.submitted_at
        );
      } catch (error) {
        this.downloadData.all.dinrform.submited = "";
      }

      try {
        this.downloadData.all.dinrform.doaDcpc = this.formatedDate(
          this.downloadData.dinrform.doaDcpc
        );
      } catch (error) {
        this.downloadData.all.dinrform.doaDcpc = "NULL";
      }

      try {
        this.downloadData.all.dinrform.dodFrom = this.formatedDate(
          this.downloadData.dinrform.dodFrom
        );
      } catch (error) {
        this.downloadData.all.dinrform.dodFrom = "NULL";
      }

      try {
        this.downloadData.all.dinrform.dodTo = this.formatedDate(
          this.downloadData.dinrform.dodTo
        );
      } catch (error) {
        this.downloadData.all.dinrform.dodTo = "NULL";
      }

      try {
        this.downloadData.all.gvhsAffected = Gvharray.join();
      } catch (error) {
        this.downloadData.all.gvhsAffected = "NULL";
      }

      try {
        this.downloadData.all.is_food_available_food = this.returnFieldvalue(
          item,
          "food",
          "food_availability",
          "foodavailable"
        );
      } catch (error) {
        this.downloadData.all.is_food_available_food = "NULL";
      }

      try {
        this.downloadData.all.medical_supply_availability = this.returnFieldItemvalue(
          item,
          "health",
          "available_health_medical",
          "Medical supply"
        );
      } catch (error) {
        this.downloadData.all.medical_supply_availability = "NULL";
      }

      try {
        this.downloadData.all.health_personel_availability = this.returnFieldItemvalue(
          item,
          "health",
          "available_health_medical",
          "Health personel"
        );
      } catch (error) {
        this.downloadData.all.health_personel_availability = "NULL";
      }

      try {
        this.downloadData.all.road_access =
          item.sectors.logistics.access_of_structures[0].accessibility;
      } catch (error) {
        this.downloadData.all.road_access = "NULL";
      }

      try {
        this.downloadData.all.food_1_2_months = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "female_HH",
          "category",
          "No. of HH with 1-2 month Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_1_2_months = 0;
      }

      try {
        this.downloadData.all.food_2_months = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "female_HH",
          "category",
          "No. of HH with more than 2 months Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_2_months = 0;
      }

      try {
        this.downloadData.all.food_stock_lost = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "female_HH",
          "category",
          "No. of HH who lost Food Stock"
        );
      } catch (error) {
        this.downloadData.all.food_stock_lost = 0;
      }

      try {
        this.downloadData.all.food_less_1_month = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "female_HH",
          "category",
          "No. of HH with less than 1 month Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_less_1_month = 0;
      }

      try {
        this.downloadData.all.food_less_1_month_male = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "male_HH",
          "category",
          "No. of HH with less than 1 month Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_less_1_month_male = 0;
      }

      try {
        this.downloadData.all.food_1_2_months_male = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "male_HH",
          "category",
          "No. of HH with 1-2 month Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_1_2_months_male = 0;
      }

      try {
        this.downloadData.all.food_2_months_male = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "male_HH",
          "category",
          "No. of HH with more than 2 months Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_2_months_male = 0;
      }

      try {
        this.downloadData.all.food_stock_lost_male = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "male_HH",
          "category",
          "No. of HH who lost Food Stock"
        );
      } catch (error) {
        this.downloadData.all.food_stock_lost_male = 0;
      }

      try {
        this.downloadData.all.food_item_damage = this.sumArrayValues(
          item,
          "agriculture",
          "food_item_damage",
          "number_of_kilos"
        );
      } catch (error) {
        this.downloadData.all.food_item_damage = 0;
      }
      try {
        this.downloadData.all.PeopleAffectedrows_female = this.sumArrayValues(
          item,
          "displaced",
          "PeopleAffectedrows",
          "number_displaced_by_gender_fhh"
        );
      } catch (error) {
        this.downloadData.all.PeopleAffectedrows_female = 0;
      }

      try {
        this.downloadData.all.PeopleAffectedrows_male = this.sumArrayValues(
          item,
          "displaced",
          "PeopleAffectedrows",
          "number_displaced_by_gender_mhh"
        );
      } catch (error) {
        this.downloadData.all.PeopleAffectedrows_male = 0;
      }

      try {
        this.downloadData.all.hectares_submerged = this.sumArrayValues(
          item,
          "agriculture",
          "crops_damaged",
          "hectares_submerged"
        );
      } catch (error) {
        this.downloadData.all.hectares_submerged = 0;
      }

      try {
        this.downloadData.all.hectares_washed_away = this.sumArrayValues(
          item,
          "agriculture",
          "crops_damaged",
          "hectares_washed_away"
        );
      } catch (error) {
        this.downloadData.all.hectares_washed_away = 0;
      }

      try {
        this.downloadData.all.impact_on_crops_hh_affected = this.sumArrayValues(
          item,
          "agriculture",
          "impact_on_crops",
          "hh_affected"
        );
      } catch (error) {
        this.downloadData.all.impact_on_crops_hh_affected = 0;
      }

      try {
        this.downloadData.all.impact_on_crops_hectares_damaged = this.sumArrayValues(
          item,
          "agriculture",
          "impact_on_crops",
          "hectares_damaged"
        );
      } catch (error) {
        this.downloadData.all.impact_on_crops_hectares_damaged = 0;
      }

      try {
        this.downloadData.all.impact_on_livestock_hh = this.sumArrayValues(
          item,
          "agriculture",
          "impact_on_livestock",
          "hh_affected_l"
        );
      } catch (error) {
        this.downloadData.all.impact_on_livestock_hh = 0;
      }

      try {
        this.downloadData.all.impact_on_livestock_la = this.sumArrayValues(
          item,
          "agriculture",
          "impact_on_livestock",
          "livestock_affected"
        );
      } catch (error) {
        this.downloadData.all.impact_on_livestock_la = 0;
      }

      try {
        this.downloadData.all.displaced_households_accommodated_male = this.sumArrayValues(
          item,
          "displaced",
          "displaced_households_accommodated",
          "accomodated_males_hh"
        );
      } catch (error) {
        this.downloadData.all.displaced_households_accommodated_male = 0;
      }
      try {
        this.downloadData.all.displaced_households_accommodated_female = this.sumArrayValues(
          item,
          "displaced",
          "displaced_households_accommodated",
          "accomodated_females_hh"
        );
      } catch (error) {
        this.downloadData.all.displaced_households_accommodated_female = 0;
      }

      try {
        this.downloadData.all.displaced_disagregated_male = this.sumArrayValues(
          item,
          "displaced",
          "displaced_disaggregated",
          "displaced_males"
        );
      } catch (error) {
        this.downloadData.all.displaced_disagregated_male = 0;
      }

      try {
        this.downloadData.all.displaced_disagregated_female = this.sumArrayValues(
          item,
          "displaced",
          "displaced_disaggregated",
          "displaced_females"
        );
      } catch (error) {
        this.downloadData.all.displaced_disagregated_female = 0;
      }

      try {
        this.downloadData.all.displaced_individuals_accommodated_male = this.sumArrayValues(
          item,
          "displaced",
          "displaced_individuals_accommodated",
          "accomodated_males"
        );
      } catch (error) {
        this.downloadData.all.displaced_individuals_accommodated_male = 0;
      }

      try {
        this.downloadData.all.displaced_individuals_accommodated_female = this.sumArrayValues(
          item,
          "displaced",
          "displaced_individuals_accommodated",
          "accomodated_females"
        );
      } catch (error) {
        this.downloadData.all.displaced_individuals_accommodated_female = 0;
      }

      try {
        this.downloadData.all.education_building_functioning = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "functioning_buildings"
        );
      } catch (error) {
        this.downloadData.all.education_building_functioning = 0;
      }

      try {
        this.downloadData.all.education_building_partly_functioning = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "partially_functioning_buildings"
        );
      } catch (error) {
        this.downloadData.all.education_building_partly_functioning = 0;
      }

      try {
        this.downloadData.all.education_closed_buildings = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "closed_buildings"
        );
      } catch (error) {
        this.downloadData.all.education_closed_buildings = 0;
      }

      try {
        this.downloadData.all.education_females_out_of_school = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "females_out_of_school"
        );
      } catch (error) {
        this.downloadData.all.education_females_out_of_school = 0;
      }

      try {
        this.downloadData.all.education_males_out_of_school = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "males_out_of_school"
        );
      } catch (error) {
        this.downloadData.all.education_males_out_of_school = 0;
      }

      try {
        this.downloadData.all.education_underwater = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "underwater"
        );
      } catch (error) {
        this.downloadData.all.education_underwater = 0;
      }

      try {
        this.downloadData.all.education_completely_damaged = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "completely_damaged"
        );
      } catch (error) {
        this.downloadData.all.education_completely_damaged = 0;
      }

      try {
        this.downloadData.all.health_partially_functioning =
          this.sumArrayValues(
            item,
            "health",
            "available_health_facilities",
            "partially_functioning"
          ) +
          this.sumArrayValues(
            item,
            "health",
            "other_health_facilities",
            "partialy_functioning"
          );
      } catch (error) {
        this.downloadData.all.health_partially_functioning = 0;
      }

      try {
        this.downloadData.all.health_verge_of_closing =
          this.sumArrayValues(
            item,
            "health",
            "available_health_facilities",
            "verge_of_closing"
          ) +
          this.sumArrayValues(
            item,
            "health",
            "other_health_facilities",
            "verge_of_closing"
          );
      } catch (error) {
        this.downloadData.all.health_verge_of_closing = 0;
      }

      try {
        this.downloadData.all.health_closed =
          this.sumArrayValues(
            item,
            "health",
            "available_health_facilities",
            "closed"
          ) +
          this.sumArrayValues(
            item,
            "health",
            "other_health_facilities",
            "closed"
          );
      } catch (error) {
        this.downloadData.all.health_closed = 0;
      }

      try {
        this.downloadData.all.livelihoods_slightly_affected = this.sumArrayValues(
          item,
          "livelihoods",
          "livelihoods_affected",
          "severely_affected"
        );
      } catch (error) {
        this.downloadData.all.livelihoods_slightly_affected = 0;
      }
      try {
        this.downloadData.all.livelihoods_severely_affected = this.sumArrayValues(
          item,
          "livelihoods",
          "livelihoods_affected",
          "slightly_affected"
        );
      } catch (error) {
        this.downloadData.all.livelihoods_severely_affected = 0;
      }

      try {
        this.downloadData.all.impact_on_vulnerable_persons_males = this.sumArrayValues(
          item,
          "protection",
          "impact_on_vulnerable_persons",
          "impacted_males"
        );
      } catch (error) {
        this.downloadData.all.impact_on_vulnerable_persons_males = 0;
      }

      try {
        this.downloadData.all.impact_on_vulnerable_persons_females = this.sumArrayValues(
          item,
          "protection",
          "impact_on_vulnerable_persons",
          "impacted_females"
        );
      } catch (error) {
        this.downloadData.all.impact_on_vulnerable_persons_females = 0;
      }

      try {
        this.downloadData.all.nutrition_affected_pop_male = this.sumArrayValues(
          item,
          "nutrition",
          "affected_population",
          "affected_males"
        );
      } catch (error) {
        this.downloadData.all.nutrition_affected_pop_male = 0;
      }

      try {
        this.downloadData.all.nutrition_affected_pop_female = this.sumArrayValues(
          item,
          "nutrition",
          "affected_population",
          "affected_females"
        );
      } catch (error) {
        this.downloadData.all.nutrition_affected_pop_female = 0;
      }

      try {
        this.downloadData.all.shelter_without_shelter_male = this.sumArrayValues(
          item,
          "shelter",
          "people_without_shelter",
          "males"
        );
      } catch (error) {
        this.downloadData.all.shelter_without_shelter_male = 0;
      }

      try {
        this.downloadData.all.shelter_without_shelter_female = this.sumArrayValues(
          item,
          "shelter",
          "people_without_shelter",
          "females"
        );
      } catch (error) {
        this.downloadData.all.shelter_without_shelter_female = 0;
      }

      try {
        this.downloadData.all.shelter_people_dead_female = this.sumArrayValues(
          item,
          "shelter",
          "PeopleDeadrows",
          "females_dead"
        );
      } catch (error) {
        this.downloadData.all.shelter_people_dead_female = 0;
      }

      try {
        this.downloadData.all.shelter_people_dead_male = this.sumArrayValues(
          item,
          "shelter",
          "PeopleDeadrows",
          "males_dead"
        );
      } catch (error) {
        this.downloadData.all.shelter_people_dead_male = 0;
      }

      try {
        this.downloadData.all.shelter_people_injured_female = this.sumArrayValues(
          item,
          "shelter",
          "PeopleInjuredrows",
          "people_injured_females"
        );
      } catch (error) {
        this.downloadData.all.shelter_people_injured_female = 0;
      }

      try {
        this.downloadData.all.shelter_people_injured_male = this.sumArrayValues(
          item,
          "shelter",
          "PeopleInjuredrows",
          "people_injured_males"
        );
      } catch (error) {
        this.downloadData.all.shelter_people_injured_male = 0;
      }

      try {
        this.downloadData.all.shelter_fhh_affected = this.sumArrayValues(
          item,
          "shelter",
          "PeopleAffectedrows",
          "damaged_fhh"
        );
      } catch (error) {
        this.downloadData.all.shelter_fhh_affected = 0;
      }

      try {
        this.downloadData.all.shelter_mhh_affected = this.sumArrayValues(
          item,
          "shelter",
          "PeopleAffectedrows",
          "damaged_mhh"
        );
      } catch (error) {
        this.downloadData.all.shelter_mhh_affected = 0;
      }

      this.download.push({
        all: this.downloadData.all,
      });
    },
  },
};
</script>

<style scoped>
.modal-body .tags-input__wrapper{
   display: flex;
   flex-direction: row ;
   flex-wrap:  wrap;
}
.el-tag.el-tag--primary{
  position: relative !important;
  display: inline;
}
.image-previewer, .row {
  display: flex;
  flex-wrap: wrap;
  padding: 10px;
  justify-items: baseline;
}

.image-previewer .img, .row .img {
  height: 300px;
  border-radius: 10px;
  cursor: pointer;
}
.image-previewer  .image--wrapper {
  position: relative;
   margin: 10px;
}
.row  .image--wrapper {
  position: relative;
  margin: 10px;
  
}
.image-previewer .row .image--wrapper {
  margin: 10px;
}
.image--wrapper  .img--overlay {
  position: absolute;
  top: 0; 
  bottom: 0; 
  right: 0; 
  left: 0; 
  z-index: 10;
  background: rgba(0, 0, 0, .3);
  border-radius: 10px;
}
.image--wrapper .img--overlay .btn {
  background: rgb(238, 5, 5);
  width: 100%;
  position: absolute;
  bottom: 0;
}
.progress, .progress-bar {
  height: 30px;
  font-weight: bold;
}
.inner{
  overflow: hidden;
}
.inner img{
  transition: all 1.5s ease;
}
.inner:hover img{
  transform: scale(2);
   display: flex;
  flex-wrap: wrap;
}
table {
  border-collapse: collapse;
  width: 100%;
  margin: 0 auto;
}

table, th, td {
  border: 1px solid black;
  margin-top: 1%;
}

td {
  padding: 1%;
}

.rotated {
  writing-mode: tb-rl;
  transform: rotate(-180deg);
  color: teal;
  padding-left: 30px;
}

.noborder {
  border: none;
}

.vertical {
  writing-mode: vertical-rl;
}

.qcont:first-letter {
  text-transform: capitalize;
}

.right-align {
  text-align: right;
}

@media print {
  .section-not-to-print {
    visibility: hidden;
  }
  #section-to-print {
    visibility: visible;
  }
}
</style>
