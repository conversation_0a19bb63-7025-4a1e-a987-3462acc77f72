<template>
  <div class="content">
    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Lean Season</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/manager/dashboard">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">REPORTS</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">All</li>
            </ol>
          </nav>
        </div>
        <div class="col-lg-6 col-6 text-right pr-3">
          <base-dropdown title-classes="btn btn-sm btn-primary mr-0" menu-on-right :has-toggle="false">
            <a slot="title" class="dropdown-toggle" type="neutral" size="sm">
              <i class="text-black ni ni-cloud-download-95"></i> EXPORT TO EXCEL
            </a>
            <a class="dropdown-item" @click="downloadExcel()">Maize Distribution Excel</a>
            <a class="dropdown-item" @click="downloadExcelCashTrans()">Cash Transfer Excel</a>

          </base-dropdown>
          <!-- <base-button title-classes="btn btn-sm btn-primary mr-0" size="sm" type="primary" @click="downloadExcel()">
              <i class="text-black ni ni-cloud-download-95"></i>
              <span class="btn-inner--text">EXPORT TO EXCEL</span>
              <span class="btn-inner--text"></span>
            </base-button> -->
        </div>
        <div class="col-lg-6 col-5 text-right"></div>
      </div>

      <!-- Card stats -->
    </base-header>
    <div class="container-fluid mt--6">
      <div>
        <card class="no-border-card" body-classes="px-0 pb-1" footer-classes="pb-2">
          <template slot="header">
            <h3 class="mb-0">Lean Season Reports</h3>
          </template>

          <div>
            <div class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap">
              <el-select class="select-primary pagination-select" v-model="pagination.perPage" placeholder="Per page">
                <el-option class="select-primary" v-for="item in pagination.perPageOptions" :key="item" :label="item"
                  :value="item"></el-option>
              </el-select>

              <div>
                <base-input v-model="searchQuery" prepend-icon="fas fa-search" placeholder="Search..."></base-input>
              </div>
            </div>
            <div>
              <b-table responsive sticky-header bordered="true" outlined="true" sort-icon="true" fixed="true"
                :items="queriedData" :fields="tableColumns">
              </b-table>
            </div>
          </div>
          <div slot="footer" class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap">
            <div class>
              <p class="card-category">
                Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
                <span v-if="selectedRows.length">&nbsp; &nbsp; {{ selectedRows.length }} rows selected</span>
              </p>
            </div>
            <base-pagination class="pagination-no-border" v-model="pagination.currentPage" :per-page="pagination.perPage"
              :total="total"></base-pagination>
          </div>
        </card>
      </div>
    </div>
  </div>
</template>
<script>
import { Table, TableColumn, Select, Option } from 'element-ui'
import RouteBreadCrumb from '@/components/Breadcrumb/RouteBreadcrumb'
import { BasePagination } from '@/components'
import clientPaginationMixin from '@/components/PaginatedTables/clientPaginationMixin'
import { admin2s } from '../api/location/admin2s'
import { flatten } from '../../../store/flatdinrs/flatten'

import {
  generateExcel,
  generateTAGVHExcel,
  generateDisasterSummaryExcel
} from '../../../util/generateExcel'
import { generateCSV } from '../../../util/generateCSV'
import utils from '../../../util/dashboard'
import { MongoReports } from '../api/MongoReports'
import downloadexcel from 'vue-json-excel'
import JSZip from 'jszip'
import FileSaver from 'file-saver'
import moment from 'moment'
import dateformat from 'dateformat'
import { mapGetters, mapActions } from 'vuex'
import { leanseason } from '../../districtmanager/api/forms/leanseasonreports.js'
import { download } from '../../../util/download'

export default {
  mixins: [clientPaginationMixin],
  components: {
    BasePagination,
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    [Table.name]: Table,
    downloadexcel,
    [TableColumn.name]: TableColumn
  },

  data() {
    return {
      months: [
        { label: 'January', value: 0 },
        { label: 'February', value: 1 },
        { label: 'March', value: 2 },
        { label: 'April', value: 3 },
        { label: 'May', value: 4 },
        { label: 'June', value: 5 },
        { label: 'July', value: 6 },
        { label: 'August', value: 7 },
        { label: 'September', value: 8 },
        { label: 'October', value: 9 },
        { label: 'November', value: 10 },
        { label: 'December', value: 11 },
      ],
      propsToSearch: ['disaster', 'district', 'date'],
      filter: '',
      dateranges: ['24hrs', '48hrs', '1 Week', '2 Weeks', 'Lifetime'],
      gridOptions: {},

      tableColumns: [
        {
          key: 'Council',
          label: 'Council',
          sortable: true
        },
        {
          key: 'population',
          label: 'Total Affected Population',
          sortable: true
        },

        {
          key: 'hh',
          label: 'Total Households',
          sortable: true
        },

        {
          key: 'maize_requirements',
          label: 'Maize requirements (Mt)',
          sortable: true
        },

        {
          key: 'tonnage_distributed',
          label: 'Tonnage distributed (Mt) ',
          sortable: true
        },

        {
          key: 'beneficiaries_remaining_hh',
          label: 'Household beneficiaries reached',
          sortable: true
        },




        {
          key: 'beneficiary_reached_period',
          label: 'Period in which beneficiaries are reached',
          sortable: true
        },


        {
          key: 'createdon',
          label: 'Submmited at ',
          sortable: true
        }
      ],
      tableData: [],
      selectedRows: [],
      TAarray: [],
      premreports: [],
      totalinjured: 0,
      totaldeaths: 0,
      totalmissing: 0
    }
  },

  computed: {
    filteredItems() {
      let counter = 0

      if (this.filter == '' || 'Lifetime') {
        counter = 240000000000
      }
      if (this.filter == '24hrs') {
        counter = 24
      }
      if (this.filter == '48hrs') {
        counter = 48
      }
      if (this.filter == '1 Week') {
        counter = 168
      }
      if (this.filter == '2 Weeks') {
        counter = 336
      }

      return this.tableData.filter(
        item => moment().diff(moment(item.dateof), 'hours') <= counter
      )
    }
  },

  created() {
    this.loadData()
  },
  methods: {
    async loadData() {
      const response = await leanseason.getReports();


      const leanseasonreports = response.map(item => {

        let monthLabel = (this.months.find(month => month.value === item.maize_distribution.month) || {}).label || "November";

        const row = {
          ...item,
          Council: item.council,
          id: item._id,
          hh: item.maize_distribution.hh,
          population: item.maize_distribution.population,
          maize_requirements: item.maize_distribution.maize_requirements,
          tonnage_distributed: item.maize_distribution.tonnage_distributed,
          beneficiaries_remaining_hh: item.maize_distribution.beneficiaries_remaining_hh,
          deficit_months: item.maize_distribution.deficit_months,
          tonnage_balance: item.maize_distribution.tonnage_balance,
          beneficiaries_reached: item.maize_distribution.beneficiaries_reached,
          beneficiary_reached_year: item.maize_distribution.year,
          beneficiary_reached_month: monthLabel,
          beneficiary_reached_week: "Week " + item.maize_distribution.week,
          beneficiary_reached_period: item.maize_distribution.year + " " + monthLabel + ", Week: " + item.maize_distribution.week,
          lessons_learnt: item.lessons_learnt.join(", "),
          challenges: item.challenges.join(", "),
          observations: item.observations.join(", "),
          recommendations: item.recommendations.join(", "),
          createdon: moment(new Date(item.createdon)).format('DD-MM-YYYY'),
          'Submited by': (item.user ? item.user.firstName + " " + item.user.lastName : 'Unknown User')
        };


        return row;
      });

      leanseasonreports.sort((a, b) => new Date(b.createdon) - new Date(a.createdon));

      const rows = [...leanseasonreports];
      this.tableData = rows;
    }
    ,


    downloadExcelCashTrans() {
      var monthsToCheck = ["November", "December", "January", "February", "March"];

      var data = this.tableData.map(item => {
        const cashTransfers = item.cash_transfers; // Assuming cash_transfers is an array

        const hhReachedForMonths = {};

        monthsToCheck.forEach(month => {
          // Filter cashTransfers to get all entries for the current month
          const monthTransfers = cashTransfers.filter(transfer => transfer.month === month);

          // Sum the parsed hh_reached values for the current month
          const hh_reached = monthTransfers.reduce((sum, transfer) => sum + parseInt(transfer.hh_reached || 0, 10), 0);

          // Store the sum for the current month
          hhReachedForMonths[month] = hh_reached;
        });

        return {
          id: item._id,
          Council: item.council,
          "Total Affected HHs": item.maize_distribution.hh,
          "Total Affected Population": item.maize_distribution.population,
          "Deficit Months": item.maize_distribution.deficit_months,
          'Submited by': (item.user ? item.user.firstName + " " + item.user.lastName : 'Unknown User'),
          ...hhReachedForMonths, // Include the parsed and summed values for each month
        };
      });

      generateTAGVHExcel(data);
    },



    downloadExcel(type) {
      // generate disaggregated excel sheet
      var data = this.tableData.map(item => {
        return {
          id: item._id,
          Council: item.council,

          "Total Affected HHs": item.maize_distribution.hh,
          "Total Affected Population": item.maize_distribution.population,
          "Maize requirements (Mt)": item.maize_distribution.maize_requirements,
          "Deficit Months": item.maize_distribution.deficit_months,
          "Actual Beneficiary HHs Reached this week": item.maize_distribution.beneficiaries_reached,
          "Beneficiary reached (Year)": item.beneficiary_reached_year,
          "Beneficiary reached (Month)": item.beneficiary_reached_month,
          "Beneficiary reached (Week)": item.beneficiary_reached_week,
          "Lessons Learnt": item.lessons_learnt,
          "Challenges": item.challenges,
          "Recommendations": item.recommendations,
          "Observations": item.observations,
          "HH beneficiaries remaining": item.maize_distribution.beneficiaries_remaining_hh,
        }
      })
      generateTAGVHExcel(data)
    }


  }
}
</script>

<style>
.no-border-card .card-footer {
  border-top: 0;
}
</style>
