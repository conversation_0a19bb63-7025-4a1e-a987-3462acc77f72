<template>
  <div id="mapId" ref="mapId" style="position:relative;width:300px;"></div>
</template>
<script>
require("topojson");
import json from "./malawi.topo.json";
import Datamap from "datamaps";
import * as d3 from "d3";

global.jQuery = require("jquery");
var $ = global.jQuery;
window.$ = $;

export default {
  name: "displaced-map",
  props: ["map"],
  data() {
    return { times: 0, districts };
  },
  methods: {
    passDistricts(district) {
      this.$emit("clicked", district);
    }
  },
  mounted() {
    var self = this;
    this.$nextTick(function() {
      var element = this.$refs.mapId;
      var map = new Datamap({
        element: element,
        projection: "equirectangular",
        scope: "mwi",
        geographyConfig: {
          highlightOnHover: true
        },
        setProjection: function(element) {
          var projection = d3
            .geoMercator()
            .scale(4000)
            .center([34.3015278, -13.2512161])
            .translate([element.offsetWidth / 2, element.offsetHeight / 2]);
          var path = d3.geoPath().projection(projection);
          return { path: path, projection: projection };
        },
        done: function(map) {
          map.svg
            .selectAll(".datamaps-subunit")
            .on("click", function(geography) {
              //console.log(self);
              self.passDistricts(geography.properties.name);
            });
        }
      });
    });
  }
};
</script>

<style>
#mapId {
  font-family: "Avenir", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  fill: rgb(59, 89, 153);
  height: 600px;
}
</style>
