<template>
  <div class="md-layout">
    

    <div class="md-layout-item md-size-66 mx-auto md-medium-size-100">
      <tabs
        :tab-name="['Description', 'Location', 'Legal Info', 'Help Center']"
        :tab-icon="['info', 'location_on', 'gavel', 'help_outline']"
        class="page-subcategories"
        nav-pills-icons
        plain
        color-button="warning"
      >
        <h3 class="title text-center" slot="header-title">
          Page Subcategories
        </h3>

        <!-- here you can add your content for tab-content -->
        <template slot="tab-pane-1">
          <md-card>
            <md-card-header>
              <h4 class="title">Description about product</h4>
              <p class="category">More information here</p>
            </md-card-header>

            <md-card-content>
              Collaboratively administrate empowered markets via plug-and-play
              networks. Dynamically procrastinate B2C users after installed base
              benefits. Dramatically visualize customer directed convergence
              without revolutionary ROI.
            </md-card-content>
          </md-card>
        </template>
        <template slot="tab-pane-2">
          <md-card>
            <md-card-header>
              <h4 class="title">Location of the product</h4>
              <p class="category">More information here</p>
            </md-card-header>

            <md-card-content>
              Efficiently unleash cross-media information without cross-media
              value. Quickly maximize timely deliverables for real-time schemas.
              Dramatically maintain clicks-and-mortar solutions without
              functional solutions.
            </md-card-content>
          </md-card>
        </template>
        <template slot="tab-pane-3">
          <md-card>
            <md-card-header>
              <h4 class="title">Legal info of the product</h4>
              <p class="category">More information here</p>
            </md-card-header>

            <md-card-content>
              Completely synergize resource taxing relationships via premier
              niche markets. Professionally cultivate one-to-one customer
              service with robust ideas. Dynamically innovate resource-leveling
              customer service for state of the art customer service.
            </md-card-content>
          </md-card>
        </template>
        <template slot="tab-pane-4">
          <md-card>
            <md-card-header>
              <h4 class="title">Help center</h4>
              <p class="category">More information here</p>
            </md-card-header>

            <md-card-content>
              Completely synergize resource taxing relationships via premier
              niche markets. Professionally cultivate one-to-one customer
              service with robust ideas. Dynamically innovate resource-leveling
              customer service for state of the art customer service.
            </md-card-content>
          </md-card>
        </template>
      </tabs>
    </div>
  </div>
</template>
<script>
import { Tabs } from "@/components";
import { Collapse } from "@/components";

export default {
  components: {
    Tabs,
    Collapse
  }
};
</script>
