<template>
  <div class="content">
    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Disaster</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/manager/dashboard">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">REPORTS</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">All</li>
            </ol>
          </nav>
        </div>
        <div class="col-lg-6 col-6 text-right pr-3">
          <!-- <router-link to="/manager/reports/excel" class="mr-2">
            <base-button size="sm" type="neutral">
              <i class="text-primary ni ni-archive-2"></i> ARCHIVES
            </base-button>
          </router-link> -->

          <base-dropdown title-classes="btn btn-sm btn-warning mr-0" menu-on-right :has-toggle="false">
            <a slot="title" class="dropdown-toggle" type="neutral" size="sm">
              <i class="text-black ni ni-cloud-download-95"></i> EXPORT TO EXCEL
            </a>
            <a class="dropdown-item" @click="generateColorCodeSheet('TA')">All TA-Based data</a>
            <a class="dropdown-item" @click="generateColorCodeSheet('GVH')">All GVH-based data</a>
            <a class="dropdown-item" @click="generateColorCodeSheet('EXCEL_data')">All Disaster Summary</a>
          </base-dropdown>

          <base-dropdown style="padding-left: 5px" title-classes="btn btn-sm btn-primary mr-0" menu-on-right
            :has-toggle="false">
            <a slot="title" class="dropdown-toggle" type="neutral" size="sm">
              <i class="text-black ni ni-cloud-download-95"></i> EXPORT TO CSV
            </a>
            <a class="dropdown-item" @click="generateColorCodeSheet('CSV_data')">All Disaster Summary</a>
          </base-dropdown>
        </div>
        <div class="col-lg-6 col-5 text-right"></div>
      </div>

      <!-- Card stats -->
    </base-header>
    <div class="container-fluid mt--6">
      <div>
        <card class="no-border-card" body-classes="px-0 pb-1" footer-classes="pb-2">
          <template slot="header">
            <h3 class="mb-0">Submitted Disaster Reports</h3>
          </template>
          <div>
            <div class="
                col-12
                d-flex
                justify-content-center justify-content-sm-between
                flex-wrap
              ">
              <el-select class="select-primary pagination-select" v-model="pagination.perPage" placeholder="Per page">
                <el-option class="select-primary" v-for="item in pagination.perPageOptions" :key="item" :label="item"
                  :value="item"></el-option>
              </el-select>

              <div>
                <base-input v-model="searchQuery" prepend-icon="fas fa-search" placeholder="Search..."></base-input>
              </div>
            </div>
            <div>
              <b-table responsive sticky-header :striped="striped" :bordered="bordered" :borderless="borderless"
                :outlined="outlined" :small="small" :hover="hover" :dark="dark" :sort-icon="true" :fixed="fixed"
                :foot-clone="footClone" :no-border-collapse="noCollapse" head-variant="light"
                :table-variant="tableVariant" :items="queriedData" :fields="tableColumns">
                <template #cell(actions)="row">
                  <b-button @click="review(row.item, row.index, $event.target)" class="edit bg-primary text-white "
                    type="primary" size="sm" icon>
                    <i class="ni ni-single-copy-04"></i> Detailed
                  </b-button>
                  <b-button @click="archiveReport(row.item)"
                    style="color: white; background: #ab6f00; border:#ab6f00;" class="edit text-white " type="primary"
                    size="sm" icon>
                    <i class="ni ni ni-badge
"></i>
                    ARCHIVE
                  </b-button>
                 
                </template>
              </b-table>
            </div>
          </div>
          <div slot="footer" class="
              col-12
              d-flex
              justify-content-center justify-content-sm-between
              flex-wrap
            ">
            <div class>
              <p class="card-category">
                Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
                <span v-if="selectedRows.length">&nbsp; &nbsp; {{ selectedRows.length }} rows selected</span>
              </p>
            </div>
            <base-pagination class="pagination-no-border" v-model="pagination.currentPage"
              :per-page="pagination.perPage" :total="total"></base-pagination>
          </div>
        </card>
      </div>
    </div>
  </div>
</template>
<script>
import { Table, TableColumn, Select, Option } from "element-ui";
import RouteBreadCrumb from "@/components/Breadcrumb/RouteBreadcrumb";
import { BasePagination } from "@/components";
import clientPaginationMixin from "@/components/PaginatedTables/clientPaginationMixin";
import { admin2s } from "../../api/location/admin2s";
import { flatten } from "./../../../../store/flatdinrs/flatten";

import { generateFlatSectorData, processGVHExcel, exportAllDisasterData  } from "../../../../util/generateFlatSectorData";
import {
  generateExcel,
  generateTAGVHExcel,
  generateDisasterSummaryExcel
} from "../../../../util/generateExcel";
import { generateCSV } from "../../../../util/generateCSV";
import utils from "../../../../util/dashboard";
// import { MongoReports } from "../../api/MongoReports";
import downloadexcel from "vue-json-excel";
import JSZip from "jszip";
import FileSaver from "file-saver";
import moment from "moment";
import dateformat from "dateformat";
import { mapGetters, mapActions } from "vuex";
import { dinrforms } from "../../../districtmanager/api/forms/dinrforms.js";
import { download } from "../../../../util/download";
import Swal from "sweetalert2";
import Fuse from "fuse.js";
export default {
  mixins: [clientPaginationMixin],
  components: {
    BasePagination,
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    [Table.name]: Table,
    downloadexcel,
    [TableColumn.name]: TableColumn
  },
  data() {
    return {
      propsToSearch: ["disaster", "district", "officer", "date"],
      searchQuery: "",
      dinrFormsData: {},
      villagesArray: [],
      campsArray: [],
      bufferDraFormsData: [],
      finalExcelData: [],
      TAarray: [],
      draFormsData: [],
      download: [],
      test: [],
      gvhDATA: [],
      allforms: [],
      districtsCopy: [],
      downloadData: [],
      testarray: [],
      TaData: [],
      admin2sData: [],
      AllDisasterSummaryExcel: [],
      disablebutton: false,
      dinrDataa: [],
      gridOptions: {},
      tableColumns: [
        {
          key: "id",
          label: "ID",
          sortable: true
        },
        {
          key: "disaster",
          label: "Disaster",
          sortable: true
        },
        {
          key: "district",
          label: "District",
          sortable: true
        },
        {
          key: "officer",
          label: "Officer",
          sortable: true
        },
        {
          key: "date",
          label: "Submmited on",
          sortable: true
        },
        {
          key: "actions",
          label: "Actions"
        }
      ],
      tableData: [],
      selectedRows: []
    };
  },
  computed: {
    ...mapGetters({
      getDinrs: "dinrs/get",
      getDraById: "dras/getById",
      getDinrById: "dinrs/getById"
    }),
    ...mapActions({
      loadFlatdinr: "loadFlatdinrs"
    })
  },

  methods: {
    async archiveReport(report) {
      const prompt = await Swal.fire({
        title: `Are you sure to archive this report?`,
        text: `You won't be able to revert this!`,
        type: "warning",
        showCancelButton: true,
        confirmButtonClass: "btn btn-success btn-fill",
        cancelButtonClass: "btn btn-danger btn-fill",
        confirmButtonText: "Yes, delete it!",
        buttonsStyling: false
      })

      if (prompt.value) {
        const response = await dinrforms.archiveReport(report.uuid)
        if (response) {
          this.tableData = this.tableData.filter(item => item.uuid !== report.uuid)
          Swal.fire({
                  title: "Archive",
                  text: `Report Archived successifully`,
                  type: "success",
                  confirmButtonClass: "md-button md-success btn-fill",
                  buttonsStyling: false
                })
        }
      }
    },

    ...mapActions("dinrs", {
      getDinrsAction: "get"
    }),
    ...mapActions("dras", {
      getDrasAction: "get"
    }),
    generateColorCodeSheet(type) {
      exportAllDisasterData(type, this.download, this.admin2sData, this.gvhDATA, this.allforms);
    },


    review(index) {
      let distid = index.uuid
      let disaster = this.dinrDataa.filter(dist => dist._id == distid)
      let dateFrom = disaster[0].dodFrom
      let dateReference = '2022/06/01'

      if (dateFrom > dateReference) {
        this.$router.push({
          path: "/admin/detailsreport/" + index.uuid
        });
      }
      else {
        this.$router.push({
          path: "/admin/detailsreportOld/" + index.uuid

        });
      }

    },
    addDays(date, days) {
      var result = new Date(date);
      result.setDate(result.getDate() + days);
      return result;
    },
    catchError(data = "") {
      if (typeof data === undefined) {
        return "";
      }

      return data;
    }
  },
  async mounted() {
    let dinrs = [...(await this.getDinrsAction())];
    let dras = [...(await this.getDrasAction())];

    let DINRData = await dinrs;
    let DRASData = await dras;

    this.dinrDataa = DINRData
    this.allforms = [...(await dinrs)];

    this.allforms.forEach((dinr, index) => {
      var draarr = [];
      DRASData.forEach((dra, point) => {
        if (dinr._id == dra.dinrFormId) {
          draarr.push({ ...dra });
        }
        dinr.dra = [...draarr];
      });
    });

    DINRData.forEach((dinr, index) => {
      DINRData.sort(function (a, b) {
        return new Date(b.createdon) - new Date(a.createdon);
      });

      let row = DINRData[index]._id;
      this.tableData.push({
        id: index + 1,
        disaster: DINRData[index].disaster,
        district: DINRData[index].district.admin2_name_en,
        date: this.formatedDate(DINRData[index].createdon),
        officer:
          DINRData[index].account.firstName +
          " " +
          DINRData[index].account.lastName,
        uuid: DINRData[index]._id,
        status: DINRData[index].status
      });

      let dinformDataset = {
        disaster: DINRData[index].disaster,
        district: DINRData[index].district.admin2_name_en,
        date: this.formatedDate(DINRData[index].createdon),
        doaAcpc: DINRData[index].doaAcpc,
        doaDcpc: DINRData[index].doaDcpc,
        dodFrom: DINRData[index].dodFrom,
        dodTo: DINRData[index].dodTo,
        officer:
          DINRData[index].account.firstName +
          " " +
          DINRData[index].account.lastName,
        uuid: DINRData[index]._id,
        country: "MALAWI",
        country_pcode: "MWI"
        //status:  DINRData[index].status
      };

      let dra = dras.filter(dra => dra.dinrFormId == row);

      this.draFormsData = [];

      this.downloadData.dinrform = {};

      dra.forEach((item, index, array) => {
        generateFlatSectorData(item, dinformDataset, this.downloadData, this.villagesArray, this.campsArray, this.download)
        //this.processExcel(item, dinformDataset);
      });
    });


    


    processGVHExcel(this.download, this.gvhDATA);

    admin2s.get().then(response => {
      //console.log(response.data);
      this.admin2sData = response.data;
    });
  }
};
</script>
<style>
.no-border-card .card-footer {
  border-top: 0;
}

button[disabled] {
  cursor: not-allowed;
}

</style>
