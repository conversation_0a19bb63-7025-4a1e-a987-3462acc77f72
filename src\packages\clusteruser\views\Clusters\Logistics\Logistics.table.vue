<template>
  <div class="content">
    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0 text-uppercase">
            {{ $session.get('userObj').cluster }}


          </h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/manager/dashboard">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">REPORTS</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">All</li>
            </ol>
          </nav>
        </div>
        <div class="col-lg-6 col-6 text-right pr-3">
          <base-dropdown style="padding-left: 5px" title-classes="btn btn-sm btn-primary mr-0" menu-on-right
            :has-toggle="false">
            <a slot="title" class="dropdown-toggle" type="neutral" size="sm">
              <i class="text-black ni ni-cloud-download-95"></i> Download
            </a>
            <a class="dropdown-item" @click="downloadExcelSheet()"
              v-if="$session.get('userObj').cluster_user_type !== 'Roads Authority'">All {{
                $session.get('userObj').cluster }} data</a>
            <a class="dropdown-item" @click="downloadExcelSheetTelecom()"
              v-if="$session.get('userObj').cluster_user_type !== 'Roads Authority'">All Telecommunications data</a>
            <a class="dropdown-item" @click="downloadExcelSheetEng()">All Engineering data</a>

            <a class="dropdown-item" @click="downloadBase64Dataset()">All Images</a>


          </base-dropdown>
        </div>
        <div class="col-lg-6 col-5 text-right"></div>
      </div>

      <!-- Card stats -->
    </base-header>

    <div class="container-fluid mt--6">
      <div>
        <card class="no-border-card" body-classes="px-0 pb-0" footer-classes="pb-0">
          <div>
            <div class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap">
              <el-select class="select-primary pagination-select" v-model="pagination.perPage" placeholder="Per page">
                <el-option class="select-primary" v-for="item in pagination.perPageOptions" :key="item" :label="item"
                  :value="item"></el-option>
              </el-select>

              <div>
                <base-input v-model="searchQuery" prepend-icon="fas fa-search" placeholder="Search..."></base-input>
              </div>
            </div>
            <div>
              <div v-if="loading" class="text-center text-6xl text-bold blink">
                Loading data...
              </div>

              <div v-else>
                <b-table responsive striped hover thead-class="bg-gray" :items="queriedData" :fields="tableColumns">
                  <template #head(ta)>
                    <th class="wrap" style="padding: 0px; color: white">
                      TA Affected
                    </th>
                  </template>

                  <template #head(approximateDate)>
                    <th class="wrap" style="padding: 0px; color: white">
                      Date of occurence
                    </th>
                  </template>


                  <template #head(dateOfVisit)>
                    <th class="wrap" style="padding: 0px; color: white">
                      Date of visit
                    </th>
                  </template>

                  <template #head(structure_damaged)>
                    <th class="wrap" style="padding: 0px; color: white">
                      Structure damaged at site
                    </th>
                  </template>

                  <template #head(is_structrure_damaged)>
                    <th class="wrap" style="padding: 0px; color: white">
                      Damaged?
                    </th>
                  </template>

                  <template #head(is_structrure_operational)>
                    <th class="wrap" style="padding: 0px; color: white">
                      Operational?
                    </th>
                  </template>

                  <template #head(condition_of_site)>
                    <th class="wrap" style="padding: 0px; color: white">
                      Surface condition of site
                    </th>
                  </template>

                  <!--   <template #head(max_vehicle_size)>
                    <th class="wrap" style="padding: 0px; color: white">
                      Maximum Size Vehicle
                    </th>
                  </template> -->

                  <template #head(tele_infrastructure)>
                    <th class="wrap" style="padding: 0px; color: white">
                      Telecom infrastructure affected
                    </th>
                  </template>

                  <template #head(gps_long)>
                    <th class="wrap" style="padding: 0px; color: white">
                      GPS Longitude
                    </th>
                  </template>

                  <template #head(gps_lat)>
                    <th class="wrap" style="padding: 0px; color: white">
                      GPS Latitude
                    </th>
                  </template>

                  <template #head(id)>
                    <th class="wrap" style="padding: 0px; color: white">ID</th>
                  </template>

                  <template #head(status)>
                    <th class="wrap" style="padding: 0px; color: white">
                      Status
                    </th>
                  </template>

                  <template #head(actions)>
                    <th class="wrap" style="padding: 0px; color: white">
                      Actions
                    </th>
                  </template>

                  <template #head(submitedby)>
                    <th class="wrap" style="padding: 0px; color: white">
                      Submited by
                    </th>
                  </template>

                  <template #head(district)>
                    <th class="wrap" style="padding: 0px; color: white">
                      District
                    </th>
                  </template>

                  <template #head(date)>
                    <th class="wrap" style="padding: 0px; color: white">
                      Submited on
                    </th>
                  </template>

                  <template #cell(condition_of_site)="row">
                    <td class="wrap" style="padding: 0px">
                      {{ row.item.condition_of_site }}
                    </td>
                  </template>

                  <template #cell(disasterdate)="row">
                    <td class="wrap" style="padding: 0px">
                      {{ row.item.disasterdate }}
                    </td>
                  </template>

                  <template #cell(ta)="row">
                    <td class="wrap" style="padding: 0px">
                      {{ row.item.ta }}
                    </td>
                  </template>

                  <template #cell(structure_damaged)="row">
                    <td class="wrap" style="padding: 0px">
                      {{ row.item.structure_damaged }}
                    </td>
                  </template>

                  <template #cell(condition_of_structure)="row">
                    <td class="wrap" style="padding: 0px">
                      {{ row.item.condition_of_structure }}
                    </td>
                  </template>

                  <template #cell(district)="row">
                    <td class="wrap" style="padding: 0px">
                      {{ row.item.district }}
                    </td>
                  </template>

                  <template #cell(max_vehicle_size)="row">
                    <td class="wrap" style="padding: 0px">
                      {{ row.item.max_vehicle_size }}
                    </td>
                  </template>

                  <template #cell(submitedby)="row">
                    <td class="wrap" style="padding: 0px">
                      {{ row.item.submitedby }}
                    </td>
                  </template>

                  <template #cell(date)="row">
                    <td class="wrap" style="padding: 0px">
                      {{ row.item.date }}
                    </td>
                  </template>

                  <template #cell(tele_infrastructure)="row">
                    <td class="wrap" style="padding: 0px">
                      {{ row.item.tele_infrastructure }}
                    </td>
                  </template>

                  <template #cell(is_structrure_damaged)="row">
                    <td class="wrap" style="padding: 0px">
                      {{ row.item.is_structrure_damaged }}
                    </td>
                  </template>


                  <template #cell(is_structrure_operational)="row">
                    <td class="wrap" style="padding: 0px">
                      {{ row.item.is_structrure_operational }}
                    </td>
                  </template>

                  <template #cell(status)="row">
                    <td class="wrap" style="padding: 0px">
                      <badge pill style="background-color: #2ec3a3"
                        v-if="row.item.editcounter > 0 && $session.get('userObj').cluster_user_type !== 'Roads Authority'">
                        <h6 style="padding-top: 5px" class="text-white">
                          Edited
                        </h6>
                      </badge>

                      <badge pill style="background-color: skyblue"
                        v-if="row.item.editcounter == 0 && $session.get('userObj').cluster_user_type !== 'Roads Authority'">
                        <h6 style="padding-top: 5px" class="text-white">
                          No change
                        </h6>
                      </badge>

                      <badge pill style="background-color: orangered"
                        v-if="row.item.status == '3' && $session.get('userObj').cluster_user_type == 'Roads Authority'">
                        <h6 style="padding-top: 5px" class="text-white">
                          Not reviewed
                        </h6>
                      </badge>

                      <badge pill style="background-color: green"
                        v-if="row.item.status == '4' && $session.get('userObj').cluster_user_type == 'Roads Authority'">
                        <h6 style="padding-top: 5px" class="text-white">
                          Reviewed
                        </h6>
                      </badge>

                    </td>
                  </template>

                  <template #cell(actions)="row">
                    <base-dropdown style="padding-left: 5px" title-classes="btn btn-sm neutral mr-0" menu-on-right
                      :has-toggle="false">
                      <a slot="title" class="dropdown-toggle" type="neutral" size="sm">
                        Options
                      </a>
                      <a class="dropdown-item" type="dark" size="sm" icon @click="downloadBase64Data(row.item)"
                        :disabled="row.item.image.length == 0">
                        <i class="ni ni-cloud-download-95"></i> Download
                        Photos</a>
                      <a class="dropdown-item" type="dark" size="sm" icon
                        v-if="row.item.editcounter < 5 && $session.get('userObj').cluster_user_type !== 'Roads Authority'"
                        @click="gotoEdit(row.item._id)">
                        <i class="ni ni-scissors"></i> Edit Record</a>

                      <a class="dropdown-item" type="dark" size="sm" icon
                        v-if="$session.get('userObj').cluster_user_type == 'Roads Authority'"
                        @click="gotoReview(row.item._id)">
                        <i class="ni ni-scissors"></i> Review Data</a>
                    </base-dropdown>
                  </template>
                </b-table>
              </div>
            </div>
          </div>
          <div slot="footer" class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap">
            <div class>
              <p class="card-category">
                Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
                <span v-if="selectedRows.length">&nbsp; &nbsp; {{ selectedRows.length }} rows selected</span>
              </p>
            </div>
            <base-pagination class="pagination-no-border" v-model="pagination.currentPage" :per-page="pagination.perPage"
              :total="total"></base-pagination>
          </div>
        </card>
      </div>
    </div>
  </div>
</template>
<script src="./logistics.js" />

<style scoped>
.wrap {
  word-wrap: break-word;
  white-space: initial;
  width: 400px;
  padding: 0px;
  text-decoration: none;
  border-bottom: 0px solid;
  border-top: 0px solid;
}

.blink {
  animation: blinking 2s linear infinite;
  border-radius: 5px;
  font-size: 30px;
}


@keyframes blinking {
  0% {
    color: orange;
  }

  100% {
    color: teal;
  }
}
</style>
