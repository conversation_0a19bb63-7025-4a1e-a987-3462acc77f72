import { Table, TableColumn, Select, Option } from 'element-ui'
import RouteB<PERSON>Crumb from '@/components/Breadcrumb/RouteBreadcrumb'
import { BasePagination } from '@/components'
import clientPaginationMixin from '@/components/PaginatedTables/clientPaginationMixin'
import { generateTAGVHExcel } from '../../../../../util/generateExcel'
import downloadexcel from 'vue-json-excel'
import dateformat from 'dateformat'
import { mapGetters, mapActions } from 'vuex'
import JSZip from 'jszip'
import { saveAs } from 'file-saver'

export default {
  mixins: [clientPaginationMixin],
  components: {
    BasePagination,
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    [Table.name]: Table,
    downloadexcel,
    [TableColumn.name]: TableColumn
  },
  data () {
    return {
      propsToSearch: [
        'disasterdate',
        'structure_damaged',
        'max_vehicle_size',
        'condition_of_structure',
        'submitedby'
      ],
      searchQuery: '',
      bufferDraFormsData: [],
      disablebutton: false,
      images: [],
      counter: 0,
      clusterreports: [],
      telecomreports:[],
      gridOptions: {},
      tableColumns: [
       /*  {
          key: 'id',
          label: 'ID',
          sortable: true
        }, */

        {
          key: 'district',
          label: 'District',
          sortable: true
        },
        {
          key: 'ta',
          label: 'TA affected',
          sortable: true
        },

        {
          key: 'disasterdate',
          label: 'Date of occurence',
          sortable: true
        },
        {
          key: 'structure_damaged',
          label: 'Structure damaged at site',
          thStyle: { width: '1px', fontsize: '34px' },
          sortable: true
        },

        {
          key: 'condition_of_structure',
          label: 'Condition of structure',
          tdClass: 'wrap',
          sortable: true
        },

        {
          key: 'condition_of_site',
          label: 'Surface condition of site',
          sortable: true
        },

        /*
        {
          key: 'structure_if_other',
          label: 'Structure damaged (If other)',
          sortable: true
        },

        {
          key: 'structure_name_if_other',
          label: 'Structure damaged (If not Road)',
          sortable: true
        },

        {
          key: 'structure_name_if_other',
          label: 'Structure damaged (If not Road)',
          sortable: true
        },
 */

        {
          key: 'max_vehicle_size',
          label: 'Maximum Size Vehicle',
          sortable: true
        },


        {
          key: 'gps_lat',
          label: 'GPS lat',
          sortable: true
        },

        {
          key: 'gps_long',
          label: 'GPS long',
          sortable: true
        },

        {
          key: 'submitedby',
          label: 'Submmited by',
          sortable: true
        },

        {
          key: 'date',
          label: 'Submmited on',
          sortable: true
        },

        {
          key: 'status',
          label: 'Status',
          sortable: true
        },
        {
          key: 'actions',
          label: 'Actions'
        }
      ],
      tableData: [],
      selectedRows: []
    }
  },
  methods: {
    ...mapActions('clusterreports', {
      getclusterreportsAction: 'get'
    }),

    formatedDate (data) {
      let finalDate = dateformat(data, 'dd-mm-yyyy hh:mm:ss')
      return finalDate
    },

    formateTime (dataObject) {
      let finalTime = dateformat(dataObject, 'HH:mm:ss')
      return finalTime
    },
    downloadExcelSheet () {
      let data = this.tableData.map(item => {
        return {
          District: item.district,
          TA: item.ta,
          'Date of occurence': item.disasterdate,
          'Which type of structure has been damaged?': item.structure_damaged,
          'Which type of structure (If other)': item.structure_if_other,
          'Name of structure (If Not Road)': item.structure_name_if_other,
          'What is the link #? (If Road)': item.linknumber,
          'What is the approximate length of the damage? (If Road) (Meters)':
            item.length_of_damage,
          'What is the current maximum size vehicle type that can access this road?':
            item.max_vehicle_size,
          'What is the condition of the damaged structure?':
            item.condition_of_structure,
          'What is the surface condition at the damaged site?':
            item.condition_of_site,
          'What is the type of telecommunication infrastructure affected':
            item.tele_infrastructure,
          'What is the functionality of the infrastructure?':
            item.tele_infrastructure_state,
          'What is the telecommunications provider?':
            item.tele_infrastructure_provider,
          'GPS lat': item.gps_lat,
          'GPS long': item.gps_long,
          'Submitted by': item.submitedby,
          'Submitted on': item.date,
          'Edited by': item.editedby,
          'Edited on': item.editedon,
          sheet: 'Logistics'
        }
      })

      this.clusterreports = data

      generateTAGVHExcel(this.clusterreports)
    },


    downloadExcelSheetTelecom () {
      let telecomData = this.tableData.filter(
        data => data.structure_damaged == 'Telecommunication infrastructure'
      )

      let data = telecomData.map(item => {
        return {
          District: item.district,
          TA: item.ta,
          'Date of occurence': item.disasterdate,
          'Which type of structure has been damaged?': item.structure_damaged,
          'What is the type of telecommunication infrastructure affected':
            item.tele_infrastructure,
          'What is the functionality of the infrastructure?':
            item.tele_infrastructure_state,
          'What is the telecommunications provider?':
            item.tele_infrastructure_provider,
          'GPS lat': item.gps_lat,
          'GPS long': item.gps_long,
          'Submitted by': item.submitedby,
          'Submitted on': item.date,
          'Edited by': item.editedby,
          'Edited on': item.editedon,
          sheet: 'Telecommications'
        }
      })

      this.telecomreports = data

      generateTAGVHExcel(this.telecomreports)
    },
    convertBase64ToFile (base64String, fileName) {
      let arr = base64String.split(',')
      let mime = arr[0].match(/:(.*?);/)[1]
      let bstr = atob(arr[1])
      let n = bstr.length
      let uint8Array = new Uint8Array(n)
      while (n--) {
        uint8Array[n] = bstr.charCodeAt(n)
      }
      let file = new File([uint8Array], fileName, { type: mime })
      return file
    },

    createArchive (images, name) {
      var zip = new JSZip()
      var img = zip.folder('images')

      for (var i = 0; i < images.length; i++) {
        img.file(name + '-' + i + '.jpg', images[i], { base64: true })
      }
      zip.generateAsync({ type: 'blob' }).then(function (file) {
        saveAs(file, name + '-' + 'images' + '.zip')
      })
    },

    downloadBase64Data (base64Strings) {
      let baseImages = base64Strings.image
      let archiveName = ''
      for (let i in baseImages) {
        archiveName = base64Strings.ta + '-' + Date.now()
        let imgFile = this.convertBase64ToFile(baseImages[i], archiveName)
        this.images.push(imgFile)
      }
      this.createArchive(this.images, archiveName)
    },

    gotoEdit (id) {
      this.$router.push({
        name: 'clusterEditreports',
        params: { id: id }
      })
    }
  },

  async mounted () {
    let clusterdata = [...(await this.getclusterreportsAction())].filter(
      data =>
        data.user.district.admin2Name_en ==
        this.$session.get('userObj').district.admin2Name_en
    )

    clusterdata.forEach((cluster, index) => {
      clusterdata.sort(function (a, b) {
        return new Date(b.createdon) - new Date(a.createdon)
      })


      this.tableData.push({
        id: index + 1,
        _id: clusterdata[index]._id,
        disasterdate: clusterdata[index].disasterdate,
        district: clusterdata[index].user.district.admin2Name_en,
        image: clusterdata[index].impact.images,
        ta: clusterdata[index].TA.admin3_name_en,
        district: clusterdata[index].TA.admin2_name_en,

        editcounter: clusterdata[index].editcounter,
        date: this.formatedDate(clusterdata[index].createdon),
        structure_damaged:
          clusterdata[index].impact.structure_affected.response,
        structure_if_other:
          clusterdata[index].impact.structure_affected.other_name,
        structure_name_if_other:
          clusterdata[index].impact.structure_affected.name,
        max_vehicle_size: clusterdata[index].impact.vehicle_size.response==
        undefined || null
        ? 'N/A'
        : clusterdata[index].impact.vehicle_size.response,
        condition_of_structure:
          clusterdata[index].impact.condition_of_structrure.response ==
            undefined || null
            ? 'N/A'
            : clusterdata[index].impact.condition_of_structrure.response,
        linknumber:
          clusterdata[index].impact.structure_affected.linknumber ==
            undefined || null
            ? 'N/A'
            : clusterdata[index].impact.structure_affected.linknumber.response,
        length_of_damage:
          clusterdata[index].impact.structure_affected.length_of_damage ==
            undefined || null
            ? 'N/A'
            : clusterdata[index].impact.structure_affected.length_of_damage,
        condition_of_site:
          clusterdata[index].impact.condition_of_site.response == undefined ||
          null
            ? 'N/A'
            : clusterdata[index].impact.condition_of_site.response,
        gps_lat: clusterdata[index].impact.site_cordinates.lat,
        gps_long: clusterdata[index].impact.site_cordinates.long,
        tele_infrastructure:
          clusterdata[index].impact.tele_infrastructure.response,
        tele_infrastructure_state:
          clusterdata[index].impact.tele_infrastructure.state,
        tele_infrastructure_provider:
          clusterdata[index].impact.tele_infrastructure.service_provider,
        submitedby:
          clusterdata[index].user.firstName +
          ' ' +
          clusterdata[index].user.lastName,
        editedby:
          clusterdata[index].editedhistory[
            clusterdata[index].editedhistory.length - 1
          ].editedby,
        editedon:
          clusterdata[index].editedhistory[
            clusterdata[index].editedhistory.length - 1
          ].editedon,
        uuid: clusterdata[index]._id,
        status: clusterdata[index].status
      })
    })
  }
}
