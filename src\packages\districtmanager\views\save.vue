<template>
    <div style="color: #32325d">
      <table width="100%" class="col-md-11">
        <tr>
          <td colspan="12" v-if="dinrFormsData.district">
            <center>
              <h2>
                <b style="text-transform:uppercase"
                  >SUMMARY REPORT FOR 
                  {{ dinrFormsData.district.admin2_name_en }}</b
                >
              </h2>
            </center>
          </td>
        </tr>
        <tr>
          <td colspan="6" style="width:50%">
            <b>Affected TAs</b>
            :
            <span>{{
              TAarray.join() == "" ? "Not reported" : TAarray.join(", ")
            }}</span>
          </td>
  
          <td colspan="6" style="width:50%">
            <b>Affected Villages</b> :
            <span>{{
              villagesArray.join() == ""
                ? "Not reported"
                : villagesArray.join(", ")
            }}</span>
          </td>
        </tr>
        <tr>
          <td colspan="6" width="50%" style="text-transform:capitalize">
            <b>Type of disasters</b>
            : {{ dinrFormsData.disaster }}
          </td>
          <td colspan="6" width="50%">
            <b>Report submitted at</b>
            :
            {{
              dinrFormsData.createdon
                ? dateFormate(
                    new Date(dinrFormsData.createdon).toLocaleDateString("en-US")
                  )
                : ""
            }}
          </td>
        </tr>
        <tr>
          <td colspan="6" width="50%">
            <b>Date Disaster started</b>
            :
            {{
              dinrFormsData.dodFrom
                ? dateFormate(
                    new Date(dinrFormsData.dodFrom).toLocaleDateString("en-US")
                  )
                : ""
            }}
          </td>
          <td colspan="6" width="50%">
            <b>Date Disaster ended</b>
            :
            {{
              dinrFormsData.dodTo
                ? dateFormate(
                    new Date(dinrFormsData.dodTo).toLocaleDateString("en-US")
                  )
                : ""
            }}
          </td>
        </tr>
        <tr>
          <td colspan="4">
            <b>Date of assessment by ACPC/VCPC</b>
            :
            {{
              dinrFormsData.doaAcpc
                ? dateFormate(
                    new Date(dinrFormsData.doaAcpc).toLocaleDateString("en-US")
                  )
                : ""
            }}
          </td>
          <td colspan="4">
            <b>Date of assessment/verification by DCPC</b>
            :
            {{
              dinrFormsData.doaDcpc
                ? dateFormate(
                    new Date(dinrFormsData.doaDcpc).toLocaleDateString("en-US")
                  )
                : ""
            }}
          </td>
          <td colspan="4">
            <b>Date reported to the DEC</b>
            :
            {{
              dinrFormsData.dateReported
                ? dateFormate(
                    new Date(dinrFormsData.dateReported).toLocaleDateString(
                      "en-US"
                    )
                  )
                : ""
            }}
          </td>
        </tr>
      </table>
  
      <table
        v-for="(item, index) in draFormsData"
        :key="index"
        v-if="index == 0"
        class="col-md-11"
      >
        <tr
          v-if="
            (PeopleInjuredrows && PeopleInjuredrows.length > 0) ||
              (PeopleDeadrows && PeopleDeadrows.length > 0)
          "
        >
          <td colspan="15" class="text-center">
            <b>GENERAL INFORMATION</b>
          </td>
        </tr>
        <tr
          v-if="item.shelter"
          style="margin:0;padding:0 !important;border:0px !important;"
        >
          <!-- shelter -->
          <td colspan="16" style="margin:0;padding:0 !important;border:0px">
            <table
              width="100%"
              style="margin:0;padding:0 !important;border:0px !important;"
            >
              <tr v-if="PeopleInjuredrows && PeopleInjuredrows.length > 0">
                <td colspan="6" :rowspan="2 + PeopleInjuredrows.length">
                  <b>People Injured</b>
                </td>
                <td colspan="4">
                  <b>TA</b>
                </td>
                <td colspan="2" width="10%">
                  <b>
                    <center>Male</center>
                  </b>
                </td>
                <td colspan="2" width="10%">
                  <b>
                    <center>Female</center>
                  </b>
                </td>
                <td style="font-weight:bold" colspan="2" width="10%">
                  <b>
                    <center>Total</center>
                  </b>
                </td>
              </tr>
              <tr
                v-if="PeopleInjuredrows && PeopleInjuredrows.length > 0"
                v-for="row in PeopleInjuredrows"
              >
                <td colspan="4">{{ row.ta }}</td>
                <td colspan="2" class="right-align">
                  {{ numberWithCommas(row.males) }}
                </td>
                <td colspan="2" class="right-align">
                  {{ numberWithCommas(row.females) }}
                </td>
                <td colspan="2" style="font-weight:bold" class="right-align">
                  {{
                    numberWithCommas(
                      (row.females ? parseInt(row.females) : 0) +
                        (row.males ? parseInt(row.males) : 0)
                    )
                  }}
                </td>
              </tr>
  
              <tr v-if="PeopleInjuredrows && PeopleInjuredrows.length > 0">
                <td style="font-weight:bold" colspan="4">
                  <center>Total</center>
                </td>
                <td style="font-weight:bold" colspan="2" class="right-align">
                  {{
                    numberWithCommas(
                      PeopleInjuredrows
                        ? PeopleInjuredrows.map(function(item) {
                            return item.males ? parseInt(item.males) : 0;
                          }).reduce((sum, value) => sum + value)
                        : 0
                    )
                  }}
                </td>
                <td style="font-weight:bold" class="right-align" colspan="2">
                  {{
                    numberWithCommas(
                      PeopleInjuredrows
                        ? PeopleInjuredrows.map(function(item) {
                            return item.females ? parseInt(item.females) : 0;
                          }).reduce((sum, value) => sum + value)
                        : 0
                    )
                  }}
                </td>
                <td style="font-weight:bold" colspan="2" class="right-align">
                  {{
                    numberWithCommas(
                      (PeopleInjuredrows
                        ? PeopleInjuredrows.map(function(item) {
                            return item.males ? parseInt(item.males) : 0;
                          }).reduce((sum, value) => sum + value)
                        : 0) +
                        (PeopleInjuredrows
                          ? PeopleInjuredrows.map(function(item) {
                              return item.females ? parseInt(item.females) : 0;
                            }).reduce((sum, value) => sum + value)
                          : 0)
                    )
                  }}
                </td>
              </tr>
  
              <tr v-if="PeopleDeadrows && PeopleDeadrows.length > 0">
                <td :rowspan="2 + PeopleDeadrows.length" colspan="6">
                  <b>People Dead</b>
                </td>
                <td colspan="4">
                  <b>TA</b>
                </td>
                <td colspan="2" width="10%">
                  <b>
                    <center>Male</center>
                  </b>
                </td>
                <td colspan="2" width="10%">
                  <b>
                    <center>Female</center>
                  </b>
                </td>
                <td colspan="2" style="font-weight:bold" width="10%">
                  <b>
                    <center>Total</center>
                  </b>
                </td>
              </tr>
              <tr
                v-if="PeopleDeadrows && PeopleDeadrows.length > 0"
                v-for="row in PeopleDeadrows"
              >
                <td colspan="4">{{ row.ta }}</td>
                <td colspan="2" class="right-align">
                  {{ numberWithCommas(row.males) }}
                </td>
                <td colspan="2" class="right-align">
                  {{ numberWithCommas(row.females) }}
                </td>
                <td colspan="2" style="font-weight:bold" class="right-align">
                  {{
                    numberWithCommas(
                      row.males
                        ? parseInt(row.males)
                        : 0 + row.females
                        ? parseInt(row.females)
                        : 0
                    )
                  }}
                </td>
              </tr>
  
              <tr v-if="PeopleDeadrows && PeopleDeadrows.length > 0">
                <td style="font-weight:bold" colspan="4">
                  <center>Total</center>
                </td>
                <td style="font-weight:bold" class="right-align" colspan="2">
                  {{
                    numberWithCommas(
                      PeopleDeadrows
                        ? PeopleDeadrows.map(function(item) {
                            return item.males ? parseInt(item.males) : 0;
                          }).reduce((sum, value) => sum + value)
                        : 0
                    )
                  }}
                </td>
                <td style="font-weight:bold" class="right-align" colspan="2">
                  {{
                    numberWithCommas(
                      PeopleDeadrows
                        ? PeopleDeadrows.map(function(item) {
                            return item.females ? parseInt(item.females) : 0;
                          }).reduce((sum, value) => sum + value)
                        : 0
                    )
                  }}
                </td>
                <td style="font-weight:bold" class="right-align" colspan="2">
                  {{
                    numberWithCommas(
                      (PeopleDeadrows
                        ? PeopleDeadrows.map(function(item) {
                            return item.males ? parseInt(item.males) : 0;
                          }).reduce((sum, value) => sum + value)
                        : 0) +
                        (PeopleDeadrows
                          ? PeopleDeadrows.map(function(item) {
                              return item.females ? parseInt(item.females) : 0;
                            }).reduce((sum, value) => sum + value)
                          : 0)
                    )
                  }}
                </td>
              </tr>
            </table>
          </td>
        </tr>
        <tr
          style="border:none"
          v-if="
            (PeopleInjuredrows && PeopleInjuredrows.length > 0) ||
              (PeopleDeadrows && PeopleDeadrows.length > 0)
          "
        >
          <td class="col-md-12" colspan="15"></td>
        </tr>
        <!-- general end -->
        <tr>
          <td style="text-align:left;" colspan="1" width="12.2%">
            <b>CLUSTER</b>
          </td>
          <td colspan="15" class="text-center">
            <b>IMPACT ASSESSMENT</b>
          </td>
        </tr>
        <tr
          v-if="item.shelter"
          style="margin:0;padding:0 !important;border:0px !important;"
        >
          <!-- shelter -->
          <td colspan="16" style="margin:0;padding:0 !important;border:0px">
            <table
              width="100%"
              style="margin:0;padding:0 !important;border:0px !important;"
            >
              <tr>
                <td
                  width="12.2%"
                  :rowspan="
                    draFormsData.length +
                      3 +
                      (people_without_shelter
                        ? people_without_shelter.length + 1
                        : 0) +
                      (PeopleAffectedrows ? PeopleAffectedrows.length + 2 : 0)
                  "
                >
                  <span class="rotated" style="font-weight: bold;"></span>
                  <img
                    src="../../../../../../../../../../static/cluster_shelter_100px_bluebox.png"
                    height="150"
                  />
                </td>
              </tr>
              <tr v-if="item.shelter.people_without_shelter">
                <td
                  colspan="6"
                  :rowspan="people_without_shelter.length + 2"
                  width="20%"
                >
                  <b>People without shelter</b>
                </td>
                <td colspan="4">
                  <b>TA</b>
                </td>
                <td colspan="2" width="10%">
                  <b>
                    <center>Male</center>
                  </b>
                </td>
                <td colspan="2" width="10%">
                  <b>
                    <center>Female</center>
                  </b>
                </td>
                <td colspan="2" style="font-weight:bold" width="10%">
                  <b>
                    <center>Total</center>
                  </b>
                </td>
              </tr>
              <tr v-for="row in people_without_shelter">
                <td colspan="4">{{ row.ta }}</td>
                <td colspan="2" width="10%" class="right-align">
                  {{ numberWithCommas(row.males) }}
                </td>
                <td colspan="2" width="10%" class="right-align">
                  {{ numberWithCommas(row.females) }}
                </td>
                <td colspan="2" class="right-align" style="font-weight:bold">
                  {{
                    numberWithCommas(
                      (row.females ? parseInt(row.females) : 0) +
                        (row.males ? parseInt(row.males) : 0)
                    )
                  }}
                </td>
              </tr>
  
              <tr
                v-if="people_without_shelter && people_without_shelter.length > 0"
              >
                <td style="font-weight:bold" colspan="4">
                  <center>Total</center>
                </td>
                <td style="font-weight:bold" class="right-align" colspan="2">
                  {{
                    numberWithCommas(
                      people_without_shelter
                        ? people_without_shelter
                            .map(function(item) {
                              return item.males ? parseInt(item.males) : 0;
                            })
                            .reduce((sum, value) => sum + value)
                        : 0
                    )
                  }}
                </td>
                <td style="font-weight:bold" class="right-align" colspan="2">
                  {{
                    numberWithCommas(
                      people_without_shelter
                        ? people_without_shelter
                            .map(function(item) {
                              return item.females ? parseInt(item.females) : 0;
                            })
                            .reduce((sum, value) => sum + value)
                        : 0
                    )
                  }}
                </td>
                <td style="font-weight:bold" class="right-align" colspan="2">
                  {{
                    numberWithCommas(
                      (people_without_shelter
                        ? people_without_shelter
                            .map(function(item) {
                              return item.males ? parseInt(item.males) : 0;
                            })
                            .reduce((sum, value) => sum + value)
                        : 0) +
                        (people_without_shelter
                          ? people_without_shelter
                              .map(function(item) {
                                return item.females ? parseInt(item.females) : 0;
                              })
                              .reduce((sum, value) => sum + value)
                          : 0)
                    )
                  }}
                </td>
              </tr>
  
              <tr v-if="PeopleAffectedrows && PeopleAffectedrows.length > 0">
                <td colspan="6" :rowspan="2 + PeopleAffectedrows.length">
                  <b>Households Affected</b>
                </td>
                <td colspan="4">
                  <b>TA</b>
                </td>
                <td colspan="2" width="10%">
                  <b>
                    <center>Male</center>
                  </b>
                </td>
                <td colspan="2" width="10%">
                  <b>
                    <center>Female</center>
                  </b>
                </td>
                <td style="font-weight:bold" colspan="2" width="10%">
                  <b>
                    <center>Total</center>
                  </b>
                </td>
              </tr>
              <tr
                v-if="PeopleAffectedrows && PeopleAffectedrows.length > 0"
                v-for="row in PeopleAffectedrows"
              >
                <td colspan="4">{{ row.ta }}</td>
                <td colspan="2" class="right-align">
                  {{ numberWithCommas(row.males) }}
                </td>
                <td colspan="2" class="right-align">
                  {{ numberWithCommas(row.females) }}
                </td>
                <td colspan="2" style="font-weight:bold" class="right-align">
                  {{
                    numberWithCommas(
                      (row.females ? parseInt(row.females) : 0) +
                        (row.males ? parseInt(row.males) : 0)
                    )
                  }}
                </td>
              </tr>
  
              <tr v-if="PeopleAffectedrows && PeopleAffectedrows.length > 0">
                <td style="font-weight:bold" colspan="4">
                  <center>Total</center>
                </td>
                <td colspan="2" style="font-weight:bold" class="right-align">
                  {{
                    numberWithCommas(
                      PeopleAffectedrows
                        ? PeopleAffectedrows.map(function(item) {
                            return item.males ? parseInt(item.males) : 0;
                          }).reduce((sum, value) => sum + value)
                        : 0
                    )
                  }}
                </td>
                <td colspan="2" style="font-weight:bold" class="right-align">
                  {{
                    numberWithCommas(
                      PeopleAffectedrows
                        ? PeopleAffectedrows.map(function(item) {
                            return item.females ? parseInt(item.females) : 0;
                          }).reduce((sum, value) => sum + value)
                        : 0
                    )
                  }}
                </td>
                <td colspan="2" style="font-weight:bold" class="right-align">
                  {{
                    numberWithCommas(
                      (PeopleAffectedrows
                        ? PeopleAffectedrows.map(function(item) {
                            return item.males ? parseInt(item.males) : 0;
                          }).reduce((sum, value) => sum + value)
                        : 0) +
                        (PeopleAffectedrows
                          ? PeopleAffectedrows.map(function(item) {
                              return item.females ? parseInt(item.females) : 0;
                            }).reduce((sum, value) => sum + value)
                          : 0)
                    )
                  }}
                </td>
              </tr>
  
              <tr v-for="(item, index) in draFormsDataOther">
                <td colspan="4">
                  <b>{{ item.admin3.admin3Name_en }}'s response needed</b>
                </td>
                <td colspan="12">
                  <u>
                    <strong>URGENT</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.shelter
                        ? item.sectors.shelter.urgent_response_needed
                          ? item.sectors.shelter.urgent_response_needed
                          : "N/A"
                        : "N/A"
                    }}
                  </li>
                  <hr style="margin:1%;" />
                  <u>
                    <strong>GENERAL</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.shelter
                        ? item.sectors.shelter.response_needed
                          ? item.sectors.shelter.response_needed
                          : "N/A"
                        : "N/A"
                    }}
                  </li>
                </td>
              </tr>
            </table>
          </td>
        </tr>
        <!-- shelter end -->
        <!-- displaced -->
        <tr
          v-if="item.displaced"
          style="margin:0;padding:0 !important;border:0px !important;"
        >
          <td colspan="16" style="margin:0;padding:0 !important;border:0px">
            <table
              width="100%"
              style="margin:0;padding:0 !important;border:0px !important;"
            >
              <tr>
                <td
                  width="12.2%"
                  v-bind:rowspan="
                    draFormsData.length +
                      (PeopleAffectedrowsD
                        ? PeopleAffectedrowsD.length > 0
                          ? PeopleAffectedrowsD.length + 3
                          : 2
                        : 1)
                  "
                  colspan="1"
                >
                  <span class="rotated" style="font-weight: bold;"
                    ></span
                  >
                  <img
                    src="../../../../../../../../../../static/crisis_population_displacement_100px_bluebox.png"
                    height="150"
                  />
                </td>
              </tr>
  
              <tr v-if="PeopleAffectedrowsD && PeopleAffectedrowsD.length > 0">
                <td
                  v-bind:rowspan="
                    PeopleAffectedrowsD.length > 0
                      ? PeopleAffectedrowsD.length + 2
                      : PeopleAffectedrowsD.length + 1
                  "
                  colspan="6"
                  width="20%"
                >
                  <b>Number of Households displaced</b>
                </td>
                <td colspan="4">
                  <b>TA</b>
                </td>
                <td colspan="2" width="10%">
                  <b>
                    <center>Male</center>
                  </b>
                </td>
                <td colspan="2" width="10%">
                  <b>
                    <center>Female</center>
                  </b>
                </td>
                <td style="font-weight:bold" colspan="2" width="10%">
                  <b>
                    <center>Total</center>
                  </b>
                </td>
              </tr>
              <tr
                v-if="PeopleAffectedrowsD && PeopleAffectedrowsD.length > 0"
                v-for="row in PeopleAffectedrowsD"
              >
                <td colspan="4">{{ row.ta }}</td>
                <td colspan="2" class="right-align">
                  {{ numberWithCommas(row.males) }}
                </td>
                <td colspan="2" class="right-align">
                  {{ numberWithCommas(row.females) }}
                </td>
                <td style="font-weight:bold" colspan="2" class="right-align">
                  {{
                    numberWithCommas(
                      parseInt(row.females ? row.females : 0) +
                        parseInt(row.males ? row.males : 0)
                    )
                  }}
                </td>
              </tr>
  
              <tr v-if="PeopleAffectedrowsD && PeopleAffectedrowsD.length > 0">
                <td style="font-weight:bold" colspan="4">
                  <center>Total</center>
                </td>
                <td style="font-weight:bold" class="right-align" colspan="2">
                  {{
                    numberWithCommas(
                      PeopleAffectedrowsD
                        ? PeopleAffectedrowsD.map(function(item) {
                            return item.males ? parseInt(item.males) : 0;
                          }).reduce((sum, value) => sum + value)
                        : 0
                    )
                  }}
                </td>
                <td style="font-weight:bold" class="right-align" colspan="2">
                  {{
                    numberWithCommas(
                      PeopleAffectedrowsD
                        ? PeopleAffectedrowsD.map(function(item) {
                            return item.females ? parseInt(item.females) : 0;
                          }).reduce((sum, value) => sum + value)
                        : 0
                    )
                  }}
                </td>
                <td style="font-weight:bold" class="right-align" colspan="2">
                  {{
                    numberWithCommas(
                      (PeopleAffectedrowsD
                        ? PeopleAffectedrowsD.map(function(item) {
                            return item.males ? parseInt(item.males) : 0;
                          }).reduce((sum, value) => sum + value)
                        : 0) +
                        (PeopleAffectedrowsD
                          ? PeopleAffectedrowsD.map(function(item) {
                              return item.females ? parseInt(item.females) : 0;
                            }).reduce((sum, value) => sum + value)
                          : 0)
                    )
                  }}
                </td>
              </tr>
  
              <tr v-for="(item, index) in draFormsDataOther" :key="index">
                <td colspan="4">
                  <b>{{ item.admin3.admin3Name_en }}'s response needed</b>
                </td>
                <td colspan="12">
                  <u>
                    <strong>URGENT</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.displaced
                        ? item.sectors.displaced.urgent_response_needed
                          ? item.sectors.displaced.urgent_response_needed
                          : "N/A"
                        : "N/A"
                    }}
                  </li>
                  <hr style="margin:1%;" />
                  <u>
                    <strong>GENERAL</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.displaced
                        ? item.sectors.displaced.response_needed
                          ? item.sectors.displaced.response_needed
                          : "N/A"
                        : "N/A"
                    }}
                  </li>
                </td>
              </tr>
            </table>
          </td>
        </tr>
        <tr
          v-if="item.agriculture"
          style="margin:0;padding:0 !important;border:0px !important;"
        >
          <td colspan="16" style="margin:0;padding:0 !important;border:0px">
            <table
              width="100%"
              style="margin:0;padding:0 !important;border:0px !important;"
            >
              <tr>
                <td
                  width="12.2%"
                  :rowspan="
                    1 +
                      draFormsData.length +
                      (crops_damaged ? crops_damaged.length > 0 ? crops_damaged.length + 3 : 0 : 1) +
  
                      draFormsData.length +
                      (food_item_damage ? food_item_damage.length + 2 : 0) +
  
                      draFormsData.length +
                      (livelihoods_affected ? livelihoods_affected.length + 3 : 0)
                     
                  "
                  colspan="1"
                >
                  <span class="rotated" style="font-weight: bold;"
                    ></span
                  >
                  <img
                    src="../../../../../../../../../../static/other_cluster_agriculture_100px_bluebox.png"
                    height="150"
                  />
                </td>
  
                <td
                  v-if="crops_damaged && crops_damaged.length > 0"
                  v-bind:rowspan="
                    crops_damaged.length > 0 ? crops_damaged.length + 2 : 1
                  "
                  colspan="6"
                  width="20%"
                >
                  <b>Damage per crop (Hectares)</b>
                </td>
                <td colspan="4" v-if="crops_damaged && crops_damaged.length > 0">
                  <b>TA</b>
                </td>
                <td
                  colspan="2"
                  width="15%"
                  v-if="crops_damaged && crops_damaged.length > 0"
                >
                  <b>Submerged</b>
                </td>
                <td
                  colspan="2"
                  width="15%"
                  v-if="crops_damaged && crops_damaged.length > 0"
                >
                  <b>Washed away</b>
                </td>
                <td
                  style="font-weight:bold"
                  colspan="2"
                  width="15%"
                  v-if="crops_damaged && crops_damaged.length > 0"
                >
                  <b>
                    <center>Total</center>
                  </b>
                </td>
              </tr>
              <tr
                v-if="crops_damaged && crops_damaged.length > 0"
                v-for="crop in crops_damaged"
              >
                <td colspan="4">{{ crop.ta }}</td>
                <td colspan="2" class="right-align">
                  {{ numberWithCommas(crop.males) }}
                </td>
                <td colspan="2" class="right-align">
                  {{ numberWithCommas(crop.females) }}
                </td>
                <td style="font-weight:bold" colspan="2" class="right-align">
                  {{
                    numberWithCommas(
                      parseInt(crop.females ? crop.females : 0) +
                        parseInt(crop.males ? crop.males : 0)
                    )
                  }}
                </td>
              </tr>
  
              <tr v-if="crops_damaged && crops_damaged.length > 0">
                <td style="font-weight:bold" colspan="4">
                  <center>Total</center>
                </td>
                <td style="font-weight:bold" class="right-align" colspan="2">
                  {{
                    numberWithCommas(
                      crops_damaged
                        ? crops_damaged
                            .map(function(item) {
                              return item.males ? parseInt(item.males) : 0;
                            })
                            .reduce((sum, value) => sum + value)
                        : 0
                    )
                  }}
                </td>
                <td style="font-weight:bold" class="right-align" colspan="2">
                  {{
                    numberWithCommas(
                      crops_damaged
                        ? crops_damaged
                            .map(function(item) {
                              return item.females ? parseInt(item.females) : 0;
                            })
                            .reduce((sum, value) => sum + value)
                        : 0
                    )
                  }}
                </td>
                <td style="font-weight:bold" class="right-align" colspan="2">
                  {{
                    numberWithCommas(
                      (crops_damaged
                        ? crops_damaged
                            .map(function(item) {
                              return item.males ? parseInt(item.males) : 0;
                            })
                            .reduce((sum, value) => sum + value)
                        : 0) +
                        (crops_damaged
                          ? crops_damaged
                              .map(function(item) {
                                return item.females ? parseInt(item.females) : 0;
                              })
                              .reduce((sum, value) => sum + value)
                          : 0)
                    )
                  }}
                </td>
              </tr>
  
              <tr v-if="food_item_damage && food_item_damage.length > 0">
                <td
                  v-bind:rowspan="
                    food_item_damage.length > 0 ? food_item_damage.length + 2 : 1
                  "
                  colspan="6"
                >
                  <b>Number of Food items damage (KGs)</b>
                </td>
                <td colspan="4">
                  <b>TA</b>
                </td>
                <td colspan="5">
                  <b>in KGs</b>
                </td>
              </tr>
              <tr
                v-if="food_item_damage && food_item_damage.length > 0"
                v-for="crop in food_item_damage"
              >
                <td colspan="4">{{ crop.ta }}</td>
                <td colspan="5" class="right-align">
                  {{ numberWithCommas(crop.males) }}
                </td>
              </tr>
  
              <tr v-if="food_item_damage && food_item_damage.length > 0">
                <td style="font-weight:bold" colspan="4">
                  <center>Total</center>
                </td>
                <td style="font-weight:bold" colspan="5" class="right-align">
                  {{
                    numberWithCommas(
                      food_item_damage
                        ? food_item_damage
                            .map(function(item) {
                              return item.males ? parseInt(item.males) : 0;
                            })
                            .reduce((sum, value) => sum + value)
                        : 0
                    )
                  }}
                </td>
              </tr>
  
              <tr v-if="livelihoods_affected && livelihoods_affected.length > 0">
                <td
                  v-bind:rowspan="
          
                    agricultureLivelihoodArray.length > 0
                      ? agricultureLivelihoodArray.length + 1
                      : agricultureLivelihoodArray.length + 1
                  "
                  colspan="6"
                >
                  <b class="center-align">Livelihoods affected</b>
                </td>
                <td colspan="2">
                  <b class="center-align">Livelihood</b>
                </td>
                <td colspan="2">
                  <b class="center-align">Population group</b>
                </td>
                <td colspan="2">
                  <b class="center-align">Severity</b>
                </td>
                <td colspan="1">
                  <b class="center-align">MHH</b>
                </td>
                <td colspan="1">
                  <b class="center-align">FHH</b>
                </td>
                <td colspan="2">
                  <b>Total</b>
                </td>
              </tr>
              <tr
                v-if ="agricultureLivelihoodArray &&
                  agricultureLivelihoodArray.length > 0
                "
                v-for="row in agricultureLivelihoodArray"
              >
                <td colspan="2" class="left-align">{{row.livelihood_type}}</td>
                <td colspan="2" class="left-align">
                  {{ row.category}}
                </td>
                <td colspan="2" class="left-align">
                  {{ row.severity }}
                </td>
                <td colspan="1" class="right-align">
                  {{ row.male_HH}}
                </td>
                <td colspan="1" class="right-align">
                  {{ row.female_HH}}
                </td>
                <td style="font-weight:bold" class="right-align">
                  {{
                    numberWithCommas(
                      parseInt(row.male_HH ? row.male_HH : 0) +
                        parseInt(row.female_HH ? row.female_HH : 0)
                    )
                  }}
                </td>
              </tr>
  
  
  
  
  
  
  
  
  
  
  
              <tr v-for="(item, index) in draFormsDataOther">
                <td colspan="4">
                  <b>{{ item.admin3.admin3Name_en }}'s response needed</b>
                </td>
                <td colspan="12">
                  <u>
                    <strong>URGENT</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.agriculture
                        ? item.sectors.agriculture.urgent_response_needed
                          ? item.sectors.agriculture.urgent_response_needed
                          : "N/A"
                        : "N/A"
                    }}
                  </li>
                  <hr style="margin:1%;" />
                  <u>
                    <strong>GENERAL</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.agriculture
                        ? item.sectors.agriculture.response_needed
                          ? item.sectors.agriculture.response_needed
                          : "N/A"
                        : "N/A"
                    }}
                  </li>
                </td>
              </tr>
            </table>
          </td>
        </tr>
  
  <!-- food security -->
        <tr
          v-if="item.food"
          style="margin:0;padding:0 !important;border:0px !important;"
        >
          <td colspan="16" style="margin:0;padding:0 !important;border:0px">
            <table
              width="100%"
              style="margin:0;padding:0 !important;border:0px !important;"
            >
              <tr>
                <td
                  :rowspan="
                    draFormsData.length +
                      (food_stocks_avaliability_array
                        ? food_stocks_avaliability_array.length > 0
                          ? food_stocks_avaliability_array.length + 1
                          : food_stocks_avaliability_array.length + 1
                        : 1)
                  "
                  colspan="1"
                  width="12.2%"
                >
                  <span class="rotated" style="font-weight: bold;"
                    ></span
                  >
                  <img
                    src="../../../../../../../../../../static/cluster_food_security_100px_bluebox.png"
                    height="150"
                  />
                </td>
                <td
                  v-if="
                    food_stocks_avaliability_array &&
                      food_stocks_avaliability_array.length > 0
                  "
                  v-bind:rowspan="
                    food_stocks_avaliability_array.length > 0
                      ? food_stocks_avaliability_array.length + 1
                      : 1
                  "
                  colspan="6"
                  width="20%"
                >
                  <b>Food stocks availability (HH)</b>
                </td>
                 <td v-if="food_stocks_avaliability_array" colspan="4">
                  <b>TA</b>
                </td>
                <td v-if="food_stocks_avaliability_array" colspan="4">
                  <b>Population group</b>
                </td>
                <td v-if="food_stocks_avaliability_array" colspan="2" width="10%">
                  <b>
                    <center>Male</center>
                  </b>
                </td>
                <td v-if="food_stocks_avaliability_array" colspan="2" width="10%">
                  <b>
                    <center>Female</center>
                  </b>
                </td>
                <td
                  v-if="food_stocks_avaliability_array"
                  style="font-weight:bold"
                  colspan="2"
                  width="10%"
                >
                  <b>
                    <center>Total</center>
                  </b>
                </td>
              </tr>
              <tr
                v-if="
                  food_stocks_avaliability_array &&
                    food_stocks_avaliability_array.length > 0
                "
                v-for="stock  in food_stocks_avaliability_array"
              >
               <td colspan="4">{{ stock.ta }}</td>
                <td colspan="4">{{ stock.category }}</td>
                <td colspan="2" class="right-align">
                  {{ parseInt(stock.male_HH) }}
                </td>
                <td colspan="2" class="right-align">
                  {{ parseInt(stock.female_HH) }}
                </td>
                <td style="font-weight:bold" colspan="2" class="right-align">
                  {{
                    (
                      parseInt(stock.female_HH ? stock.female_HH : 0) +
                        parseInt(stock.male_HH ? stock.male_HH : 0)
                    )
                  }}
                </td>
              </tr>
              <tr
                v-if="
                  food_stocks_avaliability_array &&
                    food_stocks_avaliability_array.length > 0
                "
              >
                <td style="font-weight:bold" colspan="14">
                  <center>Total</center>
                </td>
                <td style="font-weight:bold" colspan="2" class="right-align">
                  {{
                    numberWithCommas(
                     food_stocks_avaliability_array
                        ? food_stocks_avaliability_array
                            .map(function(item) {
                              return item.male_HH ? parseInt(item.male_HH) : 0;
                            })
                            .reduce((sum, value) => sum + value)
                        : 0
                    )
                  }}
                </td>
                <td style="font-weight:bold" colspan="2" class="right-align">
                  {{
                    numberWithCommas(
                      food_stocks_avaliability_array
                        ? food_stocks_avaliability_array
                            .map(function(item) {
                              return item.female_HH ? parseInt(item.female_HH) : 0;
                            })
                            .reduce((sum, value) => sum + value)
                        : 0
                    )
                  }}
                </td>
                <td style="font-weight:bold" colspan="2" class="right-align">
                  {{
                    numberWithCommas(
                      (food_stocks_avaliability_array
                        ? food_stocks_avaliability_array
                            .map(function(item) {
                              return item.male_HH ? parseInt(item.male_HH) : 0;
                            })
                            .reduce((sum, value) => sum + value)
                        : 0) +
                        (food_stocks_avaliability_array
                          ? food_stocks_avaliability_array
                              .map(function(item) {
                                return item.female_HH ? parseInt(item.female_HH) : 0;
                              })
                              .reduce((sum, value) => sum + value)
                          : 0)
                    )
                  }}
                </td>
              </tr>
              <tr v-for="(item, index) in draFormsDataOther">
                <td colspan="2">
                  <b>{{ item.admin3.admin3Name_en }}'s response needed</b>
                </td>
                <td colspan="18">
                  <u>
                    <strong>URGENT</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.food
                        ? item.sectors.food.urgent_response_needed
                          ? item.sectors.food.urgent_response_needed
                          : "N/A"
                        : "N/A"
                    }}
                  </li>
                  <hr style="margin:1%;" />
                  <u>
                    <strong>GENERAL</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.food
                        ? item.sectors.food.response_needed
                          ? item.sectors.food.response_needed
                          : "N/A"
                        : "N/A"
                    }}
                  </li>
                </td>
                
                 
              </tr>
            </table>
          </td>
        </tr>
        <!-- end food security -->
  
        <!-- education -->
        <tr
          v-if="item.education"
          style="margin:0;padding:0 !important;border:0px !important;"
        >
          <td colspan="16" style="margin:0;padding:0 !important;border:0px">
            <table
              width="100%"
              style="margin:0;padding:0 !important;border:0px !important;"
            >
              <tr>
                <td
                  width="12.2%"
                  :rowspan="
                    1 +
                      (impact_on_schools_Array
                        ? impact_on_schools_Array.length > 0
                          ? impact_on_schools_Array.length +
                            impact_on_schools_Array.length +
                            4
                          : 1
                        : 1) +
                      draFormsData.length
                  "
                  colspan="1"
                >
                  <span class="rotated" style="font-weight: bold;"
                    ></span
                  >
                  <img
                    src="../../../../../../../../../../static/cluster_education_100px_bluebox.png"
                    height="150"
                  />
                </td>
                <td
                  v-if="
                    impact_on_schools_Array && impact_on_schools_Array.length > 0
                  "
                  colspan="3"
                  width="20%"
                  :rowspan="
                    impact_on_schools_Array.length > 0
                      ? impact_on_schools_Array.length + 2
                      : 1
                  "
                >
                  <b>Impact on school buildings</b>
                </td>
                <td
                  v-if="
                    impact_on_schools_Array && impact_on_schools_Array.length > 0
                  "
                  colspan="2"
                  width="12.8%"
                >
                  <b>School type</b>
                </td>
                <td
                  v-if="
                    impact_on_schools_Array && impact_on_schools_Array.length > 0
                  "
                  colspan="2"
                  width="11%"
                >
                  <b>Structure type</b>
                </td>
                <td
                  v-if="
                    impact_on_schools_Array && impact_on_schools_Array.length > 0
                  "
                  colspan="2"
                  width="11%"
                >
                  <b>Roof affected</b>
                </td>
                <td
                  v-if="
                    impact_on_schools_Array && impact_on_schools_Array.length > 0
                  "
                  colspan="2"
                  width="11%"
                >
                  <b>Under water</b>
                </td>
                <td
                  v-if="
                    impact_on_schools_Array && impact_on_schools_Array.length > 0
                  "
                  colspan="2"
                  width="11%"
                >
                  <b>Extent of damage</b>
                </td>
               
              </tr>
              <tr
                v-if="
                  impact_on_schools_Array && impact_on_schools_Array.length > 0
                "
                v-for="row in impact_on_schools_Array"
              >
                <td colspan="2" width="12.8%">{{ row.school_type }}</td>
                <td colspan="2" width="11%">{{ row.structure_type }}</td>
                <td colspan="2" width="11%" class="left-align">
                  {{ numberWithCommas(row.roofs_affected) }}
                </td>
                <td colspan="2" width="11%" class="left-align">
                  {{ numberWithCommas(row.underwater) }}
                </td>
                <td colspan="2" width="11%" class="left-align">
                  
                  {{
                   
                    row.status
                    
                     
                     }}
                </td>
               
              </tr>
              <tr
                v-if="
                  impact_on_schools_Array && impact_on_schools_Array.length > 0
                "
              >
                <!-- <td style="font-weight:bold" colspan="4">
                  <b>
                    <center>Total</center>
                  </b>
                </td> -->
  
                <!-- <td
                  style="font-weight:bold"
                  colspan="2"
                  width="11%"
                  class="right-align"
                >
                  {{
                    numberWithCommas(
                      impact_on_schools_Array
                        ? impact_on_schools_Array
                            .map(function(item) {
                              return item.roofs_affected
                                ? parseInt(item.roofs_affected)
                                : 0;
                            })
                            .reduce(
                              (sum, value) => parseInt(sum) + parseInt(value)
                            )
                        : 0
                    )
                  }}
                </td>
                <td
                  style="font-weight:bold"
                  colspan="2"
                  width="11%"
                  class="right-align"
                >
                  {{
                    numberWithCommas(
                      impact_on_schools_Array
                        ? impact_on_schools_Array
                            .map(function(item) {
                              return item.underwater
                                ? parseInt(item.underwater)
                                : 0;
                            })
                            .reduce(
                              (sum, value) => parseInt(sum) + parseInt(value)
                            )
                        : 0
                    )
                  }}
                </td>
                <td
                  style="font-weight:bold"
                  colspan="2"
                  width="11%"
                  class="right-align"
                >
                  {{
                    numberWithCommas(
                      impact_on_schools_Array
                        ? impact_on_schools_Array
                            .map(function(item) {
                              return item.partially_functioning
                                ? parseInt(item.partially_functioning)
                                : 0;
                            })
                            .reduce(
                              (sum, value) => parseInt(sum) + parseInt(value)
                            )
                        : 0
                    )
                  }}
                </td>
  
                <td
                  style="font-weight:bold"
                  colspan="2"
                  width="11%"
                  class="right-align"
                >
                  {{
                    numberWithCommas(
                      impact_on_schools_Array
                        ? impact_on_schools_Array
                            .map(function(item) {
                              return item.completely_damaged
                                ? parseInt(item.completely_damaged)
                                : 0;
                            })
                            .reduce(
                              (sum, value) => parseInt(sum) + parseInt(value)
                            )
                        : 0
                    )
                  }}
                </td> -->
              </tr>
              <tr
                v-if="
                  impact_on_schools_Array && impact_on_schools_Array.length > 0
                "
              >
                <td
                  v-if="impact_on_schools_Array"
                  colspan="3"
                  width="20%"
                  :rowspan="
                    impact_on_schools_Array.length > 0
                      ? impact_on_schools_Array.length + 2
                      : 1
                  "
                >
                  <b>Impact on schools goers</b>
                </td>
                <td v-if="impact_on_schools_Array" colspan="2" width="12.8%">
                  <b>School type</b>
                </td>
                <td v-if="impact_on_schools_Array" colspan="2" width="11%">
                  <b>Structure type</b>
                </td>
                <td v-if="impact_on_schools_Array" colspan="4" width="21%">
                  <b>Males out of school</b>
                </td>
                <td v-if="impact_on_schools_Array" colspan="4" width="21%">
                  <b>Females out of school</b>
                </td>
              </tr>
              <tr
                v-if="
                  impact_on_schools_Array && impact_on_schools_Array.length > 0
                "
                v-for="row in impact_on_schools_Array"
              >
                <td colspan="2" width="12.8%">{{ row.school_type }}</td>
                <td colspan="2" width="11%">{{ row.structure_type }}</td>
                <td colspan="4" width="21%" class="right-align">
                  {{ numberWithCommas(row.females_out_of_school) }}
                </td>
                <td colspan="4" width="21%" class="right-align">
                  {{ numberWithCommas(row.males_out_of_school) }}
                </td>
              </tr>
              <tr
                v-if="
                  impact_on_schools_Array && impact_on_schools_Array.length > 0
                "
              >
                <td colspan="4" width="21%">
                  <center>
                    <b>Total</b>
                  </center>
                </td>
                <td
                  style="font-weight:bold"
                  colspan="4"
                  width="21%"
                  class="right-align"
                >
                  {{
                    numberWithCommas(
                      impact_on_schools_Array
                        ? impact_on_schools_Array
                            .map(function(item) {
                              return item.females_out_of_school
                                ? parseInt(item.females_out_of_school)
                                : 0;
                            })
                            .reduce(
                              (sum, value) => parseInt(sum) + parseInt(value)
                            )
                        : 0
                    )
                  }}
                </td>
                <td
                  style="font-weight:bold"
                  colspan="4"
                  width="21%"
                  class="right-align"
                >
                  {{
                    numberWithCommas(
                      impact_on_schools_Array
                        ? impact_on_schools_Array
                            .map(function(item) {
                              return item.males_out_of_school
                                ? parseInt(item.males_out_of_school)
                                : 0;
                            })
                            .reduce(
                              (sum, value) => parseInt(sum) + parseInt(value)
                            )
                        : 0
                    )
                  }}
                </td>
              </tr>
              <tr v-for="(item, index) in draFormsDataOther">
                <td colspan="2">
                  <b>{{ item.admin3.admin3Name_en }}'s response needed</b>
                </td>
                <td colspan="14">
                  <u>
                    <strong>URGENT</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.education
                        ? item.sectors.education.urgent_response_needed
                          ? item.sectors.education.urgent_response_needed
                          : "N/A"
                        : "N/A"
                    }}
                  </li>
                  <hr style="margin:1%;" />
                  <u>
                    <strong>GENERAL</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.education
                        ? item.sectors.education.response_needed
                          ? item.sectors.education.response_needed
                          : "N/A"
                        : "N/A"
                    }}
                  </li>
                </td>
              </tr>
            </table>
          </td>
        </tr>
        <!-- end eduction -->
        <!-- health -->
        <tr
          v-if="item.health"
          style="margin:0;padding:0 !important;border:0px !important;"
        >
          <td colspan="16" style="margin:0;padding:0 !important;border:0px">
            <table
              width="100%"
              style="margin:0;padding:0 !important;border:0px !important;"
            >
              <tr>
                <td
                  width="12.2%"
                  :rowspan="
                    (available_health_facilities
                      ? available_health_facilities.length > 0
                        ? available_health_facilities.length + 1
                        : 0
                      : 0) +
                      draFormsData.length +
                      2
                  "
                  colspan="1"
                >
                  <span class="rotated" style="font-weight: bold;"
                    ></span
                  >
                  <img
                    src="../../../../../../../../../../static/cluster_health_100px_bluebox.png"
                    height="150"
                  />
                </td>
              </tr>
              <tr
                v-if="
                  available_health_facilities &&
                    available_health_facilities.length > 0
                "
              >
                <td
                  :rowspan="
                    available_health_facilities.length > 0
                      ? available_health_facilities.length + 2
                      : 1
                  "
                  colspan="5"
                  width="20%"
                >
                  <b>Health facility availability</b>
                </td>
                <td v-if="available_health_facilities" colspan="4">
                  <b>TA</b>
                </td>
                <td v-if="available_health_facilities" colspan="2" width="25%">
                  <b>Health Facility</b>
                </td>
                <td v-if="available_health_facilities" colspan="2" width="15%">
                  <b>Status</b>
                </td>
                <!-- <td v-if="available_health_facilities" colspan="2" width="15%">
                  <b>Closed</b>
                </td> -->
              </tr>
  
              <tr
                v-if="
                  available_health_facilities &&
                    available_health_facilities.length > 0
                "
                v-for="row in available_health_facilities"
              >
                <td colspan="4">{{ row.ta }}</td>
                <td colspan="2" class="left-align">
                {{ row.facility_name }} &nbsp; {{ row.name }}
                </td>
                <td colspan="2" class="left-align">
                   {{ row.status }}
                </td>
                <!-- <td colspan="2" class="right-align">
                  {{ numberWithCommas(row.closed) }}
                </td> -->
              </tr>
  
              <tr
                v-if="
                  available_health_facilities &&
                    available_health_facilities.length > 0
                "
              >
                
                <!-- <td style="font-weight:bold" colspan="2" class="right-align">
                  {{
                    numberWithCommas(
                      available_health_facilities
                        ? available_health_facilities
                            .map(function(item) {
                              return item.closed ? parseInt(item.closed) : 0;
                            })
                            .reduce((sum, value) => sum + value)
                        : 0
                    )
                  }}
                </td> -->
              </tr>
  
              <tr
                v-if="
                  other_health_facilities && other_health_facilities.length > 0
                "
                v-for="row in other_health_facilities"
              >
                <td colspan="4">{{ row.ta }}</td>
                <td colspan="2" class="right-align">
                  {{ numberWithCommas(row.status) }}
                </td>
                <td colspan="2" class="right-align">
                  {{ numberWithCommas(row.status) }}
                </td>
                <!-- <td colspan="2" class="right-align">
                  {{ numberWithCommas(row.closed) }}
                </td> -->
              </tr>
  
              <tr
                v-if="
                  other_health_facilities && other_health_facilities.length > 0
                "
              >
                <td style="font-weight:bold" colspan="4">
                  <center>Total</center>
                </td>
                <td style="font-weight:bold" colspan="2" class="right-align">
                  <!-- {{
                    numberWithCommas(
                      other_health_facilities
                        ? other_health_facilities
                            .map(function(item) {
                              return item.partially_functioning
                                ? parseInt(item.partially_functioning)
                                : 0;
                            })
                            .reduce((sum, value) => sum + value)
                        : 0
                    )
                  }} -->
                </td>
                <td style="font-weight:bold" colspan="2" class="right-align">
                  <!-- {{
                    numberWithCommas(
                      other_health_facilities
                        ? other_health_facilities
                            .map(function(item) {
                              return item.verge_of_closing
                                ? parseInt(item.verge_of_closing)
                                : 0;
                            })
                            .reduce((sum, value) => sum + value)
                        : 0
                    )
                  }} -->
                </td>
                <td style="font-weight:bold" colspan="2" class="right-align">
                  <!-- {{
                    numberWithCommas(
                      other_health_facilities
                        ? other_health_facilities
                            .map(function(item) {
                              return item.closed ? parseInt(item.closed) : 0;
                            })
                            .reduce((sum, value) => sum + value)
                        : 0
                    )
                  }} -->
                </td>
              </tr>
  
              <tr v-for="(item, index) in draFormsData">
                <td colspan="4">
                  <b>{{ item.admin3.admin3Name_en }}'s response needed</b>
                </td>
                <td colspan="12">
                  <u>
                    <strong>URGENT</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.health
                        ? item.health.urgent_response_needed
                          ? item.health.urgent_response_needed
                          : "N/A"
                        : "N/A"
                    }}
                  </li>
                  <hr style="margin:1%;" />
                  <u>
                    <strong>GENERAL</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.health
                        ? item.health.response_needed
                          ? item.health.response_needed
                          : "N/A"
                        : "N/A"
                    }}
                  </li>
                </td>
              </tr>
            </table>
          </td>
        </tr>
        <!-- health end -->
  
        <!-- livestock -->
        <tr
          v-if="item.livelihoods"
          style="margin:0;padding:0 !important;border:0px !important;"
        >
          <td colspan="16" style="margin:0;padding:0 !important;border:0px">
            <table
              width="100%"
              style="margin:0;padding:0 !important;border:0px !important;"
            >
              <tr>
                <td
                  width="12.2%"
                  colspan="1"
                  :rowspan="
                    (livelihoods_affected
                      ? livelihoods_affected.length > 0
                        ? livelihoods_affected.length + 2
                        : livelihoods_affected.length + 1
                      : 2) + draFormsData.length
                  "
                >
                  <span class="rotated" style="font-weight: bold;"
                    ></span
                  >
                  <img
                    src="../../../../../../../../../../static/socioeconomic_livelihood_100px_bluebox.png"
                    height="150"
                  />
                </td>
                <td
                  v-if="livelihoods_affected && livelihoods_affected.length > 0"
                  :rowspan="
                    livelihoods_affected.length > 0
                      ? livelihoods_affected.length + 2
                      : 1
                  "
                  colspan="6"
                  width="20%"
                >
                  <b>Livelihoods affected (HH)</b>
                </td>
                <td v-if="livelihoods_affected" colspan="4">
                  <b>TA</b>
                </td>
                <td v-if="livelihoods_affected" colspan="2" width="15%">
                  <b>Severely affected</b>
                </td>
                <td v-if="livelihoods_affected" colspan="2" width="15%">
                  <b>Slightly affected</b>
                </td>
                <td
                  v-if="livelihoods_affected"
                  style="font-weight:bold"
                  width="15%"
                >
                  <b>
                    <center>Total</center>
                  </b>
                </td>
              </tr>
              <tr
                v-if="livelihoods_affected && livelihoods_affected.length > 0"
                v-for="row in livelihoods_affected"
              >
                <td colspan="4">{{ row.ta }}</td>
                <td width="15% " colspan="2" class="right-align">
                  {{ numberWithCommas(row.males) }}
                </td>
                <td width="15%" colspan="2" class="right-align">
                  {{ numberWithCommas(row.females) }}
                </td>
                <td width="15%" style="font-weight:bold" class="right-align">
                  {{
                    numberWithCommas(
                      parseInt(row.females ? row.females : 0) +
                        parseInt(row.males ? row.males : 0)
                    )
                  }}
                </td>
              </tr>
  
              <tr v-if="livelihoods_affected && livelihoods_affected.length > 0">
                <td colspan="4" style="font-weight:bold">
                  <center>Total</center>
                </td>
                <td colspan="2" style="font-weight:bold" class="right-align">
                  {{
                    numberWithCommas(
                      livelihoods_affected
                        ? livelihoods_affected
                            .map(function(item) {
                              return item.males ? parseInt(item.males) : 0;
                            })
                            .reduce((sum, value) => sum + value)
                        : 0
                    )
                  }}
                </td>
                <td colspan="2" style="font-weight:bold" class="right-align">
                  {{
                    numberWithCommas(
                      livelihoods_affected
                        ? livelihoods_affected
                            .map(function(item) {
                              return item.females ? parseInt(item.females) : 0;
                            })
                            .reduce((sum, value) => sum + value)
                        : 0
                    )
                  }}
                </td>
                <td colspan="2" style="font-weight:bold" class="right-align">
                  {{
                    numberWithCommas(
                      (livelihoods_affected
                        ? livelihoods_affected
                            .map(function(item) {
                              return item.males ? parseInt(item.males) : 0;
                            })
                            .reduce((sum, value) => sum + value)
                        : 0) +
                        (livelihoods_affected
                          ? livelihoods_affected
                              .map(function(item) {
                                return item.females ? parseInt(item.females) : 0;
                              })
                              .reduce((sum, value) => sum + value)
                          : 0)
                    )
                  }}
                </td>
              </tr>
              <tr v-for="(item, index) in draFormsDataOther">
                <td colspan="4">
                  <b>{{ item.admin3.admin3Name_en }}'s response needed</b>
                </td>
                <td colspan="12">
                  <u>
                    <strong>URGENT</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.livelihoods
                        ? item.sectors.livelihoods.urgent_response_needed
                          ? item.sectors.livelihoods.urgent_response_needed
                          : "N/A"
                        : "N/A"
                    }}
                  </li>
                  <hr style="margin:1%;" />
                  <u>
                    <strong>GENERAL</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.livelihoods
                        ? item.sectors.livelihoods.response_needed
                          ? item.sectors.livelihoods.response_needed
                          : "N/A"
                        : "N/A"
                    }}
                  </li>
                </td>
              </tr>
            </table>
          </td>
        </tr>
        <!-- livelihoods end -->
  
        <!-- Nutrititon -->
  
        <tr
          v-if="item.nutrition"
          style="margin:0;padding:0 !important;border:0px !important;"
        >
          <td colspan="16" style="margin:0;padding:0 !important;border:0px">
            <table
              width="100%"
              style="margin:0;padding:0 !important;border:0px !important;"
            >
              <tr v-if="affected_population">
                <td
                  width="12.2%"
                  :rowspan="
                    (nutritionAffected_populationArray
                      ? nutritionAffected_populationArray.length > 0
                        ? nutritionAffected_populationArray.length + 1
                        : nutritionAffected_populationArray.length + 1
                      : 1) + draFormsData.length
                  "
                  colspan="1"
                >
                  <span class="rotated" style="font-weight: bold;"
                    ></span
                  >
                  <img
                    src="../../../../../../../../../../static/cluster_nutrition_100px_bluebox.png"
                    height="150"
                  />
                </td>
                <td
                  :rowspan="
                    nutritionAffected_populationArray.length > 0
                      ? nutritionAffected_populationArray.length + 1
                      : nutritionAffected_populationArray.length + 1
                  "
                  colspan="6"
                  width="20%"
                >
                  <b>Affected Population</b>
                </td>
  
                <td colspan="4">
                  <b>Category</b>
                </td>
                <td colspan="2" width="10%">
                  <b>
                    <center>Male</center>
                  </b>
                </td>
                <td colspan="2" width="10%">
                  <b>
                    <center>Female</center>
                  </b>
                </td>
                <td style="font-weight:bold" width="10%">
                  <b>
                    <center>Total</center>
                  </b>
                </td>
              </tr>
              <tr
                v-if="
                  nutritionAffected_populationArray &&
                    nutritionAffected_populationArray.length > 0
                "
                v-for="row in nutritionAffected_populationArray"
              >
                <td colspan="4">{{ row.name }}</td>
                <td colspan="2" class="right-align">
                  {{ numberWithCommas(row.affected_males) }}
                </td>
                <td colspan="2" class="right-align">
                  {{ numberWithCommas(row.affected_females) }}
                </td>
                <td style="font-weight:bold" class="right-align">
                  {{
                    numberWithCommas(
                      parseInt(row.affected_males ? row.affected_males : 0) +
                        parseInt(row.affected_females ? row.affected_females : 0)
                    )
                  }}
                </td>
              </tr>
  
              <tr v-for="(item, index) in draFormsDataOther">
                <td colspan="4">
                  <b>{{ item.admin3.admin3Name_en }}'s response needed</b>
                </td>
                <td colspan="12">
                  <u>
                    <strong>URGENT</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.nutrition
                        ? item.sectors.nutrition.urgent_response_needed
                          ? item.sectors.nutrition.urgent_response_needed
                          : "N/A"
                        : "N/A"
                    }}
                  </li>
                  <hr style="margin:1%;" />
                  <u>
                    <strong>GENERAL</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.nutrition
                        ? item.sectors.nutrition.response_needed
                          ? item.sectors.nutrition.response_needed
                          : "N/A"
                        : "N/A"
                    }}
                  </li>
                </td>
              </tr>
            </table>
          </td>
        </tr>
  
        <!-- Protection -->
        <tr
          v-if="item.protection"
          style="margin:0;padding:0 !important;border:0px !important;"
        >
          <td colspan="16" style="margin:0;padding:0 !important;border:0px">
            <table
              width="100%"
              style="margin:0;padding:0 !important;border:0px !important;"
            >
              <tr v-if="impact_on_vulnerable_persons">
                <td
                  width="12.2%"
                  :rowspan="
                    (impact_on_vulnerable_persons.length > 0
                      ? impact_on_vulnerable_persons.length + 1
                      : impact_on_vulnerable_persons.length) +
                      draFormsData.length +
                      1
                  "
                >
                  <span class="rotated" style="font-weight: bold;"
                    ></span
                  >
                  <img
                    src="../../../../../../../../../../static/cluster_protection_100px_bluebox.png"
                    height="150"
                  />
                </td>
                <td
                  :rowspan="
                    impact_on_vulnerable_persons.length > 0
                      ? impact_on_vulnerable_persons.length + 2
                      : 1 + impact_on_vulnerable_persons.length
                  "
                  colspan="6"
                  width="20%"
                >
                  <b>Impact on vulnerable population</b>
                </td>
                <td colspan="4">
                  <b>TA</b>
                </td>
                <td colspan="2">
                  <b>
                    <center>Male</center>
                  </b>
                </td>
                <td colspan="2">
                  <b>
                    <center>Female</center>
                  </b>
                </td>
                <td style="font-weight:bold" colspan="2">
                  <b>
                    <center>Total</center>
                  </b>
                </td>
              </tr>
              <tr
                v-if="
                  impact_on_vulnerable_persons &&
                    impact_on_vulnerable_persons.length > 0
                "
                v-for="row in impact_on_vulnerable_persons"
              >
                <td colspan="4">{{ row.ta }}</td>
                <td colspan="2" width="10%" class="right-align">
                  {{ numberWithCommas(row.males) }}
                </td>
                <td colspan="2" width="10%" class="right-align">
                  {{ numberWithCommas(row.females) }}
                </td>
                <td
                  colspan="2"
                  style="font-weight:bold"
                  width="10%"
                  class="right-align"
                >
                  {{
                    numberWithCommas(
                      (row.females ? parseInt(row.females) : 0) +
                        (row.males ? parseInt(row.males) : 0)
                    )
                  }}
                </td>
              </tr>
  
              <tr
                v-if="
                  impact_on_vulnerable_persons &&
                    impact_on_vulnerable_persons.length > 0
                "
              >
                <td style="font-weight:bold" colspan="4">
                  <center>Total</center>
                </td>
                <td style="font-weight:bold" colspan="2" class="right-align">
                  {{
                    numberWithCommas(
                      impact_on_vulnerable_persons
                        ? impact_on_vulnerable_persons
                            .map(function(item) {
                              return item.males ? parseInt(item.males) : 0;
                            })
                            .reduce((sum, value) => sum + value)
                        : 0
                    )
                  }}
                </td>
                <td style="font-weight:bold" colspan="2" class="right-align">
                  {{
                    numberWithCommas(
                      impact_on_vulnerable_persons
                        ? impact_on_vulnerable_persons
                            .map(function(item) {
                              return item.females ? parseInt(item.females) : 0;
                            })
                            .reduce((sum, value) => sum + value)
                        : 0
                    )
                  }}
                </td>
                <td style="font-weight:bold" colspan="2" class="right-align">
                  {{
                    numberWithCommas(
                      (impact_on_vulnerable_persons
                        ? impact_on_vulnerable_persons
                            .map(function(item) {
                              return item.males ? parseInt(item.males) : 0;
                            })
                            .reduce((sum, value) => sum + value)
                        : 0) +
                        (impact_on_vulnerable_persons
                          ? impact_on_vulnerable_persons
                              .map(function(item) {
                                return item.females ? parseInt(item.females) : 0;
                              })
                              .reduce((sum, value) => sum + value)
                          : 0)
                    )
                  }}
                </td>
              </tr>
  
              <tr v-for="(item, index) in draFormsDataOther">
                <td colspan="4">
                  <b>{{ item.admin3.admin3Name_en }}'s response needed</b>
                </td>
                <td colspan="12">
                  <u>
                    <strong>URGENT</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.protection
                        ? item.sectors.protection.urgent_response_needed
                          ? item.sectors.protection.urgent_response_needed
                          : "N/A"
                        : "N/A"
                    }}
                  </li>
                  <hr style="margin:1%;" />
                  <u>
                    <strong>GENERAL</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.protection
                        ? item.sectors.protection.response_needed
                          ? item.sectors.protection.response_needed
                          : "N/A"
                        : "N/A"
                    }}
                  </li>
                </td>
              </tr>
            </table>
          </td>
        </tr>
        <!-- protcetion end -->
  
        <!-- Wash -->
        <tr
          v-if="item.wash"
          style="margin:0;padding:0 !important;border:0px !important;"
        >
          <td colspan="16" style="margin:0;padding:0 !important;border:0px">
            <table
              width="100%"
              style="margin:0;padding:0 !important;border:0px !important;"
            >
              <tr>
                <td :rowspan="8 + draFormsData.length" colspan="1" width="12.2%">
                  <span class="rotated" style="font-weight: bold;"></span>
                  <img
                    src="../../../../../../../../../../static/cluster_WASH_100px_bluebox.png"
                    height="150"
                  />
                </td>
                <td rowspan="2" colspan="6">
                  <b>Households without safe water</b>
                </td>
                <td colspan="3" width="10%">
                  <b>
                    <center>Male</center>
                  </b>
                </td>
                <td colspan="3" width="10%">
                  <b>
                    <center>Female</center>
                  </b>
                </td>
                <td style="font-weight:bold" colspan="3" width="10%">
                  <b>
                    <center>Total</center>
                  </b>
                </td>
              </tr>
  
              <tr v-if="washArray && washArray.length > 0">
                <td colspan="3" class="right-align">
                  {{
                    numberWithCommas(
                      washArray
                        .map(function(item) {
                          return item.with_safe_water_mhh
                            ? parseInt(item.with_safe_water_mhh)
                            : 0;
                        })
                        .reduce((sum, value) => sum + value)
                    )
                  }}
                </td>
                <td colspan="3" class="right-align">
                  {{
                    numberWithCommas(
                      washArray
                        .map(function(item) {
                          return item.with_safe_water_fhh
                            ? parseInt(item.with_safe_water_fhh)
                            : 0;
                        })
                        .reduce((sum, value) => sum + value)
                    )
                  }}
                </td>
                <td colspan="3" style="font-weight:bold" class="right-align">
                  {{
                    numberWithCommas(
                      washArray
                        .map(function(item) {
                          return item.with_safe_water_fhh
                            ? parseInt(item.with_safe_water_fhh)
                            : 0;
                        })
                        .reduce((sum, value) => sum + value) +
                        washArray
                          .map(function(item) {
                            return item.with_safe_water_mhh
                              ? parseInt(item.with_safe_water_mhh)
                              : 0;
                          })
                          .reduce((sum, value) => sum + value)
                    )
                  }}
                </td>
              </tr>
  
              <tr v-if="washArray && washArray.length > 0">
                <td rowspan="2" colspan="6">
                  <b>Households without access to toilets</b>
                </td>
                <td colspan="3">
                  <b>
                    <center>Male</center>
                  </b>
                </td>
                <td colspan="3">
                  <b>
                    <center>Female</center>
                  </b>
                </td>
                <td colspan="3">
                  <b>
                    <center>Total</center>
                  </b>
                </td>
              </tr>
              <tr v-if="washArray && washArray.length > 0">
                <td style="font-weight:bold" colspan="3" class="right-align">
                  {{
                    numberWithCommas(
                      washArray
                        .map(function(item) {
                          return item.access_to_toilets_mhh
                            ? parseInt(item.access_to_toilets_mhh)
                            : 0;
                        })
                        .reduce((sum, value) => sum + value)
                    )
                  }}
                </td>
                <td style="font-weight:bold" colspan="3" class="right-align">
                  {{
                    numberWithCommas(
                      washArray
                        .map(function(item) {
                          return item.access_to_toilets_fhh
                            ? parseInt(item.access_to_toilets_fhh)
                            : 0;
                        })
                        .reduce((sum, value) => sum + value)
                    )
                  }}
                </td>
                <td style="font-weight:bold" colspan="3" class="right-align">
                  {{
                    numberWithCommas(
                      washArray
                        .map(function(item) {
                          return item.access_to_toilets_fhh
                            ? parseInt(item.access_to_toilets_fhh)
                            : 0;
                        })
                        .reduce((sum, value) => sum + value) +
                        washArray
                          .map(function(item) {
                            return item.access_to_toilets_mhh
                              ? parseInt(item.access_to_toilets_mhh)
                              : 0;
                          })
                          .reduce((sum, value) => sum + value)
                    )
                  }}
                </td>
              </tr>
  
              <tr v-if="washArray && washArray.length > 0">
                <td rowspan="2" colspan="6">
                  <b>Households have risk of contamination</b>
                </td>
                <td colspan="3">
                  <b>
                    <center>Male</center>
                  </b>
                </td>
                <td colspan="3">
                  <b>
                    <center>Female</center>
                  </b>
                </td>
                <td style="font-weight:bold" colspan="3">
                  <center>Total</center>
                </td>
              </tr>
              <tr v-if="washArray && washArray.length > 0">
                <td style="font-weight:bold" colspan="3" class="right-align">
                  {{
                    numberWithCommas(
                      washArray
                        .map(function(item) {
                          return item.risk_contamination_mhh
                            ? parseInt(item.risk_contamination_mhh)
                            : 0;
                        })
                        .reduce((sum, value) => sum + value)
                    )
                  }}
                </td>
                <td style="font-weight:bold" colspan="3" class="right-align">
                  {{
                    numberWithCommas(
                      washArray
                        .map(function(item) {
                          return item.risk_contamination_fhh
                            ? parseInt(item.risk_contamination_fhh)
                            : 0;
                        })
                        .reduce((sum, value) => sum + value)
                    )
                  }}
                </td>
                <td style="font-weight:bold" colspan="3" class="right-align">
                  {{
                    numberWithCommas(
                      washArray
                        .map(function(item) {
                          return item.risk_contamination_fhh
                            ? parseInt(item.risk_contamination_fhh)
                            : 0;
                        })
                        .reduce((sum, value) => sum + value) +
                        washArray
                          .map(function(item) {
                            return item.risk_contamination_fhh
                              ? parseInt(item.risk_contamination_fhh)
                              : 0;
                          })
                          .reduce((sum, value) => sum + value)
                    )
                  }}
                </td>
              </tr>
  
              <tr v-for="(item, index) in draFormsDataOther">
                <td colspan="2" width="20%">
                  <b>{{ item.admin3.admin3Name_en }}'s response needed</b>
                </td>
                <td colspan="14">
                  <u>
                    <strong>URGENT</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.wash
                        ? item.sectors.wash.urgent_response_needed
                          ? item.sectors.wash.urgent_response_needed
                          : "N/A"
                        : "N/A"
                    }}
                  </li>
                  <hr style="margin:1%;" />
                  <u>
                    <strong>GENERAL</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.wash
                        ? item.sectors.wash.response_needed
                          ? item.sectors.wash.response_needed
                          : "N/A"
                        : "N/A"
                    }}
                  </li>
                </td>
              </tr>
            </table>
          </td>
        </tr>
        <!-- wash -->
      </table>
  
      <table
        class="col-md-11"
        v-if="numberOfTAs > 0"
        width="100%"
        style="text-weight:bold;text-size:120%"
      >
        <tr>
          <td style="text-align:center;">
            <b-button
              :style="{
                'max-height': '40px',
                height: '40px',
                width: '200px',
                'max-width': '150px',
                'font-size': '12px',
                padding: '5px'
              }"
              @click="handleSubmit(dinrFormsData)"
              data-toggle="tooltip"
              title="Submit for Approval"
              variant="success"
              class="py-0 px-1"
            >
              Submit</b-button
            >
          </td>
        </tr>
      </table>
  
      <table
        class="col-md-11"
        v-if="hasImages(dinrFormsData)"
        width="80%"
        style="text-weight:bold;text-size:120%"
      >
        <tr>
          <th><strong>DISASTER PHOTOS</strong></th>
        </tr>
        <tr>
          <td colspan="2">
            <div class="row">
              <div class="col-md-12">
                <div class="card" style="">
                  <div class="uploaded-images">
                    <!--images  -->
  
                    <div
                      class="image-wrapper"
                      v-for="(image, i) in dinrFormsData.disasterImages"
                      :key="i"
                    >
                      <div class="inner">
                        <img
                          :src="renderImage(image.location)"
                          style="margin:8px; height:340px;width:340px;"
                          class="img-responsive img-thumbnail"
                        />
                      </div>
                      <div class="caption box" style="margin-left:12px;">
                        {{ image.caption }}
                      </div>
                    </div>
  
                    <!-- endImage -->
                  </div>
                </div>
              </div>
            </div>
          </td>
        </tr>
      </table>
    </div>
  </template>
  <script>
  import { History } from "../../../../../../../api/History";
  import { draforms } from "../../../../../../../api/forms/draforms";
  import submitforapproval from "../../../../../../mixins/submitForApproval";
  
  import downloadexcel from "vue-json-excel";
  import { mapActions } from "vuex";
  import moment from "moment";
  import fs from "fs";
  
  export default {
    components: {
      downloadexcel
    },
    mixins: [submitforapproval],
    data() {
      return {
        uploadedImages: [],
        isLoaded: false,
        draFormsDataOther: [],
        food_stocks_avaliability_array: [],
        impact_on_schools_array: [],
        nutritionAffected_populationArray: [],
        numberOfTAs: "",
        dinrFormsData: {},
        villagesArray: [],
        bufferDraFormsData: [],
        draFormsData: [],
        download: [],
        TAarray: [],
        downloadData: [],
        otherData: [],
        vhhousesvalue: "",
        numberOfTAs: "",
        shelterArray: [],
        displacedArray: [],
        agricultureArray: [],
        cropDamageLossArray: [],
        healthArray: [],
        washArray: [],
        livelihoodsArray: [],
        protectionArray: [],
        foodArray: [],
        nutritionArray: [],
        educationArray: [],
        environmentArray: [],
        people_without_shelter: [],
        PeopleAffectedrows: [],
        PeopleAffectedrowsD: [],
        PeopleInjuredrows: [],
        PeopleDeadrows: [],
        crops_damaged: [],
        food_item_damage: [],
        food_stocks_avaliability: [],
        impact_on_schools: [],
        available_health_facilities: [],
        other_health_facilities: [],
        livelihoods_affected: [],
        affected_population: [],
        impact_on_vulnerable_persons: [],
        livelihood_type: []
      };
    },
    methods: {
      hasImages(dinr) {
        if (Object.keys(dinr).includes("disasterImages")) {
          return dinr.disasterImages.length > 0;
        }
  
        return false;
      },
      renderImage(location) {
        const imageType = location.split(".").pop();
        const base64Data = fs.readFileSync(location, { encoding: "base64" });
        const dataURI = `data:image/${imageType};base64,${base64Data}`;
  
        return dataURI;
      },
      numberWithCommas(number) {
        try {
          var parts = number.toString().split(".");
          parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
          return parts.join(".");
        } catch (e) {}
      },
      arrayAggregator(arrayData) {
        var result = [
          arrayData.reduce((acc, n) => {
            for (var prop in n) {
              if (acc.hasOwnProperty(prop)) {
                if (isNaN(acc[prop])) {
                  acc[prop] = acc[prop] + "," + n[prop];
                } else acc[prop] = parseFloat(acc[prop]) + parseFloat(n[prop]);
              } else acc[prop] = n[prop];
            }
            return acc;
          }, {})
        ];
      },
      dateFormate(dateData) {
        const cur_date = dateData;
        const formattedDate = moment(cur_date).format("DD/MM/YYYY");
  
        return formattedDate;
      },
      sumArraysShelter(
        data,
        objectname,
        member,
        males,
        females,
        cMales,
        cFemales
      ) {
        // alert(data.length);
        
  
        for (let i = 0; i < data.length; i++) {
          try {
            if (objectname == "displaced" && i > 0) {
              member = member.replace("D", "");
            }
            let sumFemales = 0;
            let sumMales = 0;
            let x = data[i];
            let actualObject = x[objectname];
  
            actualObject[member].forEach(function(obj) {
              sumFemales += parseInt(obj[females] ? obj[females] : 0);
              sumFemales += parseInt(obj[cFemales] ? obj[cFemales] : 0);
              sumMales += parseInt(obj[males] ? obj[males] : 0);
              sumMales += parseInt(obj[cMales] ? obj[cMales] : 0);
            });
  
            if (objectname == "displaced") {
              member = member + "D";
            }
  
            this[member].push({
              ta: data[i].admin3.admin3Name_en,
              females: sumFemales,
              males: sumMales
            });
          } catch (error) {}
        }
      },
      sumArrays(data, objectname, member, males, females) {
        // alert(data.length);
  
        for (let i = 0; i < data.length; i++) {
        
          try {
            if (objectname == "displaced" && i > 0) {
              member = member.replace("D", "");
            }
            let sumFemales = 0;
            let sumMales = 0;
            let x = data[i];
            let actualObject = x[objectname];
  
            actualObject[member].forEach(function(obj) {
              sumFemales += parseInt(obj[females] ? obj[females] : 0);
              sumMales += parseInt(obj[males] ? obj[males] : 0);
            });
  
            if (objectname == "displaced") {
              member = member + "D";
            }
  
            this[member].push({
              ta: data[i].admin3.admin3Name_en,
              females: sumFemales,
              males: sumMales
            });
          } catch (error) {}
        }
      },
  
      sumEducationArrays(
        data,
        objectname,
        member,
        roofs_affected,
        partially_functioning,
        underwater,
        completely_damaged,
        females_out_of_school,
        males_out_of_school
      ) {
        // alert(data.length);
  
        for (let i = 0; i < data.length; i++) {
          
          try {
            let sum1 = 0,
              sum2 = 0,
              sum3 = 0,
              sum4 = 0,
              sum5 = 0,
              sum6 = 0;
  
            let x = data[i];
            let actualObject = x[objectname];
  
            actualObject[member].forEach(function(obj) {
              sum1 += parseInt(obj[roofs_affected] ? obj[roofs_affected] : 0);
              sum2 += parseInt(
                obj[partially_functioning] ? obj[partially_functioning] : 0
              );
              sum3 += parseInt(obj[underwater] ? obj[underwater] : 0);
              sum4 += parseInt(
                obj[completely_damaged] ? obj[completely_damaged] : 0
              );
              sum5 += parseInt(
                obj[females_out_of_school] ? obj[females_out_of_school] : 0
              );
              sum6 += parseInt(
                obj[males_out_of_school] ? obj[males_out_of_school] : 0
              );
            });
  
            this[member].push({
              ta: data[i].admin3.admin3Name_en,
              roofs_affected: sum1,
              partially_functioning: sum2,
              underwater: sum3,
              completely_damaged: sum4,
              females_out_of_school: sum5,
              males_out_of_school: sum6
            });
          } catch (error) {}
        }
      },
  
      // sumHealthArrays(
      //   data,
      //   objectname,
      //   member,
      //   partially_functioning,
      //   verge_of_closing,
      //   closed
      // ) {
      //   // alert(data.length);
  
      //   for (let i = 0; i < data.length; i++) {
      //     console.log(objectname, member);
      //     try {
      //       let sum1 = 0,
      //         sum2 = 0,
      //         sum3 = 0;
  
      //       let x = data[i];
      //       let actualObject = x[objectname];
  
      //       actualObject[member].forEach(function(obj) {
      //         sum1 += parseInt(
      //           obj[partially_functioning] ? obj[partially_functioning] : 0
      //         );
      //         sum2 += parseInt(obj[verge_of_closing] ? obj[verge_of_closing] : 0);
      //         sum3 += parseInt(obj[closed] ? obj[closed] : 0);
      //       });
  
      //       this[member].push({
      //         ta: data[i].admin3.admin3Name_en,
      //         verge_of_closing: sum1,
      //         partially_functioning: sum2,
      //         closed: sum3
      //       });
      //     } catch (error) {}
      //   }
      // },
      //alert(JSON.stringify(this.PeopleAffectedrows));
  
      printdiv(printpage) {
        var headstr = "<html><head><title></title></head><body>";
        var footstr = "</body>";
        var newstr = document.all.item(printpage).innerHTML;
        var oldstr = document.body.innerHTML;
        document.body.innerHTML = headstr + newstr + footstr;
        window.print();
        document.body.innerHTML = oldstr;
        location.reload();
        return false;
      },
      formatdate(data) {
        const cur_date = data;
        const formattedDate = moment(cur_date).format("YYYY/MM/DD hh:mm");
  
        return formattedDate;
      },
      ...mapActions({
        getDinrForm: "getDinrForm",
        getDraFormsBydinrFormId: "getDraFormsBydinrFormId"
      })
    },
    computeLogo() {
      return "../../../../static/logo.png";
    },
    async mounted() {
  
      
      //Get DINR Form
      this.shelterArray = [];
      this.displacedArray = [];
      this.agricultureArray = [];
      this.healthArray = [];
      this.washArray = [];
      this.livelihoodsArray = [];
      this.protectionArray = [];
      this.foodArray = [];
      this.nutritionArray = [];
      this.educationArray = [];
      this.cropDamageLossArray = [];
      this.environmentArray = [];
      this.draFormsData = [];
  
    
  
      //initialise number TAs in DNIR
      this.numberOfTAs = 0;
  
      let count = 0;
      //Get DRA Form
      await this.getDinrForm({ _id: this.$route.params.id }).then(response => {
        this.dinrFormsData = response;
  
      
  
        ///////////
        if ("disasterImages" in response) {
          this.uploadedImages = response.disasterImages;
         
        }
      });
  
      this.food_stocks_avaliability_array = [];
      this.nutritionAffected_populationArray = [];
      this.agricultureLivelihoodArray = [],
      this.impact_on_schools_Array = [];
      var self = this;
  
      await this.getDraFormsBydinrFormId({ _id: this.$route.params.id }).then(
        response => {
          this.bufferDraFormsData = response;
  
          this.downloadData.dinrform = {};
  
          this.bufferDraFormsData.forEach(item => {
            this.downloadData.all = item;
  
            this.TAarray.push(item.admin3.admin3Name_en.trim());
  
            for (let i in item.villages) {
              item.villages[i] && item.villages[i].name
                ? this.villagesArray.push(item.villages[i].name)
                : "";
            }
  
            this.TAarray = this.TAarray.sort();
            this.villagesArray = this.villagesArray
              .join()
              .replace(/ /g, "")
              .split(",")
              .sort()
              .filter(function(item, pos, self) {
                return self.indexOf(item) == pos;
              });
  
            this.villagesArray = this.villagesArray.sort();
  
            if (!item.sectors) {
              return;
            }
  
            this.numberOfTAs++;
  
            this.draFormsDataOther.push(item);
            var preparedData = {};
            preparedData.sectors = item.sectors;
            preparedData.sectors.admin3 = item.admin3;
            //console.log(preparedData.sectors);
            this.draFormsData.push(preparedData.sectors);
            this.shelterArray.push(item.sectors.shelter);
            this.displacedArray.push(item.sectors.displaced);
            this.agricultureArray.push(item.sectors.agriculture);
            this.healthArray.push(item.sectors.health);
            
             if (item.sectors.food && item.sectors.food.food_stocks_avaliability)
              for (
                let i = 0;
                i < item.sectors.food.food_stocks_avaliability.length;
                i++
              ) {
                
                item.sectors.food.food_stocks_avaliability[i].ta =
                  item.admin3.admin3Name_en;
                item.sectors.food.food_stocks_avaliability[i]
                  ? this.food_stocks_avaliability_array.push(
                      item.sectors.food.food_stocks_avaliability[i]
                    )
                  : {};
              }
  
            if (
              item.sectors.nutrition &&
              item.sectors.nutrition.affected_population
            )
              for (
                let i = 0;
                i < item.sectors.nutrition.affected_population.length;
                i++
              ) {
                item.sectors.nutrition.affected_population[i].ta =
                  item.admin3.admin3Name_en;
                item.sectors.nutrition.affected_population[i]
                  ? this.nutritionAffected_populationArray.push(
                      item.sectors.nutrition.affected_population[i]
                    )
                  : {};
              }
                
              if (
              item.sectors.agriculture &&
              item.sectors.agriculture.livelihoods_affected
            )
              for (
                let i = 0;
                i < item.sectors.agriculture.livelihoods_affected.length;
                i++
              ) {
                item.sectors.agriculture.livelihoods_affected[i].ta =
                  item.admin3.admin3Name_en;
                item.sectors.agriculture.livelihoods_affected[i]
                  ? this.agricultureLivelihoodArray.push(
                      item.sectors.agriculture.livelihoods_affected[i]
                    )
                  : {};
              }
  
            if (
              item.sectors.education &&
              item.sectors.education.impact_on_schools
            )
              for (
                let i = 0;
                i < item.sectors.education.impact_on_schools.length;
                i++
              ) {
                item.sectors.education.impact_on_schools[i]
                  ? self.impact_on_schools_Array.push(
                      item.sectors.education.impact_on_schools[i]
                    )
                  : {};
              }
  //console.log(this.impact_on_schools_Array,"Yesssssssssssssssssssssssssss")
                if (
              item.sectors.health &&
              item.sectors.health.available_health_facilities
            )
              for (
                let i = 0;
                i < item.sectors.health.available_health_facilities.length;
                i++
              ) {
                if( i == 0){
                  item.sectors.health.available_health_facilities[i].ta =
                  item.admin3.admin3Name_en;
                item.sectors.health.available_health_facilities[i]
                  ? self.available_health_facilities.push(
                      item.sectors.health.available_health_facilities[i]
                    )
                  : {};
                } else{
                  item.sectors.health.available_health_facilities[i]
                  ? self.available_health_facilities.push(
                      item.sectors.health.available_health_facilities[i]
                    )
                  : {};
                }
                 
                
              }
  
            this.washArray.push(item.sectors.wash);
            this.livelihoodsArray.push(item.sectors.livelihoods);
            this.protectionArray.push(item.sectors.protection);
            this.foodArray.push(item.sectors.food);
            this.nutritionArray.push(item.sectors.nutrition);
            this.educationArray.push(item.sectors.education);
            this.environmentArray.push(item.sectors.environment);
          });
  
          var food_stocks_avaliability_arrayResult = [];
          var nutritionAffected_populationArrayResult = [];
          var impact_on_schools_ArrayResult = [];
  
          // this.food_stocks_avaliability_array.reduce(function(res, value) {
          //   if (!res[value.category]) {
          //     res[value.category] = {
          //       ta: value.ta,
          //       category: value.category,
          //       female_HH: 0,
          //       male_HH: 0
          //     };
          //     food_stocks_avaliability_arrayResult.push(res[value.category]);
          //   }
          //   res[value.category].female_HH += value.female_HH;
          //   res[value.category].male_HH += value.male_HH;
          //   return res;
          // }, {});
  
          // this.food_stocks_avaliability_array = food_stocks_avaliability_arrayResult;
  
          this.nutritionAffected_populationArray.reduce(function(res, value) {
            if (!res[value.name]) {
              res[value.name] = {
                ta: value.ta,
                name: value.name,
                affected_females: 0,
                affected_males: 0
              };
              nutritionAffected_populationArrayResult.push(res[value.name]);
            }
            res[value.name].affected_females += value.affected_females;
            res[value.name].affected_males += value.affected_males;
            return res;
          }, {});
  
          this.nutritionAffected_populationArray = nutritionAffected_populationArrayResult;
         
          // this.impact_on_schools_Array.reduce(function(res, value) {
          //   if (!res[value.structure_type]) {
          //     res[value.structure_type] = {
          //       school_type: value.school_type ? value.school_type : "",
          //       structure_type: value.structure_type ? value.structure_type : "",
          //       partially_functioning: 0,
          //       completely_damaged: 0,
          //       roofs_affected: 0,
          //       underwater: 0,
          //       females_out_of_school: 0,
          //       males_out_of_school: 0
          //     };
          //     impact_on_schools_ArrayResult.push(res[value.structure_type]);
          //   }
          //   res[value.structure_type].partially_functioning += parseInt(
          //     value.partially_functioning ? value.partially_functioning : 0
          //   );
          //   res[value.structure_type].completely_damaged += parseInt(
          //     value.completely_damaged ? value.completely_damaged : 0
          //   );
  
          //   res[value.structure_type].roofs_affected += parseInt(
          //     value.roofs_affected ? value.roofs_affected : 0
          //   );
          //   res[value.structure_type].underwater += parseInt(
          //     value.underwater ? value.underwater : 0
          //   );
  
          //   res[value.structure_type].females_out_of_school += parseInt(
          //     value.females_out_of_school ? value.females_out_of_school : 0
          //   );
          //   res[value.structure_type].males_out_of_school += parseInt(
          //     value.males_out_of_school ? value.males_out_of_school : 0
          //   );
          //   return res;
          // }, {});
  
         // this.impact_on_schools_Array = impact_on_schools_ArrayResult;
  
         // console.log(this.impact_on_schools_Array,"hhhhhhhhhhhhhhhhhhhhhhhhhhhh");
  
          this.villagesArray = this.villagesArray
            .join()
            .split(",")
            .filter(function(item, pos, self) {
              return self.indexOf(item) == pos;
            });
        
             
  
  
  
  
           // this.available_health_facilities = this.healthArray[0].available_health_facilities;
  
          this.sumArrays(
            this.draFormsData,
            "shelter",
            "people_without_shelter",
            "without_shelter_males",
            "without_shelter_females"
          );
          this.sumArraysShelter(
            this.draFormsData,
            "shelter",
            "PeopleInjuredrows",
            "adults_males_injured",
            "adults_females_injured",
            "children_males_injured",
            "children_females_injured"
          );
  
          this.sumArraysShelter(
            this.draFormsData,
            "shelter",
            "PeopleDeadrows",
            "adults_males_dead",
            "adults_females_dead",
            "children_males_dead",
            "children_females_dead"
          );
          this.sumArraysShelter(
            this.draFormsData,
            "shelter",
            "PeopleMissingrows",
            "adults_males_missing",
            "adults_females_missing",
            "children_males_missing",
            "children_females_missing"
          );
          this.sumArrays(
            this.draFormsData,
            "shelter",
            "PeopleAffectedrows",
            "damaged_mhh",
            "damaged_fhh"
          );
  
          this.sumArrays(
            this.draFormsData,
            "displaced",
            "PeopleAffectedrows",
            "number_displaced_by_gender_mhh",
            "number_displaced_by_gender_fhh"
          );
  
          this.sumArrays(
            this.draFormsData,
            "shelter",
            "PeopleInjuredrows",
            "people_injured_males",
            "people_injured_females"
          );
  
          this.sumArrays(
            this.draFormsData,
            "shelter",
            "PeopleDeadrows",
            "males_dead",
            "females_dead"
          );
  
          this.sumArrays(
            this.draFormsData,
            "agriculture",
            "crops_damaged",
            "hectares_submerged",
            "hectares_washed_away"
          );
  
          this.sumArrays(
            this.draFormsData,
            "agriculture",
            "livelihoods_affected",
            "livelihood_type"
          ); 
  
          this.sumArrays(
            this.draFormsData,
            "agriculture",
            "food_item_damage",
            "number_of_kilos",
            "food_item_name"
          );
  
          this.sumArrays(
            this.draFormsData,
            "food",
            "food_stocks_avaliability",
            "male_HH",
            "female_HH"
          );
  
          this.sumEducationArrays(
            this.draFormsData,
            "education",
            "impact_on_schools",
            "roofs_affected",
            "partially_functioning",
            "underwater",
            "completely_damaged",
            "females_out_of_school",
            "males_out_of_school"
          );
  
          // this.sumHealthArrays(
          //   this.draFormsData,
          //   "health",
          //   "available_health_facilities",
          //   "partially_functioning",
          //   "verge_of_closing",
          //   "closed"
          // );
  
          // this.sumHealthArrays(
          //   this.draFormsData,
          //   "health",
          //   "other_health_facilities",
          //   "partially_functioning",
          //   "verge_of_closing",
          //   "closed"
          // );
  
          // this.sumArrays(
          //   this.draFormsData,
          //   "livelihoods",
          //   "livelihoods_affected",
          //   "severely_affected",
          //   "slightly_affected"
          // );
  
          this.sumArrays(
            this.draFormsData,
            "nutrition",
            "affected_population",
            "affected_males",
            "affected_females"
          );
  
          this.sumArrays(
            this.draFormsData,
            "protection",
            "impact_on_vulnerable_persons",
            "impacted_males",
            "impacted_females"
          );
        }
      );
    }
  };
  </script>
  
  <style lang="stylus" scoped>
  table {
    border-collapse: collapse;
    width: 100%;
    margin: 0 auto;
  }
  
  .verticalize {
    transform: rotate(270deg);
  }
  
  .rotated {
    writing-mode: tb-rl;
    transform: rotate(-180deg);
    color: Teal;
    padding-left: 30px;
  }
  
  table, th, td {
    border: 1px solid black;
    margin-top: 1%;
  }
  
  td {
    padding: 1%;
  }
  
  .noborder {
    border: 0;
  }
  
  .qcont:first-letter {
    text-transform: capitalize;
  }
  
  .right-align {
    text-align: right;
  }
  .image-previewer,
  .uploaded-images {
    display: flex;
    flex-wrap: wrap;
    padding: 10px;
    justify-items: baseline;
  }
  
  .image-previewer .img,
  .uploaded-images .img {
    height: 300px;
    border-radius: 10px;
    cursor: pointer;
  }
  .image-previewer .image--wrapper {
    position: relative;
    margin: 10px;
  }
  .uploaded-images .image--wrapper {
    position: relative;
    margin: 10px;
  }
  .image-previewer .uploaded-images .image--wrapper {
    margin: 10px;
  }
  .image--wrapper .img--overlay {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    z-index: 10;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
  }
  .image--wrapper .img--overlay .btn {
    background: rgb(238, 5, 5);
    width: 100%;
    position: absolute;
    bottom: 0;
  }
  .progress,
  .progress-bar {
    height: 30px;
    font-weight: bold;
  }
  .inner {
    overflow: hidden;
  }
  .inner img {
    transition: all 1.5s ease;
  }
  .inner:hover img {
    transform: scale(2);
    display: flex;
    flex-wrap: wrap;
  }
  
  @media print {
    .section-not-to-print {
      visibility: hidden;
    }
  
    #section-to-print {
      visibility: visible;
    }
  }
  .box {
    inline-size: 300px;
    overflow: hidden;
    word-wrap: break-word;
  }
  </style>
  