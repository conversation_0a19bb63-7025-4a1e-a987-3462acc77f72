<template>
  <div>
    <div style="background:#F4F4F4">
      <div class="row pt-4">
        <div class="col">
          <div class="card">
            <div class="card-header">
              <span style="color:teal;font-size:150%">
                <b>
                  AGRICULTURE [
                  <span style="color:red"
                    >{{ data.Disaster }} - {{ data.District }}</span
                  >
                  ]
                </b>
                <b class="pull-right"
                  >ASSESSMENT PERIOD :
                  {{
                    dateFormate(
                      new Date(data.Date_of_Disaster_from).toLocaleDateString(
                        "en-US"
                      )
                    )
                  }}
                  -
                  {{
                    dateFormate(
                      new Date(data.Date_of_Disaster_to).toLocaleDateString(
                        "en-US"
                      )
                    )
                  }}</b
                >
              </span>
              <hr />
              <div class="row">
                <base-input label="TA" class="col-xl-3">
                  <el-select
                    v-model="selected"
                    @change="getInfoGraphicsData(selected)"
                    placeholder="select"
                  >
                    <el-option
                      v-for="option in data.TAList"
                      :key="option.label"
                      :label="option.label"
                      :value="option.value + ',' + option.label"
                    ></el-option>
                  </el-select>
                </base-input>
                <base-input
                  label="GVHs"
                  class="col-xl-9"
                  :value="data.GVHS_affected"
                  readonly
                ></base-input>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-xl-4 col-md-6">
          <stats-card
            title="Crops affected (Ha)"
            type="gradient-green"
            :sub-title="
              (
                parseInt(data.number_of_crop_hectares_submerged_agriculture) +
                  parseInt(data.number_crop_hectares_washed_off_agriculture) ||
                0
              ).toString()
            "
            icon="fa fa-leaf"
          >
            <template slot="footer">
              <span></span>
              <span class="pull-right"></span>
            </template>
          </stats-card>
        </div>
        <div class="col-xl-4 col-md-6">
          <stats-card
            title="No. of HH with crops affected"
            type="gradient-green"
            :sub-title="
              data.number_of_households_whose_crops_are_impacted_agriculture.toString()
            "
            icon="icon-unhcr-locations-settlement"
          >
            <template slot="footer">
              <span></span>
              <span class="pull-right"></span>
            </template>
          </stats-card>
        </div>
        <div class="col-xl-4 col-md-6">
          <stats-card
            title="Livestock affected"
            type="gradient-green"
            :sub-title="
              data.number_of_impacted_livestock_agriculture.toString()
            "
            icon="fas fa-horse"
          >
            <template slot="footer">
              <span></span>
              <span class="pull-right"></span>
            </template>
          </stats-card>
        </div>
      </div>
      <div class="row">
        <div class="col-xl-12">
          <div class="row">
            <div class="col-xl-4">
              <card>
                <template slot="header">
                  <!-- Subtitle -->
                  <h6 class="surtitle">
                    No. of HH with Crops damaged [gender]
                  </h6>
                </template>
                <div>
                  <barChart
                    :data="barChartData"
                    :options="barChartOptions"
                  ></barChart>
                </div>
              </card>
            </div>

            <div class="col-xl-4">
              <card>
                <template slot="header">
                  <!-- Subtitle -->
                  <h6 class="surtitle">No. of HA of Crops damaged</h6>
                </template>
                <div>
                  <polarAreaChart
                    :data="polarAreaChartData"
                    :options="polarAreaChartOptions"
                  ></polarAreaChart>
                </div>
              </card>
            </div>
            <div class="col-xl-4">
              <card>
                <template slot="header">
                  <!-- Subtitle -->
                  <h6 class="surtitle">
                    No. of HH with Crops damaged [gender] %
                  </h6>
                  <!-- Title -->
                </template>
                <div>
                  <doughtNutChart
                    :data="doughnutChartData"
                    :options="doughnutChartOptions"
                  ></doughtNutChart>
                </div>
              </card>
            </div>
          </div>

          <div class="row">
            <div class="col-xl-4">
              <card style="padding-bottom:7%">
                <div class="card-header">
                  <b>Impact on crops</b>
                </div>
                <div class="card-body">
                  <ul class="list-group list-group-flush list my--3">
                    <li
                      class="list-group-item px-0"
                      v-for="crop in crops"
                      :key="crop.id"
                    >
                      <div class="row align-items-center">
                        <div class="col ml--2">
                          <h4 class="mb-0">
                            <a href="#!">{{ crop.name }}</a>
                          </h4>
                        </div>
                        <div class="col-auto">
                          <i v-if="crop.number" class="fas fa-leaf"></i>
                          {{ crop.number }}
                          <br />
                        </div>
                        <div class="col-auto"></div>
                      </div>
                    </li>
                  </ul>
                </div>
              </card>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import flatPicker from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";

import RouteBreadCrumb from "@/components/Breadcrumb/RouteBreadcrumb";
import BaseHeader from "@/components/BaseHeader";
import StatsCard from "@/components/Cards/StatsCard";
import { Select, Option } from "element-ui";
import malawiMap from "./components/map-core";
import doughtNutChart from "./components/doughtNutChart";
import polarAreaChart from "./components/polarArea";
import barChart from "./components/barChart";
import TagsInput from "@/components/Inputs/TagsInput";
import moment from "moment";

window.jQuery = require("jquery");

function randomScalingFactor() {
  return Math.round(Math.random() * 100);
}

export default {
  components: {
    StatsCard,
    BaseHeader,
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    flatPicker,
    malawiMap,
    doughtNutChart,
    barChart,
    polarAreaChart,
    TagsInput
  },
  props: ["data"],
  data() {
    return {
      TAList: [],
      selected: {},
      tags: ["Floods", "2019-01-01 to 2019-12-31"],
      polarAreaChartOptions: {
        hoverBorderWidth: 20
      },
      polarAreaChartData: {
        hoverBackgroundColor: "red",
        hoverBorderWidth: 10,
        labels: ["Crops washed", "Crops underwater"],
        datasets: [
          {
            backgroundColor: ["teal", "#a97142"],
            data: [
              parseInt(this.data.number_crop_hectares_washed_off_agriculture),
              parseInt(this.data.number_of_crop_hectares_submerged_agriculture)
            ]
          }
        ]
      },
      doughnutChartOptions: {
        hoverBorderWidth: 20
      },
      doughnutChartData: {
        hoverBackgroundColor: "red",
        hoverBorderWidth: 10,
        labels: ["Livestock", "Female HH"],
        datasets: [
          {
            label: "Crops vs Livestock",
            backgroundColor: ["teal", "#a97142"],
            data: [
              parseInt(
                this.data.hh_affected_per_impacted_livestock_agriculture
              ),
              parseInt(
                this.data
                  .of_crop_hectares_damaged_in_affected_households_agriculture
              )
            ]
          }
        ]
      },
      barChartOptions: {
        hoverBorderWidth: 20
      },
      barChartData: {
        hoverBackgroundColor: "red",
        hoverBorderWidth: 10,
        labels: ["Crops Ha", "Livestock"],
        labels: ["Livestock", "Female HH"],
        datasets: [
          {
            label: "Crops vs Livestock",
            backgroundColor: "teal",
            data: [
              parseInt(
                this.data.hh_affected_per_impacted_livestock_agriculture
              ),
              parseInt(
                this.data
                  .of_crop_hectares_damaged_in_affected_households_agriculture
              )
            ]
          }
        ]
      },

      filter: {
        disasters: "Floods",
        range: "2019-01-01 to 2019-12-31"
      },
      disasters: [
        {
          name: "Heavy Rains"
        },
        {
          name: "Earth quake"
        },
        {
          name: "Floods"
        }
      ],
      districts: [
        {
          label: "Chikwawa",
          value: "Chikwawa",
          region: "South"
        },
        {
          label: "Mangochi",
          value: "Mangochi",
          region: "South"
        },
        {
          label: "Balaka",
          value: "Balaka",
          region: "North"
        },
        {
          label: "Phalombe",
          value: "Phalombe",
          region: "Central"
        }
      ],
      regions: [
        {
          region: "South"
        },
        {
          region: "Central"
        },
        {
          region: "North"
        },
        {
          region: "East"
        }
      ],
      crops: [
        {
          id: 1,
          name: "Hectares submerged",
          number: 67,
          males: 78,
          females: 59
        },
        {
          id: 2,
          name: "No. of crops damaged",
          number: 400,
          males: 76,
          females: 79
        },
        {
          id: 3,
          name: "Hectares washed away",
          number: 79,
          males: 68,
          females: 99
        },
        {
          id: 4,
          name: "",
          number: null,
          males: null,
          females: null
        }
      ]
    };
  },
  methods: {
    dateFormate(dateData) {
      const cur_date = dateData;
      const formattedDate = moment(cur_date).format("DD/MM/YYYY");

      return formattedDate;
    },
    getInfoGraphicsData(selectedTA) {
      var data = selectedTA.split(",");
      
      var TA = data[1];
      var ID = data[0];
      if (TA === "All") {
        this.$emit("Dinr", ID);
      } else {
        this.$emit("Dra", ID);
      }
    }
  },
  mounted() {
    this.selected = this.data.TAname;
  }
};
</script>
<style>
@import "../../../../assets/fonts/font-humanitarian.css";
</style>
