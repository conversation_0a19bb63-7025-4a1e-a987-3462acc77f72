import moment from "moment";
import districtsLatlongData from "../../data/latlondistricts.json"
import {sumByKey} from "../../util/sumByKey"

const flatten = function (dinrs, dras, getSum) {
    //console.log("in", dinrs, dras);
    let drasData = [];
    if (dinrs.length > 0 && dras.length > 0) {

        let approvedData = dinrs;
    
        approvedData.forEach(dinr => {

            let draArray = dras.filter(dra => dra.dinrFormId == dinr._id)
            let createdDate = new Date(dinr.createdon.split("T")[0]);

            draArray.forEach(dra => {
                let dead_females = 0,
                dead_males = 0,
                injured_males = 0,
                injured_females = 0,
                without_shelter_fhh = 0,
                without_shelter_mhh = 0,
                without_shelter_females = 0,
                displaced_fhh = 0,
                displaced_mhh = 0,
                missing_females = 0,
                missing_males = 0,
                without_shelter_males = 0;

                let shelter_sector = dra.sectors && dra.sectors.shelter ? dra.sectors.shelter : {};
                let displaced_sector = dra.sectors && dra.sectors.displaced ? dra.sectors.displaced.PeopleAffectedrows : [];

                displaced_fhh +=sumByKey(displaced_sector,"number_displaced_by_gender_fhh")||0;
                displaced_mhh +=sumByKey(displaced_sector,"number_displaced_by_gender_mhh")||0;

                dead_females +=sumByKey(shelter_sector.PeopleDeadrows,"females_dead")||0;
                dead_males +=sumByKey(shelter_sector.PeopleDeadrows,"males_dead")||0;

                injured_females +=sumByKey(shelter_sector.PeopleInjuredrows,"people_injured_females")||0;
                injured_males +=sumByKey(shelter_sector.PeopleInjuredrows,"people_injured_males")||0;
            
                without_shelter_fhh +=sumByKey(shelter_sector.PeopleAffectedrows,"damaged_fhh")||0;
                without_shelter_mhh +=sumByKey(shelter_sector.PeopleAffectedrows,"damaged_mhh")||0;


                without_shelter_females +=sumByKey(shelter_sector.people_without_shelter,"females")||0;
                without_shelter_males +=sumByKey(shelter_sector.people_without_shelter,"males")||0; 

                if (dinr.district) {
                    drasData.push({
                        disasterId: dra.dinrFormId,
                        draId: dra._id,
                        disaster: dinr.disaster || "",
                        ta: dra.admin3.admin3Name_en,
                        district: dinr.district.admin2_name_en,
                        region: dinr.district.admin1_name_en,
                        created_on: createdDate,
                        identifier: dra.admin3.admin3Name_en +
                            " " +
                            dinr.disaster +
                            " " +
                            moment(createdDate).format("DD/MM/YYYY"),
                        contacts: dinr.account ?
                            `${dinr.account.firstName} ${dinr.account.lastName}|${dinr.account.phone
                            }|${dinr.account.email}` :
                            "",
                        user: dinr.account ?
                            {
                                firstname: dinr.account.firstName || "",
                                lastname: dinr.account.lastName || "",
                                phone: dinr.account.phone || "",
                                email: dinr.account.email || ""
                            } :
                            null,
                        location: {
                            ...districtsLatlongData.filter(
                                item => item.city === dinr.district.admin2_name_en
                            )[0],
                            region: dinr.district.admin1_name_en
                        },
                        dead_females: dead_females || 0,
                        dead_males: dead_males || 0,
                        injured_males: injured_males || 0,
                        injured_females: injured_females || 0,
                        without_shelter_fhh: without_shelter_fhh || 0,
                        without_shelter_mhh: without_shelter_mhh || 0,
                        without_shelter_females: without_shelter_females || 0,
                        without_shelter_males: without_shelter_males || 0,
                        displaced_fhh: displaced_fhh || 0,
                        displaced_mhh: displaced_mhh || 0,
                        total_without_shelter_hh:
                            (without_shelter_fhh || 0) + (without_shelter_mhh || 0)
                    });
                }

            });
        });
    }
  
    return [...drasData];
};

export { flatten };
