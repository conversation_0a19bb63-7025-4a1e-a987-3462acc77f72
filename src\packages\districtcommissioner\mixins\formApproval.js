import Swal from 'sweetalert2'
import { UnapprovedForms } from '../api/UnapprovedForms'
export default {
  methods: {
    processRequest (id) {
      UnapprovedForms.update({ status: '2', _id: id }).then(
        response => {},
        reason => {
          Swal.fire({
            title: 'Failed to update remote form',
            text: 'Please let the IM Unit know of this error (' + reason + ')',
            type: 'error',
            animation: false
          })
        }
      )
    }
  }
}
