<template>
  <div :id="svgId" class="svg-container"></div>
</template>

<script>
import malawiMap from "./data/malawiMap";
export default {
  name: "ShelterMap",
  props: ["district"],
  data: function() {
    return {
      svgId: "MalawiMap",
      mapAttr: {
        viewBoxWidth: 1106,
        viewBoxHeight: 2000,
        imageWidth: 1000,
        imageHeight: 1800
      },
      svgContainer: null
    };
  },
  mounted: function() {
    this.generateVenueMap();
  },
  methods: {
    generateVenueMap: function() {
      const vue = this;
      const mapData = malawiMap.g.path;
      //console.log(mapData);
      const svgContainer = vue
        .$svg("MalawiMap")
        .size("100%", "100%")
        .viewbox(-200, 0, vue.mapAttr.viewBoxWidth, vue.mapAttr.viewBoxHeight);
      vue.svgContainer = svgContainer;
      mapData.forEach(pathObj => {
        vue.generatePath(svgContainer, pathObj);
      });
    },
    generatePath: function(svgCont, pathObj) {
      const vue = this;
      pathObj;
      //console.log(pathObj["@id"]);

      var attrs = {
        fill: "white",
        stroke: "teal",
        "stroke-width": 2,
        title: pathObj["@title"],
        "map-id": pathObj["@id"]
      };
     
      if (pathObj["@title"].toLowerCase() == this.district.toLowerCase()) {
        attrs = {
          fill: "teal",
          stroke: "white",
          "stroke-width": 2,
          title: pathObj["@title"],
          "map-id": pathObj["@id"]
        };
      }
      const element = svgCont.path(pathObj["@d"]).attr(attrs);
      element.click(function() {
        const mapId = this.node.attributes["map-id"].value;
        const title = this.node.attributes["title"].value;
        vue.$emit("map-clicked", { mapId, title });
      });
    }
  }
};
</script>
