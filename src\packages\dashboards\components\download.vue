<template>
  <div class="row mb-4" v-if="title && filteredData.length>0">
    <div class="col-12 text-uppercase m-0 p-0">
      <h3 class="m-0 p-0">
        {{title}}(S)
        <hr class="m-0 p-0 my-2" />
      </h3>
    </div>
    <div class="row m-1">
      <div class="col-sm-auto border m-1" v-for="(file, index) in filteredData" :key="index">
        <div placement="top" :content="file.title">
          <button
            v-b-popover.hover.top="file.title"
            type="button"
            @click="downloadFile(file)"
            class="btn-icon-clipboard"
            data-clipboard-text="air-baloon"
          >
            <div class="meg">
              <img :src="getIcon(file.ext)" width="100" height="100" />
            </div>
             
            <div class="text-center col-12">
               
              <div class="text1">
                <b class="text-uppercase">{{file.district}}</b>
                <div class="text">{{file.title.replace(file.district, "")}}</div>        
                {{formatDate(file.createdon)}}
              </div>
            </div>
          </button>
          <div class="arrow"></div>
        </div>
      </div>
    </div>
    
  </div>
</template>
<script>
import { downloadFromUrl } from "../../../util/download";
import prettyFileIcons from "pretty-file-icons";
export default {
  props: ["files", "title"],
  data() {
    return {
      filteredData: [],
    };
  },
  watch: {
    files: function (val) {
      this.filteredData = val.filter((i) => i.category == this.title);
    },
  },

  mounted() {
    
  },
  methods: {
    formatDate(newdate) {
      return new Date(newdate).toLocaleDateString("en-GB");
    },
    getIcon(ext = "jpg") {
      return require("../../../../public/svg/" +
        prettyFileIcons.getIcon("test." + ext, "svg"));
    },
    downloadFile(file) {
     
      downloadFromUrl(
        process.env.VUE_APP_ENGINE_URL + "/" + file.file,
        file.ext,
        file.title
      );
    },
  },
};
</script>

<style scoped>
.meg {
  display: block;
  margin-left: auto;
  margin-right: auto;
  width: 100px;
}

.me {
  padding: 0px 0;
  border: 0px;
  text-align: center;
}
.text-uppercase{
   font-size: 14px;
}
.text1{
  font-size: 16px;
  padding-top: 5%;
}
.text {
   width: 110px;
   white-space: nowrap;
   overflow: hidden;
   text-overflow: ellipsis;
   padding-left: 9px;
	 }
   .popover {
   border: 1px solid #000000;
	 }
</style>