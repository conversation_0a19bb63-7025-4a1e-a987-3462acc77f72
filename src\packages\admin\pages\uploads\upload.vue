<template>
  <div>
    <div class="vld-parent">
      <loading :active.sync="isLoading" :can-cancel="false" :is-full-page="fullPage"></loading>
    </div>

    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Upload</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">Uploads</a>
              </li>
            </ol>
          </nav>
        </div>
        <div class="col-lg-6 col-5 text-right"></div>
      </div>
    </base-header>
    <div v-if="model.category == 'Desktop App Update'" class="container-fluid mt--6">
      <div class="row justify-content-center">
        <div class="col-lg-7 card-wrapper">
          <!-- Grid system -->
          <card class="card">
            <!-- Card body -->
            <div class="row">
              <div class="col-lg-12">
                <p class="mb-0">File Uploads</p>
                
              </div>
            </div>
            <hr />
            <form
          
            class="needs-validation"
            enctype="multipart/form-data"
            >
              <div class="form-row">
                <div class="col-md-12">
                  <base-input label="Category">
                    <select class="form-control" v-model="model.category" required>
                      <option v-for="prof in profie" :key="prof">{{prof}}</option>
                    </select>
                  </base-input>
                </div>
              </div>
              <div class="form-row">
                <div class="col-md-12">
                  <div v-if="progress === 0" class="fields">
                    <label style="font-size:1em; font-color:red;">Upload File</label>
                    <br />
                      <b-form-file
                        input
                        accept=".exe"
                        @change="uploadSetup"
                        required
                      >
                      </b-form-file>
                  </div>

                  <div v-else class="progress-container">
                    <label>
                      Upload Progress <strong class="text-primary">{{progress}}%</strong>
                    </label>
                    <div class="progress">
                      <div class="progress-bar" :style="{width: `${progress}%`}"></div>
                    </div>
                  </div>
                </div>
              </div>
              <hr />
               <base-button type="primary" @click="sendToDrive">Submit</base-button>
            </form>
          </card>
        </div>
      </div>
    </div>
    <div v-else class="container-fluid mt--6">
      <div class="row justify-content-center">
        <div class="col-lg-7 card-wrapper">
          <!-- Grid system -->
          <card class="card">
            <!-- Card body -->
            <div class="row">
              <div class="col-lg-12">
                <p class="mb-0">File Uploads</p>
                
              </div>
            </div>
            <hr />
            <form
              class="needs-validation"
              @submit.prevent="validate"
              enctype="multipart/form-data"
            >
              <div class="form-row">
                <div class="col-md-12">
                  <base-input
                    label=" Title"
                    name="File name"
                    :required="true"
                    :error="getError('title')"
                    :valid="isValid('title')"
                    v-validate="'required'"
                    v-model="model.title"
                  ></base-input>
                </div>
              </div>
              <div class="form-row">
                <div class="col-md-12">
                  <base-input label="Category">
                    <select class="form-control" v-model="model.category" required>
                      <option v-for="prof in profie" :key="prof">{{prof}}</option>
                    </select>
                  </base-input>
                </div>
              </div>
              <div class="form-row">
                <div class="col-md-12">
                  <base-input label="District" placeholder="--Select District">
                    <select class="form-control" v-model="model.district" required>
                      <option v-for="dist in admin2sData" :key="dist._id">{{dist.admin2_name_en}}</option>
                      <option>Malawi</option>
                    </select>
                  </base-input>
                </div>
              </div>
              <div class="form-row">
                <div class="col-md-12">
                  <base-input
                    label="Description"
                    name="description"
                    v-validate="'required'"
                    v-model="model.description"
                  ></base-input>
                </div>
              </div>
              <div class="form-row">
                <div class="col-md-12">
                  <div v-if="progress === 0" class="fields">
                    <label style="font-size:1em; font-color:red;">Upload File</label>
                    <label style="color:red"> &nbsp;(File should be less than 5MB)</label>
                    <br />
                      <b-form-file
                        type="file"
                        ref="file"
                        @change="changeImage($event)"
                        accept="application/pdf, application/vnd.ms-excel"
                        required
                      >
                      </b-form-file>
                    <div id="preview">
                      <embed
                        style="width: 100%; height: 100%"
                        :src="imagePriview"
                        v-if="imagePriview"
                      />
                    </div>
                  </div>

                  <div v-else class="progress-container">
                    <label>
                      Upload Progress <strong class="text-primary">{{progress}}%</strong>
                    </label>
                    <div class="progress">
                      <div class="progress-bar" :style="{width: `${progress}%`}"></div>
                    </div>
                  </div>
                </div>
              </div>
              <hr />
              <base-button type="primary" native-type="submit">Submit</base-button>
            </form>
          </card>
        </div>
      </div>
    </div>
     
  </div>
</template>
<script>
import { uploads } from "../../api/uploads/uploads";
import { accounts } from "../../api/accounts/accounts";
//import {listFile, generatePublicUrl, uploadFile} from "../../../../util/drive"
import { roles } from "../../api/accounts/roles";
import { admin2s } from "../../api/location/admin2s";
import swal from "sweetalert2";
import { Select, Option } from "element-ui";
// Import component
import Loading from "vue-loading-overlay";
// Import stylesheet
import "vue-loading-overlay/dist/vue-loading.css";
import BaseInput from "../../../../components/Inputs/BaseInput.vue";
import mime from "mime-types";
export default {
  components: {
    Loading,
    [Select.name]: Select,
    BaseInput,
    [Option.name]: Option,
  },
  data() {
    return {
      validated: false,
      progress: 0,
      model: {
        title: "",
        category: "",
        district: "",
        description: "",
        file: "",
      },
      imagePriview: "",
      driveFile: "",
      file: "",
      profie: [
        "District Profile",
        "Disaster Profile",
        "Data sheet",
        "Map",
        "Desktop App Update",
        "Other",
      ],
      rolesData: [],
      admin2sData: [],
      //password: "",
      //passwordvalidation: "",
      isLoading: false,
      fullPage: true,
    };
  },
  beforeCreate() {
    admin2s.get().then((response) => {
      
      this.admin2sData = response.data;
    });
  },
  mounted() {
    roles.get(this.$session.get("jwt")).then((response) => {
      this.rolesData = response.data;
    });
    uploads.get().then((res) => {
      console.log("images :", res);
    });
    admin2s.get().then((response) => {
      this.admin2sData = response.data;
    });
  },
  methods: {
    validate() {
      this.$validator.validateAll().then((response) => {
       
        if (response === true) {
          this.validated = true;
          this.Submit();
          this.clear();
        } else response === false;
        {
          this.validated = false;
        }
      });
    },
    async uploadSetup(files) {
     let file = files.target.files[0]
     this.driveFile = file
    },
    sendToDrive() {
      const formData = new FormData();
     
      formData.append("file", this.driveFile);
     
      uploads.test(formData, this.trackUploadProgress)
    },
    createUploads(data) {
      uploads
        .create(data)
        .then(
          (response) => {
            swal({
              title: "Signature Uploaded!",
              type: "success",
              confirmButtonClass: "btn btn-success btn-fill",
              buttonsStyling: false,
            });
          },
          (reason) => {
            this.$swal({
              title: "Failed to register signature",
              text: "possible network issue (" + reason + ")",
              type: "error",
              confirmButtonClass: "btn btn-success btn-fill",
              buttonsStyling: false,
            });
          }
        )
        .finally(() => {
          this.$router.push({
            name: "DmAllSignatures",
          });
        });
    },
    resetmap() {
      this.file = "";
      this.imagePriview = "";
    },
    changeImage(e) {
      const file = e.target.files[0];
     
      if (file.size <= 5000000) {
        const reader = new FileReader();
        this.imagePriview = "";
        reader.onload = (event) => {
        ;
          this.imagePriview = event.target.result;
        };
        reader.readAsDataURL(file);
      } else {
        this.file = "";
        this.imagePriview = "";

        e.target.value = ''
        this.file = null

        swal({
          title: "File is too large, only less than 5MB is allowed",
          type: "warning",
        })
      }
    },
    Submit() {
      {
        var title = this.model.title;
        var category = this.model.category;
        var district = this.model.district;
        var description = this.model.description;
        swal({
          title: "Are you sure you want to upload a File?",
          text: `You can change!`,
          type: "warning",
          showCancelButton: true,
          confirmButtonClass: "btn btn-success btn-fill",
          cancelButtonClass: "btn btn-danger btn-fill",
          confirmButtonText: "Yes, upload it!",
          buttonsStyling: false,
        }).then((result) => {
          if (result.value) {
            let fd = new FormData();
            fd.append("file", this.$refs.file.files[0]);
            uploads.upload(fd, this.trackUploadProgress).then((response) => {
              if (response.status === 201) {
                this.model.district = district;
                this.model.title = title;
                this.model.category = category;
                this.model.description = description;
                this.model.file = response.data.path;
                this.model.ext = mime.extension(response.data.contentType);

                uploads.create(this.model);
                this.$router.push({
                  name: "ListAdminUpload",
                });
              } else {
                swal({
                  title: "File upload failed",
                  text: `Error`,
                  type: "warning",
                })
              }
            }).catch(error => {
                swal({
                  title: "File upload failed",
                  text: `Error`,
                  type: "warning",
                })
            })
          }
        });
      }
    },
    getError(name) {
      return this.errors.first(name);
    },
    isValid(name) {
      return this.validated && !this.errors.has(name);
    },
     trackUploadProgress(e) {
      this.progress = Math.round((e.loaded * 100) / e.total)
      const _self = this
      if (this.progress === 100) {
        setTimeout(function(){_self.progress = 0}, 1000)
         swal({
              title: `Successfully uploaded`,
              type: "success",
              confirmButtonClass: "btn btn-success btn-fill",
              buttonsStyling: false,
            });

      }
    },
    clear() {
      this.model = {
        title: "",
        category: "",
        district: "",
        description: "",
        file: "",
      };
      this.imagePriview = "";
      this.validated = true;
    }

   
  }
};
</script>
<style scoped>
.progress{
      height: 1.5rem!important
}
#preview {
  display: flex;
  padding-top: 30px;
  justify-content: left;
  align-items: left;
}

#preview img {
  max-width: 30%;
  max-height: 100px;
}
</style>
