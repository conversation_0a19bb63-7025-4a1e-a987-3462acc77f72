<template>
  <div>
    <h2>HEALTH</h2>
     <h3>
      *
      <small>Hint &nbsp;</small>
      <b><font color="primary">(HH: Households, FHH : Female Headed Households, MHH : Male Headed Households)</font></b>
    </h3>
    <hr class="mt-3 mb-3" />
    <h2>
      <b class="alert-suc">Health facilities affected</b>
    </h2>
    <form>
      <div
        class="row row-example"
        v-for="(value, index) in available_health_facilities"
        v-bind:key="index"
      >
        <div class="col-md">
          <base-input label="Please choose a source">
            <select class="form-control" v-model="value.name"    data-toggle="tooltip"
                    data-placement="top"
                    title="choose a source">
              <option v-for="item  in health_facilities" :value="item.name" :key="item.name">{{item.name}}</option>
            </select>
          </base-input>
        </div>
        <div class="row pt-5">
          <div class="col-md">
            <base-radio name="yes" class="mb-3" v-bind:value="'yes'"
               data-toggle="tooltip"
                    data-placement="top"
                    title="Yes" v-model="value.available">Yes</base-radio>
          </div>

          <div class="col-md">
            <base-radio name="no" class="mb-3" v-bind:value="'no'"    data-toggle="tooltip"
                    data-placement="top"
                    title="no" v-model="value.available">No</base-radio>
          </div>
        </div>

        <div class="col-md pr-5 pt-5">
          <base-button size="sm" type="warning" class="btn-icon-only rounded-circle noprint"
             data-toggle="tooltip"
                    data-placement="top"
                    title="Remove available health facility"
            @click="removeItemRow('available_health_facilities',available_health_facilities,  health_facilities, index, 'name' )"
          >X</base-button>
        </div>

        <div class="col-md-12">
          <div class="row" v-if="value.available === 'yes'">
            <div class="col-md-3">
              <b>
                Affected Health Facility
                <h4>
                  {{
                  typeof value.name !== "undefined" ? '(' + value.name + ')' : '(Specify facility)'}}
                </h4>
              </b>
            </div>
            <div class="col-md-3">
              <base-input
                v-model="value.partially_functioning"
                oninput="validity.valid||(value='');"
                type="number"
                min="0"
                :rules="[(v) => !!v || 'value is required']"
                   data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number partially functioning"
                placeholder="0"
                v-bind:max="maxValue"
                @input="init_totals('partially_functioning', available_health_facilities, 'partially_functioning')"
                label="Partially Functioning"
              />
            </div>
            <div class="col-md-3">
              <base-input
                v-model="value.verge_of_closing"
                oninput="validity.valid||(value='');"
                type="number"
                min="0"
                :rules="[(v) => !!v || 'value is required']"
                   data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of verge of closing"
                placeholder="0"
                @input="init_totals('verge_of_closing', available_health_facilities, 'verge_of_closing')"
                v-bind:max="maxValue"
                label="Verge of closing"
              />
            </div>
            <div class="col-md-3">
              <base-input
                v-model="value.closed"
                oninput="validity.valid||(value='');"
                type="number"
                min="0"
                :rules="[(v) => !!v || 'value is required']"
                   data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of closed"
                placeholder="0"
                @input="init_totals('closed', available_health_facilities, 'closed')"
                v-bind:max="maxValue"
                label="Closed"
              />
            </div>
          </div>
        </div>
      </div>
    </form>
    <base-button
      size="md"
      type="info"
      class="btn-icon-only rounded-circle noprint"
         data-toggle="tooltip"
                    data-placement="top"
                    title="Add available health facility"
             @click="addItemRow('available_health_facilities', available_health_facilities, health_facilities, 'name')"
    >
      <i class="ni ni-fat-add"></i>
    </base-button>
    <hr />

    <h2>
      <b class="alert-suc">Status of Other Health Facilities (By numbers)</b>
    </h2>
    <form>
      <div
        class="row row-example"
        v-for="(value, index) in other_health_facilities"
        v-bind:key="index"
      >
        <div class="col-md">
          <base-input
            label="Please specify facility (if any)"
               data-toggle="tooltip"
                    data-placement="top"
                    title="Specify facility type"
            placeholder="Specify a facility type"
            v-model="value.name"
            id="inputTextBox"
          ></base-input>
        </div>
        <div class="col-md">
          <base-input
            v-model.number="value.partialy_functioning"
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            :rules="[(v) => !!v || 'value is required']"
               data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of partially functioning"
            @input="init_totals('partialy_functioning', other_health_facilities, 'other_partially_functioning')"
            placeholder="0"
            :disabled="value.name == defaultName || value.name === ''"
            v-bind:max="maxValue"
            label="Partially Functioning"
          />
        </div>

        <div class="col-md">
          <base-input
            v-model.number="value.verge_of_closing"
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            :rules="[(v) => !!v || 'value is required']"
               data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number on verge of closing"
            @input="init_totals('verge_of_closing', other_health_facilities, 'other_verge_of_closing')"
            :disabled="value.name == defaultName || value.name === ''"
            placeholder="0"
            v-bind:max="maxValue"
            label="Verge of closing"
          />
        </div>

        <div class="col-md">
          <base-input
            v-model="value.closed"
            oninput="validity.valid||(value='');"
            type="number"
               data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of closed"
            min="0"
            :rules="[(v) => !!v || 'value is required']"
            @input="init_totals('closed', other_health_facilities, 'other_closed')"
            :disabled="value.name == defaultName || value.name === ''"
            placeholder="0"
            v-bind:max="maxValue"
            label="Closed"
          />
        </div>

        <div class="col-md pr-5 pt-5">
          <base-button size="sm" type="warning" class="btn-icon-only rounded-circle noprint"
             data-toggle="tooltip"
                    data-placement="top"
                    title="Remove other health facilities"
              @click="removeItemRow('other_health_facilities',other_health_facilities,  [], index, 'name' )"

          >X</base-button>
        </div>
      </div>
    </form>
    <base-button size="md" type="info" class="btn-icon-only rounded-circle noprint"
       data-toggle="tooltip"
                    data-placement="top"
                    title="Add other health facilities"
     @click="addItemRow('other_health_facilities', other_health_facilities, [], 'name')"
   >
      <i class="ni ni-fat-add"></i>
    </base-button>
    <hr />
    <div class="row">
      <div class="col-md">
        <div slot="label">Total health facilities partly functional: {{ tt_partially_functional }}</div>
      </div>
      <div class="col-md">
        <div slot="label">Total health facilities almost closed: {{ tt_verge_closing }}</div>
      </div>
      <div class="col-md">
        <div slot="label">Total health facilities closed: {{ tt_closed }}</div>
      </div>
    </div>
    <hr />
    <h2>
      <b class="alert-suc">Availablity of medical supplies and health personel</b>
    </h2>
    <form>
      <div class="row row-example" v-for="(value, index) in available_health_medical"
        v-bind:key="index"
   >
        <div class="col-md">
          <base-input label="Please choose an option">
            <select class="form-control" v-model="value.name"    data-toggle="tooltip"
                    data-placement="top"
                    title="Choose an option">
              <option v-for="item  in health_medical" :value="item.name" :key="item.name">{{item.name}}</option>
            </select>
          </base-input>
        </div>
        <div class="row pt-5">
          <div class="col-md">
            <base-radio name="No change" class="mb-3" v-bind:value="'yes'"
               data-toggle="tooltip"
                    data-placement="top"
                    title="no change" v-model="value.status">No change</base-radio>
          </div>

          <div class="col-md">
            <base-radio name="Low" class="mb-3" v-bind:value="'Low'"
               data-toggle="tooltip"
                    data-placement="top"
                    title="low" v-model="value.status">Low</base-radio>
          </div>
          <div class="col-md">
            <base-radio name="None" class="mb-3" v-bind:value="'None'"    data-toggle="tooltip"
                    data-placement="top"
                    title="none" v-model="value.status">None</base-radio>
          </div>
        </div>

        <div class="col-md pr-5 pt-5">
          <base-button size="sm" type="warning" class="btn-icon-only rounded-circle noprint"
           @click="removeItemRow('available_health_medical',available_health_medical,  [], index, 'name' )">X</base-button>
        </div>
      </div>
    </form>
    <base-button size="md" type="info" class="btn-icon-only rounded-circle noprint"
    data-toggle="tooltip"
                    data-placement="top"
                    title="Add available health/medical"
         @click="addItemRow('available_health_medical', available_health_medical, health_medical, 'name')"
 >
      <i class="ni ni-fat-add"></i>
    </base-button>
    <hr />

    <h2>
      <b class="alert-suc">Risk of disease outbreaks</b>
    </h2>
    <form>
      <div
        class="row row-example"
        v-for="(value, index) in risk_out_disease_outbreak"
        v-bind:key="index"
      >
        <div class="col-md">
          <base-input label="Please choose a disease">
            <select class="form-control" v-model="value.name" data-toggle="tooltip"
                    data-placement="top"
                    title="choose a disease">
              <option v-for="item in diseases" :value="item.name" :key="item">{{item.name}}</option>
            </select>
          </base-input>
        </div>
        <div class="row pt-5">
          <div class="col-md">
            <base-radio name="low" class="mb-3" data-toggle="tooltip"
                    data-placement="top"
                    title="Low" v-bind:value="'Low'" v-model="value.risk">Low</base-radio>
          </div>

          <div class="col-md">
            <base-radio
              name="Medium"
              v-bind:value="'Medium'"
              data-toggle="tooltip"
                    data-placement="top"
                    title="Medium"
              class="mb-3"
              v-model="value.risk"
            >Medium</base-radio>
          </div>
          <div class="col-md">
            <base-radio name="High" v-bind:value="'High'" data-toggle="tooltip"
                    data-placement="top"
                    title="High" class="mb-3" v-model="value.risk">High</base-radio>
          </div>
        </div>

        <div class="col-md pr-5 pt-5">
          <base-button
            size="sm"
            type="warning"
            class="btn-icon-only rounded-circle noprint"
            v-if="risk_out_disease_outbreak.length > 0"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Remove risk of disease outbreak"
            @click="removeItemRow('risk_out_disease_outbreak',risk_out_disease_outbreak,  diseases, index, 'name' )"
          >X</base-button>
        </div>
      </div>
    </form>
    <base-button
      size="md"
      type="info"
      class="btn-icon-only rounded-circle noprint"
      data-toggle="tooltip"
                    data-placement="top"
                    title="Add risk of disease outbreak"
      @click="addItemRow('risk_out_disease_outbreak', risk_out_disease_outbreak, diseases, 'name')"
    >
      <i class="ni ni-fat-add"></i>
    </base-button>
    <hr />
    <h2>
      <b class="alert-suc">Risk of other disease outbreaks</b>
    </h2>
    <form>
      <div
        class="row row-example"
        v-for="(value, index) in other_risks_of_disease_outbreak "
        v-bind:key="index"
      >
        <div class="col-md">
          <base-input
            label="Please specify a disease (if any)"
            data-toggle="tooltip"
                    data-placement="top"
                    title="specify a disease"
            placeholder="Specify a disease"
            v-model="value.name"
          ></base-input>
        </div>
        <div class="col-md pt-5">
          <base-radio
            name="Low"
            v-bind:value="'Low'"
            data-toggle="tooltip"
                    data-placement="top"
                    title="low"
            :disabled="value.name == defaultName || value.name === ''"
            class="mb-3"
            v-model="value.impact"
          >No change</base-radio>
        </div>

        <div class="col-md pt-5">
          <base-radio
            name="Medium"
            v-bind:value="'Medium'"
            :disabled="value.name == defaultName || value.name === ''"
            data-toggle="tooltip"
                    data-placement="top"
                    title="medium"
            class="mb-3"
            v-model="value.impact"
          >Low</base-radio>
        </div>
        <div class="col-md pt-5">
          <base-radio
            name="High"
            v-bind:value="'High'"
            :disabled="value.name == defaultName || value.name === ''"
            data-toggle="tooltip"
                    data-placement="top"
                    title="nono"
            class="mb-3"
            v-model="value.impact"
          >None</base-radio>
        </div>
      </div>
    </form>
    <hr />
<b>Response Needed for the Health Cluster</b>
    <hr class="mt-3 mb-3">
    <base-input label="General Response needed for Health cluster">
      <textarea
        class="form-control"
        id="exampleFormControlTextarea3"
        data-toggle="tooltip"
                    data-placement="top"
                    title="Type the general response needed"
        placeholder="Type the response needed for the Health cluster"
        v-model="response_needed"
        rows="3"
      ></textarea>
    </base-input>
    <base-input label="Urgent Response needed for Health cluster">
      <textarea
        class="form-control"
        id="exampleFormControlTextarea3"
        data-toggle="tooltip"
                    data-placement="top"
                    title="Type the urgent response needed"
        placeholder="Type the urgent response needed for the Health cluster"
        v-model="urgent_response_needed"
        rows="3"
      ></textarea>
    </base-input>
    <base-button size="lg" type="primary" @click.stop="save" data-toggle="tooltip"
                    data-placement="top"
                    title="Save and goto next section" class="noprint">Save & Continue</base-button>
  </div>
</template>

<script src="./index.js"/>

<style scoped>
@media print {
  .page-break {
    overflow-y: visible;
    display: block;
  }
  .not-active {
    pointer-events: none;
    cursor: default;
    text-decoration: none;
    color: black;
  }
  .noprint {
    visibility: hidden;
  }
}
</style>
