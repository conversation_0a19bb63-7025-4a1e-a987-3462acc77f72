export const set = (state, val) => {
  state.dinrs = val
}

export const update = (state, val) => {
  state.dinrs.splice(state.dinrs.map(o => o.id).indexOf(val.id), 1, val)
}

export const remove = (state, val) => {
  state.dinrs = state.dinrs.filter(x => {
    return x.id != val
  })
}

export const setOne = (state, val) => {
  state.dinrs.push(val)
}

// roles

export const setroles = (state, val) => {
  state.roles = val
}
export const updateDinr = (state, updatedDinr) => {
  const index = state.dinrs.findIndex(d => d.id === updatedDinr.id);
  if (index !== -1) {
    state.dinrs.splice(index, 1, updatedDinr);
  }
};

