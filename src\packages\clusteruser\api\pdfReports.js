import axios from 'axios';

const resource = process.env.VUE_APP_ENGINE_URL + '/DraForms';


export class dinrForms {

    static get(token) {
        return axios.get(resource + '?include=Admin2s&access_token=' + token).then(response => { return response });
    }
    static async remove(id, token) {
        let response = await axios.delete(resource + '/' + id + '?access_token=' + token).then(response => { return response.data });
        return response;
    }

    static getDraForms(id, token) {
        return axios.get(resource + '/' + id + '/DraForms?access_token=' + token).then(response => { return response.data });
    }

    static async removeDraForm(id, draId, token) {
        let response = await axios.delete(resource + '/' + id + '/DraForms/' + draId + '?access_token=' + token).then(response => { return response.data });
        return response;
    }

    static getOne(id, token) {
        return axios.get(resource + '/' + id + '?access_token=' + token).then(response => { return response.data });
    }
    static update(dinrform, token) {
        return axios.patch(resource + '/' + dinrform.id + '?access_token=' + token, dinrform).then(response => { return response.data });
    }
    static async create(id, dinrform) {
        let response = await axios.post(resource + '?access_token=' + id, dinrform).then(response => { return response.data });
        return response;
    }
    static async count(id) {
        let response = await axios.get(resource + '/count' + '?access_token=' + id).then(response => { return response.data });
        return response;
    }
}
