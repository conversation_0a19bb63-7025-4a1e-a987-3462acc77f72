import { flatten } from './flatten'

export const loadFlatdinrs = async (
  { commit, rootGetters, state },
  payload
) => {
  //console.log("agg",state.flatdinrs.length === 0)
  if (state.flatdinrs.length === 0) {
    let dras = await rootGetters['dras/get']
    let dinrs = await rootGetters['dinrs/get']
    //to add more parameters to flattener please add in flatten.js
    //console.log("agg data ",dinrs,dras)
    let data = await flatten(dinrs, dras, getSum)
    
    commit('loadFlatdinrs', data)
  
    return data
  } else {
    return state.flatdinrs
  }

}
export const getFlatReports = (contex) => {
  
}

function getSum (total, num) {
  return total + (+num ? num : 0)
}
