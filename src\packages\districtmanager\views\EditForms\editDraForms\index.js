/*Api interactions*/
import { dinrforms } from '../../../api/forms/dinrforms'

/*library imports*/
import tabs from '../../../../../components/Tabs/Tabs'
import tabpane from '../../../../../components/Tabs/Tab'
import Swal from 'sweetalert2'

/*Child component imports*/
import shelter from './sectors/shelter/shelter'
import displaced from './sectors/displaced/displaced'
import agriculture from './sectors/agriculture/agriculture'
import wash from './sectors/wash/wash'
import health from './sectors/health/health'
import food from './sectors/food/food'
import logistics from './sectors/logistics/logistics'
import protection from './sectors/protection/protection'
import nutrition from './sectors/nutrition/nutrition'
import livelihoods from './sectors/livelihoods/livelihoods'
import environment from './sectors/environment/environment'
import education from './sectors/education/education'

export default {
  components: {
    tabs,
    tabpane,
    agriculture,
    wash,
    health,
    shelter,
    displaced,
    nutrition,
    livelihoods,
    environment,
    food,
    logistics,
    protection,
    education
  },

  data () {
    return {
      /*model declarations*/
      dinrId: null,
      uuid: null,
      tabIndex: 1,
      district: '',
      TA: this.$session.get('TA'),
      dialog: false,
      notifications: false,
      isVisible: true,
      sound: true,
      widgets: false,
      i: 0,
      model: '1',
      Ta: '',
      step: 1,
      e13: 1,
      max: 50000,
      value: 0,
      accessToken: null,
      childDataLoaded: false,
      malecounter: 0,
      femalecounter: 0,

      /*Binded datasetItem declaritions*/
      sector: {
        shelter: {},
        education: {},
        agriculture: {},
        wash: {},
        protection: {},
        health: {},
        environment: {},
        food: {},
        displaced: {},
        nutrition: {},
        logistics: {},
        livelihoods: {}
      }
    }
  },

  methods: {
    setItemActive (tabpaneID) {
      //get index of clicked tab
      this.model = tabpaneID
    },

    gotoPrint () {
      this.$router.push({
        name: 'DRA Forms Preview',
        params: {
          id: this.$route.params.id,
          dinrId: this.$route.params.dinrId
        }
      })
    },

    addItemRow (sector, member, array_name = [], array2 = [], key_value) {
      this.sector[sector][member] = array_name
      let FilteredData = array_name.filter(
        value => Object.keys(value).length === 0
      )
      if (FilteredData.length === 0) {
        this.sector[sector][member].push({})
      } else {
        Swal.fire({
          title: 'Unfilled data row',
          text: 'Please fill data row before adding new one',
          type: 'warning',
          toast: true,
          animation: false,
          position: 'top-end',
          timer: 4000,
          showConfirmButton: false
        })
      }
    },
    removeItemRow (sector, member, array_name, array2 = [], index, key_value) {
      //emitted function for deleting html row
      Swal.fire({
        title: 'Are you sure?',
        text: "You won't be able to revert this!",
        animation: false,
        showCancelButton: true,
        confirmButtonColor: '#2dce89',
        cancelButtonColor: '#f5365c',
        confirmButtonText: 'Yes, delete it!'
      }).then(result => {
        if (result.value) {
          {
            array_name[index][key_value] === undefined
              ? console.log('')
              : array2.push({ name: array_name[index][key_value] })
            this.sector[sector][member] = array_name
            this.sector[sector][member].splice(index, 1)
          }
        }
      })
    },

    save (data, sector, hasError = false) {
      /*Navigate to new tab*/
      let CurrentPosition = this.model.replace(/\D/g, '') % 12
      this.model = `${parseInt(CurrentPosition) + 1}`
      this.$router.push({ hash: '' })
      window.scrollTo(500, 0)

      //console.log(JSON.stringify(this.sector))

      let committedData = { sectors: this.sector, _id: this.$route.params.id }
      draforms.update(committedData).then(
        response => {
          Swal.fire({
            title: 'Saved',
            text: 'Form data saved successfully',
            type: 'success',
            toast: true,
            animation: false,
            position: 'top-end',
            timer: 3000,
            showConfirmButton: false
          })
        },
        error => {
          if (this.sector != null) {
            /*Navigate to new tab*/
            let CurrentPosition = this.model.replace(/\D/g, '') % 12
            this.model = `${parseInt(CurrentPosition) + 1}`
            this.$router.push({ hash: '' })
            window.scrollTo(500, 0)
          }
        }
      )
    },

    autosave (data, sector, hasError = false) {
      /*  let committedData = { sectors: this.sector, _id: this.$route.params.id }
      draforms.update(committedData).then(
        response => {},
        error => {}
      ) */
    }
  },

  created () {
    this.district = this.$session.get('district')
    this.uuid = this.$route.params.id
    this.Ta = this.$session.get('TA')

    this.dinrId = this.$route.params.dinrId

    dinrforms.get(this.dinrId).then(response => {
      this.childDataLoaded = true

      response.data = [
        ...response.data.dra.filter(item => {
          return item._id == this.uuid
        })
      ][0]

      alert(JSON.stringify(response.data))

      if (typeof response.data.sectors.education == 'undefined') {
        this.childDataLoaded = true
      } else {
        localStorage.setItem(
          'education',
          JSON.stringify(response.data.sectors.education)
        )

        this.sector.education = response.data.sectors.education

        this.childDataLoaded = true
      }

      if (typeof response.data.sectors.environment == 'undefined') {
        this.childDataLoaded = true
      } else {
        localStorage.setItem(
          'environment',
          JSON.stringify(response.data.sectors.environment)
        )

        this.sector.environment = response.data.sectors.environment

        this.childDataLoaded = true
      }

      if (typeof response.data.sectors.shelter == 'undefined') {
        this.childDataLoaded = true
      } else {
        localStorage.setItem(
          'shelter',
          JSON.stringify(response.data.sectors.shelter)
        )

        this.sector.shelter = response.data.sectors.shelter

        this.childDataLoaded = true
      }

      if (typeof response.data.sectors.displaced == 'undefined') {
        this.childDataLoaded = true
      } else {
        localStorage.setItem(
          'displaced',
          JSON.stringify(response.data.sectors.displaced)
        )

        this.sector.displaced = response.data.sectors.displaced

        this.childDataLoaded = true
      }

      if (typeof response.data.sectors.livelihoods == 'undefined') {
        this.childDataLoaded = true
      } else {
        localStorage.setItem(
          'livelihoods',
          JSON.stringify(response.data.sectors.livelihoods)
        )

        this.sector.livelihoods = response.data.sectors.livelihoods

        this.childDataLoaded = true
      }

      if (typeof response.data.sectors.agriculture == 'undefined') {
        this.childDataLoaded = true
      } else {
        localStorage.setItem(
          'agriculture',
          JSON.stringify(response.data.sectors.agriculture)
        )
        this.sector.agriculture = response.data.sectors.agriculture

        this.childDataLoaded = true
      }

      if (typeof response.data.sectors.wash == 'undefined') {
        this.childDataLoaded = true
      } else {
        localStorage.setItem('wash', JSON.stringify(response.data.sectors.wash))
        this.sector.wash = response.data.sectors.wash

        this.childDataLoaded = true
      }

      if (typeof response.data.sectors.health == 'undefined') {
        this.childDataLoaded = true
      } else {
        localStorage.setItem(
          'health',
          JSON.stringify(response.data.sectors.health)
        )
        this.sector.health = response.data.sectors.health

        this.childDataLoaded = true
      }

      if (typeof response.data.sectors.food == 'undefined') {
        this.childDataLoaded = true
      } else {
        localStorage.setItem('food', JSON.stringify(response.data.sectors.food))
        this.sector.food = response.data.sectors.food

        this.childDataLoaded = true
      }

      if (typeof response.data.sectors.logistics == 'undefined') {
        this.childDataLoaded = true
      } else {
        localStorage.setItem(
          'logistics',
          JSON.stringify(response.data.sectors.logistics)
        )
        this.sector.logistics = response.data.sectors.logistics

        this.childDataLoaded = true
      }

      if (typeof response.data.sectors.protection == 'undefined') {
        this.childDataLoaded = true
      } else {
        localStorage.setItem(
          'protection',
          JSON.stringify(response.data.sectors.protection)
        )
        this.sector.protection = response.data.sectors.protection

        this.childDataLoaded = true
      }

      if (typeof response.data.sectors.nutrition == 'undefined') {
        this.childDataLoaded = true
      } else {
        localStorage.setItem(
          'nutrition',
          JSON.stringify(response.data.sectors.nutrition)
        )

        this.sector.nutrition = response.data.sectors.nutrition

        this.childDataLoaded = true
      }
    })
  }
}
