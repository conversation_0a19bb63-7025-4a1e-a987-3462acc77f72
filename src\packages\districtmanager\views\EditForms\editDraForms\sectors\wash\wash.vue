<template>
  <div>
    <h2 slot="header" class="mb-0">WASH</h2>
    <h3>
      *
      <small>Hint &nbsp;</small>
      <b
        ><font color="primary"
          >(HH: Households, FHH : Female Headed Households, MHH : Male Headed
          Households)</font
        ></b
      >
    </h3>
    <h2>
      <b class="alert-suc">Households without safe drinking water</b>
    </h2>
    <form>
      <div class="row row-example">
        <div class="col-md">
          <base-input
            label="Female HH "
            placeholder="Female HH affected"
            data-toggle="tooltip"
            data-placement="top"
            title="Enter number of female-headed households"
            type="number"
            v-model="with_safe_water_fhh"
            oninput="validity.valid||(value='');"
            min="0"
            v-bind:max="maxValue"
          />
        </div>
        <div class="col-md">
          <base-input
            label="Male HH "
            placeholder="Male HH affected"
            data-toggle="tooltip"
            data-placement="top"
            title="Enter number of male-headed households"
            type="number"
            v-model="with_safe_water_mhh"
            oninput="validity.valid||(value='');"
            min="0"
            v-bind:max="maxValue"
          />
        </div>
        <div class="col-md">
          <h6 slot="header" class="mt-5">
            Total HH: {{ total_with_safe_water }}
          </h6>
        </div>
      </div>
    </form>
    <hr />
    <h2>
      <b class="alert-suc">Households without access to toilets</b>
    </h2>
    <form>
      <div class="row row-example">
        <div class="col-md">
          <base-input
            label="Female HH "
            placeholder="Female HH affected"
            type="number"
            data-toggle="tooltip"
            data-placement="top"
            title="Enter number of female-headed households affected"
            v-model="access_to_toilets_fhh"
            oninput="validity.valid||(value='');"
            min="0"
            v-bind:max="maxValue"
          />
        </div>
        <div class="col-md">
          <base-input
            label="Male HH "
            placeholder="Male HH affected"
            v-model="access_to_toilets_mhh"
            oninput="validity.valid||(value='');"
            data-toggle="tooltip"
            data-placement="top"
            title="Enter number of male-headed affected"
            type="number"
            min="0"
            v-bind:max="maxValue"
          />
        </div>
        <div class="col-md">
          <h6 slot="header" class="mt-5">
            Total HH: {{ total_without_access_to_toilets }}
          </h6>
        </div>
      </div>
    </form>
    <hr />

    <h2>
      <b class="alert-suc">Households affected by water contamination</b>
    </h2>
    <form>
      <div class="row row-example">
        <div class="col-md">
          <base-input
            label="Female HH "
            placeholder="Female HH affected
          "
            type="number"
            v-model="risk_contamination_fhh"
            data-toggle="tooltip"
            data-placement="top"
            title="Enter number of female-headed households affected"
            oninput="validity.valid||(value='');"
            min="0"
            v-bind:max="maxValue"
          />
        </div>
        <div class="col-md">
          <base-input
            label="Male HH "
            placeholder="Male HH affected"
            v-model="risk_contamination_mhh"
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            data-toggle="tooltip"
            data-placement="top"
            title="Enter number of male-headed housed affected"
            v-bind:max="maxValue"
          />
        </div>
        <div class="col-md">
          <h6 slot="header" class="mt-5">
            Total HH: {{ total_risk_of_contamination }}
          </h6>
        </div>
      </div>
    </form>
    <hr />

    <h2>
      <b class="alert-suc">Risk of water contamination from known sources</b>
    </h2>
    <form>
      <div
        class="row row-example"
        v-for="(value, index) in risk_of_water_contamination"
        v-bind:key="index"
      >
        <div class="col-md-12">
          <div class="row">
            <div class="col-md ">
              <base-input label="Please choose a source">
                <select
                  class="form-control"
                  id="exampleFormControlSelect1"
                  data-toggle="tooltip"
                  data-placement="top"
                  title="Choose a source"
                  label="Please choose source"
                  v-model="value.source"
                >
                  <option
                    v-for="source in contamination_sources"
                    :value="source.name"
                    >{{ source.name }}</option
                  >
                </select>
              </base-input>
            </div>
            <div class="col-md pt-5">
              <base-radio
                name="none"
                class="mb-3"
                v-bind:value="'none'"
                data-toggle="tooltip"
                data-placement="top"
                title="none"
                :disabled="value.source == null || value.source === ''"
                v-model="value.risk"
                >None</base-radio
              >
            </div>

            <div class="col-md pt-5">
              <base-radio
                name="low"
                v-bind:value="'low'"
                data-toggle="tooltip"
                data-placement="top"
                title="low"
                :disabled="value.source == null || value.source === ''"
                class="mb-3"
                v-model="value.risk"
                >Low</base-radio
              >
            </div>

            <div class="col-md pt-5">
              <base-radio
                name="medium"
                v-bind:value="'medium'"
                :disabled="value.source == null || value.source === ''"
                class="mb-3"
                v-model="value.risk"
                data-toggle="tooltip"
                data-placement="top"
                title="medium"
                >Medium</base-radio
              >
            </div>
            <div class="col-md pt-5">
              <base-radio
                name="high"
                v-bind:value="'high'"
                data-toggle="tooltip"
                data-placement="top"
                title="high"
                :disabled="value.source == null || value.source === ''"
                class="mb-3"
                v-model="value.risk"
                >High</base-radio
              >
            </div>

            <div class="col-md pr-5 pt-5">
              <base-button
                size="sm"
                type="warning"
                data-toggle="tooltip"
                data-placement="top"
                title="Remove risk of contamination"
                class="btn-icon-only rounded-circle noprint"
                @click="
                  removeItemRow(
                    'risk_of_water_contamination',
                    risk_of_water_contamination,
                    contamination_sources,
                    index,
                    'source'
                  )
                "
                >X</base-button
              >
            </div>
          </div>
        </div>
      </div>
    </form>
    <base-button
      size="md"
      type="info"
      class="btn-icon-only rounded-circle noprint"
      data-toggle="tooltip"
      data-placement="top"
      title="add risk of contamination"
      @click="
        addItemRow(
          'risk_of_water_contamination',
          risk_of_water_contamination,
          contamination_sources,
          'source'
        )
      "
    >
      <i class="ni ni-fat-add"></i>
    </base-button>
    <hr />
    <h2>
      <b class="alert-suc">Impact on water sources</b>
    </h2>
    <form>
      <div
        class="row row-example"
        v-for="(value, index) in risk_of_water_contamination_other_sources"
        v-bind:key="index"
      >
        <div class="col-md-12">
          <div class="row">
            <div class="col-md">
              <base-input
                label="Please specify source (if any)"
                v-model="value.source"
                data-toggle="tooltip"
                data-placement="top"
                title="specify a source"
                placeholder="Specify a source"
              ></base-input>
            </div>
            <div class="row pt-5">
              <div class="col-md">
                <base-radio
                  name="none"
                  class="mb-3"
                  v-bind:value="'none'"
                  :disabled="value.source == null || value.source === ''"
                  v-model="value.risk"
                  data-toggle="tooltip"
                  data-placement="top"
                  title="none"
                  >None</base-radio
                >
              </div>

              <div class="col-md">
                <base-radio
                  name="low"
                  class="mb-3"
                  v-bind:value="'low'"
                  :disabled="value.source == null || value.source === ''"
                  v-model="value.risk"
                  data-toggle="tooltip"
                  data-placement="top"
                  title="low"
                  >Low</base-radio
                >
              </div>

              <div class="col-md">
                <base-radio
                  name="medium"
                  class="mb-3"
                  v-bind:value="'medium'"
                  :disabled="value.source == null || value.source === ''"
                  v-model="value.risk"
                  data-toggle="tooltip"
                  data-placement="top"
                  title="medium"
                  >Medium</base-radio
                >
              </div>
              <div class="col-md">
                <base-radio
                  name="high"
                  class="mb-3"
                  v-bind:value="'high'"
                  data-toggle="tooltip"
                  data-placement="top"
                  title="high"
                  :disabled="value.source == null || value.source === ''"
                  v-model="value.risk"
                  >High</base-radio
                >
              </div>
            </div>

            <div class="col-md pr-5 pt-5">
              <base-button
                size="sm"
                type="warning"
                data-toggle="tooltip"
                data-placement="top"
                title="Remove other sources"
                class="btn-icon-only rounded-circle noprint"
                @click="
                  removeItemRow(
                    'risk_of_water_contamination_other_sources',
                    risk_of_water_contamination_other_sources,
                    [],
                    index,
                    'source'
                  )
                "
                >X</base-button
              >
            </div>
          </div>
        </div>
      </div>
    </form>
    <base-button
      size="md"
      type="info"
      data-toggle="tooltip"
      data-placement="top"
      title="Add other sources"
      class="btn-icon-only rounded-circle noprint"
      @click="
        addItemRow(
          'risk_of_water_contamination_other_sources',
          risk_of_water_contamination_other_sources,
          [],
          'source'
        )
      "
    >
      <i class="ni ni-fat-add"></i>
    </base-button>
    <hr />

    <h2>
      <b class="alert-suc">Status of Other water sources impacted</b>
    </h2>
    <form>
      <div
        class="row row-example"
        v-for="(value, index) in other_water_sources_impacted"
        v-bind:key="index"
      >
        <div class="col-md">
          <base-input
            label="Please specify source (if any)"
            data-toggle="tooltip"
            data-placement="top"
            title="Choose a source"
            v-model="value.name"
            placeholder="Specify a source"
          ></base-input>
        </div>
        <div class="row pt-5">
          <div class="col-md">
            <base-radio
              name="Not Affected"
              class="mb-3"
              v-bind:value="'Not Affected'"
              data-toggle="tooltip"
              data-placement="top"
              title="not affected"
              :disabled="value.name == null || value.name === ''"
              v-model="value.status"
              >Not affected</base-radio
            >
          </div>

          <div class="col-md">
            <base-radio
              name="Severely Affected"
              class="mb-3"
              v-bind:value="'Severely Affected'"
              data-toggle="tooltip"
              data-placement="top"
              title="Severly affected"
              :disabled="value.name == null || value.name === ''"
              v-model="value.status"
              >Severely affected</base-radio
            >
          </div>

          <div class="col-md">
            <base-radio
              name="Slightly Affected"
              class="mb-3"
              v-bind:value="'Slightly Affected'"
              data-toggle="tooltip"
              data-placement="top"
              title="slightly affected"
              :disabled="value.name == null || value.name === ''"
              v-model="value.status"
              >Slightly affected</base-radio
            >
          </div>
        </div>

        <div class="col-md pr-5 pt-5">
          <base-button
            size="sm"
            type="warning"
            class="btn-icon-only rounded-circle"
            data-toggle="tooltip"
            data-placement="top"
            title="Remove other sources impacted"
            @click="
              removeItemRow(
                'other_water_sources_impacted',
                other_water_sources_impacted,
                [],
                index,
                'name'
              )
            "
            >X</base-button
          >
        </div>
      </div>
    </form>
    <base-button
      size="md"
      type="info"
      class="btn-icon-only rounded-circle noprint"
      data-toggle="tooltip"
      data-placement="top"
      title="Add other sources impacted"
      @click="
        addItemRow(
          'other_water_sources_impacted',
          other_water_sources_impacted,
          [],
          'name'
        )
      "
    >
      <i class="ni ni-fat-add"></i>
    </base-button>
    <hr />
    <b>Response Needed for the WASH Cluster</b>
    <hr class="mt-3 mb-3" />
    <base-input label="General Response needed for WASH cluster">
      <textarea
        class="form-control"
        id="exampleFormControlTextarea3"
        data-toggle="tooltip"
        data-placement="top"
        title="Type the general response needed"
        placeholder="Type the response needed for the WASH cluster"
        v-model="response_needed"
        rows="3"
      ></textarea>
    </base-input>
    <base-input label="Urgent Response needed for WASH cluster">
      <textarea
        class="form-control"
        id="exampleFormControlTextarea3"
        data-toggle="tooltip"
        data-placement="top"
        title="Type the urgent response needed"
        placeholder="Type the urgent response needed for the WASH cluster"
        v-model="urgent_response_needed"
        rows="3"
      ></textarea>
    </base-input>
    <base-button
      size="lg"
      @click.stop="save"
      data-toggle="tooltip"
      data-placement="top"
      title="Save and goto next section"
      type="primary"
      class="noprint"
      >Save & Continue</base-button
    >
  </div>
</template>

<script src="./index.js"/>
