
export default {
  $_veeValidate: {
    validator: 'new'
  },
  components: {},
  props: ['education', 'max'],
  data () {
    return {
      dataset: {},
      maxValue: this.max,
      response_needed: '',
      roofs_affected: 0,
      partially_functioning: 0,
      underwater: 0,
      completely_damaged: 0,
      functioning_buildings: 0,
      partially_functioning_buildings: 0,
      closed_buildings: 0,
      males_out_of_school: 0,
      females_out_of_school: 0,
      impact_on_schools: [],
      impact_on_schools_school_goers: [],
      school_buildings_school_types: [
        { name: 'Pre-School' },
        { name: 'Primary School' },
        { name: 'Secondary School' },
        { name: 'Tertially School' }
      ],
      school_goers_school_types: [
        { name: 'Pre-School' },
        { name: 'Primary School' },
        { name: 'Secondary School' },
        { name: 'Tertially School' }
      ],
      structures: [
        { name: 'Feeding shelters' },
        { name: 'handwashing facilities' },
        { name: 'Libraries' },
        { name: 'Teacher Houses' },
        { name: 'Teaching and learning facilities' },
        { name: 'Toile<PERSON>' },
        { name: 'Water Facilities' }
      ],
      radio: {
        radio1: 'radio1',
        radio2: 'radio2'
      }
    }
  },
  computed: {
    tt_school_buildings_damaged () {
      return (
        parseInt(
          this.partially_functioning === undefined ||
            this.partially_functioning.length === 0
            ? 0
            : this.partially_functioning
        ) +
        parseInt(
          this.completely_damaged === undefined ||
            this.completely_damaged.length === 0
            ? 0
            : this.completely_damaged
        )
      )
    },
    tt_learners_out_of_school () {
      return (
        parseInt(
          this.females_out_of_school === undefined ||
            this.females_out_of_school.length === 0
            ? 0
            : this.females_out_of_school
        ) +
        parseInt(
          this.males_out_of_school === undefined ||
            this.males_out_of_school.length === 0
            ? 0
            : this.males_out_of_school
        )
      )
    },
    tt_female_learners_out_of_school () {
      return parseInt(
        this.females_out_of_school === undefined ||
          this.females_out_of_school.length === 0
          ? 0
          : this.females_out_of_school
      )
    },
    tt_male_learners_out_of_school () {
      return parseInt(
        this.males_out_of_school === undefined ||
          this.males_out_of_school.length === 0
          ? 0
          : this.males_out_of_school
      )
    }
  },

  methods: {
    addItemRow (member, array_name, static_data = [], key_value) {
      this.$emit(
        'addItemRow',
        'education',
        member,
        array_name,
        static_data,
        key_value
      )
    },
    removeItemRow (
      member,
      array_name,
      static_data = [],
      index,
      key_value,
      arrayTotals
    ) {
      this.$emit(
        'removeItemRow',
        'education',
        member,
        array_name,
        static_data,
        index,
        key_value
      )
      let arrayCopy = []
      for (let i = 0; i < arrayTotals.length; i++) {
        if (array_name == '') {
          let dynamic_element = arrayTotals[i]
          arrayCopy.push({ dynamic_element: 0 })
          this.init_totals(arrayTotals[i], arrayCopy, arrayTotals[i])
        } else {
          this.init_totals(arrayTotals[i], array_name, arrayTotals[i])
        }
      }
    },
    init_totals (key, array_name, member) {
      this[member] = array_name
        .map(function (item) {
          return parseInt(item[key]) ? parseInt(item[key]) : 0
        })
        .reduce((sum, value) => sum + value, 0)
    },
    save () {
      this.education.response_needed = this.response_needed
      this.education.urgent_response_needed = this.urgent_response_needed
      this.education.impact_on_schools = this.impact_on_schools.filter(
        value => Object.keys(value).length !== 0
      )

      this.$emit('save', this.education, 'education')
    },
    autosave () {
      this.education.response_needed = this.response_needed
      this.education.urgent_response_needed = this.urgent_response_needed
      this.education.impact_on_schools = this.impact_on_schools.filter(
        value => Object.keys(value).length !== 0
      )

      this.$emit('autosave', this.education, 'education')
    }
  },
  beforeMount () {
    this.education = typeof this.education !== 'undefined' ? this.education : {}
    setInterval(this.autosave, 1200)

    let dataset = JSON.parse(localStorage.getItem('education').data)

    this.impact_on_schools =
      typeof dataset.impact_on_schools === 'undefined'
        ? this.impact_on_schools
        : dataset.impact_on_schools

    this.response_needed =
      typeof dataset.response_needed === 'undefined'
        ? this.response_needed
        : dataset.response_needed

    this.urgent_response_needed =
      typeof dataset.urgent_response_needed === 'undefined'
        ? this.urgent_response_needed
        : dataset.urgent_response_needed

    this.init_totals('roofs_affected', this.impact_on_schools, 'roofs_affected')
    this.init_totals(
      'partially_functioning',
      this.impact_on_schools,
      'partially_functioning'
    )
    this.init_totals(
      'completely_damaged',
      this.impact_on_schools,
      'completely_damaged'
    )
    this.init_totals(
      'functioning_buildings',
      this.impact_on_schools,
      'functioning_buildings'
    )
    this.init_totals(
      'partially_functioning_buildings',
      this.impact_on_schools,
      'partially_functioning_buildings'
    )
    this.init_totals(
      'closed_buildings',
      this.impact_on_schools,
      'closed_buildings'
    )
    this.init_totals(
      'males_out_of_school',
      this.impact_on_schools,
      'males_out_of_school'
    )
    this.init_totals(
      'females_out_of_school',
      this.impact_on_schools,
      'females_out_of_school'
    )
  }
}
