import { Colors } from './Colors'
const timeFormat = d3.timeFormat('%d-%m-%Y')

export const seriesOptions = (d3, dc, series) => {
  series
    .chart(function (c) {
      return dc.scatterPlot(c).symbolSize(5)
    })
    .height(450)
    .brushOn(false)
    .yAxisLabel('HH affected')
    .xAxisLabel('Month, Year')
    .clipPadding(10)
    .seriesAccessor(function (d) {
      return d.key[0]
    })
    .keyAccessor(function (d) {
      return d.key[1]
    })
    .valueAccessor(function (d) {
      return d.value
    })
    .turnOnControls(true)
    .ordinalColors(Colors)
    .title(
      d =>
        `${d.value} HH with shelter affected by ${
          d.key[0]
        }, which was submitted to the system on ${timeFormat(d.key[1])}`
    )
    .legend(
      dc
        .legend()
        .x(100)
        .y(30)
        .itemHeight(13)
        .gap(5)
        .horizontal(1)
        .legendWidth(240)
        .itemWidth(100)
    )
  /*  chart.yAxis().tickFormat(function (d) {
        return d3.format(",d")(d + 299500);
      }); */
  series.margins().left += 20
  series.margins().bottom += 20
  series.margins().top += 20

  series

    //.x(d3.scaleTime().domain([minDate, maxDate]))

    .clipPadding(10)
    .elasticY(true)
  //chart.yAxis().tickFormat(d3.format(".3s"));
  series.xAxis().tickFormat(d3.timeFormat('%b,%Y'))

  return series
}
