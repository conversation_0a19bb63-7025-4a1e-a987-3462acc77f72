<template>
  <div>
    <div class style="background:#F4F4F4">
      <div class="row pt-4">
        <div class="col">
          <div class="card">
            <div class="card-header">
              <span style="color:teal;font-size:150%">
                <b>
                  HEALTH [
                  <span style="color:red"
                    >{{ data.Disaster }} - {{ data.District }}</span
                  >
                  ]
                </b>
                <b class="pull-right"
                  >ASSESSMENT PERIOD :
                  {{
                    dateFormate(
                      new Date(data.Date_of_Disaster_from).toLocaleDateString(
                        "en-US"
                      )
                    )
                  }}
                  -
                  {{
                    dateFormate(
                      new Date(data.Date_of_Disaster_to).toLocaleDateString(
                        "en-US"
                      )
                    )
                  }}</b
                >
              </span>
              <hr />
              <div class="row">
                <!--   <base-input
                  label="TAs"
                  style="color:Lime;"
                  class="col-xl-5"
                  :value="data.TA"
                  disabled
                ></base-input>-->
                <base-input label="TA" class="col-xl-3">
                  <el-select
                    v-model="selected"
                    @change="getInfoGraphicsData(selected)"
                    placeholder="select"
                  >
                    <el-option
                      v-for="option in data.TAList"
                      :key="option.label"
                      :label="option.label"
                      :value="option.value + ',' + option.label"
                    ></el-option>
                  </el-select>
                </base-input>
                <base-input
                  label="GVHs"
                  class="col-xl-9"
                  :value="data.GVHS_affected"
                  readonly
                ></base-input>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-xl-4 col-md-6">
          <stats-card
            title="Facilities closed"
            type="gradient-green"
            :sub-title="data.number_facilities_closed_health.toString()"
            icon="icon-unhcr-locations-centre"
          >
            <template slot="footer"></template>
          </stats-card>
        </div>

        <div class="col-xl-4 col-md-6">
          <stats-card
            title="Facilites on verge to closed"
            type="gradient-green"
            :sub-title="
              data.number_of_facilities_on_verge_of_closing_health.toString()
            "
            icon="icon-unhcr-locations-centre"
          >
            <template slot="footer"></template>
          </stats-card>
        </div>
        <div class="col-xl-4 col-md-6">
          <stats-card
            title="Faclitities partially functioning"
            type="gradient-green"
            :sub-title="
              data.number_of_facilities_partially_functioning_health.toString()
            "
            icon="icon-unhcr-locations-centre"
          >
            <template slot="footer"></template>
          </stats-card>
        </div>
      </div>
      <div class="row">
        <div class="col-xl-8">
          <div class="row">
            <div class="col-xl-6">
              <card>
                <template slot="header">
                  <!-- Subtitle -->
                  <h6 class="surtitle">Facilities status</h6>
                  <!-- Title -->
                </template>
                <div>
                  <doughtNutChart
                    :data="doughnutChartData"
                    :options="doughnutChartOptions"
                  ></doughtNutChart>
                </div>
              </card>
            </div>
            <div class="col-xl-6">
              <card>
                <template slot="header">
                  <!-- Subtitle -->
                  <h6 class="surtitle">Facilities</h6>
                </template>
                <div>
                  <barChart
                    :data="barChartData"
                    :options="barChartOptions"
                  ></barChart>
                </div>
              </card>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import flatPicker from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";

import RouteBreadCrumb from "@/components/Breadcrumb/RouteBreadcrumb";
import BaseHeader from "@/components/BaseHeader";
import StatsCard from "@/components/Cards/StatsCard";
import { Select, Option } from "element-ui";
import malawiMap from "./components/map-core";
import doughtNutChart from "./components/doughtNutChart";
import barChart from "./components/barChart";
import TagsInput from "@/components/Inputs/TagsInput";
import moment from "moment";
window.jQuery = require("jquery");

function randomScalingFactor() {
  return Math.round(Math.random() * 100);
}

export default {
  props: ["data"],
  components: {
    StatsCard,
    BaseHeader,
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    flatPicker,
    malawiMap,
    doughtNutChart,
    barChart,
    TagsInput
  },
  data() {
    return {
      TAList: [],
      selected: {},
      tags: ["Floods", "2019-01-01 to 2019-12-31"],
      doughnutChartOptions: {
        hoverBorderWidth: 20
      },

      doughnutChartData: {
        hoverBackgroundColor: "red",
        hoverBorderWidth: 10,
        labels: ["Closed", "Verge to close", "Partially functioning"],
        datasets: [
          {
            backgroundColor: ["#00000", "teal", "#a97142"],
            data: [
              this.data.number_facilities_closed_health,
              this.data.number_of_facilities_on_verge_of_closing_health,
              this.data.number_of_facilities_partially_functioning_health
            ]
          }
        ]
      },
      barChartOptions: {
        hoverBorderWidth: 20
      },
      barChartData: {
        hoverBackgroundColor: "red",
        hoverBorderWidth: 10,
        labels: ["Closed", "Verge to close", "Partially functioning"],
        datasets: [
          {
            label: "Faclities",
            backgroundColor: "teal",
            data: [
              this.data.number_facilities_closed_health,
              this.data.number_of_facilities_on_verge_of_closing_health,
              this.data.number_of_facilities_partially_functioning_health
            ]
          }
        ]
      },

      filter: {
        disasters: "Floods",
        range: "2019-01-01 to 2019-12-31"
      },
      disasters: [
        {
          name: "Heavy Rains"
        },
        {
          name: "Earth quake"
        },
        {
          name: "Floods"
        }
      ],
      districts: [
        {
          label: "Chikwawa",
          value: "Chikwawa",
          region: "South"
        },
        {
          label: "Mangochi",
          value: "Mangochi",
          region: "South"
        },
        {
          label: "Balaka",
          value: "Balaka",
          region: "North"
        },
        {
          label: "Phalombe",
          value: "Phalombe",
          region: "Central"
        }
      ],
      regions: [
        {
          region: "South"
        },
        {
          region: "Central"
        },
        {
          region: "North"
        },
        {
          region: "East"
        }
      ],
      houses: [
        {
          id: 1,
          name: "Completely damaged",
          number: 67,
          males: 78,
          females: 59
        },
        {
          id: 2,
          name: "Partly damaged",
          number: 400,
          males: 76,
          females: 79
        },
        {
          id: 3,
          name: "Underwater",
          number: 79,
          males: 68,
          females: 99
        }
      ]
    };
  },
  methods: {
    dateFormate(dateData) {
      const cur_date = dateData;
      const formattedDate = moment(cur_date).format("DD/MM/YYYY");

      return formattedDate;
    },
    getInfoGraphicsData(selectedTA) {
      var data = selectedTA.split(",");
     
      var TA = data[1];
      var ID = data[0];
      if (TA === "All") {
        this.$emit("Dinr", ID);
      } else {
        this.$emit("Dra", ID);
      }
    }
  },
  mounted() {
    this.selected = this.data.TAname;
  }
};
</script>
<style>
#malawiMap {
}
@import "../../../../assets/fonts/font-humanitarian.css";
</style>
