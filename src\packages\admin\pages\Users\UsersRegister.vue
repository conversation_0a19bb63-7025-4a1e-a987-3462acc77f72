<template>
  <div class="md-layout">
    <div class="md-layout-item md-size-50 mx-auto md-small-size-50">
      <form>
        <md-card class="md-size-50 mx-auto">
          <md-card-header class="md-card-header-icon md-card-header-green">
            <div class="card-icon">
              <md-icon>perm_identity</md-icon>
            </div>
            <h4 class="title">
              Register User -
              <small>Complete user profile</small>
            </h4>
          </md-card-header>

          <md-card-content>
            <div class="md-layout">
              <label class="md-layout-item md-size-20 md-form-label"
                >First Name</label
              >
              <div class="md-layout-item">
                <md-field
                  :class="[
                    { 'md-error': errors.has('firstName') },
                    {
                      'md-valid': !errors.has('firstName') && touched.firstName
                    }
                  ]"
                >
                  <md-input
                    v-model="firstName"
                    data-vv-name="firstName"
                    type="text"
                    v-validate="modelValidations.firstName"
                    required
                  ></md-input>
                  <slide-y-down-transition>
                    <md-icon class="error" v-show="errors.has('firstName')"
                      >close</md-icon
                    >
                  </slide-y-down-transition>
                  <slide-y-down-transition>
                    <md-icon
                      class="success"
                      v-show="!errors.has('firstName') && touched.firstName"
                      >done</md-icon
                    >
                  </slide-y-down-transition>
                </md-field>
              </div>
            </div>
            <div class="md-layout">
              <label class="md-layout-item md-size-20 md-form-label"
                >Last Name</label
              >
              <div class="md-layout-item">
                <md-field
                  :class="[
                    { 'md-error': errors.has('lastName') },
                    { 'md-valid': !errors.has('lastName') && touched.lastName }
                  ]"
                >
                  <md-input
                    v-model="lastName"
                    data-vv-name="lastName"
                    type="text"
                    v-validate="modelValidations.lastName"
                    required
                  ></md-input>
                  <slide-y-down-transition>
                    <md-icon class="error" v-show="errors.has('lastName')"
                      >close</md-icon
                    >
                  </slide-y-down-transition>
                  <slide-y-down-transition>
                    <md-icon
                      class="success"
                      v-show="!errors.has('lastName') && touched.lastName"
                      >done</md-icon
                    >
                  </slide-y-down-transition>
                </md-field>
              </div>
            </div>
            <div class="md-layout">
              <label class="md-layout-item md-size-20 md-form-label"
                >Email</label
              >
              <div class="md-layout-item">
                <md-field
                  :class="[
                    { 'md-error': errors.has('email') },
                    { 'md-valid': !errors.has('email') && touched.email }
                  ]"
                >
                  <md-input
                    v-model="email"
                    data-vv-name="email"
                    type="text"
                    v-validate="modelValidations.email"
                    required
                  ></md-input>
                  <slide-y-down-transition>
                    <md-icon class="error" v-show="errors.has('email')"
                      >close</md-icon
                    >
                  </slide-y-down-transition>
                  <slide-y-down-transition>
                    <md-icon
                      class="success"
                      v-show="!errors.has('email') && touched.email"
                      >done</md-icon
                    >
                  </slide-y-down-transition>
                </md-field>
              </div>
            </div>
            <div class="md-layout">
              <label class="md-layout-item md-size-20 md-form-label"
                >Phone</label
              >
              <div class="md-layout-item">
                <md-field
                  :class="[
                    { 'md-error': errors.has('phone') },
                    { 'md-valid': !errors.has('phone') && touched.phone }
                  ]"
                >
                  <md-input
                    v-model="phone"
                    data-vv-name="phone"
                    type="phone"
                    v-validate="modelValidations.phone"
                    required
                  ></md-input>
                  <slide-y-down-transition>
                    <md-icon class="error" v-show="errors.has('phone')"
                      >close</md-icon
                    >
                  </slide-y-down-transition>
                  <slide-y-down-transition>
                    <md-icon
                      class="success"
                      v-show="!errors.has('phone') && touched.phone"
                      >done</md-icon
                    >
                  </slide-y-down-transition>
                </md-field>
              </div>
            </div>
            <div class="md-layout">
              <label class="md-layout-item md-size-20 md-form-label"
                >Password</label
              >
              <div class="md-layout-item">
                <div class="md-layout">
                  <div class="md-layout-item">
                    <md-field
                      :class="[
                        { 'md-error': errors.has('password') },
                        {
                          'md-valid':
                            !errors.has('password') && touched.password
                        }
                      ]"
                    >
                      <label>Password</label>
                      <md-input
                        v-model="password"
                        data-vv-name="password"
                        ref="password"
                        type="password"
                        v-validate="modelValidations.password"
                        required
                      ></md-input>
                      <slide-y-down-transition>
                        <md-icon class="error" v-show="errors.has('password')"
                          >close</md-icon
                        >
                      </slide-y-down-transition>
                      <slide-y-down-transition>
                        <md-icon
                          class="success"
                          v-show="!errors.has('password') && touched.password"
                          >done</md-icon
                        >
                      </slide-y-down-transition>
                    </md-field>
                  </div>
                  <div class="md-layout-item">
                    <md-field
                      :class="[
                        { 'md-error': errors.has('passwordvalidation') },
                        {
                          'md-valid':
                            !errors.has('passwordvalidation') &&
                            touched.passwordvalidation
                        }
                      ]"
                    >
                      <label>Validation</label>
                      <md-input
                        v-model="passwordvalidation"
                        data-vv-name="passwordvalidation"
                        data-vv-as="password"
                        type="password"
                        v-validate="modelValidations.passwordvalidation"
                        required
                      ></md-input>
                      <slide-y-down-transition>
                        <md-icon
                          class="error"
                          v-show="errors.has('passwordvalidation')"
                          >close</md-icon
                        >
                      </slide-y-down-transition>
                      <slide-y-down-transition>
                        <md-icon
                          class="success"
                          v-show="
                            !errors.has('passwordvalidation') &&
                              touched.passwordvalidation
                          "
                          >done</md-icon
                        >
                      </slide-y-down-transition>
                    </md-field>
                  </div>
                </div>
              </div>
            </div>
            <div class="md-layout">
              <label class="md-layout-item md-size-20 md-form-label"
                >Role</label
              >
              <div class="md-layout-item md-size-40">
                <md-field>
                  <md-select name="items" id="items" v-model="roleName">
                    <md-option
                      v-for="item in rolesData"
                      :key="item.name"
                      :value="item.name"
                      >{{ item.name }}</md-option
                    >
                  </md-select>
                </md-field>
              </div>
            </div>
          </md-card-content>

          <md-card-actions class="text-center">
            <md-button
              native-type="submit"
              @click.native.prevent="validate"
              class="md-success"
              >Validate Inputs</md-button
            >
          </md-card-actions>
        </md-card>
      </form>
    </div>
  </div>
</template>
<script>
import { SlideYDownTransition } from "vue2-transitions";
import VueButtonSpinner from "vue-button-spinner";
import { users } from "../../Api/users";
import { roles } from "../../Api/roles";
import Swal from "sweetalert2";

export default {
  components: {
    SlideYDownTransition
  },
  data() {
    return {
      formModel: {},
      firstName: "",
      lastName: "",
      phone: "",
      email: "",
      roleName: null,
      password: "",
      passwordvalidation: "",
      rolesData: [],
      isLoading: false,
      touched: {
        firstName: false,
        lastName: false,
        email: false,
        phone: false,
        url: false,
        password: false,
        passwordvalidation: false
      },
      modelValidations: {
        firstName: {
          required: true
        },
        lastName: {
          required: true
        },
        email: {
          required: true,
          email: true
        },
        phone: {
          required: true,
          numeric: true
        },
        url: {
          required: true,
          url: true
        },
        password: {
          required: true,
          min: 6
        },
        passwordvalidation: {
          required: true,
          confirmed: "password",
          min: 6
        }
      }
    };
  },
  methods: {
    validate() {
      this.$validator.validateAll().then(isValid => {
        this.isLoading = true;
        this.submit();
      });
    },
    submit() {
      this.formModel.firstName = this.firstName;
      this.formModel.lastName = this.lastName;
      this.formModel.phone = this.phone;
      this.formModel.nrbNumber = "";
      this.formModel.employeeNumber = "";
      this.formModel.email = this.email;
      this.formModel.statusId = 1;
      this.formModel.roleName = this.roleName;
      this.formModel.username = this.firstName + this.lastName;
      this.formModel.password = this.password;
      users
        .create(
          "P2KHLaJQv2GK1ZE7aMncyRgAdIUxWBmyI9DknRipdvoQ9si8zY8IBYZXaXPZBdsq",
          this.formModel
        )
        .then(
          response => {
            Swal.fire({
              title: "Succesfully created account",
              confirmButtonClass: "md-button md-success btn-fill",
              cancelButtonClass: "md-button md-danger btn-fill",
              text:
                "Please check your email and click on the verification link and account details",
              type: "success",
              animation: false
            });
          },
          reason => {
            Swal.fire({
              title: "Failed to create account",
              confirmButtonClass: "md-button md-success btn-fill",
              cancelButtonClass: "md-button md-danger btn-fill",
              text:
                "possible duplication of  email and username (" + reason + ")",
              type: "error",
              animation: false
            });
          }
        );
      this.isLoading = false;
    }
  },
  watch: {
    firstName() {
      this.touched.firstName = true;
    },
    lastName() {
      this.touched.lastName = true;
    },
    email() {
      this.touched.email = true;
    },
    phone() {
      this.touched.phone = true;
    },
    url() {
      this.touched.url = true;
    },
    password() {
      this.touched.password = true;
    },
    passwordvalidation() {
      this.touched.passwordvalidation = true;
    }
  },
  mounted() {
    roles
      .get("P2KHLaJQv2GK1ZE7aMncyRgAdIUxWBmyI9DknRipdvoQ9si8zY8IBYZXaXPZBdsq")
      .then(response => {
        this.rolesData = response.data;
      });
  }
};
</script>
<style lang="scss" scoped>
.md-card .md-card-actions {
  border: none;
}

.text-center {
  justify-content: center !important;
}
</style>
