<template>
  <div class="content">
    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Disaster</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/manager/dashboard">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">Acknownledge Reports</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">All</li>
            </ol>
          </nav>
        </div>
        <!-- <div class="col-lg-6 col-6 text-right pr-3"> -->
         <!--  <router-link to="/manager/reports/excel" class="mr-2">
            <base-button size="sm" type="neutral">
              <i class="text-primary ni ni-archive-2"></i> ARCHIVES
            </base-button>
          </router-link> -->

          <!-- <base-dropdown title-classes="btn btn-sm btn-warning mr-0" menu-on-right :has-toggle="false">
            <a slot="title" class="dropdown-toggle" type="neutral" size="sm">
              <i class="text-black ni ni-cloud-download-95"></i> EXPORT ALL TO EXCEL
            </a>
            <a class="dropdown-item" @click="generateColorCodeSheet('TA')">All TA-Based data</a>
            <a class="dropdown-item" @click="generateColorCodeSheet('GVH')">All GVH-based data</a>
            <a class="dropdown-item" @click="generateColorCodeSheet('EXCEL_data')">All Disaster Summary</a>
          </base-dropdown> -->

          <!-- <base-dropdown style="padding-left: 5px" title-classes="btn btn-sm btn-primary mr-0" menu-on-right
            :has-toggle="false">
            <a slot="title" class="dropdown-toggle" type="neutral" size="sm">
              <i class="text-black ni ni-cloud-download-95"></i> EXPORT ALL TO CSV
            </a>
            <a class="dropdown-item" @click="generateColorCodeSheet('CSV_data')">All Disaster Summary</a>
          </base-dropdown> -->
        <!-- </div> -->
        <div class="col-lg-6 col-5 text-right"></div>
      </div>

      <!-- Card stats -->
    </base-header>
    <div class="container-fluid mt--6">
      <div>
        <card class="no-border-card" body-classes="px-0 pb-1" footer-classes="pb-2">
          <template slot="header">
            <h3 class="mb-0">Acknownledge Receipt</h3>
          </template>
          <div>
            <div class="
                col-12
                d-flex
                justify-content-center justify-content-sm-between
                flex-wrap
              ">
              <el-select class="select-primary pagination-select" v-model="pagination.perPage" placeholder="Per page">
                <el-option class="select-primary" v-for="item in pagination.perPageOptions" :key="item" :label="item"
                  :value="item"></el-option>
              </el-select>

              <div>
                <base-input v-model="searchQuery" prepend-icon="fas fa-search" placeholder="Search..."></base-input>
              </div>
            </div>
            <div>
              <b-table responsive sticky-header :striped="striped" :bordered="bordered" :borderless="borderless"
                :outlined="outlined" :small="small" :hover="hover" :dark="dark" :sort-icon="true" :fixed="fixed"
                :foot-clone="footClone" :no-border-collapse="noCollapse" head-variant="light"
                :table-variant="tableVariant" :items="queriedData" :fields="tableColumns">
                 <!-- Scoped Slot for Acted On Date -->

                <template #cell(actions)="row">




                  <b-button
                  @click="submitAck(row.item)"
                  class="edit bg-primary text-white"
                  type="primary"
                  size="sm"
                  icon
                >
                  <i class="ni ni-submit"></i> Acknowledge
                </b-button>



                </template>
              </b-table>
            </div>
          </div>
          <div slot="footer" class="
              col-12
              d-flex
              justify-content-center justify-content-sm-between
              flex-wrap
            ">
            <div class>
              <p class="card-category">
                Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
                <span v-if="selectedRows.length">&nbsp; &nbsp; {{ selectedRows.length }} rows selected</span>
              </p>
            </div>
            <base-pagination class="pagination-no-border" v-model="pagination.currentPage"
              :per-page="pagination.perPage" :total="total"></base-pagination>
          </div>
        </card>
      </div>
    </div>
  </div>
</template>
<script>
import Swal from "sweetalert2";
import { Table, TableColumn, Select, Option } from "element-ui";
import RouteBreadCrumb from "@/components/Breadcrumb/RouteBreadcrumb";
import { BasePagination } from "@/components";
import clientPaginationMixin from "@/components/PaginatedTables/clientPaginationMixin";
import { admin2s } from "../api/location/admin2s";
import EventBus from "../../../helpers/event-bus"
import { flatten } from "../../../store/flatdinrs/flatten";
import { resource } from '../../../api/dinrs/index.js';
import { generateFlatSectorData, processGVHExcel, exportAllDisasterData  } from "../../../util/generateFlatSectorData";
import {
  generateExcel,
  generateTAGVHExcel,
  generateDisasterSummaryExcel
} from "../../../util/generateExcel";
import { generateCSV } from "../../../util/generateCSV";
import utils from "../../../util/dashboard";
import { MongoReports } from "../api/MongoReports";
import downloadexcel from "vue-json-excel";
import JSZip from "jszip";
import FileSaver from "file-saver";
import moment from "moment";
import dateformat from "dateformat";
import { mapGetters, mapActions } from "vuex";
import { dinrforms } from "../../districtmanager/api/forms/dinrforms.js";
import { download } from "../../../util/download";

export default {
  mixins: [clientPaginationMixin],
  components: {
    BasePagination,
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    [Table.name]: Table,
    downloadexcel,
    [TableColumn.name]: TableColumn
  },
  computed:{
    userData() {
      const sessionData = sessionStorage.getItem('vue-session-key');
      return sessionData ? JSON.parse(sessionData).userObj : { firstName: 'N/A', lastName: 'N/A' };
    }
  },
  data() {
    return {
      propsToSearch: ["disaster", "district", "officer", "date"],
      searchQuery: "",
      dinrFormsData: {},
      villagesArray: [],
      campsArray: [],
      bufferDraFormsData: [],
      finalExcelData: [],
      TAarray: [],
      draFormsData: [],
      download: [],
      test: [],
      gvhDATA: [],
      allforms: [],
      districtsCopy: [],
      downloadData: [],
      testarray: [],
      TaData: [],
      admin2sData: [],
      AllDisasterSummaryExcel: [],
      disablebutton: false,
      dinrDataa: [],
      gridOptions: {},
      acknowledgedBy:'',
      acknowledgedOn:'',
      tableColumns: [
        {
          key: "id",
          label: "ID",
          sortable: true
        },
        {
          key: "disaster",
          label: "Disaster",
          sortable: true
        },
        {
          key: "district",
          label: "District",
          sortable: true
        },
        {
          key: "officer",
          label: "Officer",
          sortable: true
        },

        {
          key: "date",
          label: "Submmited on",
          sortable: true
        },

        {
          key: "actions",
          label: "Actions"
        }
      ],
      dateReference:new Date('2022-06-01'),
      tableData: [],
      selectedRows: []
    };
  },
  computed: {
    ...mapGetters({
      getDinrs: "dinrs/get",
      getDraById: "dras/getById",
      getDinrById: "dinrs/getById"
    }),
    ...mapActions({
      loadFlatdinr: "loadFlatdinrs"
    }),
    userData() {
      const sessionData = sessionStorage.getItem('vue-session-key');
      return sessionData ? JSON.parse(sessionData).userObj : { firstName: 'N/A', lastName: 'N/A' };
    }
  },
  $route(to, from) {
    if (to.name === 'Reports') {
      this.fetchLatestReports(); // Re-fetch reports when navigating back
    }
  },
  methods: {
    submitAck: function (item) {
  this.loading = true; // Show loader
  const updateUrl = resource + "/" + item.uuid; // URL for the PATCH request

  const payload = {
    acknowledgeOn: new Date(), // Current date and time
    acknowledgeBy: this.userData.firstName + " " + this.userData.lastName, // User who acknowledged
  };

  axios.patch(updateUrl, payload)
    .then(response => {
      console.log("Acknowledgment successful:", response.data);
      this.tableData = this.tableData.filter(report => report.uuid !== item.uuid);

      // Ensure data is available for notification
      if (this.dinrFormsData && this.dinrFormsData.disaster && this.dinrFormsData.district) {
        this.sendNotification(this.dinrFormsData);
      }

      Swal.fire("Acknowledgment Successful", "Report acknowledged by " + this.userData.firstName + " " + this.userData.lastName, "success");
    })
    .catch(error => {
      console.error("Error acknowledging report:", error);
      Swal.fire("Acknowledgment Failed", (error.response && error.response.data && error.response.data.message) || "An error occurred. Please try again.", "error");
    })
    .finally(() => this.loading = false);
},

sendNotification: function (data) {
  const notificationPayload = {
    district: data.district.admin2_name_en,
    disaster: data.disaster,
    endDate: this.formatDate(data.dodTo),
    startDate: this.formatDate(data.dodFrom),
  };
  axios.post("https://engine.immalawi.org/appsmail/api/notifications/disaster-response", notificationPayload)
    .then(notificationResponse => console.log("Notification sent successfully:", notificationResponse.data))
    .catch(notificationError => console.error("Failed to send notification:", notificationError));
},







beforeRouteEnter(to, from, next) {
  next((vm) => {
    // Fetch data every time this route is entered
    vm.fetchLatestReports(); // Ensure it always fetches fresh data
  });
},
async fetchLatestReports() {
  this.isLoading = true; // Show a loading spinner
  try {
    // Fetch the latest reports data
    const response = await this.getDinrsAction();

    // Filter out reports that have already been acknowledged
    const unacknowledgedReports = response.filter(report => !report.acknowledgeBy);

    // Update tableData with unacknowledged reports only
    this.tableData = unacknowledgedReports.map((dinr, index) => ({
      id: index + 1,
      disaster: dinr.disaster,
      district: dinr.district.admin2_name_en,
      date: this.formatedDate(dinr.createdon),
      officer: dinr.account.firstName + " " + dinr.account.lastName,
      actedonDate: dinr.actedonDate ? this.formatedDate(dinr.actedonDate) : null,
      uuid: dinr._id,
      status: dinr.status,
    }));
  } catch (error) {
    console.error("Error fetching latest reports:", error);
  } finally {
    this.isLoading = false; // Stop loading spinner
  }
},



  beforeRouteEnter(to, from, next) {
    next((vm) => {
      // Fetch data every time this route is entered
      vm.fetchLatestReports();
    });
  },
    generateReport (index) {
      this.modalShow = !this.modalShow;
      this.review(index)

      setTimeout(()=>{
        EventBus.$emit("printPDF",this)
      },5000)


    },

    generateSummaryReport (index) {
      this.reviewSummary(index)

      setTimeout(()=>{
        EventBus.$emit("printSummaryPDF",this)
      },5000)
    },

    hasPictures(index) {
      let response = this.getDinrById(index.uuid);

      if (
        response.disasterImages == "undefined" ||
        response.disasterImages == null
      ) {
        this.buttonText = "No Photos";
        return true;
      }

      this.buttonText = "Photos";
      return false;
    },

    downloadDpictures(form) {
      const resource = process.env.VUE_APP_ENGINE_URL + "/forms/dinr";
      const url = `${resource}/${form.uuid}/archives`;
      axios
        .get(url, {
          responseType: "blob",
          onDownloadProgress: e => {
            console.log(e);
          }
        })
        .then(response => {
          const blob = new Blob([response.data]);
          const a = document.createElement("a");
          a.href = window.URL.createObjectURL(blob);
          a.download = `${form.disaster.split(" ").join("_")}_${moment(
            form.dodFrom
          ).format("DDMMYYYY")}.zip`;

          a.click();
        });
    },

    ...mapActions("dinrs", {
      getDinrsAction: "get"
    }),
    ...mapActions("dras", {
      getDrasAction: "get"
    }),
    generateColorCodeSheet(type) {
      exportAllDisasterData(type, this.download, this.admin2sData, this.gvhDATA, this.allforms);
    },

    generateColorSheet(type, id){


      console.log(this.download, "data")
      exportAllDisasterData(type, this.download.filter(res => res.all.dinrFormId == id), this.admin2sData, this.gvhDATA, this.allforms);

    },

    review(index) {
      let distid = index.uuid
      let disaster = this.dinrDataa.find(dist => dist._id == distid)
      let dateFrom = new Date(disaster.submitted_at)
      this.switchReportByDateSubmited(dateFrom,this.dateReference,"details",distid)
    },


    actOn(index) {
  this.$router.push({ path: `/manager/reports/summary/${index.uuid}` });
},
    reviewSummary(index) {
      let distid = index.uuid
      let disaster = this.dinrDataa.find(dist => dist._id == distid)
      let dateFrom = new Date(disaster.submitted_at)
      this.switchReportByDateSubmited(dateFrom,this.dateReference,"summary",distid)
    },
    handleInfographics(row) {
      this.$router.push({ path: "/manager/dinrreports/" + row.uuid });
    },


    addDays(date, days) {
      var result = new Date(date);
      result.setDate(result.getDate() + days);
      return result;
    },
    catchError(data = "") {
      if (typeof data === undefined) {
        return "";
      }

      return data;
    }
  },
  async mounted() {
    let dinrs = [...(await this.getDinrsAction())];
    let dras = [...(await this.getDrasAction())];

    let DINRData = await dinrs;
    let DRASData = await dras;

    this.dinrDataa = DINRData
    this.allforms = [...(await dinrs)];

    this.allforms.forEach((dinr, index) => {
      var draarr = [];
      DRASData.forEach((dra, point) => {
        if (dinr._id == dra.dinrFormId) {
          draarr.push({ ...dra });
        }
        dinr.dra = [...draarr];
      });
    });

    DINRData.forEach((dinr, index) => {
      DINRData.sort(function (a, b) {
        return new Date(b.createdon) - new Date(a.createdon);
      });

      let row = DINRData[index]._id;
      this.tableData.push({
        id: index + 1,
        disaster: DINRData[index].disaster,
        district: DINRData[index].district.admin2_name_en,
        date: this.formatedDate(DINRData[index].createdon),
        officer:
          DINRData[index].account.firstName +
          " " +
          DINRData[index].account.lastName,
          actedonDate: dinr.actedonDate ? this.formatedDate(dinr.actedonDate) : null,
        uuid: DINRData[index]._id,
        status: DINRData[index].status
      });

      let dinformDataset = {
        disaster: DINRData[index].disaster,
        district: DINRData[index].district.admin2_name_en,
        date: this.formatedDate(DINRData[index].createdon),
        doaAcpc: DINRData[index].doaAcpc,
        doaDcpc: DINRData[index].doaDcpc,
        dodFrom: DINRData[index].dodFrom,
        dodTo: DINRData[index].dodTo,

        officer:
          DINRData[index].account.firstName +
          " " +
          DINRData[index].account.lastName,
        uuid: DINRData[index]._id,
        country: "MALAWI",
        country_pcode: "MWI"
        //status:  DINRData[index].status
      };

      let dra = dras.filter(dra => dra.dinrFormId == row);

      this.draFormsData = [];

      this.downloadData.dinrform = {};

      dra.forEach((item, index, array) => {
        generateFlatSectorData(item, dinformDataset, this.downloadData, this.villagesArray, this.campsArray, this.download)
        //this.processExcel(item, dinformDataset);
      });
    });



    if (!this.tableData.length) {
    await this.fetchLatestReports();
  }

    processGVHExcel(this.download, this.gvhDATA);

    admin2s.get().then(response => {
      //console.log(response.data);
      this.admin2sData = response.data;
    });
  }
};
</script>
<style>
.no-border-card .card-footer {
  border-top: 0;
}

button[disabled] {
  cursor: not-allowed;
}
</style>
