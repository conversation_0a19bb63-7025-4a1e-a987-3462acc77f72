export default {
  $_veeValidate: {
    validator: 'new'
  },
  components: {},
  props: ['wash', 'max'],
  data () {
    return {
      maxValue: this.max,

      with_safe_water_fhh: '',
      with_safe_water_mhh: '',
      access_to_toilets_fhh: '',
      access_to_toilets_mhh: '',
      risk_contamination_fhh: '',
      risk_contamination_mhh: '',
      response_needed: '',
      dataset: {},
      other_water_sources_impacted: [],
      risk_of_water_contamination_other_sources: [],
      risk_of_water_contamination: [],
      radio: {
        radio1: 'radio1',
        radio2: 'radio3'
      },
      impact_on_water_sources: [],
      contamination_sources: [
        { name: 'Faecal Matter' },
        { name: 'Refuse' },
        { name: 'Chemicals' },
        { name: 'Flood Water' }
      ],
      water_sources: [
        { name: 'Piped Water supply' },
        { name: 'Borehole' },
        { name: 'Disability accessible Boreholes' },
        { name: 'Unprotected Spring/Well' },
        { name: 'Bowser/Tank' },
        { name: 'Other improved water source' },
        { name: 'Other unimproved water source' }
      ]
    }
  },

  methods: {
    save () {
      this.wash.with_safe_water_mhh = this.with_safe_water_mhh
      this.wash.with_safe_water_fhh = this.with_safe_water_fhh
      this.wash.access_to_toilets_fhh = this.access_to_toilets_fhh
      this.wash.access_to_toilets_mhh = this.access_to_toilets_mhh
      this.wash.risk_contamination_fhh = this.risk_contamination_fhh
      this.wash.risk_contamination_mhh = this.risk_contamination_mhh
      this.wash.response_needed = this.response_needed
      this.wash.urgent_response_needed = this.urgent_response_needed
      this.wash.other_water_sources_impacted = this.other_water_sources_impacted.filter(
        value => Object.keys(value).length !== 0
      )
      this.wash.risk_of_water_contamination = this.risk_of_water_contamination.filter(
        value => Object.keys(value).length !== 0
      )
      this.wash.impact_on_water_sources = this.impact_on_water_sources.filter(
        value => Object.keys(value).length !== 0
      )
      this.wash.risk_of_water_contamination_other_sources = this.risk_of_water_contamination_other_sources.filter(
        value => Object.keys(value).length !== 0
      )
      this.$emit('save', this.wash, 'wash')
    },

    autosave () {
      this.wash.with_safe_water_mhh = this.with_safe_water_mhh
      this.wash.with_safe_water_fhh = this.with_safe_water_fhh
      this.wash.access_to_toilets_fhh = this.access_to_toilets_fhh
      this.wash.access_to_toilets_mhh = this.access_to_toilets_mhh
      this.wash.risk_contamination_fhh = this.risk_contamination_fhh
      this.wash.risk_contamination_mhh = this.risk_contamination_mhh
      this.wash.response_needed = this.response_needed
      this.wash.urgent_response_needed = this.urgent_response_needed
      this.wash.other_water_sources_impacted = this.other_water_sources_impacted.filter(
        value => Object.keys(value).length !== 0
      )
      this.wash.risk_of_water_contamination = this.risk_of_water_contamination.filter(
        value => Object.keys(value).length !== 0
      )
      this.wash.impact_on_water_sources = this.impact_on_water_sources.filter(
        value => Object.keys(value).length !== 0
      )
      this.wash.risk_of_water_contamination_other_sources = this.risk_of_water_contamination_other_sources.filter(
        value => Object.keys(value).length !== 0
      )
      this.$emit('autosave', this.wash, 'wash')
    },

    addItemRow (member, array_name, static_data = [], key_value) {
      this.$emit(
        'addItemRow',
        'wash',
        member,
        array_name,
        static_data,
        key_value
      )
    },
    removeItemRow (member, array_name, static_data = [], index, key_value) {
      this.$emit(
        'removeItemRow',
        'wash',
        member,
        array_name,
        static_data,
        index,
        key_value
      )
    }
  },

  beforeMount () {
    this.wash = typeof this.wash !== 'undefined' ? this.wash : {}

    setInterval(this.autosave, 1200)

    let dataset = JSON.parse(localStorage.getItem('wash').data)

    this.risk_contamination_fhh =
      typeof dataset.risk_contamination_fhh === 'undefined'
        ? this.risk_contamination_fhh
        : dataset.risk_contamination_fhh

    this.risk_contamination_mhh =
      typeof dataset.risk_contamination_mhh === 'undefined'
        ? this.risk_contamination_mhh
        : dataset.risk_contamination_mhh

    this.access_to_toilets_fhh =
      typeof dataset.access_to_toilets_fhh === 'undefined'
        ? this.access_to_toilets_fhh
        : dataset.access_to_toilets_fhh

    this.access_to_toilets_mhh =
      typeof dataset.access_to_toilets_mhh === 'undefined'
        ? this.access_to_toilets_mhh
        : dataset.access_to_toilets_mhh

    this.with_safe_water_fhh =
      typeof dataset.with_safe_water_fhh === 'undefined'
        ? this.with_safe_water_fhh
        : dataset.with_safe_water_fhh

    this.with_safe_water_mhh =
      typeof dataset.with_safe_water_mhh === 'undefined'
        ? this.with_safe_water_mhh
        : dataset.with_safe_water_mhh

    this.response_needed =
      typeof dataset.response_needed === 'undefined'
        ? this.response_needed
        : dataset.response_needed

    this.urgent_response_needed =
      typeof dataset.urgent_response_needed === 'undefined'
        ? this.urgent_response_needed
        : dataset.urgent_response_needed

    this.risk_of_water_contamination =
      typeof dataset.risk_of_water_contamination === 'undefined'
        ? this.risk_of_water_contamination
        : dataset.risk_of_water_contamination
    this.impact_on_water_sources =
      typeof dataset.impact_on_water_sources === 'undefined'
        ? this.impact_on_water_sources
        : dataset.impact_on_water_sources
    this.other_water_sources_impacted =
      typeof dataset.other_water_sources_impacted === 'undefined'
        ? this.other_water_sources_impacted
        : dataset.other_water_sources_impacted
    this.risk_of_water_contamination_other_sources =
      typeof dataset.risk_of_water_contamination_other_sources === 'undefined'
        ? this.risk_of_water_contamination_other_sources
        : dataset.risk_of_water_contamination_other_sources
  },

  computed: {
    total_with_safe_water () {
      let sum =
        parseInt(
          this.with_safe_water_fhh === undefined ||
            this.with_safe_water_fhh.length === 0
            ? 0
            : this.with_safe_water_fhh
        ) +
        parseInt(
          this.with_safe_water_mhh === undefined ||
            this.with_safe_water_mhh.length === 0
            ? 0
            : this.with_safe_water_mhh
        )

      return sum
    },
    total_without_access_to_toilets () {
      let sum =
        parseInt(
          this.access_to_toilets_fhh === undefined ||
            this.access_to_toilets_fhh.length === 0
            ? 0
            : this.access_to_toilets_fhh
        ) +
        parseInt(
          this.access_to_toilets_mhh === undefined ||
            this.access_to_toilets_mhh.length === 0
            ? 0
            : this.access_to_toilets_mhh
        )

      return sum
    },
    total_risk_of_contamination () {
      let sum =
        parseInt(
          this.risk_contamination_fhh === undefined ||
            this.risk_contamination_fhh.length === 0
            ? 0
            : this.risk_contamination_fhh
        ) +
        parseInt(
          this.risk_contamination_mhh === undefined ||
            this.risk_contamination_mhh.length === 0
            ? 0
            : this.risk_contamination_mhh
        )

      return sum
    }
  }
}
