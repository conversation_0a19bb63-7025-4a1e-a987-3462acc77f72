import axios from 'axios';



const resource = process.env.VUE_APP_ENGINE_URL + '/prelim-reports'
const resource2 = process.env.VUE_APP_ENGINE_URL + '/prelim-reports/latest'


export class prelimreports {
    // static resource() {
    //     return resource
    // }

    // static get(_id = null) {
    //     if (_id == null) {
    //         return axios.get(resource).then(response => { return response });
    //     } else {
    //         return axios.get(resource + '/' + _id).then(response => { return response });
    //     }

    // }
    static count() {
        return axios.get(resource + '/count').then(response => { return response });
    }
    static async create(data) {
        let response = await axios.post(resource, data).then(response => { return response });
        return response;
    }
    static async remove(_id) {
        let response = await axios.delete(resource + '/' + _id).then(response => { return response });
        return response;
    }
    static update(data, dataID) {

       return axios.patch(resource + '/' + dataID.id, data).then(response => { return response });
    }

    static updateCoverText(data) {
        console.log("consoled", data)
        const resource = process.env.VUE_APP_ENGINE_URL + '/prelim-reports'
        return axios.put(resource + '/' + data.id, data).then(response => { return response });
    }
    static updateprelimReports(data) {

        const resource = process.env.VUE_APP_ENGINE_URL + '/prelim-reports'
        return axios.put(resource + '/' + data._id, data).then(response => { return response });
    }

    static updateAttribute(data, attributeName) {

        const resource = process.env.VUE_APP_ENGINE_URL + '/prelim-reports'
        const url = `${resource}/${data._id}/attributes/${attributeName}`
        return axios.put(url, data).then(response => { return response });
    }

    static addAttribute(data, attributeName) {

        const resource = process.env.VUE_APP_ENGINE_URL + '/prelim-reports'
        const url = `${resource}/${data._id}/attributes/${attributeName}`
        return axios.post(url, data).then(response => { return response });
    }

    static async getReports() {
        const resource = process.env.VUE_APP_ENGINE_URL + '/prelim-reports'
        const response = await axios.get(resource)

        return response.data
    }

    static async getReportsLatest() {
      const response = await axios.get(resource2)

      return response.data
  }



}
