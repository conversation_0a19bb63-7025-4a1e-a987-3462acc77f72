
export default {
  $_veeValidate: {
    validator: 'new'
  },
  components: {},
  props: ['food', 'max'],
  data () {
    return {
      response_needed: '',
      dataset: {},
      maxValue: this.max,
      food_stocks_avaliability: [],
      food_availability: [{}],
      radio: {
        radio1: 'radio1',
        radio2: 'radio3'
      },
      female_HH: 0,
      male_HH: 0,
      food_access: [],
      food_access_questions: [
        { name: 'Is marketplace still functioning?' },
        { name: 'Is marketplace accessible to all affected?' }
      ],
      food_stocks: [
        { name: 'No. of HH who lost Food Stock' },
        { name: 'No. of HH with less than 1 month Food Availability' },
        { name: 'No. of HH with 1-2 month Food Availability' },
        { name: 'No. of HH with less than 1 month Food Availability' },
        { name: 'No. of HH with more than 2 months Food Availability' }
      ]
    }
  },
  computed: {
    total_hh () {
      return (
        parseInt(
          this.female_HH === undefined || this.female_HH.length === 0
            ? 0
            : this.female_HH
        ) +
        parseInt(
          this.male_HH === undefined || this.male_HH.length === 0
            ? 0
            : this.male_HH
        )
      )
    }
  },

  methods: {
    addItemRow (member, array_name, static_data = [], key_value) {
      this.$emit(
        'addItemRow',
        'food',
        member,
        array_name,
        static_data,
        key_value
      )
    },
    removeItemRow (
      member,
      array_name,
      static_data = [],
      index,
      key_value,
      arrayTotals
    ) {
      this.$emit(
        'removeItemRow',
        'food',
        member,
        array_name,
        static_data,
        index,
        key_value
      )
      let arrayCopy = []
      for (let i = 0; i < arrayTotals.length; i++) {
        if (array_name == '') {
          let dynamic_element = arrayTotals[i]
          arrayCopy.push({ dynamic_element: 0 })
          this.init_totals(arrayTotals[i], arrayCopy, arrayTotals[i])
        } else {
          this.init_totals(arrayTotals[i], array_name, arrayTotals[i])
        }
      }
    },
    init_totals (key, array_name, member) {
      this[member] = array_name
        .map(function (item) {
          return parseInt(item[key]) ? parseInt(item[key]) : 0
        })
        .reduce((sum, value) => sum + value, 0, 0)
    },
    save () {
      this.food.response_needed = this.response_needed
      this.food.urgent_response_needed = this.urgent_response_needed
      this.food.food_availability = this.food_availability.filter(
        value => Object.keys(value).length !== 0
      )
      this.food.food_access = this.food_access.filter(
        value => Object.keys(value).length !== 0
      )
      this.food.food_stocks_avaliability = this.food_stocks_avaliability.filter(
        value => Object.keys(value).length !== 0
      )

      this.$emit('save', this.food, 'food')
    },
    autosave () {
      this.food.response_needed = this.response_needed
      this.food.urgent_response_needed = this.urgent_response_needed
      this.food.food_availability = this.food_availability.filter(
        value => Object.keys(value).length !== 0
      )
      this.food.food_access = this.food_access.filter(
        value => Object.keys(value).length !== 0
      )
      this.food.food_stocks_avaliability = this.food_stocks_avaliability.filter(
        value => Object.keys(value).length !== 0
      )

      this.$emit('autosave', this.food, 'food')
    }
  },

  beforeMount () {
    this.food = typeof this.food !== 'undefined' ? this.food : {}
    setInterval(this.autosave, 1200)

    let dataset = JSON.parse(localStorage.getItem('food').data)

    this.food_availability =
      dataset.food_availability.length == 0
        ? this.food_availability
        : dataset.food_availability

    this.food_stocks_avaliability =
      typeof dataset.food_stocks_avaliability === 'undefined'
        ? this.food_stocks_avaliability
        : dataset.food_stocks_avaliability

    this.food_access =
      typeof dataset.food_access === 'undefined'
        ? this.food_access
        : dataset.food_access

    this.response_needed =
      typeof dataset.response_needed === 'undefined'
        ? this.response_needed
        : dataset.response_needed

    this.urgent_response_needed =
      typeof dataset.urgent_response_needed === 'undefined'
        ? this.urgent_response_needed
        : dataset.urgent_response_needed

    this.init_totals('female_HH', this.food_stocks_avaliability, 'female_HH')
    this.init_totals('male_HH', this.food_stocks_avaliability, 'male_HH')
  }
}
