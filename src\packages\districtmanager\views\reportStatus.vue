<template>
  <div class="content">
    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Disaster</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="#">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">REPORT STATUS</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">All</li>
            </ol>
          </nav>
        </div>
        <div class="col-lg-6 col-5 text-right"></div>
      </div>
      <!-- Card stats -->
    </base-header>
    <div class="container-fluid mt--6">
      <div>
        <card
          class="no-border-card"
          body-classes="px-0 pb-1"
          footer-classes="pb-2"
        >
          <template slot="header">
            <h3 class="mb-0">REPORT STATUS</h3>
          </template>
          <div>
            <div
              class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
            >
              <el-select
                class="select-primary pagination-select"
                v-model="pagination.perPage"
                placeholder="Per page"
              >
                <el-option
                  class="select-primary"
                  v-for="item in pagination.perPageOptions"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>

              <div>
                <base-input
                  v-model="searchQuery"
                  prepend-icon="fas fa-search"
                  placeholder="Search..."
                ></base-input>
              </div>
            </div>
            <el-table
              :data="queriedData"
              :filter="searchQuery"
              row-key="id"
              header-row-class-name="thead-light"
              @sort-change="sortChange"
            >
              <el-table-column
                v-for="column in tableColumns"
                :key="column.label"
                v-bind="column"
                id="reportForm"
              ></el-table-column>
              <el-table-column min-width="110px" label="Status">
                <div slot-scope="{ $index, row }">
                    <b-badge pill :variant="row.notStat !== 'Rejected' ? row.variant : 'danger'"
                >{{ row.notStat }} 
              </b-badge>
                  
                </div>
              </el-table-column>
              
              <el-table-column min-width="130px" align="right" label="Actions">
                <div slot-scope="{ $index, row }" class="d-flex">
                  <!--  <base-button
                    @click.native="handleInfographics($index, row)"
                    class="like btn-link"
                    type="info"
                    size="sm"
                    icon
                  >
                    <i class="text-white ni ni-chart-bar-32"></i>
                  </base-button>-->
                  <b-button
                    :style="{
                      'height': '40px',
                      'width': '150px',
                      'font-size': '14px',
                    }"
                    @click="reviewStat($index, row)"
                    data-toggle="tooltip"
                    size="sm"
                    title="Request for Approval"
                    variant="primary"
                    class="py-1 px-1"
                  ><b-icon size="lg" :style="{
                      'height': '20px',
                      'width': '20px',
                    }" icon="view-list"></b-icon>
                    Details</b-button
                  >
                  <!-- <base-button
                    @click.native="review(row)"
                    class="edit pull-right"
                    type="primary"
                    size="sm"
                    icon
                  >
                    CHECK & SIGN
                  </base-button> -->
                  <!-- <base-button
                    @click.native="reviewSummary(row)"
                    class="edit"
                    type="primary"
                    size="sm"
                    icon
                  >
                    <i class="text-white ni ni-badge"></i>Summary
                  </base-button>

                  <base-button
                    @click.native="handleInfographics(row)"
                    class="edit"
                    type="primary"
                    size="sm"
                    icon
                  >
                    <i class="text-white ni ni-chart-bar-32"></i>Infographics
                  </base-button> -->
                </div>
              </el-table-column>
            </el-table>
          </div>
          <div
            slot="footer"
            class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
          >
            <div class>
              <p class="card-category">
                Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
                <span v-if="selectedRows.length"
                  >&nbsp; &nbsp; {{ selectedRows.length }} rows selected</span
                >
              </p>
            </div>
            <base-pagination
              class="pagination-no-border"
              v-model="pagination.currentPage"
              :per-page="pagination.perPage"
              :total="total"
            ></base-pagination>
          </div>
        </card>
      </div>
        <b-modal
        scrollable
        body-class="p-0"
        centered
        hide-backdrop
        size="xl"
        class="modal-lg"
        v-model="modalShow"
        hide-header="true"
        hide-footer="true"
      >
        <b-card
          no-body
          class="p-0 m-0 look"
          header-tag="header"
          footer-tag="footer"
        >
          <template #header>
            <h3 class="mb-0">Report Status</h3>
          </template>
          <b-table
            striped
            hover
            :items="items"
            :fields="fields"
            class="p-0 m-0"
          >
            <template #cell(badge)="data">
              <b-badge pill :variant="data.item.status !== 'Rejected' ? data.item.variant : 'danger'"
                ><b-icon :variant="data.item.status !== 'Rejected' ? data.item.variant : 'danger'" icon="circle-fill"></b-icon
              ></b-badge>
            </template>
            <template #cell(status)="data">
              <b-badge pill :variant="data.item.status !== 'Rejected' ? data.item.variant : 'danger'"
                >{{data.item.status}}
              </b-badge>
            </template>
          </b-table>
        </b-card>
      </b-modal>
    </div>
  </div>
</template>
<script>
import { Table, TableColumn, Select, Option } from "element-ui";
import RouteBreadCrumb from "@/components/Breadcrumb/RouteBreadcrumb";
import { BasePagination } from "@/components";
import { mapGetters, mapActions } from "vuex";
import clientPaginationMixin from "@/components/PaginatedTables/clientPaginationMixin";
import swal from "sweetalert2";
import { MongoReports } from "../api/MongoReports";
var moment = require("moment");

export default {
  mixins: [clientPaginationMixin],
  components: {
    BasePagination,
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn
  },
  data() {
    return {
      propsToSearch: ["name", "email", "age"],
      searchQuery: '',
      tableColumns: [
        {
          prop: "id",
          label: "ID",
          minWidth: 60,
          sortable: true
        },
        {
          prop: "disaster",
          label: "Disaster",
          minWidth: 200,
          sortable: true
        },
        {
          prop: "officer",
          label: "Officer",
          minWidth: 150,
          sortable: true
        },
        {
          prop: "date",
          label: "Date Created",
          minWidth: 150,
          sortable: true
        }
      ],
      tableData: [],
      selectedRows: [],
      modalShow: false,
       items: [
      ],
      fields: [
        { key: 'badge', label: ''},
        { key: 'disaster', label: 'Disaster'},
        { key: 'status', label: 'Status', class: 'text-center' },
        { key: 'dateSub', label: 'Date', class: 'text-center' },
        {
          key: 'location',
          label: 'Report Stage',
        },
      ],
    };
  },
   computed: {
    ...mapGetters({
      getBell: "dims/getNewBell"
    }),
    queriedData() {
      if (this.searchQuery) {
        return this.tableData.filter((item) => {
          return this.searchQuery
            .toLowerCase()
            .split(" ")
            .every((v) => item.disaster.toLowerCase().includes(v) 
            || item.district.toLowerCase().includes(v));
        });
      } else {
        return this.tableData;
      }
    }
  },
  methods: {
    ...mapActions("dims", {
      addBell: "addBell"
    }),
        async reviewStat(index, row) {
      this.items = []

      row.notification.forEach(element => {
        if (element.show == 1) {
          this.items.push(element)
        }
      });
      // if (row.status == "1") {
      //   status = "draft"
      // } else if(row.status == "2") {
      //   status = "On Approval"
      // }

      // if (!row.isApproved && !row.isRejected && row.status == "1") {
      //   location = `${row.account.firstName} ${row.account.lastName}`
      // } else if(!row.isApproved && !row.isRejected && row.status == "2") {
      //   location = `District Manager (${row.account.district.admin2_name_en})`
      // }

      //let stat = { disaster: disaster, status: status, dateSub: dateSub, location: location}
      this.items[0].disaster = row.disaster
      this.modalShow = !this.modalShow;
    },
    review(row) {
      this.$router.push({
        path: "/districtmanager/unapproveddetailsreport/" + row.uuid
      });
    },
    reviewSummary(row) {
      this.$router.push({ path: "/districtmanager/summaryreport/" + row.uuid });
    },
    handleInfographics(row) {
      this.$router.push({ path: "/districtmanager/infographics/" + row.uuid });
    }
  },
  created() {
    MongoReports.getUnapprovedDinrs().then(response => {
      let data = response.data
        .filter(data =>
          data.district.admin2_name_en.includes(
            this.$session.get("user").admin2_name_en
          )
        )

      data.sort(function(a, b) {
        return new Date(b.createdon) - new Date(a.createdon);
      });

      let bell = []

      for (let i = 0; i < data.length; i++) {
        let row = data[i]._id;
        if (data[i].notification !== undefined && data[i].notification.length > 0) {
          
        
         data[i].notification.forEach(el => {
              if (el.show == 1) {
                data[i].notStat = el.status
                data[i].variant = el.variant
                if (el.show == 1 && el.status == "Waiting approval" && el.variant == "info") {
                  bell.push({
                    id: row,
                    disaster: el.disaster
                  })
                 

                }
              }
            });
        this.tableData.push({
          id: i + 1,
          disaster: data[i].disaster,
          district: data[i].district.admin2_name_en,
          date: moment(data[i].createdon).format("MM-DD-YYYY HH:mm:ss"),
          officer: data[i].account.firstName + " " + data[i].account.lastName,
          uuid: data[i]._id,
          variant: data[i].variant,
          notStat: data[i].notStat,
          notification: data[i].notification
        });
        } else {
          if (data[i].isApproved) {
            data[i].notification = []
                    data[i].notification.push({
  dateSub: "",
disaster: data[i].disaster,
location: "Officer",
show: 1,
status: "Sent for approval",
variant: "warning"
            })
            data[i].notification.push({
              dateSub: "",
disaster: data[i].disaster,
location: "District Manager",
show: 1,
status: "Done",
variant: "info"
            })
             data[i].notification.push({
              dateSub: "",
dateSub: "",
disaster: data[i].disaster,
location: "District Commisioner",
show: 1,
status: "Approved",
variant: "success"
            })

            data[i].notification.forEach(el => {
              if (el.show == 1) {
                data[i].notStat = el.status
                data[i].variant = el.variant
               
              }
            });
        this.tableData.push({
          id: i + 1,
          disaster: data[i].disaster,
          district: data[i].district.admin2_name_en,
          date: moment(data[i].createdon).format("MM-DD-YYYY HH:mm:ss"),
          officer: data[i].account.firstName + " " + data[i].account.lastName,
          uuid: data[i]._id,
          variant: data[i].variant,
          notStat: data[i].notStat,
          notification: data[i].notification
        });
          }
        }
         
      }
      this.addBell(bell)
    });
  }
};
</script>
<style>
.no-border-card .card-footer {
  border-top: 0;
}
</style>
