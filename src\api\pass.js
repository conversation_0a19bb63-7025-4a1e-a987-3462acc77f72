
const randomFunc = {
	lower: getRandom<PERSON>ower,
	upper: getRandomUpper,
	number: getRandomNumber,
	symbol: getRandomSymbol
}

const userPass = {
  email: "<EMAIL>",
  password: "admin429"
}

const checkEmails = ["lchipu<PERSON><PERSON>@gmail.com","fmwa<PERSON><EMAIL>","<PERSON><PERSON>zal<PERSON>@gmail.com","<EMAIL>"]
export async function generatePassword() {
    let lower = true
    let upper = true
    let number = true
    let symbol = true
    let length = 20

	let generatedPassword = '';
	const typesCount = lower + upper + number + symbol;
	const typesArr = [{lower}, {upper}, {number}, {symbol}].filter(item => Object.values(item)[0]);

	// Doesn't have a selected type
	if(typesCount === 0) {
		return '';
	}

	// create a loop
	for(let i=0; i<length; i+=typesCount) {
		typesArr.forEach(type => {
			const funcName = Object.keys(type)[0];
			generatedPassword += randomFunc[funcName]();
		});
	}

	const finalPassword = generatedPassword.slice(0, length);
	return finalPassword;
}


function getRandomLower() {
	return String.fromCharCode(Math.floor(Math.random() * 26) + 97);
}

function getRandomUpper() {
	return String.fromCharCode(Math.floor(Math.random() * 26) + 65);
}

function getRandomNumber() {
	return +String.fromCharCode(Math.floor(Math.random() * 10) + 48);
}

function getRandomSymbol() {
	//'!@#$%^&*(){}[]=<>/,.'
	const symbols = '!@#$%&(){}[]/'
	return symbols[Math.floor(Math.random() * symbols.length)];
}

export default { generatePassword, userPass, checkEmails}
