import { Table, TableColumn, Select, Option } from 'element-ui'
import RouteBreadCrumb from '@/components/Breadcrumb/RouteBreadcrumb'
import { BasePagination } from '@/components'
import clientPaginationMixin from '@/components/PaginatedTables/clientPaginationMixin'
import dateformat from 'dateformat'
import { mapGetters, mapActions } from 'vuex'

import moment from 'moment'
import { prelimreports } from '../../../dcpc/api/forms/prelimreports.js'
import swal from 'sweetalert2'
export default {
  mixins: [clientPaginationMixin],
  components: {
    BasePagination,
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn
  },
  data () {
    return {
      report: [],
      tableData: [],

      updatedData: []
    }
  },
  methods: {
    ...mapActions('clusterreports', {
      getclusterreportsAction: 'get'
    }),

    formatedDate (data) {
      let finalDate = dateformat(data, 'dd-mm-yyyy hh:mm:ss')
      return finalDate
    },

    loadData () {
      prelimreports.getReports().then(response => {
        let district = this.$session.get('user').admin2_name_en

        let impacts = []
        const rows = []
        const processedRows = []

        let targetDataset = response.filter(
          item => item._id == this.$route.params.id
        )
        for (const item of targetDataset) {
          //check report status and distict
          if (
            item.status === '2' ||
            (item.status === '3' &&
              item.user.district.admin2_name_en === district)
          ) {
            const row = item.impact.map(impact => {
              return {
                ...impact,
                TA: item.TA,
                disaster: item.disaster,
                id: item._id,
                status: item.status,
                covertext: item.covertext,
                // disater: response[item].disaster,
                district: item.user.district.admin2_name_en,
                dateof: item.disasterdate,
                Date:
                  moment(item.disasterdate).format('DD-MM-YYYY') +
                  ' ' +
                  new Date(item.createdon).toLocaleTimeString('it-IT')
                // status: response[item].status,
              }
            })

            impacts.push(...row)
          }
        }

        impacts = impacts.sort((a, b) => a.disaster.localeCompare(b.disaster))

        for (const impact of impacts) {
          const key = `${impact.TA}${impact.disaster}`
          if (!processedRows.includes(key)) {
            rows.push(impact)
            processedRows.push(key)
          } else {
            Object.assign(impact, {
              TA: '',
              district: '',
              Date: '',
              covertext: ''
            })

            rows.push(impact)
          }
        }

        this.tableData = rows.map((item, i) => {
          return {
            ...item,
            district: item.district,
            ta: item.TA,
            gvh: item.gvh,
            pregnant: item.pregnant,
            date: item.Date,
            dateof: item.dateof,
            status: item.status
          }
        })
      })
    },

    updateData (dataObject, editedData) {
      /* let editedDataObject = Object.assign({}, editedData[0])
        let dateOfupdate = new Date().toLocaleString()
        let editedby = this.$session.get('jwtuser')

        this.updatedData.push({
          editedby: editedby,
          editedon: dateOfupdate,
          updateddata: editedDataObject
        })
 */
      // dataObject.editedhistory = [...this.updatedData]

      let user =
        this.$session.get('userObj').firstName +
        ' ' +
        this.$session.get('userObj').lastName
      let nowTime = new Date()
      let datafinal = { impact: [], edits: {} }

      datafinal.edits.user = user
      datafinal.edits.edittime = nowTime

      for (let i in editedData) {
        datafinal.impact.push({
          id: editedData[i]._id,
          gvh: editedData[i].gvh,
          disabled: editedData[i].disabled,
          fhh: editedData[i].fhh,
          lactactingwomen: editedData[i].lactactingwomen,
          dead: editedData[i].dead,
          injured: editedData[i].injured,
          mhh: editedData[i].mhh,
          missing: editedData[i].missing,
          pregnant: editedData[i].pregnant,
          underfive: editedData[i].underfive
        })
      }

      this.confirmSubmission(dataObject, datafinal)
    },

    confirmSubmission (dataObject, editedData) {
      prelimreports.update(editedData, dataObject).then(
        () => {
          swal({
            title: 'Succesfully updated report details',
            type: 'success',
            confirmButtonClass: 'btn btn-success btn-fill',
            buttonsStyling: false
          })
        },
        reason => {
          swal({
            title: 'Failed to update client',
            text: 'possible error in details (' + reason + ')',
            type: 'error',
            confirmButtonClass: 'btn btn-success btn-fill',
            buttonsStyling: false
          })
        }
      )
    }
  },

  async mounted () {
    this.loadData()
  }
}
