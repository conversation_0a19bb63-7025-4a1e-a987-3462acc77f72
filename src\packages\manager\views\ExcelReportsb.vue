<template>
  <div class="content">
    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Disaster</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">REPORTS</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">ARCHIVES</li>
            </ol>
          </nav>
        </div>

        <div class="col-lg-6 col-5 text-right">
          <base-button size="sm" type="neutral" @click="onBtnExport()">
            <i class="text-black ni ni-cloud-download-95"></i> EXPORT TO CSV
          </base-button>
        </div>
      </div>

      <!-- Card stats -->
    </base-header>
    <div class="container-fluid mt--6">
      <div>
        <card class="no-border-card" body-classes="px-0 pb-1" footer-classes="pb-2">
          <template slot="header">
            <h3 class="mb-0">
              Filter rows by clicking control to the far right of each column of
              heading
            </h3>
          </template>
          <ag-grid-vue
            id="myGrid"
            style="height: 600px;margin:1%"
            class="ag-theme-balham"
            :gridOptions="gridOptions"
            :columnDefs="columns"
            :pagination="true"
            :suppressMenuHide="true"
            :frameworkComponents="frameworkComponents"
            :paginationAutoPageSize="true"
            :rowData="rows"
            @grid-ready="onGridReady"
          ></ag-grid-vue>
        </card>
      </div>
    </div>
  </div>
</template>
<script>
import { Table, TableColumn, Select, Option } from "element-ui";
import RouteBreadCrumb from "@/components/Breadcrumb/RouteBreadcrumb";
import { BasePagination } from "@/components";
import clientPaginationMixin from "@/components/PaginatedTables/clientPaginationMixin";
import swal from "sweetalert2";
import { Reports } from "../api/reports";
import downloadexcel from "vue-json-excel";
import moment from "moment";
import { AgGridVue } from "ag-grid-vue";
import "ag-grid-community/dist/styles/ag-grid.css";
import "ag-grid-community/dist/styles/ag-theme-balham.css";
export default {
  mixins: [clientPaginationMixin],
  components: {
    AgGridVue,
    BasePagination,
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    [Table.name]: Table,
    downloadexcel,
    [TableColumn.name]: TableColumn
  },
  data() {
    return {
      gridOptions: {},
      rows: [],
      frameworkComponents: null,
      columns: []
    };
  },
  methods: {
    onGridReady(params) {},
    onBtnExport() {
      var params = {
        skipHeader: false,
        skipFooters: true,
        skipGroups: true,
        fileName: "DMIS.csv"
      };
      this.gridOptions.api.exportDataAsCsv(params);
    },
    getColumns() {
      const doacpc = {
        headerName: "Date_of_assessment_ACPC",
        field: "Date_of_assessment_ACPC",
        filter: "agDateColumnFilter",
        filterParams: {
          // provide comparator function
          comparator: function(filterLocalDateAtMidnight, cellValue) {
            var dateAsString = cellValue;
            if (dateAsString == null) return 0;

            // In the example application, dates are stored as dd/mm/yyyy
            // We create a Date object for comparison against the filter date
            var dateParts = dateAsString.split("/");
            var day = Number(dateParts[2]);
            var month = Number(dateParts[1]) - 1;
            var year = Number(dateParts[0]);
            var cellDate = new Date(day, month, year);

            // Now that both parameters are Date objects, we can compare
            if (cellDate < filterLocalDateAtMidnight) {
              return -1;
            } else if (cellDate > filterLocalDateAtMidnight) {
              return 1;
            } else {
              return 0;
            }
          }
        }
      };
      const doadcpc = {
        headerName: "Date_of_assessment_DCPC",
        field: "Date_of_assessment_DCPC",
        filter: "agDateColumnFilter",
        filterParams: {
          // provide comparator function
          comparator: function(filterLocalDateAtMidnight, cellValue) {
            var dateAsString = cellValue;
            if (dateAsString == null) return 0;

            // In the example application, dates are stored as dd/mm/yyyy
            // We create a Date object for comparison against the filter date
            var dateParts = dateAsString.split("/");
            var day = Number(dateParts[2]);
            var month = Number(dateParts[1]) - 1;
            var year = Number(dateParts[0]);
            var cellDate = new Date(day, month, year);

            // Now that both parameters are Date objects, we can compare
            if (cellDate < filterLocalDateAtMidnight) {
              return -1;
            } else if (cellDate > filterLocalDateAtMidnight) {
              return 1;
            } else {
              return 0;
            }
          }
        }
      };

      const dodf = {
        headerName: "Date_of_Disaster_from",
        field: "Date_of_Disaster_from",
        filter: "agDateColumnFilter",
        filterParams: {
          // provide comparator function
          comparator: function(filterLocalDateAtMidnight, cellValue) {
            var dateAsString = cellValue;
            if (dateAsString == null) return 0;

            // In the example application, dates are stored as dd/mm/yyyy
            // We create a Date object for comparison against the filter date
            var dateParts = dateAsString.split("/");
            var day = Number(dateParts[2]);
            var month = Number(dateParts[1]) - 1;
            var year = Number(dateParts[0]);
            var cellDate = new Date(day, month, year);

            // Now that both parameters are Date objects, we can compare
            if (cellDate < filterLocalDateAtMidnight) {
              return -1;
            } else if (cellDate > filterLocalDateAtMidnight) {
              return 1;
            } else {
              return 0;
            }
          }
        }
      };

      const dodt = {
        headerName: "Date_of_Disaster_to",
        field: "Date_of_Disaster_to",
        filter: "agDateColumnFilter",
        filterParams: {
          // provide comparator function
          comparator: function(filterLocalDateAtMidnight, cellValue) {
            var dateAsString = cellValue;
            if (dateAsString == null) return 0;

            // In the example application, dates are stored as dd/mm/yyyy
            // We create a Date object for comparison against the filter date
            var dateParts = dateAsString.split("/");
            var day = Number(dateParts[2]);
            var month = Number(dateParts[1]) - 1;
            var year = Number(dateParts[0]);
            var cellDate = new Date(day, month, year);

            // Now that both parameters are Date objects, we can compare
            if (cellDate < filterLocalDateAtMidnight) {
              return -1;
            } else if (cellDate > filterLocalDateAtMidnight) {
              return 1;
            } else {
              return 0;
            }
          }
        }
      };
      const columns = [
        {
          headerName: "Disaster ID",
          field: "dinr",
          filter: "agTextColumnFilter"
        },
        { headerName: "DRA ID", field: "oid", filter: "agTextColumnFilter" },

        {
          headerName: "Disaster",
          field: "Disaster",
          filter: "agTextColumnFilter"
        },
        { headerName: "Country", field: "Country" },
        {
          headerName: "Country_PCODE",
          field: "Country_PCODE"
        },
        { headerName: "Region", field: "Region", filter: "agTextColumnFilter" },
        doacpc,
        doadcpc,
        dodf,
        dodt,
        {
          headerName: "food_items_damaged_KGs_agriculture",
          field: "food_items_damaged_KGs_agriculture",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_of_crop_hectares_submerged_agriculture",
          field: "number_of_crop_hectares_submerged_agriculture",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_crop_hectares_washed_off_agriculture",
          field: "number_crop_hectares_washed_off_agriculture",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_crop_hectares_washed_off_agriculture",
          field: "number_crop_hectares_washed_off_agriculture",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_crop_hectares_washed_off_agriculture",
          field: "number_crop_hectares_washed_off_agriculture",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_crop_hectares_washed_off_agriculture",
          field: "number_crop_hectares_washed_off_agriculture",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_crop_hectares_washed_off_agriculture",
          field: "number_crop_hectares_washed_off_agriculture",
          filter: "agNumberColumnFilter"
        },

        {
          headerName:
            "number_of_households_whose_crops_are_impacted_agriculture",
          field: "number_of_households_whose_crops_are_impacted_agriculture",
          filter: "agNumberColumnFilter"
        },
        {
          headerName:
            "of_crop_hectares_damaged_in_affected_households_agriculture",
          field: "of_crop_hectares_damaged_in_affected_households_agriculture",
          filter: "agNumberColumnFilter"
        },

        {
          headerName: "hh_affected_per_impacted_livestock_agriculture",
          field: "hh_affected_per_impacted_livestock_agriculture",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_of_impacted_livestock_agriculture",
          field: "number_of_impacted_livestock_agriculture",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_male_HH_accomadeted_displaced",
          field: "number_male_HH_accomadeted_displaced",
          filter: "agNumberColumnFilter"
        },

        {
          headerName: "number_male_HH_accomadeted_displaced",
          field: "number_male_HH_accomadeted_displaced",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_female_HH_accomadeted_displaced",
          field: "number_female_HH_accomadeted_displaced",
          filter: "agNumberColumnFilter"
        },

        {
          headerName: "number_of_males_disaggregated_displaced",
          field: "number_of_males_disaggregated_displaced",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_of_females_disaggregated_displaced",
          field: "number_of_females_disaggregated_displaced",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_of_males_accomodated_displaced",
          field: "number_of_males_accomodated_displaced",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_of_females_accomodated_displaced",
          field: "number_of_females_accomodated_displaced",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_of_school_buildings_functioning_education",
          field: "number_of_school_buildings_functioning_education",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_of_school_buildings_underwater_education",
          field: "number_of_school_buildings_underwater_education",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_of_school_buildings_completely_damaged_education",
          field: "number_of_school_buildings_completely_damaged_education",
          filter: "agNumberColumnFilter"
        },
        {
          headerName:
            "number_of_school_buildings_partially_functioning_education",
          field: "number_of_school_buildings_partially_functioning_education",
          filter: "agNumberColumnFilter"
        },

        {
          headerName: "number_of_school_buildings_closed_education",
          field: "number_of_school_buildings_closed_education",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "males_of_out_school_education",
          field: "males_of_out_school_education",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "females_of_out_school_education",
          field: "females_of_out_school_education",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "is_food_available_food",
          field: "is_food_available_food",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_of_facilities_partially_functioning_health",
          field: "number_of_facilities_partially_functioning_health",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_of_facilities_on_verge_of_closing_health",
          field: "number_of_facilities_on_verge_of_closing_health",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_facilities_closed_health",
          field: "number_facilities_closed_health",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "state_of_medical_supply_availability_health",
          field: "state_of_medical_supply_availability_health",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "state_of_health_personnel_availability_health",
          field: "state_of_health_personnel_availability_health",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_severely_affected_livelihoods",
          field: "number_severely_affected_livelihoods",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_slightly_affected_livelihoods",
          field: "number_slightly_affected_livelihoods",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "impacted_females_protection",
          field: "impacted_females_protection",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "impacted_males_protection",
          field: "impacted_males_protection",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "state_of_access_to_main_roads_logistics",
          field: "state_of_access_to_main_roads_logistics",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_of_affected_males_nutrition",
          field: "number_of_affected_males_nutrition",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_of_affected_females_nutrition",
          field: "number_of_affected_females_nutrition",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "FHH_with_safe_water_wash",
          field: "FHH_with_safe_water_wash",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "MHH_with_safe_water_wash",
          field: "MHH_with_safe_water_wash",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "FHH_with_toilet_access_wash",
          field: "FHH_with_toilet_access_wash",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "MHH_with_toilet_access_wash",
          field: "MHH_with_toilet_access_wash",
          filter: "agNumberColumnFilter"
        },

        {
          headerName: "FHH_risking_contamination_wash",
          field: "FHH_risking_contamination_wash",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "MHH_risking_contamination_wash",
          field: "MHH_risking_contamination_wash",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "Houses_partly_damaged_shelter",
          field: "Houses_partly_damaged_shelter",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "Houses_underwater_shelter",
          field: "Houses_underwater_shelter",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "Houses_completely_shelter",
          field: "Houses_completely_shelter",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_of_males_without_shelter_shelter",
          field: "number_of_males_without_shelter_shelter",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_of_females_without_shelter_shelter",
          field: "number_of_females_without_shelter_shelter",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_of_female_HH_with_houses_damaged_shelter",
          field: "number_of_female_HH_with_houses_damaged_shelter",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_of_male_HH_with_houses_damaged_shelter",
          field: "number_of_male_HH_with_houses_damaged_shelter",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_of_injured_females_shelter",
          field: "number_of_injured_females_shelter",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_of_injured_males_in_category_shelter",
          field: "number_of_injured_males_in_category_shelter",
          filter: "agNumberColumnFilter"
        },

        {
          headerName: "number_of_dead_females_shelter",
          field: "number_of_dead_females_shelter",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_of_dead_males_shelter",
          field: "number_of_dead_males_shelter",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_of_displaced_male_displaced",
          field: "number_of_displaced_male_displaced",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "number_of_displaced_females_displaced",
          field: "number_of_displaced_females_displaced",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "No_of_FHH_with_1_2_month_Food_Availability_food",
          field: "No_of_FHH_with_1_2_month_Food_Availability_food",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "No_of_FHH_with_less_than_1_month_Food_Availability_food",
          field: "No_of_FHH_with_less_than_1_month_Food_Availability_food",
          filter: "agNumberColumnFilter"
        },
        {
          headerName:
            "No_of_FHH_with_more_than_2_months_Food_Availability_food",
          field: "No_of_FHH_with_more_than_2_months_Food_Availability_food",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "No_of_FHH_who_lost_Food_Stock_food",
          field: "No_of_FHH_who_lost_Food_Stock_food",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "No_of_MHH_with_1_2_month_Food_Availability_food",
          field: "No_of_MHH_with_1_2_month_Food_Availability_food",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "No_of_MHH_with_less_than_1_month_Food_Availability_food",
          field: "No_of_MHH_with_less_than_1_month_Food_Availability_food",
          filter: "agNumberColumnFilter"
        },
        {
          headerName:
            "No_of_MHH_with_more_than_2_months_Food_Availability_food",
          field: "No_of_MHH_with_more_than_2_months_Food_Availability_food",
          filter: "agNumberColumnFilter"
        },
        {
          headerName: "No_of_MHH_who_lost_Food_Stock_food",
          field: "No_of_MHH_who_lost_Food_Stock_food",
          filter: "agNumberColumnFilter"
        }
      ];

      return columns;
    }
  },
  beforeMount() {
    this.gridOptions = {};
    Reports.getReports().then(item => {
      item.data.forEach(dra => {
        this.rows.push(dra);
      });

      this.columns = this.getColumns();
    });
  },
  mounted() {
    this.gridApi = this.gridOptions.api;
    this.gridColumnApi = this.gridOptions.columnApi;
  }
};
</script>
<style>
.no-border-card .card-footer {
  border-top: 0;
}
</style>
