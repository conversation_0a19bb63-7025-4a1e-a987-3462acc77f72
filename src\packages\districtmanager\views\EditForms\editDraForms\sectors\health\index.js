
export default {
  $_veeValidate: {
    validator: 'new'
  },
  components: {},
  props: ['health', 'max'],
  data () {
    return {
      defaultName: null,
      maxValue: this.max,
      response_needed: '',
      dataset: {},
      partially_functioning: 0,
      verge_of_closing: 0,
      closed: 0,
      health_medical: [{ name: 'Medical supply' }, { name: 'Health personel' }],
      other_partially_functioning: 0,
      other_verge_of_closing: 0,
      other_closed: 0,
      other_risks_of_disease_outbreak: [{}],
      other_health_facilities: [],
      available_health_facilities: [],
      available_health_medical: [],
      risk_out_disease_outbreak: [],
      health_facilities: [
        { name: 'Village Clinic' },
        { name: 'Health Post' },
        { name: 'Dispensary' },
        { name: 'Health Center' },
        { name: 'Hospital' }
      ],
      diseases: [{ name: 'Malaria' }, { name: 'Cholera' }]
    }
  },
  computed: {
    tt_partially_functional () {
      return (
        parseInt(
          this.partially_functioning === undefined ||
            this.partially_functioning.length === 0
            ? 0
            : this.partially_functioning
        ) +
        parseInt(
          this.other_partially_functioning === undefined ||
            this.other_partially_functioning.length === 0
            ? 0
            : this.other_partially_functioning
        )
      )
    },
    tt_verge_closing () {
      return (
        parseInt(
          this.other_verge_of_closing === undefined ||
            this.other_verge_of_closing.length === 0
            ? 0
            : this.other_verge_of_closing
        ) +
        parseInt(
          this.verge_of_closing === undefined ||
            this.verge_of_closing.length === 0
            ? 0
            : this.verge_of_closing
        )
      )
    },
    tt_closed () {
      return (
        parseInt(
          this.closed === undefined || this.closed.length === 0
            ? 0
            : this.closed
        ) +
        parseInt(
          this.other_closed === undefined || this.other_closed.length === 0
            ? 0
            : this.other_closed
        )
      )
    }
  },

  methods: {
    addItemRow (member, array_name, static_data = [], key_value) {
      this.$emit(
        'addItemRow',
        'health',
        member,
        array_name,
        static_data,
        key_value
      )
    },
    removeItemRow (
      member,
      array_name,
      static_data = [],
      index,
      key_value,
      arrayTotals
    ) {
      this.$emit(
        'removeItemRow',
        'health',
        member,
        array_name,
        static_data,
        index,
        key_value
      )
      let arrayCopy = []
      for (let i = 0; i < arrayTotals.length; i++) {
        if (array_name == '') {
          let dynamic_element = arrayTotals[i]
          arrayCopy.push({ dynamic_element: 0 })
          this.init_totals(arrayTotals[i], arrayCopy, arrayTotals[i])
        } else {
          this.init_totals(arrayTotals[i], array_name, arrayTotals[i])
        }
      }
    },
    allowOnlyText () {
      $('#inputTextBox').keypress(function (event) {
        

        var inputValue = event.charCode

        if (
          !(inputValue >= 65 && inputValue <= 120) &&
          inputValue != 32 && inputValue != 0
        ) {
          event.preventDefault()
        }
      })
    },
    init_totals (key, array_name, member) {
      this[member] = array_name
        .map(function (item) {
          return parseInt(item[key]) ? parseInt(item[key]) : 0
        })
        .reduce((sum, value) => sum + value, 0)
    },
    save () {
      this.health.response_needed = this.response_needed
      this.health.urgent_response_needed = this.urgent_response_needed
      this.health.available_health_facilities = this.available_health_facilities.filter(
        value => Object.keys(value).length !== 0
      )
      this.health.risk_out_disease_outbreak = this.risk_out_disease_outbreak.filter(
        value => Object.keys(value).length !== 0
      )
      this.health.other_risks_of_disease_outbreak = this.other_risks_of_disease_outbreak.filter(
        value => Object.keys(value).length !== 0
      )
      this.health.other_health_facilities = this.other_health_facilities.filter(
        value => Object.keys(value).length !== 0
      )

      this.health.available_health_medical = this.available_health_medical.filter(
        value => Object.keys(value).length !== 0
      )

      this.$emit('save', this.health, 'health')
    },
    autosave () {
      this.health.response_needed = this.response_needed

      this.health.urgent_response_needed = this.urgent_response_needed
      this.health.available_health_facilities = this.available_health_facilities.filter(
        value => Object.keys(value).length !== 0
      )
      this.health.risk_out_disease_outbreak = this.risk_out_disease_outbreak.filter(
        value => Object.keys(value).length !== 0
      )
      this.health.other_risks_of_disease_outbreak = this.other_risks_of_disease_outbreak.filter(
        value => Object.keys(value).length !== 0
      )
      this.health.other_health_facilities = this.other_health_facilities.filter(
        value => Object.keys(value).length !== 0
      )

      this.health.available_health_medical = this.available_health_medical.filter(
        value => Object.keys(value).length !== 0
      )

      this.$emit('autosave', this.health, 'health')
    }
  },

  beforeMount () {
    this.health = typeof this.health !== 'undefined' ? this.health : {}
    setInterval(this.autosave, 1200)

    let dataset = JSON.parse(localStorage.getItem('health').data)

    this.available_health_facilities =
      typeof dataset.available_health_facilities === 'undefined'
        ? this.available_health_facilities
        : dataset.available_health_facilities
    this.risk_out_disease_outbreak =
      typeof dataset.risk_out_disease_outbreak === 'undefined'
        ? this.risk_out_disease_outbreak
        : dataset.risk_out_disease_outbreak

    this.other_risks_of_disease_outbreak =
      typeof dataset.other_risks_of_disease_outbreak === 'undefined'
        ? this.other_risks_of_disease_outbreak
        : dataset.other_risks_of_disease_outbreak

    this.available_health_medical =
      typeof dataset.available_health_medical === 'undefined'
        ? this.available_health_medical
        : dataset.available_health_medical

    this.other_health_facilities =
      typeof dataset.other_health_facilities === 'undefined'
        ? this.other_health_facilities
        : dataset.other_health_facilities

    this.response_needed =
      typeof dataset.response_needed === 'undefined'
        ? this.response_needed
        : dataset.response_needed

    this.urgent_response_needed =
      typeof dataset.urgent_response_needed === 'undefined'
        ? this.urgent_response_needed
        : dataset.urgent_response_needed

    this.available_health_facilities.length > 0
      ? this.init_totals(
          'partially_functioning',
          this.available_health_facilities,
          'partially_functioning'
        )
      : ''

    this.available_health_facilities.length > 0
      ? this.init_totals(
          'verge_of_closing',
          this.available_health_facilities,
          'verge_of_closing'
        )
      : ''

    this.available_health_facilities.length > 0
      ? this.init_totals('closed', this.available_health_facilities, 'closed')
      : ''

    this.other_health_facilities.length > 0
      ? this.init_totals(
          'partialy_functioning',
          this.other_health_facilities,
          'partialy_functioning'
        )
      : ''

    this.other_health_facilities.length > 0
      ? this.init_totals(
          'verge_of_closing',
          this.other_health_facilities,
          'verge_of_closing'
        )
      : ''

    this.other_health_facilities.length > 0
      ? this.init_totals('closed', this.other_health_facilities, 'closed')
      : ''
  }
}
