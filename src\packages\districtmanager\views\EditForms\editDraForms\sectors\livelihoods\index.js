
export default {
  $_veeValidate: {
    validator: 'new'
  },
  components: {},
  props: ['livelihoods', 'max'],
  data () {
    return {
      maxValue: this.max,
      livelihoods_affected: [],
      dataset: {},
      response_needed: '',
      slightly_affected: 0,
      severely_affected: 0,
      valid: false,
      alt_tt: 0,
      livelihood_types: [
        { name: 'Crop Production' },
        { name: 'Landowner (Rentals)' },
        { name: 'Livestock & Poultry' },
        { name: 'Fishing' },
        { name: 'Agriculture commodities trade' },
        { name: 'Employment(Gvt/Pvt) ' },
        { name: 'Shopkeeper/Trader' },
        { name: 'Skilled Wage Labour' },
        { name: 'Unskilled Wage Labour' },
        { name: 'Tourism/Hotel' },
        { name: 'Remittances' }
      ]
    }
  },
  computed: {
    tt_affected () {
      return (
        parseInt(
          this.severely_affected === undefined ||
            this.severely_affected.length === 0
            ? 0
            : this.severely_affected
        ) +
        parseInt(
          this.slightly_affected === undefined ||
            this.slightly_affected.length === 0
            ? 0
            : this.slightly_affected
        )
      )
    }
  },

  methods: {
    init_totals (key, array_name, member) {
      this[member] = array_name
        .map(function (item) {
          return parseInt(item[key]) ? parseInt(item[key]) : 0
        })
        .reduce((sum, value) => sum + value, 0)
    },
    addItemRow (member, array_name, static_data = [], key_value) {
      this.$emit(
        'addItemRow',
        'livelihoods',
        member,
        array_name,
        static_data,
        key_value
      )
    },
    removeItemRow (
      member,
      array_name,
      static_data = [],
      index,
      key_value,
      arrayTotals
    ) {
      this.$emit(
        'removeItemRow',
        'livelihoods',
        member,
        array_name,
        static_data,
        index,
        key_value
      )

      let arrayCopy = []
      for (let i = 0; i < arrayTotals.length; i++) {
        if (array_name == '') {
          let dynamic_element = arrayTotals[i]
          //alert(dynamic_element+"mia")
          arrayCopy.push({ dynamic_element: 0 })
          this.init_totals(arrayTotals[i], arrayCopy, arrayTotals[i])
        } else {
          this.init_totals(arrayTotals[i], array_name, arrayTotals[i])
        }
      }
    },

    save () {
      this.livelihoods.response_needed = this.response_needed
      this.livelihoods.urgent_response_needed = this.urgent_response_needed
      this.livelihoods.livelihoods_affected = this.livelihoods_affected.filter(
        value => Object.keys(value).length !== 0
      )
      this.$emit('save', this.livelihoods, 'livelihoods')
    },
    autosave () {
      this.livelihoods.response_needed = this.response_needed

      this.livelihoods.urgent_response_needed = this.urgent_response_needed
      this.livelihoods.livelihoods_affected = this.livelihoods_affected.filter(
        value => Object.keys(value).length !== 0
      )
      this.$emit('autosave', this.livelihoods, 'livelihoods')
    }
  },

  beforeMount () {
    this.livelihoods =
      typeof this.livelihoods !== 'undefined' ? this.livelihoods : {}

    setInterval(this.autosave, 1200)

    let dataset = JSON.parse(localStorage.getItem('livelihoods').data)

    this.livelihoods_affected =
      typeof dataset.livelihoods_affected === 'undefined'
        ? this.livelihoods_affected
        : dataset.livelihoods_affected

    this.response_needed =
      typeof dataset.response_needed === 'undefined'
        ? this.response_needed
        : dataset.response_needed
    this.urgent_response_needed =
      typeof dataset.urgent_response_needed === 'undefined'
        ? this.urgent_response_needed
        : dataset.urgent_response_needed

    this.init_totals(
      'severely_affected',
      this.livelihoods_affected,
      'severely_affected'
    )
    this.init_totals(
      'slightly_affected',
      this.livelihoods_affected,
      'slightly_affected'
    )
  }
}
