import axios from 'axios';


//const resource = 'http://localhost:3204/forms/dinr';

const resource = process.env.VUE_APP_ENGINE_URL + '/signatures'


export class signatures
{
  static get() {
    return axios.get(resource + "/").then(response => { return response });
}


static getOne (id) {
  return axios.get(resource + '/' + id ).then(response => { return response.data });
}
  static async create (data) {
    let response = await axios.post(resource, data).then(response => { return response });
    return response;
  }
  static async remove (_id) {
    let response = await axios.delete(resource + '/' + _id).then(response => { return response });
    return response;
  }
  static update (data) {
    return axios.patch(resource + '/' + data._id, data).then(response => { return response });
  }
  static async upload(data) {
      alert('uploadig')
    let response = await axios.post(resource + '/upload/', data, { headers: { 'Content-Type': 'multipart/form-data' } }).then(response => { return response });
    return response;
  }

}
