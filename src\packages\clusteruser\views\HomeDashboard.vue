<template>
  <div>
    <base-header class="pb-6" type>
      <div class="row align-items-center  pb-4 ">
        <div class="col-lg-12 ">
          <tabs
            v-if="isLoaded"
            fill
            tabNavClasses="nav-fill flex-column flex-sm-row nav-wrapper"
            tabContentClasses="card shadow p-0 m-0"
            class="p-0 m-0"
          >
            <div shadow class="card p-1 m-0 pt-2">
              <div class="p-0 m-0 card-body">
                 <tabPane
                id="1"
                active="true"
                icon="ni ni-cloud-upload-96"
                class="active"
                title="Overview"
              >
                <span slot="title">
                  <center style>
                    <span
                    id="2"
                      style="font-size:150%;"
                      data-toggle="tooltip"
                      data-placement="top"
                      title="Overview"
                      class="humanitarianicons-House"
                    ></span>
                    <p style="margin-bottom:0px;font-weight:bold;">Overview</p>
                  </center>
                </span>
                <p class="description">
                  <keep-alive>
                  <overview-dashboard
                    :dinrs="dinrsData"
                    :dras="dras"
                    :flatData="flatData"
                  ></overview-dashboard></keep-alive>
                </p>
              </tabPane>
              <tabPane id="2" icon="ni ni-bell-55" title="Map">
                <span slot="title">
                  <center>
                    <span
                    id="3"
                      style="font-size:150%;"
                      data-toggle="tooltip"
                      data-placement="top"
                      title="Map"
                      class="humanitarianicons-Location"
                    ></span>
                    <p style="margin-bottom:0px;font-weight:bold;">Map</p>
                  </center>
                </span>
                <p class="description">
                 <keep-alive>
                  <map-dashboard
                    :flatData="flatData"
                  ></map-dashboard></keep-alive>
                </p>
              </tabPane>

              <!-- <tabPane id="3" icon="ni ni-calendar-grid-58" title="Charts">
                <span slot="title">
                  <center>
                    <span
                      style="font-size:150%;"
                      data-toggle="tooltip"
                      data-placement="top"
                      title="Charts"
                      class="humanitarianicons-Chart"
                    ></span>
                    <p style="margin-bottom:0px;font-weight:bold;">Charts</p>
                  </center>
                </span>
                <p class="description">
                  <keep-alive>
                  <chart-dashboard
                    :flatData="flatData"
                  ></chart-dashboard></keep-alive>
                </p>
              </tabPane> -->
              <tabPane id="4" icon="ni ni-calendar-grid-58" title="Reports">
                <span slot="title">
                  <center>
                    <span
                    id="4"
                      style="font-size:150%;"
                      data-toggle="tooltip"
                      data-placement="top"
                      title="Reports"
                      class="humanitarianicons-Report"
                    ></span>
                    <p style="margin-bottom:0px;font-weight:bold;">Reports</p>
                  </center>
                </span>
                <p class="description">
                  <keep-alive>
                  <report-dashboard
                    :dinrs="dinrsData"
                    :dras="dras"
                    :flatData="flatData"
                  ></report-dashboard></keep-alive>
                </p>
              </tabPane>
              <tabPane id="5" icon="ni ni-calendar-grid-58" title="Dataset">
                <span slot="title">
                  <center>
                    <span
                    id="5"
                      style="font-size:150%;"
                      data-toggle="tooltip"
                      data-placement="top"
                      title="Dataset"
                      class="humanitarianicons-Table"
                    ></span>
                    <p style="margin-bottom:0px;font-weight:bold;">Dataset</p>
                  </center>
                </span>
                <p class="description">
                  <keep-alive>
                  <dataset-dashboard></dataset-dashboard></keep-alive>
                </p>
              </tabPane>
              <!-- <tabPane id="6" icon="ni ni-calendar-grid-58" title="Downloads">
                <span slot="title">
                  <center>
                    <span
                    id="6"
                      style="font-size:150%;"
                      data-toggle="tooltip"
                      data-placement="top"
                      title="Downloads"
                      class="humanitarianicons-Download"
                    ></span>
                    <p style="margin-bottom:0px;font-weight:bold;">Downloads</p>
                  </center>
                </span>
                <p class="description">
                  <keep-alive>
                  <download-dashboard
                  ></download-dashboard></keep-alive>
                </p>
              </tabPane> -->
              </div>

            </div>
          </tabs>
          <p class="description col-12">
            <center>
              <grid-loader
                :loading="loading"
                v-if="!isLoaded"
                :color="'#bf6c00'"
                :width="'100%'"
                size="40px"
                style="margin-top:20%"
              ></grid-loader>
            </center>
          </p>
        </div>
      </div>
    </base-header>
  </div>
</template>

<script>
// Charts

import tabPane from "../../../components/Tabs/Tab";
import tabs from "../../../components/Tabs/Tabs";
const ChartDashboard=() => import("../../dashboards/charts/index_manager");
const OverviewDashboard =() => import("../../dashboards/overview/index_manager");
const ReportDashboard =() => import("../../dashboards/reports/index");
const MapDashboard =() => import("../../dashboards/maps/index_manager");
const DatasetDashboard =() => import("../../dashboards/dataset/index_manager");
const DownloadDashboard =() => import("../../dashboards/downloads/index_manager");
import gridLoader from "vue-spinner/src/GridLoader";
import { mapActions } from "vuex";
var _ = require("lodash");
export default {
  name: "ManagerDashboard",
  components: {
    gridLoader,
    tabs,
    tabPane,
    ChartDashboard,
    OverviewDashboard,
    ReportDashboard,
    DatasetDashboard,
    DownloadDashboard,
    MapDashboard
  },
  data() {
    return { isLoaded: false, loading: true, model: "1" };
  },
  computed: {
    dinrs: function() {
      return this.dinrsData;
    },
    dras: function() {
      return this.drasData;
    },
    flatData: function() {
      return this.flatRecords;
    },
    aggregatedData: function() {
      return this.aggregatedRecords;
    }
  },

  methods: {
    ...mapActions("dinrs", {
      loadDinrs: "get"
    }),
    ...mapActions("dras", {
      loadDras: "get"
    }),

    ...mapActions("flatdinrs", {
      loadFlatdinrs: "loadFlatdinrs"
    }),

    getPath(object) {
      let self = this;
      return Object.entries(object).reduce((r, [k, v], i) => {
        if (v && typeof v === "object") {
          r.push(
            ...self
              .getPath(v)
              .map(([left, right]) => [
                (Array.isArray(object)
                  ? "F" + (i + 1).toString().padStart(2, 0)
                  : k) +
                  "_" +
                  left,
                right
              ])
          );
        } else {
          r.push([k, v]);
        }
        return r;
      }, []);
    },
    compressData(data) {
      let self = this;
      return data.map(o =>
        Object.fromEntries(
          Object.entries(o).reduce((r, [k, v]) => {
            if (k === "station") {
              r.push([k, v.name]);
            } else if (v && typeof v === "object") {
              if (k.endsWith("Values")) k = k.slice(0, -6);
              r.push(
                ...self
                  .getPath(v)
                  .map(([left, right]) => [k + "_" + left, right])
              );
            } else {
              r.push([k, v]);
            }
            return r;
          }, [])
        )
      );
    },
    normilizeObjectProperties(newdata, properties) {
      let data = [...newdata];
      for (var i in data) {
        for (var j in properties) {
          data[i][properties[j]] = data[i][properties[j]] || "";
        }
      }
      return data;
    }
  },
  async mounted() {
    this.dinrsData = await this.loadDinrs();
    // console.log([...this.dinrsData], "mika");
    this.drasData = await this.loadDras();
    this.flatRecords = await this.loadFlatdinrs();
    //console.log(this.flatRecords,"count")
    /*   this.drasData.forEach((dra, index) => {
      //console.log(index);
      this.drasData[index].dinr = this.dinrsData.find(
        (dinr) => dinr._id == dra.dinrFormId
      );
    }); */
    /* let dra = this.compressData([...this.drasData]);

    let arrayofKeys = [];
    let newShapeArray = [];
    //cleaning the output
    dra.forEach(function (obj, index) {
      let newObject = {};
      Object.keys(obj).forEach(function (key) {
        //console.log(obj[key]);
        if (
          key.indexOf("code") != -1 ||
          key.indexOf("id") != -1 ||
          key.indexOf("dinr_approvalMetadata") != -1
        ) {
        } else {
          if (typeof key == "string") {
            arrayofKeys.push(key);
            newObject[key] = obj[key];
          }
        }
      });

      newShapeArray.push(newObject);
    });

    this.detailedFlatData = newShapeArray;

    this.columnsData = [...new Set(arrayofKeys)];

    //normalize
    this.detailedFlatData = await this.normilizeObjectProperties(
      this.detailedFlatData,
      this.columnsData
    );

    let columnsBuffer = [];
    columnsBuffer[0] = {
      type: "selection",
    };

    this.columnsData.forEach((colName, index) => {
      columnsBuffer.push({
        prop: colName,
        label: colName.replace(/_/g, " "),
        minWidth: 200,
        sortable: true,
      });
    });

    this.columnsData = columnsBuffer; */

    this.isLoaded = true;
    this.loading = false;
  }
};
</script>
