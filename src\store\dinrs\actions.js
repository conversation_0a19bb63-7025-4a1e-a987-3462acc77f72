import Dinrs from "../../api/dinrs";
export const get = async(context, payload) => {
    if (context.state.dinrs.length == 0) {
        let response = await Dinrs.get().then(response => response);
        context.commit("set", response.data);
        // console.log(response.data);
        return response.data;
    }

    return context.state.dinrs;
};

export const updateDinr = async ({ commit }, { id, model }) => {
  const response = await axios.patch(id, model);
  commit('updateDinr', response.data);
};
