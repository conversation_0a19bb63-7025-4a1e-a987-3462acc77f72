var amqp = require("amqplib/callback_api");
import { auth } from "@/api/auth";
import swal from "sweetalert2";
import Swal from "sweetalert2";
import { dinrforms } from "../../api/forms/dinrforms";
import { draforms } from "../../api/forms/draforms";
import { MongoReports } from "../../api/MongoReports";

import { UnapprovedForms } from "../../api/UnapprovedForms";

export default {
  methods: {
    async handleSubmit(row) {
      let self = this;
      var { value: password } = await swal({
        title: "Enter your password",
        text: `This is to endorse that you ${
          this.$session.get("user").firstName
        } ${this.$session.get("user").lastName} (${
          this.$session.get("user").email
        }) have completed this form to the best of your ability and submit it as a final report .`,
        input: "password",
        type: "warning",
        buttonsStyling: false,
        confirmButtonClass: "btn btn-success btn-fill",
        cancelButtonClass: "btn btn-danger btn-fill",
        confirmButtonText: "Yes, submit it!",
        showCancelButton: true,
        inputPlaceholder: "password",
        inputAttributes: {
          autocapitalize: "off",
          autocorrect: "off",
          required: true
        }
      }).then(result => {
        if (result.value != null) {
          auth
            .login({
              email: this.$session.get("user").email,
              password: result.value
            })
            .then(
              response => {
                this.handleRequest(row);
              },
              reason => {
                swal({
                  text:
                    "Failed submit for possible invalid password (  password : " +
                    reason +
                    " )",
                  type: "error",
                  toast: true,
                  position: "top-end",
                  confirmButtonClass: "btn btn-success btn-fill",
                  buttonsStyling: false
                });
              }
            );
        }
      });
    },

    processRequest(id) {
      UnapprovedForms.update({ status: "2", _id: id }).then(
        response => {
          let signature = response.data ? response.data.approvalMetadata : {}
          this.submitFormToQueue(id, signature);
        },
        reason => {
          Swal.fire({
            title: "Failed to submit form",
            text: "possible invalid data (" + reason + ")",
            type: "error",
            animation: false
          });
        }
      );
    },

    submitFormToQueue(id, signature = {}) {

      let self = this;
      let RBMQ_URL = process.env.VUE_APP_RBMQ_URL;
      dinrforms.get(id).then(response => {
        amqp.connect(RBMQ_URL, function(error0, connection) {
          if (error0 === null) {
            connection.createChannel(function(error1, channel) {
              var queue = "dinr_forms";

              channel.assertQueue(queue, {
                durable: true
              });

              var dinr = response.data;

              dinr.approvalMetadata = signature

              dinr.isRejected = false;

              dinr.isApproved = true;

              dinr.status = "2";


           

              channel.sendToQueue(queue, Buffer.from(JSON.stringify(dinr)));
              console.log(" [x] Sent");

              self.refresh();
            });
          } else {
            Swal.fire({
              title: "Failed to submit form",
              text: "Possible network fail (Could not connect to queue server)",
              type: "error",
              animation: false
            });
          }
        });
      });
    },

    async handleRequest(row) {
      var { data } = await draforms.query({ dinrFormId: row._id });
      this.draFormsData = data;
      this.processRequest(row._id);
    },

    refresh() {
      UnapprovedForms.getDinrs().then(response => {
        this.tableData = response.data.filter(
          data =>
            data.isApproved === true &&
            data.account.email.includes(this.$session.get("user").email)
        );
      });
    }
  }
};
