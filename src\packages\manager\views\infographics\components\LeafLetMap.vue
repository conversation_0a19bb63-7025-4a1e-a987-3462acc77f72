<template>
  <div id="leafletmap">
    <l-map :center="center" :zoom="zoom">
      <l-tile-layer :url="url" :attribution="attribution"></l-tile-layer>
      <l-marker :lat-lng="center"></l-marker>
    </l-map>
  </div>
</template>
<script>
import * as Vue2Leaflet from "vue2-leaflet";
var { LMap, LTileLayer, LMarker } = Vue2Leaflet;
export default {
  name: "vue-leaflet-map",
  components: { LMap, LTileLayer, LMarker },
  data() {
    return {
      zoom: 5,
      center: L.latLng(-13.2512161, 34.3015278),
      url: "https://tiles.stadiamaps.com/tiles/alidade_smooth/{z}/{x}/{y}{r}.png",
      attribution:
        '&copy; <a href="http://osm.org/copyright">OpenStreetMap</a> contributors',
      marker: <PERSON><PERSON>latLng(-13.2512161, 34.3015278)
    };
  },
  mounted() {
    setTimeout(function() {
      window.dispatchEvent(new Event("resize"));
    }, 250);
  }
};
</script>
<style scoped>
#leafletmap {
  width: 100%;
  height: 300px;
}
</style>
