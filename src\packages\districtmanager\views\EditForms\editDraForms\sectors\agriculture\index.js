//import { draforms } from "../../../../../api/forms/draforms";

export default {
  $_veeValidate: {
    validator: 'new'
  },
  components: {},
  props: ['agriculture', 'max'],
  data () {
    return {
      /*model declarations */
      rows: [],
      maxValue: this.max,
      response_needed: '',
      dataset: {},
      hectares_damaged: 0,
      livestock_affected: 0,
      valid: false,
      total_hh_affected_crops_livestock: 0,
      hh_affected: 0,
      hh_livestock: 0,
      hectares_submerged: 0,
      hectares_washed_away: 0,
      number_of_kilos: 0,
      impact_on_livestock: [],
      hh_affected_l: 0,
      food_item_damage: [],
      impact_on_crops: [],
      crops_damaged: [],
      impact_on_livestock: [],

      /*Static datasets for form elements*/
      crop_types: [
        { name: 'Staple Crop Field' },
        { name: 'Cash Crops' },
        { name: 'Fruits' }
      ],
      dummy_list: [{ name: 'dummy value' }],
      crops: [
        { name: '<PERSON><PERSON><PERSON>' },

        { name: 'Cabbage' },
        { name: 'Coffee' },
        { name: 'Cow<PERSON><PERSON>' },
        { name: '<PERSON>' },

        { name: 'Groundnuts' },
        { name: '<PERSON><PERSON>' },
        { name: '<PERSON><PERSON>' },

        { name: 'Onions' },
        { name: 'Oranges' },

        { name: 'Pegeon peas' },

        { name: 'Rice' },
        { name: 'Sugarcane' },
        { name: 'soya beans' },
        { name: 'Sorghum' },
        { name: 'Sweetpotato' },
        { name: 'Tea' },
        { name: 'Tangerines' },
        { name: 'Tomatoes' }
      ],
      livestock_list: [
        { name: 'Cattle' },
        { name: 'Goats' },
        { name: 'Sheep' },
        { name: 'Pigs' },
        { name: 'Poultry' },
        { name: 'Rabbits' }
      ],
      fruits_list: [
        { name: 'Tangerines' },
        { name: 'Oranges' },
        { name: 'Banana' },
        { name: 'Tomatoes' },
        { name: 'Cabbage' },
        { name: 'Onions' }
      ]
    }
  },
  computed: {
    hh_affected_crops: {
      //computation method
      get () {
        return this.hh_affected
      },
      set (new_total) {
        this.hh_affected = new_total
      }
    },
    hh_affected_livestock: {
      //computation method
      get () {
        return this.hh_affected_l
      },
      set (new_total) {
        this.hh_affected_l = new_total
      }
    },
    hectares_damaged_total: {
      //computation method
      get () {
        return this.hectares_damaged
      },
      set (new_total) {
        this.hectares_damaged = new_total
      }
    },
    tt_livestock_affected: {
      //computation method
      get () {
        return this.livestock_affected
      },
      set (new_total) {
        this.livestock_affected = new_total
      }
    },
    tt_hectares_submerged: {
      //computation method
      get () {
        return this.hectares_submerged
      },
      set (new_total) {
        this.hectares_submerged = new_total
      }
    },
    tt_hectares_washed_away: {
      //computation method
      get () {
        return this.hectares_washed_away
      },
      set (new_total) {
        this.hectares_washed_away = new_total
      }
    },
    tt_kgs_damaged: {
      //computation method
      get () {
        return this.number_of_kilos
      },
      set (new_total) {
        this.number_of_kilos = new_total
      }
    }
  },

  methods: {
    addItemRow (member, array_name, static_data = [], key_value) {
      //add html row
      this.$emit(
        'addItemRow',
        'agriculture',
        member,
        array_name,
        static_data,
        key_value
      )
    },
    removeItemRow ( //delete html row
      member,
      array_name,
      static_data = [],
      index,
      key_value,
      arrayTotals
    ) {
      this.$emit(
        'removeItemRow',
        'agriculture',
        member,
        array_name,
        static_data,
        index,
        key_value
      )

      let arrayCopy = []
      for (let i = 0; i < arrayTotals.length; i++) {
        if (array_name == '') {
          let dynamic_element = arrayTotals[i]
          //alert(dynamic_element+"mia")
          arrayCopy.push({ dynamic_element: 0 })
          this.init_totals(arrayTotals[i], arrayCopy, arrayTotals[i])
        } else {
          this.init_totals(arrayTotals[i], array_name, arrayTotals[i])
        }
      }
    },
    init_totals (key, array_name, member) {
      //initialize total computation onload
      this[member] = array_name
        .map(function (item) {
          return parseInt(item[key]) ? parseInt(item[key]) : 0
        })
        .reduce((sum, value) => sum + value, 0)
    },
    save () {
      //commit data object to DB
      this.agriculture.response_needed = this.response_needed
      this.agriculture.urgent_response_needed = this.urgent_response_needed
      this.agriculture.food_item_damage = this.food_item_damage.filter(
        value => Object.keys(value).length !== 0
      )
      this.agriculture.impact_on_crops = this.impact_on_crops.filter(
        value => Object.keys(value).length !== 0
      )
      this.agriculture.crops_damaged = this.crops_damaged.filter(
        value => Object.keys(value).length !== 0
      )
      this.$emit('save', this.agriculture, 'agriculture')
    },

    autosave () {
      //commit data object to DB
      this.agriculture.response_needed = this.response_needed
      this.agriculture.urgent_response_needed = this.urgent_response_needed
      this.agriculture.food_item_damage = this.food_item_damage.filter(
        value => Object.keys(value).length !== 0
      )
      this.agriculture.impact_on_crops = this.impact_on_crops.filter(
        value => Object.keys(value).length !== 0
      )
      this.agriculture.crops_damaged = this.crops_damaged.filter(
        value => Object.keys(value).length !== 0
      )
      this.$emit('autosave', this.agriculture, 'agriculture')
    }
  },

  beforeMount () {
    this.agriculture =
      typeof this.agriculture !== 'undefined' ? this.agriculture : {}

    setInterval(this.autosave, 1200)
    let dataset = JSON.parse(localStorage.getItem('agriculture').data)

    this.impact_on_livestock =
      typeof dataset.impact_on_livestock === 'undefined'
        ? this.impact_on_livestock
        : dataset.impact_on_livestock
    this.food_item_damage =
      typeof dataset.food_item_damage === 'undefined'
        ? this.food_item_damage
        : dataset.food_item_damage
    this.impact_on_crops =
      typeof dataset.impact_on_crops === 'undefined'
        ? this.impact_on_crops
        : dataset.impact_on_crops
    this.crops_damaged =
      typeof dataset.crops_damaged === 'undefined'
        ? this.crops_damaged
        : dataset.crops_damaged

    this.response_needed =
      typeof dataset.response_needed === 'undefined'
        ? this.response_needed
        : dataset.response_needed

    this.urgent_response_needed =
      typeof dataset.urgent_response_needed === 'undefined'
        ? this.urgent_response_needed
        : dataset.urgent_response_needed

    this.crops_damaged.length > 0
      ? this.init_totals(
          'hectares_submerged',
          this.crops_damaged,
          'hectares_submerged'
        )
      : ''

    this.crops_damaged.length > 0
      ? this.init_totals(
          'hectares_washed_away',
          this.crops_damaged,
          'hectares_washed_away'
        )
      : ''

    this.impact_on_crops.length > 0
      ? this.init_totals('hh_affected', this.impact_on_crops, 'hh_affected')
      : ''

    this.impact_on_crops.length > 0
      ? this.init_totals(
          'hectares_damaged',
          this.impact_on_crops,
          'hectares_damaged'
        )
      : ''

    this.impact_on_livestock.length > 0
      ? this.init_totals(
          'hh_affected_l',
          this.impact_on_livestock,
          'hh_affected_l'
        )
      : ''

    this.impact_on_livestock.length > 0
      ? this.init_totals(
          'livestock_affected',
          this.impact_on_livestock,
          'livestock_affected'
        )
      : ''

    this.food_item_damage.length > 0
      ? this.init_totals(
          'number_of_kilos',
          this.food_item_damage,
          'number_of_kilos'
        )
      : ''
  }
}
