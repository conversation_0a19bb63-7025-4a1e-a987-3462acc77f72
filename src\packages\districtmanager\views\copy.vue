
/* eslint-disable */
<template>
  <div class="col-md-11" style="margin:0 auto">
    <base-header class="pb-1 col-12" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-6">
          <h6 class="h2 d-inline-block mb-0 ">Disaster</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/districtcommissioner/dashboard">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">Report</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">
                Detailed
              </li>
            </ol>
          </nav>
        </div>

        <div class="col-lg-6 col-6 text-right pr-5">
          <!-- <base-button size="sm" type="neutral" @click="handleInfographics()">
            Infographics
          </base-button> -->
          <base-button
            size="sm"
            type="neutral"
            @click="printdiv('section-to-print')"
          >
            <span class="btn-inner--icon">
              <!--  <i class="ni ni-fat-add"></i> -->
            </span>
            <span class="btn-inner--text">Print</span>
          </base-button>
          
          <base-button size="sm" type="neutral" @click="downloadPDF()">
            <span class="btn-inner--icon">
              <!--  <i class="ni ni-fat-add"></i> -->
            </span>Download PDF
            <span class="btn-inner--text"></span>
          </base-button>
        </div>
      </div>
    </base-header>
    <div id="section-to-print" ref="content">
      <card
        class="no-border-card"
        body-classes="px-0 pb-1"
        footer-classes="pb-2"
      >
        <div>
          <div class="component-example__container">
            <dl class="headings">
              <dd class="text-center" style="font-size:22px; color: #32325d">
                <p>
                  <span
                    class="badge badge-md  badge-floating badge-danger border-white"
                    ><b>UNAPPROVED REPORT</b></span
                  >
                </p>
                <p style="font-size:25px;color:green">
                  Please check this report and thereafter approve using your
                  (D.C.) credentials below it
                </p>
                <p>
                  <img src="../../../../static/logo.png" height="70" />
                </p>
                <p>
                  <b>
                    <b>
                      <strong>Government of Malawi</strong>
                    </b>
                  </b>
                  <br />
                  <span>
                    <b>
                      <b>Department of Disaster Management Affairs (DoDMA)</b>
                    </b>
                  </span>
                  <br />
                  <span
                    style="background:orange;width:100vh;padding-left:6px;padding-right:6px"
                  >
                    <b>
                      <b>Disaster Impact and Needs Reporting Form</b>
                    </b>
                  </span>
                </p>
              </dd>
            </dl>
            <div>
              <br />
              <p style="text-align:center;font-size:120%">
                <b>ACRONYMS</b>
              </p>
              <table
                style="margin:0 auto;padding:0px !important; border:none !important;width:92%;"
              >
                <tr
                  style="margin:0px !important;padding:0px !important; border:none;"
                >
                  <td
                    style="margin:0px !important;padding:0px !important; border:none;"
                  >
                    <table style="margin:0 auto;border:1px solid;padding:0px">
                      <tr>
                        <td style="width:20%">
                          <b>ADRMC </b>
                        </td>
                        <td>Area Disaster Risk Management Committee</td>
                      </tr>
                      <tr>
                        <td>
                          <b>DDRMC</b>
                        </td>
                        <td>District Disaster Risk Management Committee</td>
                      </tr>
                      <tr>
                        <td>
                          <b>GVH</b>
                        </td>
                        <td>Group Village Headman</td>
                      </tr>
                      <tr>
                        <td>
                          <b>HH</b>
                        </td>
                        <td>Household</td>
                      </tr>
                      <tr>
                        <td>
                          <b>NR</b>
                        </td>
                        <td>Not reported</td>
                      </tr>
                    </table>
                  </td>
                  <td
                    style="margin:4px !important;padding:4px !important; border:none;"
                  ></td>
                  <td
                    style="margin:0px !important;padding:0px !important; border:none;"
                  >
                    <table style="margin:0 auto;border:1px solid;padding:0px">
                      <tr>
                        <td>
                          <b>N/A</b>
                        </td>
                        <td>Not Applicable</td>
                      </tr>

                      <tr>
                        <td>
                          <b>TA</b>
                        </td>
                        <td>Tradition Authority</td>
                      </tr>
                      <tr>
                        <td>
                          <b>T</b>
                        </td>
                        <td>Total</td>
                      </tr>
                      <tr>
                        <td>
                          <b>VDRMC</b>
                        </td>
                        <td>Village Disaster  Risk Management Committee</td>
                      </tr>
                      <tr>
                        <td>
                          <b>VH</b>
                        </td>
                        <td>Village Headman</td>
                      </tr>
                    </table>
                  </td>
                </tr>
              </table>
              <br />
            </div>
            <br />
            <table class="col-md-11">
              <tr>
                <td colspan="8">
                  <center>
                    <h2>
                      <b style="text-transform:uppercase"
                        >DETAILED REPORT FOR
                        <span id="district">{{ dinrFormsData.district.admin2_name_en }}</span>
                      </b>
                    </h2>
                  </center>
                </td>
              </tr>
              <tr>
                <td colspan="6" width="50%">
                  <b>Affected TAs</b>
                  :
                  <span>
                    {{
                    TAarray.join() == "" ? "Not reported" : TAarray.join(", ")
                    }}
                  </span>
                </td>

                <td colspan="6" width="50%">
                  <b>Affected Villages</b>
                  :
                  <span>
                    {{
                    villagesArray.filter(Boolean).sort((a, b) => a.localeCompare(b)).join(", ")
                    || "Not reported"
                    }}
                  </span>
                </td>
              </tr>
              <tr>
                <td colspan="6" width="50%" style="text-transform:capitalize">
                  <b>Type of disasters</b>
                  : <span id="disastertype"> {{ dinrFormsData.disaster }} </span>
                </td>
                <td colspan="6" width="50%">
                  <b>Report submitted at</b>
                  :
                  {{
                  dateFormate(
                  new Date(dinrFormsData.createdon).toLocaleDateString(
                  "en-US"
                  )
                  )
                  }}
                </td>
              </tr>
              <tr>
                <td colspan="6" width="50%">
                  <b>Date Disaster started</b>
                  :
                 <span id="disasterstart"> {{
                  dateFormate(
                  new Date(dinrFormsData.dodFrom).toLocaleDateString(
                  "en-US"
                  )
                  )
                  }} </span>
                </td>
                <td colspan="6" width="50%">
                  <b>Date Disaster ended</b>
                  :
                  {{
                  dateFormate(
                  new Date(dinrFormsData.dodTo).toLocaleDateString("en-US")
                  )
                  }}
                </td>
              </tr>
             
            <tr>
              <td colspan="4">
                <b>Date of assessment by ACPC/VCPC</b>
                :
                {{
                  dateFormate(
                    new Date(dinrFormsData.doaAcpc).toLocaleDateString("en-US")
                  )
                }}
              </td>
              <td colspan="4">
                <b>Date of assessment/verification by DCPC</b>
                :
                {{
                  dateFormate(
                    new Date(dinrFormsData.doaDcpc).toLocaleDateString("en-US")
                  )
                }}
              </td>
                <td colspan="4">
                <b>Date reported to the DEC</b>
                :
                {{
                  dinrFormsData.dateReported?
                  dateFormate(
                    new Date(dinrFormsData.dateReported).toLocaleDateString("en-US")
                  )
                  :""
                }}
              </td>

            </tr>
        </table>

            <table
              v-for="(item, index) in draFormsData"
              v-bind:key="index"
              class="col-md-11"
            >
              <!-- display ta name -->
              <tr>
                <td colspan="15">
                  <h3 class="text-center" style="text-transform:uppercase">
                    {{ item.admin3.admin3Name_en }}
                  </h3>
                </td>
              </tr>
              <!-- end display ta name -->

              <tr>
                <td colspan="6" style="width:30%">
                  <b>
                    <b>GVH</b>
                  </b>
                  :
                  <span v-for="(gvh, i) in sortArrayByKey(item.gvhs)" :key="i">
                    <span>{{ i > 0 ? ", " : "" }}</span>
                    <span class="text-nowrap">{{
                      gvh.name || "Not reported"
                    }}</span>
                  </span>
                </td>
                <td colspan="5" style="width:35%">
                  <b>Camps</b>
                  :
                  <span>{{
                    item.camps
                      .map(i => i.name)
                      .sort((a, b) => a.localeCompare(b))
                      .filter(Boolean)
                      .join(", ") || "Not reported"
                  }}</span>
                </td>

                <td colspan="4" style="width:35%">
                  <b>Affected Villages</b>
                  :
                  <span>{{
                    item.villages
                      .map(i => i.name)
                      .sort((a, b) => a.localeCompare(b))
                      .filter(Boolean)
                      .join(", ") || "Not reported"
                  }}</span>
                </td>
              </tr>
              <!-- general -->
              <tr
                v-if="item.sectors.shelter"
                style="margin-botton:9padding:0 !important;border:0px !important;"
              >
                <td
                  colspan="15"
                  style="margin:0 !important;padding:0 !important;border:0px"
                >
                  <table
                    width="100%"
                    style="margin:0;padding:0 !important;border:0px !important;"
                  >
                    <tr
                      v-if="
                        (item.sectors.shelter.PeopleInjuredrows &&
                          item.sectors.shelter.PeopleInjuredrows.length > 0) ||
                          (item.sectors.shelter.PeopleDeadrows &&
                            item.sectors.shelter.PeopleDeadrows.length > 0)
                      "
                    >
                      <td colspan="13" class="text-center">
                        <span><b>GENERAL INFORMATION </b></span>
                      </td>
                    </tr>

                    <tr
                      v-if="
                        item.sectors.shelter.PeopleInjuredrows &&
                          item.sectors.shelter.PeopleInjuredrows.length > 0
                      "
                    >
                      <td
                        width="20%"
                        :rowspan="
                          item.sectors.shelter.PeopleInjuredrows.length > 0
                            ? item.sectors.shelter.PeopleInjuredrows.length + 2
                            : item.sectors.shelter.PeopleInjuredrows.length + 1
                        "
                      >
                        <strong>People Injured</strong>
                      </td>
                      <td colspan="6" width="40%">
                        <b>GVH</b>
                      </td>
                      <td colspan="2" width="10%">
                        <b>
                          <center>Male</center>
                        </b>
                      </td>
                      <td colspan="2" width="10%">
                        <b>
                          <center>Female</center>
                        </b>
                      </td>
                      <td colspan="2" width="10%" style="font-weight:bold">
                        <b>
                          <center>Total</center>
                        </b>
                      </td>
                    </tr>
                    <tr
                      v-if="
                        item.sectors.shelter.PeopleInjuredrows &&
                          item.sectors.shelter.PeopleInjuredrows.length > 0
                      "
                      v-for="row in item.sectors.shelter.PeopleInjuredrows"
                    >
                      <td width="40%" colspan="6">
                        {{ row.name || "Not reported" }}
                      </td>
                      <td width="10%" colspan="2" class="right-align">
                        {{ numberWithCommas(row.people_injured_males || 0) }}
                      </td>
                      <td width="10%" colspan="2" class="right-align">
                        {{ numberWithCommas(row.people_injured_females || 0) }}
                      </td>
                      <td
                        style="font-weight:bold"
                        width="10%"
                        colspan="2"
                        class="right-align"
                      >
                        {{
                          numberWithCommas(
                            (row.people_injured_females
                              ? parseInt(row.people_injured_females)
                              : 0) +
                              (row.people_injured_males
                                ? parseInt(row.people_injured_males)
                                : 0)
                          )
                        }}
                      </td>
                    </tr>
                    <tr
                      v-if="
                        item.sectors.shelter.PeopleInjuredrows &&
                          item.sectors.shelter.PeopleInjuredrows.length > 0
                      "
                    >
                      <td width="40%" style="font-weight:bold" colspan="6">
                        Total
                      </td>
                      <td
                        width="10%"
                        colspan="2"
                        style="font-weight:bold;"
                        class="right-align"
                      >
                        {{
                          numberWithCommas(
                            item.sectors.shelter.PeopleInjuredrows
                              ? item.sectors.shelter.PeopleInjuredrows.map(
                                  function(item) {
                                    return item.people_injured_males
                                      ? parseInt(item.people_injured_males)
                                      : 0;
                                  }
                                ).reduce((sum, value) => sum + value)
                              : 0
                          )
                        }}
                      </td>
                      <td
                        width="10%"
                        colspan="2"
                        style="font-weight:bold"
                        class="right-align"
                      >
                        {{
                          numberWithCommas(
                            item.sectors.shelter.PeopleInjuredrows
                              ? item.sectors.shelter.PeopleInjuredrows.map(
                                  function(item) {
                                    return item.people_injured_females
                                      ? parseInt(item.people_injured_females)
                                      : 0;
                                  }
                                ).reduce((sum, value) => sum + value)
                              : 0
                          )
                        }}
                      </td>
                      <td
                        width="10%"
                        colspan="2"
                        style="font-weight:bold"
                        class="right-align"
                      >
                        {{
                          numberWithCommas(
                            (item.sectors.shelter.PeopleInjuredrows
                              ? item.sectors.shelter.PeopleInjuredrows.map(
                                  function(item) {
                                    return item.people_injured_females
                                      ? parseInt(item.people_injured_females)
                                      : 0;
                                  }
                                ).reduce((sum, value) => sum + value)
                              : 0) +
                              (item.sectors.shelter.PeopleInjuredrows
                                ? item.sectors.shelter.PeopleInjuredrows.map(
                                    function(item) {
                                      return item.people_injured_males
                                        ? parseInt(item.people_injured_males)
                                        : 0;
                                    }
                                  ).reduce((sum, value) => sum + value)
                                : 0)
                          )
                        }}
                      </td>
                    </tr>

                    <tr
                      v-if="
                        item.sectors.shelter.PeopleDeadrows &&
                          item.sectors.shelter.PeopleDeadrows.length > 0
                      "
                    >
                      <td
                        width="20%"
                        :rowspan="
                          item.sectors.shelter.PeopleDeadrows.length > 0
                            ? item.sectors.shelter.PeopleDeadrows.length + 2
                            : item.sectors.shelter.PeopleDeadrows + 1
                        "
                      >
                        <b>People Dead</b>
                      </td>
                      <td width="20%" colspan="3">
                        <b>GVH</b>
                      </td>
                      <td width="20%" colspan="3">
                        <b>Cause</b>
                      </td>
                      <td colspan="2" width="10%">
                        <b>
                          <center>Male</center>
                        </b>
                      </td>
                      <td colspan="2" width="10%">
                        <b>
                          <center>Female</center>
                        </b>
                      </td>
                      <td colspan="2" width="10%">
                        <b>
                          <center>Total</center>
                        </b>
                      </td>
                    </tr>
                    <tr
                      v-if="
                        item.sectors.shelter.PeopleDeadrows &&
                          item.sectors.shelter.PeopleDeadrows.length > 0
                      "
                      v-for="row in item.sectors.shelter.PeopleDeadrows"
                    >
                      <td width="20%" colspan="3">
                        {{ row.name || "Not reported" }}
                      </td>
                      <td width="20%" colspan="3">
                        {{ row.cause_of_death || 0 }}
                      </td>
                      <td width="10%" colspan="2" class="right-align">
                        {{ numberWithCommas(row.males_dead || 0) }}
                      </td>
                      <td width="10%" colspan="2" class="right-align">
                        {{ numberWithCommas(row.females_dead || 0) }}
                      </td>
                      <td
                        width="10%"
                        colspan="2"
                        style="font-weight:bold"
                        class="right-align"
                      >
                        {{
                          numberWithCommas(
                            (row.males_dead ? parseInt(row.males_dead) : 0) +
                              (row.females_dead
                                ? parseInt(row.females_dead)
                                : 0)
                          )
                        }}
                      </td>
                    </tr>
                    <tr
                      v-if="
                        item.sectors.shelter.PeopleDeadrows &&
                          item.sectors.shelter.PeopleDeadrows.length > 0
                      "
                    >
                      <td width="40%" colspan="6" style="font-weight:bold">
                        Total
                      </td>
                      <td
                        width="10%"
                        colspan="2"
                        style="font-weight:bold"
                        class="right-align"
                      >
                        {{
                          numberWithCommas(
                            item.sectors.shelter.PeopleDeadrows
                              ? item.sectors.shelter.PeopleDeadrows.map(
                                  function(item) {
                                    return item.males_dead
                                      ? parseInt(item.males_dead)
                                      : 0;
                                  }
                                ).reduce((sum, value) => sum + value)
                              : 0
                          )
                        }}
                      </td>
                      <td
                        style="font-weight:bold"
                        width="10%"
                        colspan="2"
                        class="right-align"
                      >
                        {{
                          numberWithCommas(
                            item.sectors.shelter.PeopleDeadrows
                              ? item.sectors.shelter.PeopleDeadrows.map(
                                  function(item) {
                                    return item.females_dead
                                      ? parseInt(item.females_dead)
                                      : 0;
                                  }
                                ).reduce((sum, value) => sum + value)
                              : 0
                          )
                        }}
                      </td>
                      <td
                        width="10%"
                        colspan="2"
                        style="font-weight:bold"
                        class="right-align"
                      >
                        {{
                          numberWithCommas(
                            (item.sectors.shelter.PeopleDeadrows
                              ? item.sectors.shelter.PeopleDeadrows.map(
                                  function(item) {
                                    return item.females_dead
                                      ? parseInt(item.females_dead)
                                      : 0;
                                  }
                                ).reduce((sum, value) => sum + value)
                              : 0) +
                              (item.sectors.shelter.PeopleDeadrows
                                ? item.sectors.shelter.PeopleDeadrows.map(
                                    function(item) {
                                      return item.males_dead
                                        ? parseInt(item.males_dead)
                                        : 0;
                                    }
                                  ).reduce((sum, value) => sum + value)
                                : 0)
                          )
                        }}
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
              <tr
                style="border:none"
                v-if="
                  (item.sectors.shelter.PeopleInjuredrows &&
                    item.sectors.shelter.PeopleInjuredrows.length > 0) ||
                    (item.sectors.shelter.PeopleDeadrows &&
                      item.sectors.shelter.PeopleDeadrows.length > 0)
                "
              >
                <td class="col-md-12" colspan="16"></td>
              </tr>
              <!-- end general -->
              <!-- shelter -->
              <tr
                v-if="item.sectors.shelter"
                style="margin:0 !important;padding:0 !important;border:0px !important;"
              >
                <td
                  colspan="15"
                  style="margin:0 !important;padding:0 !important;border:0px"
                >
                  <table
                    width="100%"
                    style="margin:0;padding:0 !important;border:0px !important;"
                  >
                    <tr>
                      <td style="text-align:left;">
                        <b>CLUSTER</b>
                      </td>
                      <td colspan="13" class="text-center">
                        <b>IMPACT ASSESSMENT</b>
                      </td>
                    </tr>
                    <tr>
                      <td
                        width="10%"
                        class="clearfix"
                        :rowspan="
                          (item.sectors.shelter.people_without_shelter
                            ? item.sectors.shelter.people_without_shelter
                                .length > 0
                              ? item.sectors.shelter.people_without_shelter
                                  .length + 2
                              : 1
                            : 1) +
                            (item.sectors.shelter.PeopleAffectedrows
                              ? item.sectors.shelter.PeopleAffectedrows.length >
                                0
                                ? item.sectors.shelter.PeopleAffectedrows
                                    .length + 2
                                : 1
                              : 1) +
                            4
                        "
                      >
                        <span class="rotated" style="font-weight: bold;"
                          ></span
                        >
                        <img
                          src="../../../../static/cluster_shelter_100px_bluebox.png"
                          height="150"
                        />
                      </td>
                    </tr>
                    <tr v-if="item.sectors.shelter.people_without_shelter">
                      <td width="20%">
                        <b>Indicators</b>
                      </td>
                      <td colspan="6" width="40%">
                        <b>Category</b>
                      </td>
                      <td width="10%" colspan="2">
                        <b>
                          <center>Male</center>
                        </b>
                      </td>
                      <td width="10%" colspan="2">
                        <b>
                          <center>Female</center>
                        </b>
                      </td>
                      <td width="10%" colspan="2">
                        <b>
                          <center>Total</center>
                        </b>
                      </td>
                    </tr>
                    <tr v-if="item.sectors.shelter.people_without_shelter">
                      <td
                        width="20%"
                        :rowspan="
                          item.sectors.shelter.people_without_shelter.length > 0
                            ? item.sectors.shelter.people_without_shelter
                                .length + 2
                            : item.sectors.shelter.people_without_shelter
                                .length + 1
                        "
                      >
                        <strong>People without shelter</strong>
                      </td>
                    </tr>
                    <tr
                      v-for="row in item.sectors.shelter.people_without_shelter"
                      v-if="item.sectors.shelter.people_without_shelter"
                      style="border:1px"
                    >
                      <td width="40%" colspan="6">
                        {{ row.name || "Not reported" }}
                      </td>
                      <td width="10%" colspan="2" class="right-align">
                        {{ numberWithCommas(row.males || 0) }}
                      </td>
                      <td width="10%" colspan="2" class="right-align">
                        {{ numberWithCommas(row.females || 0) }}
                      </td>
                      <td
                        width="10%"
                        colspan="2"
                        style="font-weight:bold"
                        class="right-align"
                      >
                        {{
                          numberWithCommas(
                            (row.females ? parseInt(row.females) : 0) +
                              (row.males ? parseInt(row.males) : 0) || 0
                          )
                        }}
                      </td>
                    </tr>

                    <tr
                      v-if="
                        item.sectors.shelter.people_without_shelter &&
                          item.sectors.shelter.people_without_shelter.length > 0
                      "
                    >
                      <td width="40%" style="font-weight:bold" colspan="6">
                        Total
                      </td>
                      <td width="10%" colspan="2" class="right-align">
                        {{
                          numberWithCommas(
                            item.sectors.shelter.people_without_shelter
                              ? item.sectors.shelter.people_without_shelter
                                  .map(function(item) {
                                    return item.males
                                      ? parseInt(item.males)
                                      : 0;
                                  })
                                  .reduce((sum, value) => sum + value)
                              : 0
                          )
                        }}
                      </td>
                      <td
                        width="10%"
                        colspan="2"
                        style="font-weight:bold"
                        class="right-align"
                      >
                        {{
                          numberWithCommas(
                            item.sectors.shelter.people_without_shelter
                              ? item.sectors.shelter.people_without_shelter
                                  .map(function(item) {
                                    return item.females
                                      ? parseInt(item.females)
                                      : 0;
                                  })
                                  .reduce((sum, value) => sum + value)
                              : 0
                          )
                        }}
                      </td>
                      <td
                        width="10%"
                        colspan="2"
                        style="font-weight:bold"
                        class="right-align"
                      >
                        {{
                          numberWithCommas(
                            (item.sectors.shelter.people_without_shelter
                              ? item.sectors.shelter.people_without_shelter
                                  .map(function(item) {
                                    return item.males
                                      ? parseInt(item.males)
                                      : 0;
                                  })
                                  .reduce((sum, value) => sum + value)
                              : 0) +
                              (item.sectors.shelter.people_without_shelter
                                ? item.sectors.shelter.people_without_shelter
                                    .map(function(item) {
                                      return item.females
                                        ? parseInt(item.females)
                                        : 0;
                                    })
                                    .reduce((sum, value) => sum + value)
                                : 0)
                          )
                        }}
                      </td>
                    </tr>

                    <tr v-if="item.sectors.shelter.PeopleAffectedrows">
                      <td
                        width="20%"
                        :rowspan="
                          item.sectors.shelter.PeopleAffectedrows.length > 0
                            ? item.sectors.shelter.PeopleAffectedrows.length + 2
                            : item.sectors.shelter.PeopleAffectedrows.length + 1
                        "
                      >
                        <strong>Households Affected</strong>
                      </td>
                      <td width="40%" colspan="6">
                        <b>GVH</b>
                      </td>
                      <td width="10%" colspan="2" style>
                        <b>
                          <center>Male</center>
                        </b>
                      </td>
                      <td width="10%" colspan="2">
                        <b>
                          <center>Female</center>
                        </b>
                      </td>
                      <td width="10%" style="font-weight:bold" colspan="2">
                        <b>
                          <center>Total</center>
                        </b>
                      </td>
                    </tr>
                    <tr
                      v-if="item.sectors.shelter.PeopleAffectedrows"
                      v-for="row in item.sectors.shelter.PeopleAffectedrows"
                    >
                      <td width="40%" colspan="6">
                        {{ row.name || "Not reported" }}
                      </td>
                      <td width="10%" colspan="2" class="right-align">
                        {{ numberWithCommas(row.damaged_mhh || 0) }}
                      </td>
                      <td width="10%" colspan="2" class="right-align">
                        {{ numberWithCommas(row.damaged_fhh || 0) }}
                      </td>
                      <td
                        style="font-weight:bold"
                        width="10%"
                        colspan="2"
                        class="right-align"
                      >
                        {{
                          numberWithCommas(
                            (row.damaged_fhh ? parseInt(row.damaged_fhh) : 0) +
                              (row.damaged_mhh
                                ? parseInt(row.damaged_mhh)
                                : 0) || 0
                          )
                        }}
                      </td>
                    </tr>
                    <tr
                      v-if="
                        item.sectors.shelter.PeopleAffectedrows &&
                          item.sectors.shelter.PeopleAffectedrows.length > 0
                      "
                    >
                      <td style="font-weight:bold" width="40%" colspan="6">
                        Total
                      </td>
                      <td
                        style="font-weight:bold;"
                        colspan="2"
                        width="10%"
                        class="right-align"
                      >
                        {{
                          numberWithCommas(
                            item.sectors.shelter.PeopleAffectedrows
                              ? item.sectors.shelter.PeopleAffectedrows.map(
                                  function(item) {
                                    return item.damaged_mhh
                                      ? parseInt(item.damaged_mhh)
                                      : 0;
                                  }
                                ).reduce((sum, value) => sum + value)
                              : 0
                          )
                        }}
                      </td>
                      <td
                        style="font-weight:bold"
                        colspan="2"
                        width="10%"
                        class="right-align"
                      >
                        {{
                          numberWithCommas(
                            item.sectors.shelter.PeopleAffectedrows
                              ? item.sectors.shelter.PeopleAffectedrows.map(
                                  function(item) {
                                    return item.damaged_fhh
                                      ? parseInt(item.damaged_fhh)
                                      : 0;
                                  }
                                ).reduce((sum, value) => sum + value)
                              : 0
                          )
                        }}
                      </td>
                      <td
                        style="font-weight:bold"
                        colspan="2"
                        width="10%"
                        class="right-align"
                      >
                        {{
                          numberWithCommas(
                            (item.sectors.shelter.PeopleAffectedrows
                              ? item.sectors.shelter.PeopleAffectedrows.map(
                                  function(item) {
                                    return item.damaged_mhh
                                      ? parseInt(item.damaged_mhh)
                                      : 0;
                                  }
                                ).reduce((sum, value) => sum + value)
                              : 0) +
                              (item.sectors.shelter.PeopleAffectedrows
                                ? item.sectors.shelter.PeopleAffectedrows.map(
                                    function(item) {
                                      return item.damaged_fhh
                                        ? parseInt(item.damaged_fhh)
                                        : 0;
                                    }
                                  ).reduce((sum, value) => sum + value)
                                : 0)
                          )
                        }}
                      </td>
                    </tr>
                    <tr>
                      <td style="width:20%">
                        <strong>Response Needed</strong>
                      </td>
                      <td colspan="14">
                        <u>
                          <strong>URGENT</strong>
                        </u>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          {{
                            item.sectors.shelter
                              ? item.sectors.shelter.urgent_response_needed
                                ? item.sectors.shelter.urgent_response_needed
                                : "N/A"
                              : "N/A"
                          }}
                        </li>
                        <hr style="margin:1%;" />
                        <u>
                          <strong>GENERAL</strong>
                        </u>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          {{
                            item.sectors.shelter
                              ? item.sectors.shelter.response_needed
                                ? item.sectors.shelter.response_needed
                                : "N/A"
                              : "N/A"
                          }}
                        </li>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
              <!-- end shelter -->
              <!--  displaced -->
              <tr
                v-if="item.sectors.displaced"
                style="margin:0 !important;padding:0 !important;border:0px !important;"
              >
                <td
                  colspan="15"
                  style="margin:0 !important;padding:0 !important;border:0px"
                >
                  <table
                    width="100%"
                    style="margin:0;padding:0 !important;border:0px !important;"
                  >
                    <tr>
                      <td
                        width="10%"
                        colspan="2"
                        class="clearfix"
                        :rowspan="
                          (item.sectors.displaced
                            .displaced_households_accommodated
                            ? item.sectors.displaced
                                .displaced_households_accommodated.length > 0
                              ? 2
                              : 1
                            : 1) +
                            (item.sectors.displaced.PeopleAffectedrows
                              ? item.sectors.displaced.PeopleAffectedrows
                                  .length > 0
                                ? item.sectors.displaced.PeopleAffectedrows
                                    .length + 3
                                : 1
                              : 1) +
                            (item.sectors.displaced
                              .displaced_individuals_accommodated
                              ? item.sectors.displaced
                                  .displaced_individuals_accommodated.length > 0
                                ? 2
                                : 1
                              : 1)
                        "
                      >
                        <span class="rotated" style="font-weight: bold;"
                          ></span
                        >
                        <img
                          src="../../../../static/crisis_population_displacement_100px_bluebox.png"
                          height="150"
                        />
                      </td>
                      <td
                        v-if="
                          item.sectors.displaced
                            .displaced_households_accommodated &&
                            item.sectors.displaced
                              .displaced_households_accommodated.length > 0
                        "
                        colspan="8"
                        width="20%"
                        :rowspan="
                          item.sectors.displaced
                            .displaced_households_accommodated
                            ? item.sectors.displaced
                                .displaced_households_accommodated.length > 0
                              ? 2
                              : 1
                            : 1
                        "
                      >
                        <strong
                          >Number of HH accomodated in difference
                          structures</strong
                        >
                      </td>

                      <td
                        colspan="2"
                        width="15%"
                        v-if="
                          item.sectors.displaced
                            .displaced_households_accommodated &&
                            item.sectors.displaced
                              .displaced_households_accommodated.length > 0
                        "
                      >
                        <b>
                          <center>Male</center>
                        </b>
                      </td>
                      <td
                        colspan="2"
                        width="15%"
                        v-if="
                          item.sectors.displaced
                            .displaced_households_accommodated &&
                            item.sectors.displaced
                              .displaced_households_accommodated.length > 0
                        "
                      >
                        <b>
                          <center>Female</center>
                        </b>
                      </td>
                      <td
                        colspan="2"
                        width="15%"
                        v-if="
                          item.sectors.displaced
                            .displaced_households_accommodated &&
                            item.sectors.displaced
                              .displaced_households_accommodated.length > 0
                        "
                      >
                        <b>
                          <center>Total</center>
                        </b>
                      </td>
                    </tr>
                    <tr
                      v-if="
                        item.sectors.displaced
                          .displaced_households_accommodated &&
                          item.sectors.displaced
                            .displaced_households_accommodated.length > 0
                      "
                    >
                      <td width="15%" colspan="2" class="right-align">
                        {{
                          numberWithCommas(
                            item.sectors.displaced
                              .displaced_households_accommodated
                              ? item.sectors.displaced.displaced_households_accommodated
                                  .map(function(item) {
                                    return item.accomodated_males_hh
                                      ? parseInt(item.accomodated_males_hh)
                                      : 0;
                                  })
                                  .reduce((sum, value) => sum + value)
                              : 0
                          )
                        }}
                      </td>
                      <td width="15%" colspan="2" class="right-align">
                        {{
                          numberWithCommas(
                            item.sectors.displaced
                              .displaced_households_accommodated
                              ? item.sectors.displaced.displaced_households_accommodated
                                  .map(function(item) {
                                    return item.accomodated_females_hh
                                      ? parseInt(item.accomodated_females_hh)
                                      : 0;
                                  })
                                  .reduce((sum, value) => sum + value)
                              : 0
                          )
                        }}
                      </td>
                      <td colspan="2" width="15%" class="right-align">
                        {{
                          numberWithCommas(
                            parseInt(
                              item.sectors.displaced
                                .displaced_households_accommodated
                                ? item.sectors.displaced.displaced_households_accommodated
                                    .map(function(item) {
                                      return item.accomodated_males_hh
                                        ? parseInt(item.accomodated_males_hh)
                                        : 0;
                                    })
                                    .reduce((sum, value) => sum + value)
                                : 0
                            ) +
                              parseInt(
                                item.sectors.displaced
                                  .displaced_households_accommodated
                                  ? item.sectors.displaced.displaced_households_accommodated
                                      .map(function(item) {
                                        return item.accomodated_females_hh
                                          ? parseInt(
                                              item.accomodated_females_hh
                                            )
                                          : 0;
                                      })
                                      .reduce((sum, value) => sum + value)
                                  : 0
                              )
                          )
                        }}
                      </td>
                    </tr>

                    <tr
                      v-if="
                        item.sectors.displaced
                          .displaced_individuals_accommodated &&
                          item.sectors.displaced
                            .displaced_individuals_accommodated.length > 0
                      "
                    >
                      <td
                        colspan="8"
                        width="20%"
                        :rowspan="
                          item.sectors.displaced
                            .displaced_individuals_accommodated
                            ? item.sectors.displaced
                                .displaced_individuals_accommodated.length > 0
                              ? 2
                              : 1
                            : 1
                        "
                      >
                        <strong
                          >Number of People accomodated in difference
                          structures</strong
                        >
                      </td>
                      <td colspan="2" width="15%">
                        <b>
                          <center>Male</center>
                        </b>
                      </td>
                      <td colspan="2" width="15%">
                        <b>
                          <center>Female</center>
                        </b>
                      </td>
                      <td colspan="2" width="15%">
                        <b>
                          <center>Total</center>
                        </b>
                      </td>
                    </tr>

                    <tr
                      v-if="
                        item.sectors.displaced
                          .displaced_individuals_accommodated &&
                          item.sectors.displaced
                            .displaced_individuals_accommodated.length > 0
                      "
                    >
                      <td width="15%" colspan="2" class="right-align">
                        {{
                          numberWithCommas(
                            item.sectors.displaced
                              .displaced_individuals_accommodated
                              ? item.sectors.displaced.displaced_individuals_accommodated
                                  .map(function(item) {
                                    return item.accomodated_males
                                      ? parseInt(item.accomodated_males)
                                      : 0;
                                  })
                                  .reduce((sum, value) => sum + value)
                              : 0
                          )
                        }}
                      </td>
                      <td width="15%" colspan="2" class="right-align">
                        {{
                          numberWithCommas(
                            item.sectors.displaced
                              .displaced_individuals_accommodated
                              ? item.sectors.displaced.displaced_individuals_accommodated
                                  .map(function(item) {
                                    return item.accomodated_females
                                      ? parseInt(item.accomodated_females)
                                      : 0;
                                  })
                                  .reduce((sum, value) => sum + value)
                              : 0
                          )
                        }}
                      </td>
                      <td width="15%" colspan="2" class="right-align">
                        {{
                          numberWithCommas(
                            parseInt(
                              item.sectors.displaced
                                .displaced_individuals_accommodated
                                ? item.sectors.displaced.displaced_individuals_accommodated
                                    .map(function(item) {
                                      return item.accomodated_males
                                        ? parseInt(item.accomodated_males)
                                        : 0;
                                    })
                                    .reduce((sum, value) => sum + value)
                                : 0
                            ) +
                              parseInt(
                                item.sectors.displaced
                                  .displaced_individuals_accommodated
                                  ? item.sectors.displaced.displaced_individuals_accommodated
                                      .map(function(item) {
                                        return item.accomodated_females
                                          ? parseInt(item.accomodated_females)
                                          : 0;
                                      })
                                      .reduce((sum, value) => sum + value)
                                  : 0
                              )
                          )
                        }}
                      </td>
                    </tr>
                    <tr
                      v-if="
                        item.sectors.displaced.PeopleAffectedrows &&
                          item.sectors.displaced.PeopleAffectedrows.length > 0
                      "
                    >
                      <td
                        width="20%"
                        v-bind:rowspan="
                          item.sectors.displaced.PeopleAffectedrows.length
                            ? item.sectors.displaced.PeopleAffectedrows.length >
                              0
                              ? item.sectors.displaced.PeopleAffectedrows
                                  .length + 2
                              : 1
                            : 1
                        "
                        colspan="5"
                      >
                        <strong>Number of Households displaced</strong>
                      </td>
                      <td colspan="3">
                        <b>
                          <b>GVH</b>
                        </b>
                      </td>
                      <td colspan="2" width="8%">
                        <b>
                          <center>Male</center>
                        </b>
                      </td>
                      <td colspan="2" width="8%">
                        <b>
                          <center>Female</center>
                        </b>
                      </td>
                      <td colspan="2" width="8%" style="font-weight:bold">
                        <b>
                          <b>
                            <center>Total</center>
                          </b>
                        </b>
                      </td>
                    </tr>
                    <tr
                      v-if="
                        item.sectors.displaced.PeopleAffectedrows &&
                          item.sectors.displaced.PeopleAffectedrows.length > 0
                      "
                      v-for="row in item.sectors.displaced.PeopleAffectedrows"
                    >
                      <td colspan="3">{{ row.name }}</td>
                      <td colspan="2" class="right-align">
                        {{
                          numberWithCommas(row.number_displaced_by_gender_mhh)
                        }}
                      </td>
                      <td colspan="2" class="right-align">
                        {{
                          numberWithCommas(row.number_displaced_by_gender_fhh)
                        }}
                      </td>
                      <td
                        colspan="2"
                        style="font-weight:bold"
                        class="right-align"
                      >
                        {{
                          numberWithCommas(
                            (row.number_displaced_by_gender_fhh
                              ? parseInt(row.number_displaced_by_gender_fhh)
                              : 0) +
                              (row.number_displaced_by_gender_mhh
                                ? parseInt(row.number_displaced_by_gender_mhh)
                                : 0)
                          )
                        }}
                      </td>
                    </tr>
                    <tr
                      v-if="
                        item.sectors.displaced.PeopleAffectedrows &&
                          item.sectors.displaced.PeopleAffectedrows.length > 0
                      "
                    >
                      <td colspan="3" style="font-weight:bold">Total</td>
                      <td
                        colspan="2"
                        style="font-weight:bold"
                        class="right-align"
                      >
                        {{
                          numberWithCommas(
                            item.sectors.displaced.PeopleAffectedrows
                              ? item.sectors.displaced.PeopleAffectedrows.map(
                                  function(item) {
                                    return item.number_displaced_by_gender_mhh
                                      ? parseInt(
                                          item.number_displaced_by_gender_mhh
                                        )
                                      : 0;
                                  }
                                ).reduce((sum, value) => sum + value)
                              : 0
                          )
                        }}
                      </td>
                      <td
                        colspan="2"
                        style="font-weight:bold"
                        class="right-align"
                      >
                        {{
                          numberWithCommas(
                            item.sectors.displaced.PeopleAffectedrows
                              ? item.sectors.displaced.PeopleAffectedrows.map(
                                  function(item) {
                                    return item.number_displaced_by_gender_fhh
                                      ? parseInt(
                                          item.number_displaced_by_gender_fhh
                                        )
                                      : 0;
                                  }
                                ).reduce((sum, value) => sum + value)
                              : 0
                          )
                        }}
                      </td>
                      <td
                        colspan="2"
                        style="font-weight:bold"
                        class="right-align"
                      >
                        {{
                          numberWithCommas(
                            (item.sectors.displaced.PeopleAffectedrows
                              ? item.sectors.displaced.PeopleAffectedrows.map(
                                  function(item) {
                                    return item.number_displaced_by_gender_fhh
                                      ? parseInt(
                                          item.number_displaced_by_gender_fhh
                                        )
                                      : 0;
                                  }
                                ).reduce((sum, value) => sum + value)
                              : 0) +
                              (item.sectors.displaced.PeopleAffectedrows
                                ? item.sectors.displaced.PeopleAffectedrows.map(
                                    function(item) {
                                      return item.number_displaced_by_gender_mhh
                                        ? parseInt(
                                            item.number_displaced_by_gender_mhh
                                          )
                                        : 0;
                                    }
                                  ).reduce((sum, value) => sum + value)
                                : 0)
                          )
                        }}
                      </td>
                    </tr>
                    <tr>
                      <td style="width:20%" colspan="5">
                        <strong>Response Needed</strong>
                      </td>
                      <td colspan="9" style="width:70%">
                        <u>
                          <strong>URGENT</strong>
                        </u>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          {{
                            item.sectors.displaced
                              ? item.sectors.displaced.urgent_response_needed
                                ? item.sectors.displaced.urgent_response_needed
                                : "N/A"
                              : "N/A"
                          }}
                        </li>
                        <hr style="margin:1%;" />
                        <u>
                          <strong>GENERAL</strong>
                        </u>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          {{
                            item.sectors.displaced
                              ? item.sectors.displaced.response_needed
                                ? item.sectors.displaced.response_needed
                                : "N/A"
                              : "N/A"
                          }}
                        </li>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
              <!-- end displaced -->
              <!--  agricultutre -->
              <tr
                v-if="item.sectors.agriculture"
                style="margin:0 !important;padding:0 !important;border:0px !important;"
              >
                <td
                  colspan="15"
                  style="margin:0 !important;padding:0 !important;border:0px"
                >
                  <table
                    width="100%"
                    style="margin:0 !important;padding:0 !important;border:0px"
                  >
                    <tr>
                      <td
                        width="10%"
                        class="clearfix"
                        :rowspan="
                          (item.sectors.agriculture.impact_on_crops ? 1 : 1) +
                            (item.sectors.agriculture.crops_damaged
                              ? item.sectors.agriculture.crops_damaged.length >=
                                0
                                ? item.sectors.agriculture.crops_damaged
                                    .length + 2
                                : 1
                              : 1) +
                            (item.sectors.agriculture.impact_on_livestock
                              ? item.sectors.agriculture.impact_on_livestock
                                  .length > 0
                                ? 1
                                : 1
                              : 1) +
                            (item.sectors.agriculture.food_item_damage
                              ? item.sectors.agriculture.food_item_damage
                                  .length > 0
                                ? item.sectors.agriculture.food_item_damage
                                    .length + 1
                                : 1
                              : 1) +
                            1
                        "
                      >
                        <span class="rotated" style="font-weight: bold;"
                          ></span
                        >
                        <img
                          src="../../../../static/other_cluster_agriculture_100px_bluebox.png"
                          height="150"
                        />
                      </td>
                      <td
                        colspan="12"
                        width="80%"
                        v-if="item.sectors.agriculture.crops_damaged"
                      >
                        <strong>Number of Crops damaged (Hectares)</strong>
                      </td>
                      <td
                        colspan="2"
                        width="10%"
                        v-if="item.sectors.agriculture.crops_damaged"
                        class="right-align"
                      >
                        <span
                          v-if="
                            item.sectors.agriculture.crops_damaged &&
                              item.sectors.agriculture.crops_damaged.length > 0
                          "
                          class="right-align"
                          >{{
                            numberWithCommas(
                              item.sectors.agriculture.crops_damaged
                                ? item.sectors.agriculture.crops_damaged
                                    .map(function(item) {
                                      return item.hectares_washed_away
                                        ? parseInt(item.hectares_washed_away)
                                        : 0;
                                    })
                                    .reduce((sum, value) => sum + value) +
                                    item.sectors.agriculture.crops_damaged
                                      .map(function(item) {
                                        return item.hectares_submerged
                                          ? parseInt(item.hectares_submerged)
                                          : 0;
                                      })
                                      .reduce((sum, value) => sum + value)
                                : 0
                            )
                          }}</span
                        >
                      </td>
                    </tr>
                    <tr
                      v-if="
                        item.sectors.agriculture.impact_on_crops ||
                          item.sectors.agriculture.impact_on_livestock
                      "
                    >
                      <td colspan="12" width="80%">
                        <strong>Number of Households affected</strong>
                      </td>
                      <td colspan="2" width="10%" class="right-align">
                        <span
                          v-if="
                            item.sectors.agriculture.impact_on_crops &&
                              item.sectors.agriculture.impact_on_crops.length >
                                0
                          "
                          class="right-align"
                          >{{
                            numberWithCommas(
                              item.sectors.agriculture.impact_on_crops
                                ? item.sectors.agriculture.impact_on_crops
                                    .map(function(item) {
                                      return item.hh_affected
                                        ? parseInt(item.hh_affected)
                                        : 0;
                                    })
                                    .reduce((sum, value) => sum + value)
                                : 0
                            )
                          }}</span
                        >
                      </td>
                    </tr>
                    <tr v-if="item.sectors.agriculture.impact_on_livestock">
                      <td colspan="12" width="80%">
                        <strong>Number of Livestock affected</strong>
                      </td>
                      <td colspan="2" width="10%" class="right-align">
                        <span
                          v-if="
                            item.sectors.agriculture.impact_on_livestock &&
                              item.sectors.agriculture.impact_on_livestock
                                .length > 0
                          "
                          class="right-align"
                          >{{
                            numberWithCommas(
                              item.sectors.agriculture.impact_on_livestock
                                ? item.sectors.agriculture.impact_on_livestock
                                    .map(function(item) {
                                      return item.livestock_affected
                                        ? parseInt(item.livestock_affected)
                                        : 0;
                                    })
                                    .reduce((sum, value) => sum + value)
                                : 0
                            )
                          }}</span
                        >
                      </td>
                    </tr>

                    <tr
                      v-if="
                        item.sectors.agriculture.crops_damaged &&
                          item.sectors.agriculture.crops_damaged.length > 0
                      "
                    >
                      <td
                        v-bind:rowspan="
                          item.sectors.agriculture.crops_damaged
                            ? item.sectors.agriculture.crops_damaged.length + 1
                            : 1
                        "
                        colspan="5"
                        width="20%"
                      >
                        <strong>Damage per crop (Hectares)</strong>
                      </td>
                      <td colspan="3" width="40%">
                        <b>Name</b>
                      </td>
                      <td colspan="2" width="10%">
                        <b>Submerged</b>
                      </td>
                      <td colspan="2" width="10%">
                        <b>Washed away</b>
                      </td>
                      <td colspan="2" width="10%">
                        <b>
                          <center>Total</center>
                        </b>
                      </td>
                    </tr>
                    <tr
                      v-if="
                        item.sectors.agriculture.crops_damaged &&
                          item.sectors.agriculture.crops_damaged.length > 0
                      "
                      v-for="crop in item.sectors.agriculture.crops_damaged"
                    >
                      <td colspan="3" width="40%">{{ crop.name }}</td>
                      <td width="10%" colspan="2" class="right-align">
                        {{ numberWithCommas(crop.hectares_submerged) }}
                      </td>
                      <td width="10%" colspan="2" class="right-align">
                        {{ numberWithCommas(crop.hectares_washed_away) }}
                      </td>
                      <td width="10%" colspan="2" class="right-align">
                        {{
                          numberWithCommas(
                            (crop.hectares_submerged
                              ? parseInt(crop.hectares_submerged)
                              : 0) +
                              (crop.hectares_washed_away
                                ? parseInt(crop.hectares_washed_away)
                                : 0)
                          )
                        }}
                      </td>
                    </tr>

                    <tr
                      v-if="
                        item.sectors.agriculture.food_item_damage &&
                          item.sectors.agriculture.food_item_damage.length > 0
                      "
                    >
                      <td
                        v-bind:rowspan="
                          item.sectors.agriculture.food_item_damage.length + 1
                        "
                        colspan="5"
                        width="20%"
                      >
                        <strong>Number of Food items damage (KGs)</strong>
                      </td>
                      <td colspan="7" width="60%">
                        <b>Name</b>
                      </td>
                      <td colspan="2" width="10%">
                        <b>in KGs</b>
                      </td>
                    </tr>
                    <tr
                      v-if="
                        item.sectors.agriculture.food_item_damage &&
                          item.sectors.agriculture.food_item_damage.length > 0
                      "
                      v-for="crop in item.sectors.agriculture.food_item_damage"
                    >
                      <td colspan="7" width="60%">{{ crop.food_item_name }}</td>
                      <td colspan="2" width="10%" class="right-align">
                        {{ numberWithCommas(crop.number_of_kilos) }}
                      </td>
                    </tr>
                    <tr>
                      <td colspan="5" style="width:20%">
                        <strong>Response Needed</strong>
                      </td>
                      <td colspan="9" style="width:70%">
                        <u>
                          <strong>URGENT</strong>
                        </u>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          {{
                            item.sectors.agriculture
                              ? item.sectors.agriculture.urgent_response_needed
                                ? item.sectors.agriculture
                                    .urgent_response_needed
                                : "N/A"
                              : "N/A"
                          }}
                        </li>
                        <hr style="margin:1%;" />
                        <u>
                          <strong>GENERAL</strong>
                        </u>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          {{
                            item.sectors.agriculture
                              ? item.sectors.agriculture.response_needed
                                ? item.sectors.agriculture.response_needed
                                : "N/A"
                              : "N/A"
                          }}
                        </li>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
              <!-- end agricultutre -->
              <!--  Food security -->
              <tr
                v-if="item.sectors.food"
                style="margin:0;padding:0 !important;border:0px !important;"
              >
                <td
                  colspan="15"
                  style="margin:0;padding:0 !important;border:0px"
                >
                  <table
                    width="100%"
                    style="margin:0;padding:0 !important;border:0px !important;"
                  >
                    <tr>
                      <td
                        :rowspan="
                          item.sectors.food.food_stocks_avaliability
                            ? item.sectors.food.food_stocks_avaliability
                                .length + 10
                            : 1
                        "
                        width="10%"
                        class="clearfix"
                      >
                        <span class="rotated" style="font-weight: bold;"
                          ></span
                        >
                        <img
                          src="../../../../static/cluster_food_security_100px_bluebox.png"
                          height="150"
                        />
                      </td>
                      <td
                        v-if="
                          item.sectors.food.food_stocks_avaliability &&
                            item.sectors.food.food_stocks_avaliability.length >
                              0
                        "
                        v-bind:rowspan="
                          item.sectors.food.food_stocks_avaliability.length > 0
                            ? item.sectors.food.food_stocks_avaliability
                                .length + 1
                            : 1
                        "
                        colspan="6"
                        style="width:20%"
                      >
                        <strong>Food stocks availability (HH)</strong>
                      </td>
                      <td
                        colspan="2"
                        width="40%"
                        v-if="
                          item.sectors.food.food_stocks_avaliability &&
                            item.sectors.food.food_stocks_avaliability.length >
                              0
                        "
                      >
                        <b>Category</b>
                      </td>
                      <td
                        width="10%"
                        colspan="2"
                        v-if="
                          item.sectors.food.food_stocks_avaliability &&
                            item.sectors.food.food_stocks_avaliability.length >
                              0
                        "
                      >
                        <b>
                          <center>Male</center>
                        </b>
                      </td>
                      <td
                        width="10%"
                        colspan="2"
                        v-if="
                          item.sectors.food.food_stocks_avaliability &&
                            item.sectors.food.food_stocks_avaliability.length >
                              0
                        "
                      >
                        <b>
                          <center>Female</center>
                        </b>
                      </td>
                      <td
                        width="10%"
                        colspan="2"
                        v-if="
                          item.sectors.food.food_stocks_avaliability &&
                            item.sectors.food.food_stocks_avaliability.length >
                              0
                        "
                      >
                        <b>
                          <center>Total</center>
                        </b>
                      </td>
                    </tr>
                    <tr
                      v-if="
                        item.sectors.food.food_stocks_avaliability &&
                          item.sectors.food.food_stocks_avaliability.length > 0
                      "
                      v-for="crop in item.sectors.food.food_stocks_avaliability"
                    >
                      <td colspan="2" class="clearfix" width="40%">
                        {{ crop.category }}
                      </td>
                      <td colspan="2" width="10%" class="right-align">
                        {{ numberWithCommas(crop.male_HH) }}
                      </td>
                      <td colspan="2" width="10%" class="right-align">
                        {{ numberWithCommas(crop.female_HH) }}
                      </td>
                      <td colspan="2" width="10%" class="right-align">
                        {{
                          numberWithCommas(
                            parseInt(crop.male_HH) + parseInt(crop.female_HH)
                          )
                        }}
                      </td>
                    </tr>
          <!------------------work from dc starts----------------------------------------->
                    <!---start---------------------->
                    <tr
                      v-if="
                        item.sectors.food.food_item_damage 
                      "
                    >
                      <td style="width:20%" rowspan="3" colspan="4">
                        <b>Food Items Damaged</b>
                      </td>
                      <td colspan="5">
                        <b>
                          <center>Food Name</center>
                        </b>
                      </td>
                      <td colspan="5">
                        <b>
                          <center>Mass(KGs)</center>
                        </b>
                      </td>
                     
                    </tr>
                    <tr 
                      v-for="index in  item.sectors.food.food_item_damage" :key="index">
                      <td colspan="5" class="right-align">
                        {{
                          index.food_item_name
                        }}
                      </td>
                      <td colspan="5" class="right-align">
                        {{
                          numberWithCommas(
                           index.number_of_kilos
                          )
                        }}
                      </td>
                      
                    </tr>
                    <!----------------end----------->
                     <!---food ---------------------->
                     <tr
                      v-if="
                        item.sectors.food.food_availability"

                        v-bind:rowspan="
                          item.sectors.food.food_availability.length > 0
                            ? item.sectors.food.food_availability
                                .length + 1
                            : 1
                        "
                    >
                      <td style="width:20%" rowspan="2" colspan="4">
                        <b>Food availability</b>
                      </td>
                      <td colspan="5">
                        <b>
                          <center>Food available</center>
                        </b>
                      </td>
                      <td colspan="5">
                        <b>
                          <center>Explaination if No food</center>
                        </b>
                      </td>
                     
                    </tr>
                    <tr 
                      v-for="index in  item.sectors.food.food_availability" :key="index">
                      <td colspan="5" class="right-align">
                        {{
                          index.foodavailable
                        }}
                      </td>
                      <td colspan="5" class="right-align" v-if="index.foodavailable=='no'">
                        {{
                          index.no_food_availability_explanation
                        }}
                      </td>
                      
                    </tr>
                    <!----------------foood availability ends----------->
                    <!---food  accessibility---------------------->
                    <tr
                    v-if="
                      item.sectors.food.food_access.length>0 && item.sectors.food.food_access
                    "
                    >
                    <td style="width:20%" rowspan="3" colspan="5">
                      <b>Food accessibility</b>
                    </td>
                    <td colspan="5">
                      <b>
                        <center>Food accessible?</center>
                      </b>
                    </td>
                    <td colspan="5">
                      <b>
                        <center>Response</center>
                      </b>
                    </td>

                    </tr>
                    <tr 
                    v-for="index in  item.sectors.food.food_access" :key="index">
                    <td colspan="5" class="right-align">
                      {{
                        index.name
                      }}
                    </td>
                    <td colspan="5" class="right-align">
                      {{
                        index.response
                      }}
                    </td>
                    </tr>
                    <!----------------Food accessibility ends----------->
          <!----------------work from dc finish---------------------------------->
                    <tr>
                      <td colspan="6" style="width:20%">
                        <strong>Response Needed</strong>
                      </td>
                      <td colspan="9" style="width:80%">
                        <u>
                          <strong>URGENT</strong>
                        </u>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          {{
                            item.sectors.food
                              ? item.sectors.food.urgent_response_needed
                                ? item.sectors.food.urgent_response_needed
                                : "N/A"
                              : "N/A"
                          }}
                        </li>
                        <hr style="margin:1%;" />
                        <u>
                          <strong>GENERAL</strong>
                        </u>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          {{
                            item.sectors.food
                              ? item.sectors.food.response_needed
                                ? item.sectors.food.response_needed
                                : "N/A"
                              : "N/A"
                          }}
                        </li>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
              <!-- end Food security -->
              <!--  Education-->
              <tr
                v-if="item.sectors.education"
                style="margin:0;padding:0 !important;border:0px !important;"
              >
                <td
                  colspan="15"
                  style="margin:0;padding:0 !important;border:0px"
                >
                  <table
                    width="100%"
                    style="margin:0;padding:0 !important;border:0px !important;"
                  >
                    <tr>
                      <td
                        :rowspan="
                          4 +
                            (item.sectors.education.impact_on_schools
                              ? item.sectors.education.impact_on_schools
                                  .length +
                                item.sectors.education.impact_on_schools.length
                              : 0)
                        "
                        width="10%"
                        class="clearfix"
                      >
                        <span class="rotated" style="font-weight: bold;"
                          ></span
                        >
                        <img
                          src="../../../../static/cluster_education_100px_bluebox.png"
                          height="150"
                        />
                      </td>
                      <td
                        v-if="
                          item.sectors.education.impact_on_schools &&
                            item.sectors.education.impact_on_schools.length > 0
                        "
                        :rowspan="
                          1 + item.sectors.education.impact_on_schools.length
                        "
                        colspan="4"
                        style="20%"
                      >
                        <b>Impact on schools buildings</b>
                      </td>
                      <td
                        v-if="
                          item.sectors.education.impact_on_schools &&
                            item.sectors.education.impact_on_schools.length > 0
                        "
                        width="10%"
                      >
                        <b>School type</b>
                      </td>
                      <td
                        v-if="
                          item.sectors.education.impact_on_schools &&
                            item.sectors.education.impact_on_schools.length > 0
                        "
                        width="10%"
                      >
                        <b>Structure type</b>
                      </td>
                      <td
                        v-if="
                          item.sectors.education.impact_on_schools &&
                            item.sectors.education.impact_on_schools.length > 0
                        "
                        width="10%"
                      >
                        <b>Name</b>
                      </td>
                      <td
                        v-if="
                          item.sectors.education.impact_on_schools &&
                            item.sectors.education.impact_on_schools.length > 0
                        "
                        width="10%"
                      >
                        <b>Roof affected</b>
                      </td>
                      <td
                        v-if="
                          item.sectors.education.impact_on_schools &&
                            item.sectors.education.impact_on_schools.length > 0
                        "
                        width="10%"
                      >
                        <b>Under water</b>
                      </td>
                      <td
                        v-if="
                          item.sectors.education.impact_on_schools &&
                            item.sectors.education.impact_on_schools.length > 0
                        "
                        width="10%"
                      >
                        <b>Partly damaged</b>
                      </td>

                      <td
                        v-if="
                          item.sectors.education.impact_on_schools &&
                            item.sectors.education.impact_on_schools.length > 0
                        "
                        width="10%"
                      >
                        <b>Completely damaged</b>
                      </td>
                    </tr>

                    <tr
                      v-if="
                        item.sectors.education.impact_on_schools &&
                          item.sectors.education.impact_on_schools.length > 0
                      "
                      v-for="row in item.sectors.education.impact_on_schools"
                    >
                      <td width="10%">{{ row.school_type }}</td>
                      <td width="10%">{{ row.structure_type }}</td>
                      <td width="10%">{{ row.school_name }}</td>
                      <td width="10%" class="right-align">
                        {{ numberWithCommas(row.roofs_affected) }}
                      </td>
                      <td width="10%" class="right-align">
                        {{ numberWithCommas(row.underwater) }}
                      </td>
                      <td width="10%" class="right-align">
                        {{ numberWithCommas(row.partially_functioning) }}
                      </td>
                      <td width="10%" class="right-align">
                        {{ numberWithCommas(row.completely_damaged) }}
                      </td>
                    </tr>
                    <tr
                      v-if="
                        item.sectors.education.impact_on_schools &&
                          item.sectors.education.impact_on_schools.length > 0
                      "
                    >
                      <td
                        v-if="item.sectors.education.impact_on_schools"
                        :rowspan="
                          1 + item.sectors.education.impact_on_schools.length
                        "
                        colspan="7"
                        style="20%"
                      >
                        <b>Impact on school goers</b>
                      </td>
                      <td
                        v-if="item.sectors.education.impact_on_schools"
                        width="10%"
                      >
                        <b>School type</b>
                      </td>
                      <td
                        v-if="item.sectors.education.impact_on_schools"
                        width="10%"
                      >
                        <b>Name</b>
                      </td>
                      <td
                        v-if="item.sectors.education.impact_on_schools"
                        width="10%"
                      >
                        <b>Males out of school</b>
                      </td>
                      <td
                        v-if="item.sectors.education.impact_on_schools"
                        width="10%"
                      >
                        <b>Females out of school</b>
                      </td>
                    </tr>
                    <tr
                      v-if="
                        item.sectors.education.impact_on_schools &&
                          item.sectors.education.impact_on_schools.length > 0
                      "
                      v-for="row in item.sectors.education.impact_on_schools"
                    >
                      <td width="10%">{{ row.school_type }}</td>
                      <td width="10%">{{ row.school_name }}</td>
                      <td width="10%" class="right-align">
                        {{ numberWithCommas(row.females_out_of_school) }}
                      </td>
                      <td width="10%" class="right-align">
                        {{ numberWithCommas(row.males_out_of_school) }}
                      </td>
                    </tr>
                    <tr>
                      <td colspan="2" style="width:20%">
                        <strong>Response Needed</strong>
                      </td>
                      <td colspan="12">
                        <u>
                          <strong>URGENT</strong>
                        </u>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          {{
                            item.sectors.education
                              ? item.sectors.education.urgent_response_needed
                                ? item.sectors.education.urgent_response_needed
                                : "N/A"
                              : "N/A"
                          }}
                        </li>
                        <hr style="margin:1%;" />
                        <u>
                          <strong>GENERAL</strong>
                        </u>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          {{
                            item.sectors.education
                              ? item.sectors.education.response_needed
                                ? item.sectors.education.response_needed
                                : "N/A"
                              : "N/A"
                          }}
                        </li>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
              <!-- end Education -->
          <!--  Healthy -->
              <tr
                v-if="item.sectors.health"
                style="margin:0;padding:0 !important;border:0px !important;"
              >
                <td
                  colspan="15"
                  style="margin:0;padding:0 !important;border:0px"
                >
                  <table
                    width="100%"
                    style="margin:0;padding:0 !important;border:0px !important;"
                  >
                    <tr>
                      <td
                        v-if="
                          item.sectors.health.available_health_facilities &&
                            item.sectors.health.available_health_facilities
                              .length > 0
                        "
                        :rowspan="
                          (item.sectors.health.available_health_facilities
                            ? item.sectors.health.available_health_facilities
                                .length
                            : 0) +
                            (item.sectors.health.risk_out_disease_outbreak
                              ? item.sectors.health.risk_out_disease_outbreak
                                  .length + 1
                              : 0) +
                            6
                        "
                        width="10%"
                        class="clearfix"
                      >
                        <span class="rotated" style="font-weight: bold;"
                          ></span
                        >
                        <img
                          src="../../../../static/cluster_health_100px_bluebox.png"
                          height="150"
                        />
                      </td>
                      <!--------------------->
                      <td
                        v-if="
                          item.sectors.health.available_health_facilities &&
                            item.sectors.health.available_health_facilities
                              .length > 0
                        "
                        :rowspan="
                          1 +
                            item.sectors.health.available_health_facilities
                              .length
                        "
                        colspan="5"
                        style="width:22%"
                      >
                        <strong>Health facility availability</strong>
                      </td>
                      <td
                        style="width:40%"
                        colspan="2"
                        v-if="
                          item.sectors.health.available_health_facilities &&
                            item.sectors.health.available_health_facilities
                              .length > 0
                        "
                      >
                        <b>Health facility available</b>
                      </td>
                       <td
                        style="width:60%"
                        colspan=""
                      >
                        <b>Condition</b>
                      </td>

<!-------------------------->

                    </tr>

                    <tr
                      v-if="
                        item.sectors.health.available_health_facilities &&
                          item.sectors.health.available_health_facilities
                            .length > 0
                      "
                      v-for="row in item.sectors.health
                        .available_health_facilities"
                    >
                      <td style="width:40%" colspan="2">{{ row.facility_name }}  &nbsp; {{ row.name }}</td>
                      <td style="width:10%" colspan="2" class="left-align">
                        {{ numberWithCommas(row.partially_functioning) }}  {{ numberWithCommas(row.verge_of_closing) }}   {{ numberWithCommas(row.closed) }}
                      </td>

                    </tr>
                    <!---------start----------->
                     <tr
                      v-if="
                        item.sectors.health.available_health_medical &&
                          item.sectors.health.available_health_medical.length >
                            0
                      "
                    >
                      <td
                        :rowspan="
                          1 +
                            item.sectors.health.available_health_medical.length
                        "
                        colspan="5"
                        style="width:22%"
                      >
                        <strong>Availability of medical and health personnel</strong>
                      </td>
                      <td colspan="2" style="width:40%">
                        <b>Service</b>
                      </td>
                      <td colspan="6" style="width:30%">
                        <b>Status</b>
                      </td>
                    </tr>
                    <tr
                      v-if="
                        item.sectors.health.available_health_medical &&
                          item.sectors.health.available_health_medical.length >
                            0
                      "
                      v-for="row in item.sectors.health
                        .available_health_medical"
                    >
                      <td style="width:40%" colspan="2">{{ row.name }}</td>
                      <td style="width:30%" colspan="6">{{ row.status }}</td>

                    </tr>
                    <!------------end------------------>
                    <tr
                      v-if="
                        item.sectors.health.risk_out_disease_outbreak &&
                          item.sectors.health.risk_out_disease_outbreak.length >
                            0
                      "
                    >
                      <td
                        :rowspan="
                          1 +
                            item.sectors.health.risk_out_disease_outbreak.length
                        "
                        colspan="5"
                        style="width:22%"
                      >
                        <strong>Risks of disease outbreak</strong>
                      </td>
                      <td colspan="2" style="width:40%">
                        <b>Disease</b>
                      </td>
                      <td colspan="2" style="width:30%">
                        <b>Risk</b>
                      </td>
                    </tr>
                    <tr
                      v-if="
                        item.sectors.health.risk_out_disease_outbreak &&
                          item.sectors.health.risk_out_disease_outbreak.length >
                            0
                      "
                      v-for="row in item.sectors.health
                        .risk_out_disease_outbreak"
                    >
                      <td style="width:40%" colspan="2">{{ row.name }}</td>
                      <td style="width:30%" colspan="6">{{ row.risk }}</td>

                    </tr>

                    <tr>
                      <td colspan="5" style="width:22%">
                        <strong>Emergency Needs</strong>
                      </td>
                      <td colspan="8">

                        <li style="margin-left:2%" class="qcont">
                           {{
                    item.sectors.health.emergent_needs.join() == "" ? "Not reported" : item.sectors.health.emergent_needs.join(", ")
                    }}
                        </li>
                      </td>
                    </tr>
                    <tr>
                      <td colspan="6" style="width:20%">
                        <strong>Emergency Children Needs</strong>
                      </td>
                      <td colspan="8">

                        <li style="margin-left:2%" class="qcont">
                           {{
                    item.sectors.health.emergent_children_needs.join() == "" ? "Not reported" : item.sectors.health.emergent_children_needs.join(", ")
                    }}
                        </li>
                      </td>
                    </tr>
                    <tr>
                    <tr>
                      <td colspan="6" style="width:20%">
                        <strong>Emergency Women Needs</strong>
                      </td>
                      <td colspan="8">

                        <li style="margin-left:2%" class="qcont">
                           {{
                    item.sectors.health.emergent_women_needs.join() == "" ? "Not reported" : item.sectors.health.emergent_women_needs.join(", ")
                    }}
                        </li>
                      </td>
                    </tr>
                    <tr>
                      <td colspan="6" style="width:20%">
                        <strong>Response Needed</strong>
                      </td>
                      <td colspan="8" style="width:70%">
                        <u>
                          <strong>URGENT</strong>
                        </u>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          {{
                            item.sectors.health
                              ? item.sectors.health.urgent_response_needed
                                ? item.sectors.health.urgent_response_needed
                                : "N/A"
                              : "N/A"
                          }}
                        </li>
                        <hr style="margin:1%;" />
                        <u>
                          <strong>GENERAL</strong>
                        </u>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          {{
                            item.sectors.health
                              ? item.sectors.health.response_needed
                                ? item.sectors.health.response_needed
                                : "N/A"
                              : "N/A"
                          }}
                        </li>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
              <!-- end Healthy -->
              <!-- Food livelihoods -->
              <tr
                v-if="item.sectors.livelihoods"
                style="margin:0;padding:0 !important;border:0px !important;"
              >
                <td
                  colspan="15"
                  style="margin:0;padding:0 !important;border:0px"
                >
                  <table
                    width="100%"
                    style="margin:0;padding:0 !important;border:0px !important;"
                  >
                    <tr>
                      <td
                        width="10%"
                        class="clearfix"
                        v-if="
                          item.sectors.livelihoods.livelihoods_affected &&
                            item.sectors.livelihoods.livelihoods_affected
                              .length > 0
                        "
                        :rowspan="
                          item.sectors.livelihoods.livelihoods_affected
                            ? item.sectors.livelihoods.livelihoods_affected
                                .length + 2
                            : 2
                        "
                      >
                        <span class="rotated" style="font-weight: bold;"
                          ></span
                        >
                        <img
                          src="../../../../static/socioeconomic_livelihood_100px_bluebox.png"
                          height="150"
                        />
                      </td>
                      <td
                        v-if="
                          item.sectors.livelihoods.livelihoods_affected &&
                            item.sectors.livelihoods.livelihoods_affected
                              .length > 0
                        "
                        :rowspan="
                          1 +
                            item.sectors.livelihoods.livelihoods_affected.length
                        "
                        style="width:20%"
                      >
                        <strong>Livelihoods affected (HH)</strong>
                      </td>
                      <td
                        width="50%"
                        v-if="
                          item.sectors.livelihoods.livelihoods_affected &&
                            item.sectors.livelihoods.livelihoods_affected
                              .length > 0
                        "
                        colspan="8"
                      >
                        <b>Category</b>
                      </td>
                      <td
                        width="10%"
                        v-if="
                          item.sectors.livelihoods.livelihoods_affected &&
                            item.sectors.livelihoods.livelihoods_affected
                              .length > 0
                        "
                        colspan="2"
                      >
                        <b>Severely affected</b>
                      </td>
                      <td
                        width="10%"
                        v-if="
                          item.sectors.livelihoods.livelihoods_affected &&
                            item.sectors.livelihoods.livelihoods_affected
                              .length > 0
                        "
                        colspan="2"
                      >
                        <b>Slightly affected</b>
                      </td>
                    </tr>
                    <tr
                      v-if="
                        item.sectors.livelihoods.livelihoods_affected &&
                          item.sectors.livelihoods.livelihoods_affected.length >
                            0
                      "
                      v-for="row in item.sectors.livelihoods
                        .livelihoods_affected"
                    >
                      <td width="50%" colspan="8">{{ row.name }}</td>
                      <td width="10%" colspan="2" class="right-align">
                        {{ numberWithCommas(row.severely_affected) }}
                      </td>
                      <td width="10%" colspan="2" class="right-align">
                        {{ numberWithCommas(row.slightly_affected) }}
                      </td>
                    </tr>
                    <tr>
                      <td colspan="1" style="width:20%">
                        <strong>Response Needed</strong>
                      </td>
                      <td colspan="13" style="width:70%">
                        <u>
                          <strong>URGENT</strong>
                        </u>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          {{
                            item.sectors.livelihoods
                              ? item.sectors.livelihoods.urgent_response_needed
                                ? item.sectors.livelihoods
                                    .urgent_response_needed
                                : "N/A"
                              : "N/A"
                          }}
                        </li>
                        <hr style="margin:1%;" />
                        <u>
                          <strong>GENERAL</strong>
                        </u>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          {{
                            item.sectors.livelihoods
                              ? item.sectors.livelihoods.response_needed
                                ? item.sectors.livelihoods.response_needed
                                : "N/A"
                              : "N/A"
                          }}
                        </li>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
              <!-- end livelihoods -->
              <!--  Nutrition -->
              <tr
                v-if="item.sectors.nutrition"
                style="margin:0;padding:0 !important;border:0px !important;"
              >
                <td
                  colspan="15"
                  style="margin:0;padding:0 !important;border:0px"
                >
                  <table
                    width="100%"
                    style="margin:0;padding:0 !important;border:0px !important;"
                  >
                    <tr
                      v-if="
                        item.sectors.nutrition.affected_population &&
                          item.sectors.nutrition.affected_population.length > 0
                      "
                    >
                      <td
                        :rowspan="
                          3 +
                            (item.sectors.nutrition.affected_population
                              ? item.sectors.nutrition.affected_population
                                  .length
                              : 0) +
                            (item.sectors.nutrition
                              .impact_on_nutrition_programmes
                              ? item.sectors.nutrition
                                  .impact_on_nutrition_programmes.length
                              : 0)
                        "
                        width="10%"
                        class="clearfix"
                      >
                        <span class="rotated" style="font-weight: bold;"
                          ></span
                        >
                        <img
                          src="../../../../static/cluster_nutrition_100px_bluebox.png"
                          height="150"
                        />
                      </td>
                      <td
                        v-if="item.sectors.nutrition.affected_population"
                        width="20%"
                        :rowspan="
                          1 + item.sectors.nutrition.affected_population.length
                        "
                        colspan="4"
                      >
                        <strong>Affected Population</strong>
                      </td>
                      <td colspan="4" width="40%">
                        <b>Category</b>
                      </td>
                      <td colspan="2" width="10%">
                        <b>
                          <center>Male</center>
                        </b>
                      </td>
                      <td colspan="2" width="10%">
                        <b>
                          <center>Female</center>
                        </b>
                      </td>
                      <td colspan="2" width="10%">
                        <b>
                          <center>Total</center>
                        </b>
                      </td>
                    </tr>
                    <tr
                      v-if="item.sectors.nutrition.affected_population"
                      v-for="row in item.sectors.nutrition.affected_population"
                    >
                      <td colspan="4" width="40%">{{ row.name }}</td>
                      <td colspan="2" width="10%" class="right-align">
                        {{ numberWithCommas(row.affected_males) }}
                      </td>
                      <td colspan="2" width="10%" class="right-align">
                        {{ numberWithCommas(row.affected_females) }}
                      </td>
                      <td colspan="2" width="10%" class="right-align">
                        {{
                          numberWithCommas(
                            parseInt(row.affected_females) +
                              parseInt(row.affected_males)
                          )
                        }}
                      </td>
                    </tr>

                    <tr
                      v-if="
                        item.sectors.nutrition.impact_on_nutrition_programmes
                      "
                    >
                      <td
                        :rowspan="
                          1 +
                            item.sectors.nutrition
                              .impact_on_nutrition_programmes.length
                        "
                        colspan="4"
                        width="20%"
                      >
                        <strong>Impact on nutrition programmes</strong>
                      </td>
                      <td colspan="4">
                        <b>Category</b>
                      </td>
                      <td colspan="6">
                        <b>Status</b>
                      </td>
                    </tr>
                    <tr
                      v-if="
                        item.sectors.nutrition.impact_on_nutrition_programmes
                      "
                      v-for="row in item.sectors.nutrition
                        .impact_on_nutrition_programmes"
                    >
                      <td colspan="4">{{ row.name }}</td>
                      <td colspan="6">{{ row.status }}</td>
                    </tr>

                    <tr
                      v-if="
                        item.sectors.nutrition.impact_on_nutrition_programmes
                      "
                    >
                      <td colspan="4" style="width:20%">
                        <strong>Response Needed</strong>
                      </td>
                      <td colspan="10" style="width:70%">
                        <u>
                          <strong>URGENT</strong>
                        </u>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          {{
                            item.sectors.nutrition
                              ? item.sectors.nutrition.urgent_response_needed
                                ? item.sectors.nutrition.urgent_response_needed
                                : "N/A"
                              : "N/A"
                          }}
                        </li>
                        <hr style="margin:1%;" />
                        <u>
                          <strong>GENERAL</strong>
                        </u>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          {{
                            item.sectors.nutrition
                              ? item.sectors.nutrition.response_needed
                                ? item.sectors.nutrition.response_needed
                                : "N/A"
                              : "N/A"
                          }}
                        </li>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
              <!-- end Nutrition -->
              <!--  Protection -->
              <tr
                v-if="item.sectors.protection"
                style="margin:0;padding:0 !important;border:0px !important;"
              >
                <td
                  colspan="15"
                  style="margin:0;padding:0 !important;border:0px"
                >
                  <table
                    width="100%"
                    style="margin:0;padding:0 !important;border:0px !important;"
                  >
                    <tr
                      v-if="
                        item.sectors.protection.impact_on_vulnerable_persons
                      "
                    >
                      <td
                        :rowspan="
                          2 +
                            item.sectors.protection.impact_on_vulnerable_persons
                              .length
                        "
                        width="10%"
                        class="clearfix"
                      >
                        <span class="rotated" style="font-weight: bold;"
                          ></span
                        >
                        <img
                          src="../../../../static/cluster_protection_100px_bluebox.png"
                          height="150"
                        />
                      </td>
                      <td
                        :rowspan="
                          1 +
                            item.sectors.protection.impact_on_vulnerable_persons
                              .length
                        "
                        width="20%"
                      >
                        <strong>Impact on vulnerable population</strong>
                      </td>
                      <td colspan="7" width="40%">
                        <b>Group affected</b>
                      </td>
                      <td colspan="2" width="10%">
                        <b>
                          <center>Male</center>
                        </b>
                      </td>
                      <td colspan="2" width="10%">
                        <b>
                          <center>Female</center>
                        </b>
                      </td>
                      <td colspan="2" width="10%">
                        <b>
                          <center>Total</center>
                        </b>
                      </td>
                    </tr>
                    <tr
                      v-if="
                        item.sectors.protection.impact_on_vulnerable_persons
                      "
                      v-for="row in item.sectors.protection
                        .impact_on_vulnerable_persons"
                    >
                      <td colspan="7" width="40%">{{ row.name }}</td>
                      <td colspan="2" width="10%" class="right-align">
                        {{ numberWithCommas(row.impacted_males) }}
                      </td>
                      <td colspan="2" width="10%" class="right-align">
                        {{ numberWithCommas(row.impacted_females) }}
                      </td>
                      <td colspan="2" width="10%" class="right-align">
                        {{
                          numberWithCommas(
                            (row.impacted_females
                              ? parseInt(row.impacted_females)
                              : 0) +
                              (row.impacted_males
                                ? parseInt(row.impacted_males)
                                : 0)
                          )
                        }}
                      </td>
                    </tr>
                    <tr
                      v-if="
                        item.sectors.protection.impact_on_vulnerable_persons
                      "
                    >
                      <td style="width:20%">
                        <strong>Response Needed</strong>
                      </td>
                      <td colspan="13" style="width:70%">
                        <u>
                          <strong>URGENT</strong>
                        </u>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          {{
                            item.sectors.protection
                              ? item.sectors.protection.urgent_response_needed
                                ? item.sectors.protection.urgent_response_needed
                                : "N/A"
                              : "N/A"
                          }}
                        </li>
                        <hr style="margin:1%;" />
                        <u>
                          <strong>GENERAL</strong>
                        </u>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          {{
                            item.sectors.protection
                              ? item.sectors.protection.response_needed
                                ? item.sectors.protection.response_needed
                                : "N/A"
                              : "N/A"
                          }}
                        </li>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
              <!-- end Protection -->
              <!--  Wash -->
              <tr
                v-if="item.sectors.wash"
                style="margin:0;padding:0 !important;border:0px !important;"
              >
                <td
                  colspan="15"
                  style="margin:0;padding:0 !important;border:0px"
                >
                  <table
                    width="100%"
                    style="margin:0;padding:0 !important;border:0px !important;"
                  >
                    <tr v-if="item.sectors.wash">
                      <td
                        :rowspan="
                          6 +
                            (item.sectors.wash.risk_of_water_contamination
                              ? item.sectors.wash.risk_of_water_contamination
                                  .length + 2
                              : 2)
                        "
                        width="10%"
                        class="clearfix"
                      >
                        <span class="rotated" style="font-weight: bold;"
                          ></span
                        >
                        <img
                          src="../../../../static/cluster_WASH_100px_bluebox.png"
                          height="150"
                        />
                      </td>
                      <td
                        :rowspan="
                          item.sectors.wash.risk_of_water_contamination
                            ? item.sectors.wash.risk_of_water_contamination
                                .length + 1
                            : 1
                        "
                        colspan="5" 
                        style="width:30%"
                      >
                        <b>Risk of contamination</b>
                      </td>
                      <td colspan="6">
                        <b>Source</b>
                      </td>
                      <td colspan="3">
                        <b>Risk</b>
                      </td>
                    </tr>
                    <tr
                      v-if="item.sectors.wash.risk_of_water_contamination"
                      v-for="row in item.sectors.wash
                        .risk_of_water_contamination"
                    >
                      <td colspan="5">{{ row.source }}</td>
                      <td colspan="5">{{ row.risk }}</td>
                    </tr>

                    <tr
                      v-if="
                        item.sectors.wash.accessToToilets
                      "
                    >
                      <td style="width:30%" rowspan="2" colspan="5">
                        <b>Households without access to toilets</b>
                      </td>
                      <td colspan="3">
                        <b>
                          <center>Male</center>
                        </b>
                      </td>
                      <td colspan="3">
                        <b>
                          <center>Female</center>
                        </b>
                      </td>
                      <td colspan="3">
                        <b>
                          <center>Total</center>
                        </b>
                      </td>
                    </tr>
                    <tr
                    v-if="
                     item.sectors.wash.accessToToilets
                    "
                  >
                    <td colspan="3" class="right-align">
                      {{
                        numberWithCommas(
                          item.sectors.wash.access_to_toilets_mhh
                        )
                      }}
                    </td>
                    <td colspan="3" class="right-align">
                      {{
                        numberWithCommas(
                          item.sectors.wash.access_to_toilets_fhh
                        )
                      }}
                    </td>
                    <td colspan="3" class="right-align">
                      {{
                        numberWithCommas(
                          item.sectors.wash.access_to_toilets_mhh ||
                            item.sectors.wash.access_to_toilets_fhh
                            ? parseInt(
                                item.sectors.wash.access_to_toilets_mhh
                                 
                              ) +
                                parseInt(
                                  item.sectors.wash.access_to_toilets_fhh
                                    
                                )
                            : 0
                        )
                      }}
                    </td>
                  </tr>
                    <!---start---------------------->
                    <tr
                      v-if="
                       item.sectors.wash.accessToToilets
                      "
                    >
                      <td style="width:20%" rowspan="2" colspan="5">
                        <b>Households without access to safe drinking water</b>
                      </td>
                      <td colspan="3">
                        <b>
                          <center>Male</center>
                        </b>
                      </td>
                      <td colspan="3">
                        <b>
                          <center>Female</center>
                        </b>
                      </td>
                      <td colspan="3">
                        <b>
                          <center>Total</center>
                        </b>
                      </td>
                    </tr>
                    <tr
                      v-if="
                        item.sectors.wash.with_safe_water_mhh ||
                          item.sectors.wash.with_safe_water_fhh
                      "
                    >
                      <td colspan="3" class="right-align">
                        {{
                          numberWithCommas(
                            item.sectors.wash.with_safe_water_fhh
                          )
                        }}
                      </td>
                      <td colspan="3" class="right-align">
                        {{
                          numberWithCommas(
                            item.sectors.wash.with_safe_water_mhh
                          )
                        }}
                      </td>
                      <td colspan="3" class="right-align">
                        {{
                          numberWithCommas(
                            item.sectors.wash.with_safe_water_mhh ||
                              item.sectors.wash.with_safe_water_fhh
                              ? parseInt(
                                  item.sectors.wash.with_safe_water_mhh
                                   
                                 
                                ) +
                                  parseInt(
                                    item.sectors.wash.with_safe_water_fhh
                                     
                                    
                                  )
                              : 0
                          )
                        }}
                      </td>
                    </tr>


                    <!----------------end----------->

                    <tr v-if="item.sectors.wash.risk_contamination_mhh">
                      <td style="width:20%" rowspan="2" colspan="5">
                        <b>Households with risk of contamination</b>
                      </td>
                      <td colspan="3">
                        <b>
                          <center>Male</center>
                        </b>
                      </td>
                      <td colspan="3">
                        <b>
                          <center>Female</center>
                        </b>
                      </td>
                      <td colspan="3">
                        <b>
                          <center>Total</center>
                        </b>
                      </td>
                    </tr>
                    <tr v-if="item.sectors.wash.risk_contamination_mhh">
                      <td colspan="3" class="right-align">
                        {{
                          numberWithCommas(
                            item.sectors.wash.risk_contamination_mhh
                          )
                        }}
                      </td>
                      <td colspan="3" class="right-align">
                        {{
                          numberWithCommas(
                            item.sectors.wash.risk_contamination_fhh
                          )
                        }}
                      </td>
                      <td colspan="3" class="right-align">
                        {{
                          numberWithCommas(
                            parseInt(item.sectors.wash.risk_contamination_mhh) +
                              parseInt(item.sectors.wash.risk_contamination_fhh)
                              ? parseInt(
                                  item.sectors.wash.risk_contamination_mhh
                                ) +
                                  parseInt(
                                    item.sectors.wash.risk_contamination_fhh
                                  )
                              : ""
                          )
                        }}
                      </td>
                    </tr>

                    <tr>
                      <td colspan="5" style="width:20%">
                        <strong>Response Needed</strong>
                      </td>
                      <td colspan="9">
                        <u>
                          <strong>URGENT</strong>
                        </u>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          {{
                            item.sectors.wash
                              ? item.sectors.wash.urgent_response_needed
                                ? item.sectors.wash.urgent_response_needed
                                : "N/A"
                              : "N/A"
                          }}
                        </li>
                        <hr style="margin:1%;" />
                        <u>
                          <strong>GENERAL</strong>
                        </u>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          {{
                            item.sectors.wash
                              ? item.sectors.wash.response_needed
                                ? item.sectors.wash.response_needed
                                : "N/A"
                              : "N/A"
                          }}
                        </li>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
              <!-- end Wash -->
              <!--  Logisticts -->
              <tr
                v-if="item.sectors.logistics"
                style="margin:0;padding:0 !important;border:0px !important;"
              >
                <td
                  colspan="15"
                  style="margin:0;padding:0 !important;border:0px"
                >
                  <table
                    width="100%"
                    style="margin:0;padding:0 !important;border:0px !important;"
                  >
                    <tr>
                      <td
                        :rowspan="
                          2 +
                            (item.sectors.logistics.impacted_structures
                              ? item.sectors.logistics.impacted_structures
                                  .length
                              : 0)
                        "
                        width="10%"
                        class="clearfix"
                      >
                        <span class="rotated" style="font-weight: bold;"
                          ></span
                        >
                        <img
                          src="../../../../static/cluster_logistics_100px_bluebox.png"
                          height="150"
                        />
                      </td>
                      <td
                        v-if="item.sectors.logistics.impacted_structures"
                        :rowspan="
                          1 + item.sectors.logistics.impacted_structures.length
                        "
                        colspan="4"
                        style="width:20%"
                      >
                        <strong>Impact on infrastures</strong>
                      </td>
                      <td
                        colspan="5"
                        v-if="item.sectors.logistics.impacted_structures"
                        style="width:35%"
                      >
                        <b>Category</b>
                      </td>
                      <td
                        v-if="item.sectors.logistics.impacted_structures"
                        colspan="5"
                        style="width:35%"
                      >
                        <b>Status</b>
                      </td>
                    </tr>
                    <tr
                      v-if="item.sectors.logistics.impacted_structures"
                      v-for="row in item.sectors.logistics.impacted_structures"
                    >
                      <td colspan="5" style="width:35%">{{ row.name }}</td>
                      <td colspan="5" style="width:35%">{{ row.status }}</td>
                    </tr>
                    <tr>
                      <td colspan="4" style="width:20%">
                        <strong>Response Needed</strong>
                      </td>
                      <td colspan="10" style="width:70%">
                        <u>
                          <strong>URGENT</strong>
                        </u>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          {{
                            item.sectors.logistics
                              ? item.sectors.logistics.urgent_response_needed
                                ? item.sectors.logistics.urgent_response_needed
                                : "N/A"
                              : "N/A"
                          }}
                        </li>
                        <hr style="margin:1%;" />
                        <u>
                          <strong>GENERAL</strong>
                        </u>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          {{
                            item.sectors.logistics
                              ? item.sectors.logistics.response_needed
                                ? item.sectors.logistics.response_needed
                                : "N/A"
                              : "N/A"
                          }}
                        </li>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
              <!-- end Logisticts -->
              <!--  Environment -->
              <tr
                v-if="item.sectors.environment"
                style="margin:0;padding:0 !important;border:0px !important;"
              >
                <td
                  colspan="15"
                  style="margin:0;padding:0 !important;border:0px"
                >
                  <table
                    width="100%"
                    style="margin:0;padding:0 !important;border:0px !important;"
                  >
                    <tr>
                      <td
                        :rowspan="
                          3 +
                            (item.sectors.environment.impact_on_environment
                              ? item.sectors.environment.impact_on_environment
                                  .length
                              : 0)
                        "
                        width="10%"
                        class="clearfix"
                      >
                        <span class="rotated" style="font-weight: bold;"
                          ></span
                        >
                        <img
                          src="../../../../static/other_cluster_environment_100px_bluebox.png"
                          height="150"
                        />
                      </td>
                      <td
                        v-if="item.sectors.environment.impact_on_environment"
                        :rowspan="
                          1 +
                            item.sectors.environment.impact_on_environment
                              .length
                        "
                        colspan="3"
                        style="width:20%"
                      >
                        <strong>Impact on structures</strong>
                      </td>
                      <td
                        v-if="item.sectors.environment.impact_on_environment"
                        colspan="9"
                        style="width:60%"
                      >
                        <b>Question</b>
                      </td>
                      <td
                        v-if="item.sectors.environment.impact_on_environment"
                        colspan="2"
                        width="10%"
                      >
                        <b>Response</b>
                      </td>
                    </tr>
                    <tr
                      v-if="item.sectors.environment.impact_on_environment"
                      v-for="row in item.sectors.environment
                        .impact_on_environment"
                    >
                      <td colspan="9" style="width:60%">
                        {{ row.impact_question }}
                      </td>
                      <td colspan="2" width="10%">{{ row.response }}</td>
                    </tr>
                    <tr>
                      <td colspan="3" style="width:20%">
                        <strong>Response Needed</strong>
                      </td>
                      <td colspan="11">
                        <b style="padding-bottom:20px">
                          <u>
                            <strong>URGENT</strong>
                          </u>
                        </b>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          {{
                            item.sectors.environment
                              ? item.sectors.environment.urgent_response_needed
                                ? item.sectors.environment
                                    .urgent_response_needed
                                : "N/A"
                              : "N/A"
                          }}
                        </li>
                        <hr style="margin:1%;" />
                        <b style="padding-bottom:20px">
                          <u>
                            <strong>GENERAL</strong>
                          </u>
                        </b>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          {{
                            item.sectors.environment
                              ? item.sectors.environment.response_needed
                                ? item.sectors.environment.response_needed
                                : "N/A"
                              : "N/A"
                          }}
                        </li>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
              <!-- end Environment -->
            </table>

            <table
              class="col-md-11"
              v-if="numberOfTAs > 0"
              width="100%"
              style="text-weight:bold;text-size:120%"
            >
              <tr>
                <td style="width:500px">
                  <strong>Data has been entered by</strong>
                </td>
                <td>
                  <strong
                    >I,
                    <strong
                      >{{ dinrFormsData.district.admin2_name_en }} DC,
                    </strong>
                    I am confirming that the filled data is correct and the best
                    reflection of the disaster type
                    {{ dinrFormsData.disaster }} which occurred</strong
                  >
                </td>
              </tr>
              <tr>
                <td>
                  <strong>Name :</strong>
                  {{
                    dinrFormsData.account.firstName +
                      " " +
                      dinrFormsData.account.lastName
                  }}
                </td>
                <td>
                  <strong>Name :</strong>
                  {{ signature.signatory_fname }}
                  {{ signature.signatory_lname }}
                </td>
              </tr>
              <tr>
                <td>
                  <strong>Signature :</strong>
                  {{
                    dinrFormsData.account.firstName[0] +
                      "." +
                      dinrFormsData.account.lastName[0] +
                      "."
                  }}
                </td>
                <td>
                  <div class="row">
                    <div>
                      <strong class="mx-3">Signature :</strong>
                      <span v-if="this.signature">
                        <base-button type="success" @click="handleApprove()"
                          >Approve</base-button
                        >
                        <base-button type="danger" @click="handleReject()"
                          >Reject</base-button
                        ></span
                      >
                      <span class="text-danger" v-else>
                        Create signature to be able to approve this report</span
                      >
                    </div>
                  </div>
                </td>
              </tr>

              <tr>
                <td>
                  <strong>Date :</strong>
                  {{ formatdate(dinrFormsData.createdon) }}
                </td>
                <td>
                  <strong>Date :</strong>
                  {{ formatdate(new Date()) }}
                </td>
              </tr>
            </table>
            <table  class="col-md-11" v-if="hasPictures"
              width="100%"
              style="text-weight:bold;text-size:120%"
              >
           <tr>
             <td style="text-align:center">
              <strong>Disaster Photos</strong>
             </td>
           </tr>
           <tr>
             <td colspan="2">
                <!-- <div class="card" >
                  <div  class="q-pt-none " v-for="(image, i) in dinrFormsData.disasterImages" :key="i">
                    <img :src="disasterImage(dinrFormsData._id, image.filename)"
                       height="170"
                       width="240"
                       padding-bottom="30"
                       class="img-responsive img-thumbnail"
                       />
                  </div>
                </div> -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="row" style="">
                            <div class="row">
                                <!--images  -->

                                    <div  class="image-wrapper col-md-4" v-for="(image, i) in dinrFormsData.disasterImages" :key="i">
                                    <div class="inner">
                                    <img :src="disasterImage(dinrFormsData._id, image.filename)"


                                      style="margin:8px; height:340px;width:340px;"
                                      class="img-responsive img-thumbnail"
                                      />
                                  </div>
                                    <div class="caption" style="margin-left:12px;">
                                         {{ image.caption }}
                                    </div>
                                </div>
                                <!-- endImage -->

                            </div>
                        </div>
                    </div>
                </div>
             </td>
           </tr>
           </table>
            <br />
            <br />
            <br />
            <p v-if="numberOfTAs <= 0">
              <center>
                <h3>
                  <strong>NO DATA</strong>
                </h3>
              </center>
            </p>
          </div>
        </div>
      </card>
    </div>
  </div>
</template>
<script>
import { MongoReports } from "../api/MongoReports";
import formApproval from "../mixins/formApproval";
import { dinrforms } from "../api/dinrforms";
var util = require("util");

import { Relay } from "../api/relay";
import { auth } from "../../../api/auth";
import swal from "sweetalert2";
import Swal from "sweetalert2";
import { signatures } from "../api/accounts/signatures";
import downloadexcel from "vue-json-excel";
import moment from "moment";
import axios from "axios";
import circularJson from "circular-json";
import { mapGetters, mapActions } from "vuex";
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

export default {
  mixins: [formApproval],
  components: {
    downloadexcel
  },

  data() {
    return {
      signature: "",
      numberOfTAs: "",
      dinrFormsData: {},
      villagesArray: [],
      appuri: "http://***********/#/login",
      campsArray: [],
      bufferDraFormsData: [],
      TAarray: [],
      draFormsData: [],
      download: [],
      downloadData: [],
      otherData: [],
      vhhousesvalue: "",
      filters: {}
    };
  },

  computeLogo() {
    return "../../../../static/logo.png";
  },
  mounted() {
    //Get DINR Form

    MongoReports.getOneUnApprovedDinr(
      this.$route.params.uuid /* , this.$session.get("jwt") */
    ).then(response => {
      this.dinrFormsData = response.data;
      

      //console.log(response.data);
      signatures.get(this.$session.get("jwt")).then(response => {
        //console.log(this.$session.get("user"))
        this.signature = response.data
          .filter(item => {
            return (
              this.$session.get("user").admin2_name_en == item.district &&
              item.signatory_email == this.$session.get("userObj").email
            );
          })
          .filter(item => item.status == "Active")[0];
      });

      //console.log(this.$route);

      //initialise number TAs in DNIR
      this.numberOfTAs = 0;

      let count = 0;
      //Get DRA Form

      let dradata = JSON.parse(JSON.stringify(this.dinrFormsData.dra));
      this.bufferDraFormsData = dradata;
     
      this.draFormsData = [];
      //console.log(bufferDraFormsData);
      this.downloadData.dinrform = {};

      this.bufferDraFormsData.forEach(item => {
        this.processExcel(item);

        this.TAarray.push(item.admin3.admin3Name_en.trim());
        for (let i in item.villages) {
          this.villagesArray.push(item.villages[i].name);
        }
        for (let i in item.camps) {
          this.campsArray.push(item.camps[i].name);
        }
      });

      this.TAarray = this.TAarray.sort();

      this.villagesArray = this.villagesArray
        .join()
        .replace(/ /g, "")
        .split(",")
        .sort()
        .filter(function(item, pos, self) {
          return self.indexOf(item) == pos;
        });
      this.campsArray = this.campsArray
        .join()
        .replace(/ /g, "")
        .split(",")
        .sort()
        .filter(function(item, pos, self) {
          return self.indexOf(item) == pos;
        });
    });
  },

  computed: {
    ...mapGetters({
      getBell: "dims/getNewBell"
    }),
    hasPictures: function() {
      if ("disasterImages" in this.dinrFormsData) {
        return this.dinrFormsData.disasterImages.length > 0;
      }
      return false;
    }
  },

  methods: {
    ...mapActions("dims", {
      addBell: "addBell"
    }),

    disasterImage(disasterId, filename) {
      const resource = process.env.VUE_APP_ENGINE_URL + "/forms/dinr";

      return `${resource}/${disasterId}/images/${filename}`;
    },

    reviewSummary() {
      this.$router.push({
        path: "/districtcommissioner/summaryreport/" + this.$route.params.uuid
      });
    },
    updateBell() {
      MongoReports.getUnapprovedDinrs().then(response => {
      
        let data = response.data
          .filter(data =>
            data.district.admin2_name_en.includes(
              this.$session.get("user").admin2_name_en
            )
          )
          .filter(
            item =>
              (!item.isApproved || item.isApproved == false) &&
              (!item.isRejected || item.isRejected == false) &&
              item.approvalMetadata &&
              item.approvalMetadata.signature &&
              item.approvalMetadata.signature.length > 0
          );

        data.sort(function(a, b) {
          return new Date(b.createdon) - new Date(a.createdon);
        });

        let bell = [];

        for (let i = 0; i < data.length; i++) {
          let row = data[i]._id;

          data[i].notification.forEach(el => {
            if (el.show == 1) {
              data[i].notStat = el.status;
              data[i].variant = el.variant;
              if (
                el.show == 1 &&
                el.status == "Waiting approval" &&
                el.variant == "success"
              ) {
                bell.push({
                  id: row,
                  disaster: el.disaster
                });
               
              }
            }
          });
        }
        this.addBell(bell);
      });
    },
    async handleRequest(data) {
      dinrforms.updateUnapproved(data).then(
        response => {
          swal.fire({
            title: "Done Succesfully",
            text: "Your decision has been saved on this disaster",
            type: "success",
            animation: false
          });
          this.$router.push({
            path: "/districtcommissioner/unapprovedreports"
          });
          this.updateBell();
        },
        reason => {
          swal.fire({
            title: "Failed to submit form",
            text: "possible invalid data (" + reason + ")",
            type: "error",
            animation: false
          });
        }
      );
    },
    async handleApprove() {
      var { value: password } = await swal({
        title: "Confirm Approval by entering your password",
        text: `I, ${this.$session.get("userObj").firstName} ${
          this.$session.get("userObj").lastName
        } (${this.$session.get("userObj").email}) - ${
          this.$session.get("user").admin2_name_en
        } DC, I am confirming that the filled data is correct and the best reflection of the disaster which occurred.`,
        input: "password",
        type: "warning",
        buttonsStyling: false,
        confirmButtonClass: "btn btn-success btn-fill",
        cancelButtonClass: "btn btn-danger btn-fill",
        confirmButtonText: "Yes, approve this!",
        showCancelButton: true,
        inputPlaceholder: "password",
        inputAttributes: {
          autocapitalize: "off",
          autocorrect: "off",
          required: true
        }
      }).then(result => {
        
        if (result.value != null) {
          auth
            .login({
              email: this.$session.get("userObj").email,
              password: result.value
            })
            .then(
              response => {
                let signature = this.dinrFormsData.approvalMetadata.signature;
                signature.push(this.signature);
                let self = this;
                var notification = [...this.dinrFormsData.notification];
                var date = new Date();

                notification[2].status = "Approved";
                notification[2].disaster = this.dinrFormsData.disaster;
                notification[2].dateSub =
                  date.getDate() +
                  "/" +
                  (date.getMonth() + 1) +
                  "/ " +
                  date.getFullYear();
                notification[2].show = 1;
                this.dinrFormsData.notification = notification;
                Relay.post(this.dinrFormsData).then(res => {
                  //console.log(res);
                  if (res && res.isSent) {
                    swal.fire({
                      title: "Form Approved successfully",
                      text:
                        "This disaster report has been approved and sent successfully",
                      type: "success",
                      animation: false
                    });
                    this.signature.createdon = new Date();
                    signature.push(this.signature);
                   
                    this.handleRequest({
                      notification: notification,
                      isApproved: true,
                      _id: this.$route.params.uuid,
                      approvalMetadata: { signature },
                      status: "2"
                    });
                    //this.processRequest(self.dinrFormsData._id);
                  } else {
                    Swal.fire({
                      title: "Failed to submit form",
                      text: "possible invalid data",
                      type: "error",
                      animation: false
                    });
                  }
                });
              },
              reason => {
                swal({
                  text:
                    "Failed submit for possible invalid password (  password : " +
                    reason +
                    " )",
                  type: "error",
                  toast: true,
                  position: "top-end",
                  confirmButtonClass: "btn btn-success btn-fill",
                  buttonsStyling: false
                });
              }
            );
        }
      });
    },
    async handleReject() {
      let self = this;
      var { value: password } = await swal({
        title: "Confirm Rejection",
        text: `I, ${this.$session.get("userObj").firstName} ${
          this.$session.get("userObj").lastName
        } (${this.$session.get("userObj").email}) - ${
          this.$session.get("user").admin2_name_en
        } DC, I am rejecting this disaster.`,
        html:
          '<textarea class="swal2-textarea" required id="comment" rows="4" placeholder="Why are you rejecting this?"></textarea><input id="password" required type="password" class="swal2-input" placeholder="password">',
        type: "warning",
        buttonsStyling: false,
        confirmButtonClass: "btn btn-success btn-fill",
        cancelButtonClass: "btn btn-danger btn-fill",
        confirmButtonText: "Yes, reject this!",
        showCancelButton: true,
        inputAttributes: {
          autocapitalize: "off",
          autocorrect: "off",
          required: true
        }
      }).then(result => {
        let password = document.getElementById("password").value;
        let comment = document.getElementById("comment").value;
       
        if (password != "" && comment != "") {
          auth
            .login({
              email: this.$session.get("userObj").email,
              password
            })
            .then(
              response => {
                var notification = [...this.dinrFormsData.notification];
                var date = new Date();
                notification[2].status = "Rejected";
                notification[2].disaster = this.dinrFormsData.disaster;
                notification[2].dateSub =
                  date.getDate() +
                  "/" +
                  (date.getMonth() + 1) +
                  "/ " +
                  date.getFullYear();
                notification[2].show = 1;
                Relay.sendMail({
                  from: "district commissioner",
                  to: "district manager",
                  district: this.dinrFormsData.district.admin2_name_en,
                  disaster: this.dinrFormsData.disaster,
                  dodFrom: this.dinrFormsData.dodFrom,
                  comment: comment,
                  status: "rejected"
                })
                  .then(response => console.log("Email has been sent"))
                  .catch(r => console.log("There is an error", r));
                Relay.sendMail({
                  from: "district commissioner",
                  to: "officer",
                  district: this.dinrFormsData.district.admin2_name_en,
                  disaster: this.dinrFormsData.disaster,
                  dodFrom: this.dinrFormsData.dodFrom,
                  comment: comment,
                  status: "rejected"
                })
                  .then(response => console.log("Email has been sent"))
                  .catch(r => console.log("There is an error", r));
                this.handleRequest(
                  {
                    notification: notification,
                    isRejected: true,
                    _id: this.$route.params.uuid,
                    approvalMetadata: { comment }
                  },
                  false
                );
              },
              reason => {
                swal({
                  text:
                    "Failed submit for possible invalid password (  password : " +
                    reason +
                    " )",
                  type: "error",
                  toast: true,
                  position: "top",
                  confirmButtonClass: "btn btn-success btn-fill",
                  buttonsStyling: false
                });
              }
            );
        } else {
          swal({
            text: "Fill in all fields",
            type: "error",
            toast: true,
            position: "top",
            confirmButtonClass: "btn btn-success btn-fill",
            buttonsStyling: false
          });
        }
      });
    },
    handleInfographics() {
     
      this.$router.push({
        path: "/districtcommissioner/infographics/" + this.$route.params.uuid
      });
    },
    numberWithCommas(number) {
      try {
        var parts = number.toString().split(".");
        parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        return parts.join(".");
      } catch (e) {}
    },
    comparator(key, order = "asc") {
      return function innerSort(a, b) {
        if (!a.hasOwnProperty(key) || !b.hasOwnProperty(key)) {
          return 0;
        }

        const varA = typeof a[key] === "string" ? a[key].toUpperCase() : a[key];
        const varB = typeof b[key] === "string" ? b[key].toUpperCase() : b[key];

        let comparison = 0;
        if (varA > varB) {
          comparison = 1;
        } else if (varA < varB) {
          comparison = -1;
        }
        return order === "desc" ? comparison * -1 : comparison;
      };
    },

    sortArrayByKey(arrayName) {
      return arrayName.slice().sort(this.comparator("name", "asc"));
    },
    downloadPDF(){
                var quotes = document.getElementById('section-to-print');
                html2canvas(quotes).then((canvas) => {
                    
                          var imgData = canvas.toDataURL('image/png');

                        var imgWidth = 210; 
                        var pageHeight = 295;  
                        var imgHeight = canvas.height * imgWidth / canvas.width;
                        var heightLeft = imgHeight;

                        var doc = new jsPDF('p', 'mm', 'A4');
                        var position = 0;

                        doc.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                        heightLeft -= pageHeight;

                        while (heightLeft >= 0) {
                            position = heightLeft - imgHeight;
                            doc.addPage();
                            doc.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                            heightLeft -= pageHeight;
                        }
                        
                        const disasterdistrict = document.getElementById('district').innerHTML;
                        const disastertype = document.getElementById('disastertype').innerHTML;
                        const disasterdate = document.getElementById('disasterstart').innerHTML;

                        const filename = disasterdistrict + disastertype + disasterdate;

                        doc.save( filename );
            });
    },
    printdiv(printpage) {

                var headstr = "<html><head><title></title></head><body>";
                var footstr = "</body>";
                var newstr = document.all.item(printpage).innerHTML;
                var oldstr = document.body.innerHTML;
                var disasterdistrict = document.getElementById('district').innerHTML;
                var disastertype = document.getElementById('disastertype').innerHTML;
                var disasterdate = document.getElementById('disasterstart').innerHTML;

                document.body.innerHTML = headstr + newstr + footstr;
                document.title = disasterdate + "_" + disasterdistrict + "_"+ disastertype;
                window.print();
                document.body.innerHTML = oldstr;
                location.reload();
                return false;
    },
    dateFormate(dateData) {
      const cur_date = dateData;
      const formattedDate = moment(cur_date).format("DD/MM/YYYY");

      return formattedDate;
    },
    formatdate(data) {
      const cur_date = data;
      const formattedDate = moment(cur_date).format("DD/MM/YYYY hh:mm");

      return formattedDate;
    },

    formatedDate(data) {
      const cur_date = data;
      const formDate = moment(cur_date).format("YYYY/MM/DD");

      return formDate;
    },

    sumArrayValues(item, sector, array, key) {
      if (typeof item["sectors"][sector][array] !== "undefined") {
        return item["sectors"][sector][array]
          .map(function(item) {
            return parseInt(item[key]) ? parseInt(item[key]) : 0;
          })
          .reduce((sum, value) => sum + value, 0);
      } else if (typeof item[sector][array] === "undefined") {
        return 0;
      }
    },

    returnFieldvalue(item, sector, array, key) {
      if (typeof item["sectors"][sector][array] !== "undefined") {
        return item["sectors"][sector][array][0][key];
      } else if (typeof item["sectors"][sector][array] === "undefined") {
        return "NULL";
      }
    },

    returnFieldItemvalue(item, sector, array, key) {
      if (typeof item["sectors"][sector][array] !== "undefined") {
        for (let i in item["sectors"][sector][array]) {
          if (item["sectors"][sector][array][i].name === key) {
            return item["sectors"][sector][array][i].status;
          } else if (
            typeof item["sectors"][sector][array][i].key === "undefined"
          ) {
            return "NULL";
          }
        }
      }
    },

    sumArrayValuesNoAggregate(item, sector, array, key, filterBy, filterValue) {
      if (typeof item["sectors"][sector][array] !== "undefined") {
        return item["sectors"][sector][array]
          .filter(item => item[filterBy] === filterValue)
          .map(function(item) {
            return parseInt(item[key]) ? parseInt(item[key]) : 0;
          })
          .reduce((sum, value) => sum + value, 0);
      } else if (typeof item[sector][array] === "undefined") {
        return 0;
      }
    },

    processExcel(item) {
      this.downloadData.all = item;

      this.draFormsData.push(item);

      for (let i = 0; i < this.draFormsData.length; i++) {
        for (let a = 0; a < this.draFormsData[i].villages.length; a++) {
          this.villagesArray.push(this.draFormsData[i].villages[a].name);
        }

        for (let a = 0; a < this.draFormsData[i].camps.length; a++) {
          this.campsArray.push(this.draFormsData[i].camps[a].name);
        }
      }

      this.numberOfTAs++;

      let Gvharray = [];

      this.downloadData.dinrform = Object.assign({}, this.dinrFormsData);

      for (let i = 0; i < item.gvhs.length; i++) {
        let GVHname = item.gvhs[i].name;

        Gvharray.push(GVHname);
      }

      this.downloadData.all.dinrform = Object.assign(
        {},
        this.downloadData.dinrform
      );

      try {
        this.downloadData.all.dinrform.doaAcpc = this.formatedDate(
          this.downloadData.dinrform.doaAcpc
        );
      } catch (error) {
        this.downloadData.all.dinrform.doaAcpc = "NULL";
      }

      try {
        this.downloadData.all.dinrform.doaDcpc = this.formatedDate(
          this.downloadData.dinrform.doaDcpc
        );
      } catch (error) {
        this.downloadData.all.dinrform.doaDcpc = "NULL";
      }

      try {
        this.downloadData.all.dinrform.dodFrom = this.formatedDate(
          this.downloadData.dinrform.dodFrom
        );
      } catch (error) {
        this.downloadData.all.dinrform.dodFrom = "NULL";
      }

      try {
        this.downloadData.all.dinrform.dodTo = this.formatedDate(
          this.downloadData.dinrform.dodTo
        );
      } catch (error) {
        this.downloadData.all.dinrform.dodTo = "NULL";
      }

      try {
        this.downloadData.all.gvhsAffected = Gvharray.join();
      } catch (error) {
        this.downloadData.all.gvhsAffected = "NULL";
      }

      try {
        this.downloadData.all.is_food_available_food = this.returnFieldvalue(
          item,
          "food",
          "food_availability",
          "foodavailable"
        );
      } catch (error) {
        this.downloadData.all.is_food_available_food = "NULL";
      }

      try {
        this.downloadData.all.medical_supply_availability = this.returnFieldItemvalue(
          item,
          "health",
          "available_health_medical",
          "Medical supply"
        );
      } catch (error) {
        this.downloadData.all.medical_supply_availability = "NULL";
      }

      try {
        this.downloadData.all.health_personel_availability = this.returnFieldItemvalue(
          item,
          "health",
          "available_health_medical",
          "Health personel"
        );
      } catch (error) {
        this.downloadData.all.health_personel_availability = "NULL";
      }

      try {
        this.downloadData.all.road_access =
          item.sectors.logistics.access_of_structures[0].accessibility;
      } catch (error) {
        this.downloadData.all.road_access = "NULL";
      }

      try {
        this.downloadData.all.food_1_2_months = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "female_HH",
          "category",
          "No. of HH with 1-2 month Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_1_2_months = 0;
      }

      try {
        this.downloadData.all.food_2_months = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "female_HH",
          "category",
          "No. of HH with more than 2 months Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_2_months = 0;
      }

      try {
        this.downloadData.all.food_stock_lost = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "female_HH",
          "category",
          "No. of HH who lost Food Stock"
        );
      } catch (error) {
        this.downloadData.all.food_stock_lost = 0;
      }

      try {
        this.downloadData.all.food_less_1_month = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "female_HH",
          "category",
          "No. of HH with less than 1 month Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_less_1_month = 0;
      }

      try {
        this.downloadData.all.food_less_1_month_male = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "male_HH",
          "category",
          "No. of HH with less than 1 month Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_less_1_month_male = 0;
      }

      try {
        this.downloadData.all.food_1_2_months_male = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "male_HH",
          "category",
          "No. of HH with 1-2 month Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_1_2_months_male = 0;
      }

      try {
        this.downloadData.all.food_2_months_male = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "male_HH",
          "category",
          "No. of HH with more than 2 months Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_2_months_male = 0;
      }

      try {
        this.downloadData.all.food_stock_lost_male = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "male_HH",
          "category",
          "No. of HH who lost Food Stock"
        );
      } catch (error) {
        this.downloadData.all.food_stock_lost_male = 0;
      }

      try {
        this.downloadData.all.food_item_damage = this.sumArrayValues(
          item,
          "agriculture",
          "food_item_damage",
          "number_of_kilos"
        );
      } catch (error) {
        this.downloadData.all.food_item_damage = 0;
      }
      try {
        this.downloadData.all.PeopleAffectedrows_female = this.sumArrayValues(
          item,
          "displaced",
          "PeopleAffectedrows",
          "number_displaced_by_gender_fhh"
        );
      } catch (error) {
        this.downloadData.all.PeopleAffectedrows_female = 0;
      }

      try {
        this.downloadData.all.PeopleAffectedrows_male = this.sumArrayValues(
          item,
          "displaced",
          "PeopleAffectedrows",
          "number_displaced_by_gender_mhh"
        );
      } catch (error) {
        this.downloadData.all.PeopleAffectedrows_male = 0;
      }

      try {
        this.downloadData.all.hectares_submerged = this.sumArrayValues(
          item,
          "agriculture",
          "crops_damaged",
          "hectares_submerged"
        );
      } catch (error) {
        this.downloadData.all.hectares_submerged = 0;
      }

      try {
        this.downloadData.all.hectares_washed_away = this.sumArrayValues(
          item,
          "agriculture",
          "crops_damaged",
          "hectares_washed_away"
        );
      } catch (error) {
        this.downloadData.all.hectares_washed_away = 0;
      }

      try {
        this.downloadData.all.impact_on_crops_hh_affected = this.sumArrayValues(
          item,
          "agriculture",
          "impact_on_crops",
          "hh_affected"
        );
      } catch (error) {
        this.downloadData.all.impact_on_crops_hh_affected = 0;
      }

      try {
        this.downloadData.all.impact_on_crops_hectares_damaged = this.sumArrayValues(
          item,
          "agriculture",
          "impact_on_crops",
          "hectares_damaged"
        );
      } catch (error) {
        this.downloadData.all.impact_on_crops_hectares_damaged = 0;
      }

      try {
        this.downloadData.all.impact_on_livestock_hh = this.sumArrayValues(
          item,
          "agriculture",
          "impact_on_livestock",
          "hh_affected_l"
        );
      } catch (error) {
        this.downloadData.all.impact_on_livestock_hh = 0;
      }

      try {
        this.downloadData.all.impact_on_livestock_la = this.sumArrayValues(
          item,
          "agriculture",
          "impact_on_livestock",
          "livestock_affected"
        );
      } catch (error) {
        this.downloadData.all.impact_on_livestock_la = 0;
      }

      try {
        this.downloadData.all.displaced_households_accommodated_male = this.sumArrayValues(
          item,
          "displaced",
          "displaced_households_accommodated",
          "accomodated_males_hh"
        );
      } catch (error) {
        this.downloadData.all.displaced_households_accommodated_male = 0;
      }
      try {
        this.downloadData.all.displaced_households_accommodated_female = this.sumArrayValues(
          item,
          "displaced",
          "displaced_households_accommodated",
          "accomodated_females_hh"
        );
      } catch (error) {
        this.downloadData.all.displaced_households_accommodated_female = 0;
      }

      try {
        this.downloadData.all.displaced_disagregated_male = this.sumArrayValues(
          item,
          "displaced",
          "displaced_disaggregated",
          "displaced_males"
        );
      } catch (error) {
        this.downloadData.all.displaced_disagregated_male = 0;
      }

      try {
        this.downloadData.all.displaced_disagregated_female = this.sumArrayValues(
          item,
          "displaced",
          "displaced_disaggregated",
          "displaced_females"
        );
      } catch (error) {
        this.downloadData.all.displaced_disagregated_female = 0;
      }

      try {
        this.downloadData.all.displaced_individuals_accommodated_male = this.sumArrayValues(
          item,
          "displaced",
          "displaced_individuals_accommodated",
          "accomodated_males"
        );
      } catch (error) {
        this.downloadData.all.displaced_individuals_accommodated_male = 0;
      }

      try {
        this.downloadData.all.displaced_individuals_accommodated_female = this.sumArrayValues(
          item,
          "displaced",
          "displaced_individuals_accommodated",
          "accomodated_females"
        );
      } catch (error) {
        this.downloadData.all.displaced_individuals_accommodated_female = 0;
      }

      try {
        this.downloadData.all.education_building_functioning = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "functioning_buildings"
        );
      } catch (error) {
        this.downloadData.all.education_building_functioning = 0;
      }

      try {
        this.downloadData.all.education_building_partly_functioning = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "partially_functioning_buildings"
        );
      } catch (error) {
        this.downloadData.all.education_building_partly_functioning = 0;
      }

      try {
        this.downloadData.all.education_closed_buildings = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "closed_buildings"
        );
      } catch (error) {
        this.downloadData.all.education_closed_buildings = 0;
      }

      try {
        this.downloadData.all.education_females_out_of_school = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "females_out_of_school"
        );
      } catch (error) {
        this.downloadData.all.education_females_out_of_school = 0;
      }

      try {
        this.downloadData.all.education_males_out_of_school = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "males_out_of_school"
        );
      } catch (error) {
        this.downloadData.all.education_males_out_of_school = 0;
      }

      try {
        this.downloadData.all.education_underwater = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "underwater"
        );
      } catch (error) {
        this.downloadData.all.education_underwater = 0;
      }

      try {
        this.downloadData.all.education_completely_damaged = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "completely_damaged"
        );
      } catch (error) {
        this.downloadData.all.education_completely_damaged = 0;
      }

      try {
        this.downloadData.all.health_partially_functioning =
          this.sumArrayValues(
            item,
            "health",
            "available_health_facilities",
            "partially_functioning"
          ) +
          this.sumArrayValues(
            item,
            "health",
            "other_health_facilities",
            "partialy_functioning"
          );
      } catch (error) {
        this.downloadData.all.health_partially_functioning = 0;
      }

      try {
        this.downloadData.all.health_verge_of_closing =
          this.sumArrayValues(
            item,
            "health",
            "available_health_facilities",
            "verge_of_closing"
          ) +
          this.sumArrayValues(
            item,
            "health",
            "other_health_facilities",
            "verge_of_closing"
          );
      } catch (error) {
        this.downloadData.all.health_verge_of_closing = 0;
      }

      try {
        this.downloadData.all.health_closed =
          this.sumArrayValues(
            item,
            "health",
            "available_health_facilities",
            "closed"
          ) +
          this.sumArrayValues(
            item,
            "health",
            "other_health_facilities",
            "closed"
          );
      } catch (error) {
        this.downloadData.all.health_closed = 0;
      }

      try {
        this.downloadData.all.livelihoods_slightly_affected = this.sumArrayValues(
          item,
          "livelihoods",
          "livelihoods_affected",
          "severely_affected"
        );
      } catch (error) {
        this.downloadData.all.livelihoods_slightly_affected = 0;
      }
      try {
        this.downloadData.all.livelihoods_severely_affected = this.sumArrayValues(
          item,
          "livelihoods",
          "livelihoods_affected",
          "slightly_affected"
        );
      } catch (error) {
        this.downloadData.all.livelihoods_severely_affected = 0;
      }

      try {
        this.downloadData.all.impact_on_vulnerable_persons_males = this.sumArrayValues(
          item,
          "protection",
          "impact_on_vulnerable_persons",
          "impacted_males"
        );
      } catch (error) {
        this.downloadData.all.impact_on_vulnerable_persons_males = 0;
      }

      try {
        this.downloadData.all.impact_on_vulnerable_persons_females = this.sumArrayValues(
          item,
          "protection",
          "impact_on_vulnerable_persons",
          "impacted_females"
        );
      } catch (error) {
        this.downloadData.all.impact_on_vulnerable_persons_females = 0;
      }

      try {
        this.downloadData.all.nutrition_affected_pop_male = this.sumArrayValues(
          item,
          "nutrition",
          "affected_population",
          "affected_males"
        );
      } catch (error) {
        this.downloadData.all.nutrition_affected_pop_male = 0;
      }

      try {
        this.downloadData.all.nutrition_affected_pop_female = this.sumArrayValues(
          item,
          "nutrition",
          "affected_population",
          "affected_females"
        );
      } catch (error) {
        this.downloadData.all.nutrition_affected_pop_female = 0;
      }

      try {
        this.downloadData.all.shelter_without_shelter_male = this.sumArrayValues(
          item,
          "shelter",
          "people_without_shelter",
          "males"
        );
      } catch (error) {
        this.downloadData.all.shelter_without_shelter_male = 0;
      }

      try {
        this.downloadData.all.shelter_without_shelter_female = this.sumArrayValues(
          item,
          "shelter",
          "people_without_shelter",
          "females"
        );
      } catch (error) {
        this.downloadData.all.shelter_without_shelter_female = 0;
      }

      try {
        this.downloadData.all.shelter_people_dead_female = this.sumArrayValues(
          item,
          "shelter",
          "PeopleDeadrows",
          "females_dead"
        );
      } catch (error) {
        this.downloadData.all.shelter_people_dead_female = 0;
      }

      try {
        this.downloadData.all.shelter_people_dead_male = this.sumArrayValues(
          item,
          "shelter",
          "PeopleDeadrows",
          "males_dead"
        );
      } catch (error) {
        this.downloadData.all.shelter_people_dead_male = 0;
      }

      try {
        this.downloadData.all.shelter_people_injured_female = this.sumArrayValues(
          item,
          "shelter",
          "PeopleInjuredrows",
          "people_injured_females"
        );
      } catch (error) {
        this.downloadData.all.shelter_people_injured_female = 0;
      }

      try {
        this.downloadData.all.shelter_people_injured_male = this.sumArrayValues(
          item,
          "shelter",
          "PeopleInjuredrows",
          "people_injured_males"
        );
      } catch (error) {
        this.downloadData.all.shelter_people_injured_male = 0;
      }

      try {
        this.downloadData.all.shelter_fhh_affected = this.sumArrayValues(
          item,
          "shelter",
          "PeopleAffectedrows",
          "damaged_fhh"
        );
      } catch (error) {
        this.downloadData.all.shelter_fhh_affected = 0;
      }

      try {
        this.downloadData.all.shelter_mhh_affected = this.sumArrayValues(
          item,
          "shelter",
          "PeopleAffectedrows",
          "damaged_mhh"
        );
      } catch (error) {
        this.downloadData.all.shelter_mhh_affected = 0;
      }

      this.download.push({ all: this.downloadData.all });
    }
  }
};
</script>

<style lang="stylus" scoped>
.modal-body .tags-input__wrapper{
   display: flex;
   flex-direction: row ;
   flex-wrap:  wrap;
}
.el-tag.el-tag--primary{
  position: relative !important;
  display: inline;
}
.image-previewer, .row {
  display: flex;
  flex-wrap: wrap;
  padding: 10px;
  justify-items: baseline;
}

.image-previewer .img, .row .img {
  height: 300px;
  border-radius: 10px;
  cursor: pointer;
}
.image-previewer  .image--wrapper {
  position: relative;
   margin: 10px;
}
.row  .image--wrapper {
  position: relative;
  margin: 10px;
  
}
.image-previewer .row .image--wrapper {
  margin: 10px;
}
.image--wrapper  .img--overlay {
  position: absolute;
  top: 0; 
  bottom: 0; 
  right: 0; 
  left: 0; 
  z-index: 10;
  background: rgba(0, 0, 0, .3);
  border-radius: 10px;
}
.image--wrapper .img--overlay .btn {
  background: rgb(238, 5, 5);
  width: 100%;
  position: absolute;
  bottom: 0;
}
.progress, .progress-bar {
  height: 30px;
  font-weight: bold;
}
.inner{
  overflow: hidden;
}
.inner img{
  transition: all 1.5s ease;
}
.inner:hover img{
  transform: scale(2);
   display: flex;
  flex-wrap: wrap;
}
table {
  border-collapse: collapse;
  width: 100%;
  margin: 0 auto;
}

table, th, td {
  border: 1px solid black;
  margin-top: 1%;
}

td {
  padding: 1%;
}

.rotated {
  writing-mode: tb-rl;
  transform: rotate(-180deg);
  color: teal;
  padding-left: 30px;
}

.noborder {
  border: none;
}

.vertical {
  writing-mode: vertical-rl;
}

.qcont:first-letter {
  text-transform: capitalize;
}

.right-align {
  text-align: right;
}

@media print {
  .section-not-to-print {
    visibility: hidden;
  }
  #section-to-print {
    visibility: visible;
  }
}
</style>
