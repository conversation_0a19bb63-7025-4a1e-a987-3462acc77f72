<template>
  <div class="content">
    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Disaster</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">barTrends</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">All</li>
            </ol>
          </nav>
        </div>
        <div class="col-lg-6 col-5 text-right"></div>
      </div>
      <!-- Card stats -->
    </base-header>
    <div class="container-fluid mt--6">
      <div>
        <card
          class="no-border-card"
          body-classes="px-0 pb-1"
          footer-classes="pb-2"
        >
          <template slot="header">
            <h3 class="mb-0">DISASTER REPORTS STATUS</h3>
          </template>
          <div>
            <div style="text-align:center">
              <h2>{{ filter.split("-")[1].toUpperCase() }}</h2>
            </div>
            <div
              class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
            >
              <div class="row col-12 mt-4">
                <div class="col-6">
                  <span style="margin-right:10px;">Period :</span>
                  <span id="identifier-c"></span>
                </div>
                <div class="col-1"></div>
              </div>
            </div>
            <div style="width:100%;text-algin:center;" id="barTrends"></div>
            <div style="width:100%;text-algin:center;" id="lineTrends"></div>
            <div style="width:100%;text-algin:center;" id="heatTrends"></div>
            <div style="width:100%;text-algin:center;" id="TableTrends">
              <table id="trendsTable">
                <tr>
                  <th>Period</th>
                  <th>Unsigned</th>
                  <th>unApproved</th>
                  <th>Submitted</th>
                </tr>
                <tr v-if="filter === '1-last 24 Hours'">
                  <td>last 24 Hours</td>
                  <td>
                    {{ tableData.find(d => d.key === "1-Unsigned").value }}
                  </td>
                  <td>
                    {{ tableData.find(d => d.key === "2-UnApproved").value }}
                  </td>
                  <td>
                    {{ tableData.find(d => d.key === "3-Submitted").value }}
                  </td>
                </tr>
                <tr v-if="filter === '2-last 7 day'">
                  <td>last 7 days</td>
                  <td>
                    {{ tableData.find(d => d.key === "1-Unsigned").value }}
                  </td>
                  <td>
                    {{ tableData.find(d => d.key === "2-UnApproved").value }}
                  </td>
                  <td>
                    {{ tableData.find(d => d.key === "3-Submitted").value }}
                  </td>
                </tr>
                <tr v-if="filter === '3-last 30 day'">
                  <td>last 30 days</td>
                  <td>
                    {{ tableData.find(d => d.key === "1-Unsigned").value }}
                  </td>
                  <td>
                    {{ tableData.find(d => d.key === "2-UnApproved").value }}
                  </td>
                  <td>
                    {{ tableData.find(d => d.key === "3-Submitted").value }}
                  </td>
                </tr>
                <tr v-if="filter === '4-last 90 days'">
                  <td>last 90 days</td>
                  <td>
                    {{ tableData.find(d => d.key === "1-Unsigned").value }}
                  </td>
                  <td>
                    {{ tableData.find(d => d.key === "2-UnApproved").value }}
                  </td>
                  <td>
                    {{ tableData.find(d => d.key === "3-Submitted").value }}
                  </td>
                </tr>
                <tr v-if="filter === '5-last 12 Months'">
                  <td>last 12 Months</td>
                  <td>
                    {{ tableData.find(d => d.key === "1-Unsigned").value }}
                  </td>
                  <td>
                    {{ tableData.find(d => d.key === "2-UnApproved").value }}
                  </td>
                  <td>
                    {{ tableData.find(d => d.key === "3-Submitted").value }}
                  </td>
                </tr>
                <tr v-if="filter === '6-Lifetime'">
                  <td>Lifetime</td>
                  <td>
                    {{ tableData.find(d => d.key === "1-Unsigned").value }}
                  </td>
                  <td>
                    {{ tableData.find(d => d.key === "2-UnApproved").value }}
                  </td>
                  <td>
                    {{ tableData.find(d => d.key === "3-Submitted").value }}
                  </td>
                </tr>
              </table>
            </div>
          </div>
          <div
            slot="footer"
            class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
          ></div>
        </card>
      </div>
    </div>
  </div>
</template>
<script>
import { Table, TableColumn, Select, Option } from "element-ui";
import RouteBreadCrumb from "@/components/Breadcrumb/RouteBreadcrumb";
import * as d3 from "d3";
import * as dc from "dc";
import crossfilter from "crossfilter2";
var globalData = [];
var group = "trendCharts";
var dcGroupFilterHandler = {};
var typeDimesion = {};
var seriesTypeDateDimension = {};
var parent = {};
import { MongoReports } from "../../districtmanager/api/MongoReports";
var moment = require("moment");

export default {
  components: {
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn
  },
  data() {
    return {
      filter: "6-Lifetime",
      data: [],
      tableData: [
        {
          key: "1-Unsigned",
          value: 0
        },
        {
          key: "2-UnApproved",
          value: 0
        },
        {
          key: "3-Submitted",
          value: 0
        }
      ],
      Graphtitle: ""
    };
  },
  computed: {
    drawGraph1() {
      ///tooltip
    },
    getData() {
      return this.data;
    }
  },
  async mounted() {
    parent = this;
    var numberFormat = d3.format(",");
    var formatDate = d3.timeFormat("%b %Y");

    this.loadForms().then(response => {
      var ndx = crossfilter(this.data);
      //###################MENU############################################
      var dcGroupFilterHandler = ndx.dimension(function(d) {
        return d.group;
      });
      typeDimesion = ndx.dimension(function(d) {
        return d.type;
      });

      

      new dc.SelectMenu("#identifier-c", group)
        .dimension(dcGroupFilterHandler)
        .group(dcGroupFilterHandler.group())

        .promptText("SELECT")
        .on("postRender", function() {
          d3.select(".dc-select-menu").attr(
            "class",
            "btn btn-outline-secondary border p-2 mr-4 text-left"
          );
        })
        .on("renderlet", chart => {
          d3.selectAll(".dc-select-option").text(function(d) {
            var key = d.key;
            return key.split("-")[1];
          });
        })
        .on("filtered", function(chart, filter) {
          parent.filter = filter;
          dcGroupFilterHandler.filter(d => d === filter);
          var unsigned = typeDimesion
            .group()
            .reduceCount(d => 1)
            .all();
          parent.tableData = unsigned;

          dc.redrawAll(group);
        });
      parent.filter = dcGroupFilterHandler.group().all()[0].key;
      dcGroupFilterHandler.filter(
        d => d === dcGroupFilterHandler.group().all()[0].key
      );
      var unsigned = typeDimesion
        .group()
        .reduceCount(d => 1)
        .all();
      parent.tableData = unsigned;
      //#############################BAR############################################

      let typeGroup = typeDimesion.group().reduceCount(item => 1);
      var colors = ["#C20000", "#Ff9000", "#04AA6D"];
      var containerWidth =
        document.querySelector("#barTrends").clientWidth - 300;
      let barchart = new dc.BarChart("#barTrends", group)
        .dimension(typeDimesion)
        .group(typeGroup);

      barchart
        .width(containerWidth)
        .height(480)
        .gap(100)
        .x(d3.scaleBand())
        .xUnits(dc.units.ordinal)
        .brushOn(true)
        .yAxisLabel("Number of disasters")
        .elasticY(true)
        .colorAccessor(d => d.key)
        .colorCalculator(function(d) {
         
          if (d.key === "1-Unsigned") {
            return colors[0];
          } else if (d.key === "2-UnApproved") {
            return colors[1];
          } else if (d.key === "3-Submitted") {
           
            return colors[2];
          }
        })
        .on("renderlet", chart => {
          chart.selectAll("rect").attr("rx", "7");
          chart
            .selectAll("rect")
            .selectAll("title")
            .text(function(d) {
              var key = d.data.key;
              var value = d.data.value;
              return key.split("-")[1] + " : " + value;
            });
          chart
            .select(".x")
            .selectAll(".tick")
            .selectAll("text")
            .text(function(d) {
              var value = d;
              return value.toString().split("-")[1];
            });
        });
      //#############################COMPOSITE########################################

      var seriesUnsignedDimension = ndx.dimension(function(d) {
        return d.createdon;
      });

      var seriesUnApprovedDimension = ndx.dimension(function(d) {
        return d.createdon;
      });

      var seriesSubmittedDimension = ndx.dimension(function(d) {
        return d.createdon;
      });

      var minDate = d3.min(ndx.all(), function(d) {
        return d.date;
      });

      var maxDate = d3.max(ndx.all(), function(d) {
        return d.date;
      });

      var typeTimeDimesion = ndx.dimension(function(d) {
        return [d.type, new Date(d.date)];
      });
      var typgroup = typeTimeDimesion.group().reduceCount(d => 1);

      var chart = new dc.SeriesChart("#lineTrends", group).seriesAccessor(
        function(d) {
          return d.key[0];
        }
      );

      chart
        .width(containerWidth)
        .height(480)
        .chart(function(c) {
          return dc.scatterPlot(c).symbolSize(15);
        })
        .x(d3.scaleTime().domain([new Date(minDate), new Date(maxDate)]))
        .brushOn(false)
        .yAxisLabel("Number of disasters")
        .xAxisLabel("Month , Year")
        .clipPadding(10)
        .ordinalColors(["#C20000", "#Ff9000", "#04AA6D"])
        .elasticY(true)
        .dimension(typeTimeDimesion)
        .group(typgroup)
        .on("renderlet", chart => {
          chart
            .selectAll(".symbol")
            .selectAll("title")
            .text(function(d) {
             
              var key = d.key[0];
              return key.split("-")[1] + " : " + d.value;
            });
          chart
            .selectAll(".dc-legend-item")
            .selectAll("text")
            .text(function(d) {
              var key = d.name;
              return key.split("-")[1];
            });
        })
        .seriesAccessor(function(d) {
          return d.key[0];
        })
        .keyAccessor(function(d) {
          return +d.key[1];
        })
        .valueAccessor(function(d) {
          return +d.value;
        })
        .legend(
          dc
            .legend()
            .x(containerWidth - 200)
            .y(10)
            .itemHeight(16)
            .gap(8)
            .horizontal(1)
            .legendWidth(150)
            .itemWidth(150)
        );

      //#########################HEAT MAP###################################################
      var typeTimeDimesionHeatMap = ndx.dimension(function(d) {
        
        return [d.district.admin2_name_en,d.type];
      });
      var typgroupHeatMAp = typeTimeDimesionHeatMap.group().reduceCount(d => 1);
    
      var chart = new dc.HeatMap("#heatTrends", group)
      .dimension(typeTimeDimesionHeatMap)
        .group(typgroupHeatMAp)
        .keyAccessor(function(d) {
          return d.key[0];
        })
        .valueAccessor(function(d) {
           
          return d.key[1];
        })
        .colorAccessor(function(d) {
           
          return +d.value;
        });

      chart
        .width(45 * 20 + 8)
    .height(45 * 12)
    .title(function(d) {
        return "District:   " + d.key[0] + "\n" +
               "Disasters:  " + d.key[1].split('-')[1] + "\n" +
               "Number: " + ( d.value) ;})
        .colors(["#ffffd9","#edf8b1","#c7e9b4","#7fcdbb","#41b6c4","#1d91c0","#225ea8","#253494","#081d58"])
    .calculateColorDomain()
        .legend(
          dc
            .legend()
            .x(containerWidth - 200)
            .y(10)
            .itemHeight(16)
            .gap(8)
            .horizontal(1)
            .legendWidth(150)
            .itemWidth(150)
        );

      // .formatNumber(numberFormat);

      dc.renderAll(group);
    });
  },
  methods: {
    async loadForms() {
      var response = await MongoReports.getUnapprovedDinrs().then(response => {
        let unsigneddata = response.data
         
          .filter(
            item =>
              !item ||
              ((!item.isApproved &&
                item.isApproved == false &&
                !item &&
                (!item.isRejected && item.isRejected == false)) ||
                (!item.approvalMetadata ||
                  !item.approvalMetadata.signature ||
                  item.approvalMetadata.signature.length == 0))
          )
          .map(obj => ({
            ...obj,
            hours: moment().diff(moment(obj.createdon), "hours"),
            createdon: new Date(obj.createdon.split("T")[0]),
            date: moment(obj.createdon).format("YYYY-MM-DD")
          }));
        this.pushUnsigned(unsigneddata);
        //format("DD-MM-YYYY")
        let unapprovedData = response.data
          
          .filter(
            item =>
              (!item.isApproved || item.isApproved == false) &&
              (!item.isRejected || item.isRejected == false) &&
              item.approvalMetadata &&
              item.approvalMetadata.signature &&
              item.approvalMetadata.signature.length > 0
          )
          .map(obj => ({
            ...obj,
            hours: moment().diff(moment(obj.createdon), "hours"),
            createdon: new Date(obj.createdon.split("T")[0]),
            date: moment(obj.createdon).format("YYYY-MM-DD")
          }));
        this.pushUnApproved(unapprovedData);
        return response;
      });
      var response2 = await MongoReports.getDinrs().then(response => {
        let submittedData = response.data.map(obj => ({
          ...obj,
          hours: moment().diff(moment(obj.createdon), "hours"),
          createdon: new Date(obj.createdon.split("T")[0]),
          date: moment(obj.createdon).format("YYYY-MM-DD")
        }));
       
        this.pushSubmitted(submittedData);
        return response;
      });
      return response;
    },
    pushUnsigned(data) {
      var type = "1-Unsigned";
      data.forEach(form => {
        if (form.hours <= 24) {
          this.data.push({ ...form, type: type, group: "1-last 24 Hours" });
        }
        if (form.hours <= 168) {
          this.data.push({ ...form, type: type, group: "2-last 7 day" });
        }
        if (form.hours <= 720) {
          this.data.push({ ...form, type: type, group: "3-last 30 day" });
        }
        if (form.hours <= 2160) {
          this.data.push({ ...form, type: type, group: "4-last 90 days" });
        }
        if (form.hours <= 8760) {
          this.data.push({ ...form, type: type, group: "5-last 12 Months" });
        }
        this.data.push({ ...form, type: type, group: "6-Lifetime" });
      });
      return;
    },
    pushUnApproved(data) {
      var type = "2-UnApproved";
      data.forEach(form => {
        if (form.hours <= 24) {
          this.data.push({ ...form, type: type, group: "1-last 24 Hours" });
        }
        if (form.hours <= 168) {
          this.data.push({ ...form, type: type, group: "2-last 7 day" });
        }
        if (form.hours <= 720) {
          this.data.push({ ...form, type: type, group: "3-last 30 day" });
        }
        if (form.hours <= 2160) {
          this.data.push({ ...form, type: type, group: "4-last 90 days" });
        }
        if (form.hours <= 8760) {
          this.data.push({ ...form, type: type, group: "5-last 12 Months" });
        }
        this.data.push({ ...form, type: type, group: "6-Lifetime" });
      });
      return;
    },
    pushSubmitted(data) {
      var type = "3-Submitted";
      data.forEach(form => {
        if (form.hours <= 24) {
          this.data.push({ ...form, type: type, group: "1-last 24 Hours" });
        }
        if (form.hours <= 168) {
          this.data.push({ ...form, type: type, group: "2-last 7 day" });
        }
        if (form.hours <= 720) {
          this.data.push({ ...form, type: type, group: "3-last 30 day" });
        }
        if (form.hours <= 2160) {
          this.data.push({ ...form, type: type, group: "4-last 90 days" });
        }
        if (form.hours <= 8760) {
          this.data.push({ ...form, type: type, group: "5-last 12 Months" });
        }
        this.data.push({ ...form, type: type, group: "6-Lifetime" });
      });
      return;
    },
    drawGraph(filter) {
      // console.log("data",this.data)
      //dc.renderAll(group);
    }
  }
};
</script>

<style>
#barTrends,
#TableTrends {
  font-family: Arial, Helvetica, sans-serif;
  border-collapse: collapse;
  width: 90%;
  margin-left: 5%;
}
#trendsTable {
  font-family: Arial, Helvetica, sans-serif;
  border-collapse: collapse;
  width: 74%;
  margin-left: 3%;
}
#lineTrends {
  font-family: Arial, Helvetica, sans-serif;
  border-collapse: collapse;
  width: 90%;
  margin-left: 5%;
}
#heatTrends {
  font-family: Arial, Helvetica, sans-serif;
  border-collapse: collapse;
  width: 90%;
  margin-left: 5%;
}

#trendsTable td,
#trendsTable th {
  border: 1px solid #ddd;
  padding: 8px;
}

#trendsTable tr:nth-child(even) {
  background-color: #f2f2f2;
}

#trendsTable tr:hover {
  background-color: #ddd;
}

#trendsTable th {
  padding-top: 12px;
  padding-bottom: 12px;
  text-align: left;
  background-color: #04aa6d;
  color: white;
}
#identifier-c select {
  padding-top: 12px !important;
  padding-bottom: 12px !important;
}
@import "../../../assets/css/nucleo/dc.css";
</style>
