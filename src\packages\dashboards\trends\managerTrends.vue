<template style="background: white">
  <div>
    <p class="description col-12">
      <center>
        <grid-loader
          :loading="loading"
          :color="'#bf6c00'"
          :width="'100%'"
          size="40px"
          style="margin-top: 20%"
        ></grid-loader>
      </center>
    </p>

    <div class="content" v-if="loading !== true">
      <base-header class="pb-6" type>
        <div class="row align-items-center py-4">
          <div class="col-lg-6 col-7">
            <h6 class="h2 d-inline-block mb-0">Disaster</h6>
            <nav
              aria-label="breadcrumb"
              class="d-none d-md-inline-block ml-md-4"
            >
              <ol class="breadcrumb breadcrumb-links">
                <li class="breadcrumb-item">
                  <router-link to="/manager/dashboard">
                    <i class="fas fa-home"></i>
                  </router-link>
                </li>
                <li class="breadcrumb-item">
                  <a href="#">Trends</a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">
                  {{ filter.split("-")[1] }}
                </li>
              </ol>
            </nav>
          </div>
          <div class="col-lg-6 col-5 text-right"></div>
        </div>
        <!-- Card stats -->
      </base-header>
      <div class="container-fluid mt--6">
        <div>
          <card
            class="row mx-0 px-0"
            style="margin-bottom:5px;"
          >
            <div class="col-12 mx-0 px-0 mt--2 ml--2" style="font-weight:bold">
                DISASTER REPORTS STATUS
            </div>
            <hr class="col-12 mx-0 ml--3 mt-2 pr-4 my-0 py-2"/>
            <div class="col-12  mx-0 px-0">
              <span style="margin-right:10px;color:black">Period :</span>
              <span id="identifier-c" style="width:50%;"></span>
            </div>
          </card>
        </div>

        <div class="row" style="padding-bottom:0px;">
          <div class="col-6" style="padding-right:3px;">
            <div>
              <card
                class="no-border-card cb-bdr trendCard"
                body-classes="px-0 pb-1 cd-bg-color"
                footer-classes="pb-2"
                style=""
              >
                <div class="chartPaddingLeft">
                  <div
                    style="width:100%;text-algin:center;"
                    id="barTrends"
                  ></div>
                </div>
              </card>
            </div>
          </div>
          <div class="col-6" style="padding-left:3px;">
            <div>
              <card
                class="no-border-card cb-bdr trendCard"
                body-classes="px-0 pb-3 cd-bg-color"
                footer-classes="pb-3"
              >
                <div class="row pt-0 mt--2">
                  <div class="col-12 pl-4 pt-0 mt-0 pb-3 text-uppercase" style="color:#999999;font-weight: bold">
                     # of disasters by status
                  </div>
                </div>
                <div
                  style="width:100%;text-align:center;height:305px;"
                  id="TableTrends"
                  class="pr-2"
                >
                  <table id="trendsTable" style="color:black;" >
                    <tr >
                      <th style="text-align:left">District</th>
                      <th style="text-align:center">Unsigned</th>
                      <th style="text-align:center">Unapproved</th>
                      <th style="text-align:center">Submitted</th>
                    </tr>
                    <tr
                      v-for="item in gettypgroupHeatMApArray"
                      :key="item.name"
                    >
                      <td style="text-align:left">{{ item.name }}</td>
                      <td>
                        {{ item.Unsigned }}
                      </td>
                      <td>
                        {{ item.Unapproved }}
                      </td>
                      <td>
                        {{ item.Submitted }}
                      </td>
                    </tr>
                  </table>
                </div>
              </card>
            </div>
          </div>
        </div>
        <div style="">
          <card
            class="no-border-card cb-bdr trendCard"
            body-classes="px-0 pb-1 cd-bg-color"
            footer-classes="pb-2"
          >
            <div
              style="width:100%;text-algin:center;"
              id="lineTrends"
              class="chartPaddingLeft"
            ></div>
          </card>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import gridLoader from "vue-spinner/src/GridLoader";

import { Table, TableColumn, Select, Option } from "element-ui";
import RouteBreadCrumb from "@/components/Breadcrumb/RouteBreadcrumb";
import * as d3 from "d3";
import * as dc from "dc";
import crossfilter from "crossfilter2";
var globalData = [];
var group = "trendCharts";
var dcGroupFilterHandler = {};
var typeDimesion = {};
var seriesTypeDateDimension = {};
var parent = {};
import { MongoReports } from "../../districtmanager/api/MongoReports";
var moment = require("moment");

export default {
  components: {
    gridLoader,
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn
  },
  data() {
    return {
      loading: true,
      filter: "6-Lifetime",
      data: [],
      typgroupHeatMApArray: [],
      queriedData: [
        {
          disaster: "jsdfjsd",
          Unsigned: 8
        }
      ],
      tableColumns: [
        {
          prop: "name",
          label: "District",
          minWidth: 200,
          sortable: true
        },
        {
          prop: "Unsigned",
          label: "Unsigned",
          minWidth: 100,
          sortable: true
        },
        {
          prop: "Unapproved",
          label: "Unapproved",
          minWidth: 150,
          sortable: true
        },
        {
          prop: "Submitted",
          label: "Submitted",
          minWidth: 135,
          sortable: true
        }
      ],
      tableData: [
        {
          key: "1-Unsigned",
          value: 0
        },
        {
          key: "2-Unapproved",
          value: 0
        },
        {
          key: "3-Submitted",
          value: 0
        }
      ],
      Graphtitle: ""
    };
  },
  computed: {
    gettypgroupHeatMApArray() {
      var data = [];
      
      this.typgroupHeatMApArray.forEach(element => {
        var found = data.find(e => e.name === element.key[0]);
        if (found === undefined) {
          var toPush = {
            name: element.key[0]
          };

          toPush[element.key[1].split("-")[1]] = element.value;
          data.push(toPush);
        } else {
          var foundIndex = data.findIndex(e => e.name === element.key[0]);
          found[element.key[1].split("-")[1]] = element.value;
        }
      });
      data.forEach(element => {
        if (element.Unsigned === undefined) {
          element.Unsigned = 0;
        }
        if (element.Unapproved === undefined) {
          element.Unapproved = 0;
        }
        if (element.Submitted === undefined) {
          element.Submitted = 0;
        }
        return element;
      });
      data = data.sort((a, b) => b.Unsigned - a.Unsigned);
      data = data.sort((a, b) => b.Unapproved - a.Unapproved);
      data = data.sort((a, b) => b.Submitted - a.Submitted);
      data.push({
        name: "Total",
        Unsigned: data
          .map(d => d.Unsigned)
          .reduce((d, i) => parseInt(d | 0) + parseInt(i | 0), 0),
        Unapproved: data
          .map(d => d.Unapproved)
          .reduce((d, i) => parseInt(d | 0) + parseInt(i | 0), 0),
        Submitted: data
          .map(d => d.Submitted)
          .reduce((d, i) => parseInt(d | 0) + parseInt(i | 0), 0)
      });
      return data;
    },
    getData() {
      return this.data;
    }
  },
  async mounted() {
    parent = this;
    var numberFormat = d3.format(",");
    var formatDate = d3.timeFormat("%b %Y");
    setTimeout(() => {
      this.loadForms().then(response => {
        const uniqueIds = [];

const unique = this.data.filter(element => {
  const isDuplicate = uniqueIds.includes(element._id);
  if (!isDuplicate) {
    uniqueIds.push(element.id);

    return true;
  }

  return false;
});
        var ndx = crossfilter(unique);

        //", ndx.all());
        //###################MENU############################################
        var dcGroupFilterHandler = ndx.dimension(function(d) {
          return d.group;
        });
        typeDimesion = ndx.dimension(function(d) {
          return d.type;
        });

        //  console.log("SUUM", dcGroupFilterHandler.group().all());

        new dc.SelectMenu("#identifier-c", group)
          .dimension(dcGroupFilterHandler)
          .group(dcGroupFilterHandler.group())

          .promptText("SELECT")
          .on("postRender", function() {
            d3.select(".dc-select-menu").attr(
              "class",
              "border p-2 mr-4 text-left fullwidth bg-white"
            );
          })
          .on("renderlet", chart => {
            d3.selectAll(".dc-select-option").text(function(d) {
              var key = d.key;
              return key.split("-")[1];
            });
          })
          .on("filtered", function(chart, filter) {
            parent.filter = filter;
            dcGroupFilterHandler.filter(d => d === filter);
            var unsigned = typeDimesion
              .group()
              .reduceCount(d => 1)
              .all();
            parent.tableData = unsigned;

            dc.redrawAll(group);
          });
        //parent.filter = dcGroupFilterHandler.group().all()[0].key;
        dcGroupFilterHandler.filter(this.filter);
        var unsigned = typeDimesion
          .group()
          .reduceCount(d => 1)
          .all();
        parent.tableData = unsigned;
        //#############################BAR############################################

        let typeGroup = typeDimesion.group().reduceCount(item => 1);
        var containerWidth =
          document.querySelector("#barTrends").clientWidth - 10;
        let barchart = new dc.BarChart("#barTrends", group)
          .dimension(typeDimesion)
          .group(typeGroup);
        var colors = ["#C20000", "#Ff9000", "#04AA6D"];
         barchart.xUnits(function(){return 10;});
        barchart
          .width(containerWidth)
          .height(350)
          .gap(160)
          .x(d3.scaleBand())
          .xUnits(dc.units.ordinal)
          .brushOn(true)
          .centerBar(true)
          .yAxisLabel("# of disasters")
          .elasticY(true)
          .colorCalculator(function(d) {
            if (d.key === "1-Unsigned") {
              return colors[0];
            } else if (d.key === "2-Unapproved") {
              return colors[1];
            } else if (d.key === "3-Submitted") {
              return colors[2];
            }
          })
          .on("renderlet", chart => {
            d3.selectAll(".tick").attr("color", "black");
            chart
              .select(".y")
              .selectAll(".tick")
              .select("text")
              .text(function(d) {
                var newdata = parseInt(d);
                return newdata === d ? newdata : "";
              });
            chart
              .select(".y")
              .selectAll(".tick")
              .select("line")
              .attr("x2", function(d) {
                var newdata = parseInt(d - 1);
                return newdata === parseFloat(d - 1) ? -6 : 0;
              });
            chart.selectAll("rect").attr("rx", "7");
            
            chart
              .selectAll("rect")
              .selectAll("title")
              .text(function(d) {
                var key = d.data.key;
                var value = d.data.value;
                return key.split("-")[1] + " : " + value;
              });

          
              
            chart
              .select(".x")
              .selectAll(".tick")
              .selectAll("text")
              .text(function(d) {
                var value = d;
                return value.toString().split("-")[1];
              });
          });
        //#############################COMPOSITE########################################

        var minDate = d3.min(ndx.all(), function(d) {
          return d.created_on;
        });

        var maxDate = d3.max(ndx.all(), function(d) {
          return d.created_on;
        });

        var typeTimeDimesion = ndx.dimension(function(d) {
          return [d.type, new Date(d.date)];
        });
       
        var typgroup = typeTimeDimesion.group().reduceCount(d => 1);
        var filtered_group = remove_empty_bins(typgroup);
        var containerWidth =
          document.querySelector("#lineTrends").clientWidth - 10;
        var chart = new dc.SeriesChart("#lineTrends", group)
          .seriesAccessor(function(d) {
            return d.key[0];
          })
          .colorAccessor(function(d, i) {
            return i;
          });
        //chart.yAxis().tickFormat(function(v) {return v;})
        chart
          .width(containerWidth)
          .height(480)
          .chart(function(c) {
            return dc.scatterPlot(c).symbolSize(15);
          })
          .elasticX(true)
          .y(d3.scaleLinear().domain([0, 6]))
          .x(d3.scaleTime().domain([minDate, maxDate]))
          .brushOn(false)
          .yAxisLabel("# of disasters")
          .clipPadding(10)
          .elasticX(true)
          .xAxisLabel("Month , Year")
          .clipPadding(10)
          .ordinalColors(["#C20000", "#Ff9000", "#04AA6D"])
          .dimension(typeTimeDimesion)
          .group(typgroup)
          .on("renderlet", chart => {
            chart
              .select(".y")
              .selectAll(".tick")
              .select("text")
              .text(function(d) {
                var newdata = parseInt(d);
                return newdata === parseFloat(d) ? newdata : "";
              });
            chart
              .select(".y")
              .selectAll(".tick")
              .select("line")
              .attr("x2", function(d) {
                var newdata = parseInt(d - 1);
                return newdata === parseFloat(d - 1) ? -6 : 0;
              });
            chart
              .selectAll(".symbol")
              .selectAll("title")
              .text(function(d) {
                var key = d.key[0];
                return key.split("-")[1] + " : " + d.value;
              });
            chart
              .selectAll(".dc-legend-item")
              .selectAll("text")
              .text(function(d) {
                var key = d.name;
                return key.split("-")[1];
              });

            chart

              .selectAll(".x")
              .selectAll(".tick")
              .selectAll("text")
              .text(function(d) {
                return moment(d).format("MMM,YYYY");
              });
          })
          .seriesAccessor(function(d) {
            return d.key[0];
          })

          .keyAccessor(function(d) {
            return +d.key[1];
          })
          .valueAccessor(function(d) {
            return +d.value;
          })

          .colorCalculator(function(d) {
            if (d.key === "1-Unsigned") {
              return colors[0];
            } else if (d.key === "2-Unapproved") {
              return colors[1];
            } else if (d.key === "3-Submitted") {
              return colors[2];
            }
          });

        // .legend(
        //   dc
        //     .legend()
        //     .x(containerWidth - 200)
        //     .y(10)
        //     .itemHeight(16)
        //     .gap(8)
        //     .horizontal(1)
        //     .legendWidth(150)
        //     .itemWidth(150)
        // );
        //####################SERIES CHART################################################################
        // var typeTimeDimesion2 = ndx.dimension(function(d) {
        //   return [d.type, new Date(d.date)];
        // });

        // var typgroup2 = typeTimeDimesion2.group().reduceCount(d => 1);
        // var containerWidth =
        //   document.querySelector("#lineTrends2").clientWidth - 10;
        //       var chart = new dc.SeriesChart("#lineTrends2", group)
        //         .seriesAccessor(function(d) {
        //           return d.key[0];
        //         })
        //         .colorAccessor(function(d, i) {
        //           return i;
        //         });
        //       //chart.yAxis().tickFormat(function(v) {return v;})
        //       chart
        //         .width(containerWidth)
        //         .height(480)
        //         .chart(function(c) { return new dc.LineChart(c) })
        //         .elasticX(true)
        //         .y(d3.scaleLinear().domain([0, 6]))
        //         .x(d3.scaleTime().domain([minDate,maxDate]))
        //         .brushOn(false)
        //         .yAxisLabel("Number of disasters")
        // .elasticX(true)
        //         .xAxisLabel("Month , Year")
        //         .clipPadding(10)
        //         .ordinalColors(["#C20000", "#Ff9000", "#04AA6D"])
        //         .dimension(typeTimeDimesion2)
        //         .group(typgroup2)
        //         .on("renderlet", chart => {
        //            chart
        //             .selectAll("circle")
        //             .selectAll("title")
        //            .text(function(d) {
        //               return moment(d.data.key[1]).format("ll") + " : " + d.data.value;
        //             });
        //           chart
        //             .select(".y")
        //             .selectAll(".tick")
        //             .select("text")
        //             .text(function(d) {
        //               var newdata = parseInt(d);
        //               return newdata === parseFloat(d ) ? newdata : "";
        //             });
        //           chart
        //             .select(".y")
        //             .selectAll(".tick")
        //             .select("line")
        //             .attr("x2", function(d) {
        //               var newdata = parseInt(d - 1);
        //               return newdata === parseFloat(d - 1) ? -6 : 0;
        //             });
        //           chart
        //             .selectAll(".symbol")
        //             .selectAll("title")
        //             .text(function(d) {
        //               var key = d.key[0];
        //               return key.split("-")[1] + " : " + d.value;
        //             });
        //           chart
        //             .selectAll(".dc-legend-item")
        //             .selectAll("text")
        //             .text(function(d) {
        //               var key = d.name;
        //               return key.split("-")[1];
        //             });

        //  chart

        //             .selectAll(".x")
        //             .selectAll(".tick").selectAll('text')
        //             .text(function(d) {
        //               return moment(d).format("MMM,YYYY");
        //             });

        //         })
        //         .seriesAccessor(function(d) {
        //           return d.key[0];
        //         })

        //         .keyAccessor(function(d) {
        //           return +d.key[1];
        //         })
        //         .valueAccessor(function(d) {
        //           return +d.value;
        //         })

        //         .colorCalculator(function(d) {
        //           if (d.key === "1-Unsigned") {
        //             return colors[0];
        //           } else if (d.key === "2-Unapproved") {
        //             return colors[1];
        //           } else if (d.key === "3-Submitted") {
        //             return colors[2];
        //           }
        //         })
        //#########################HEAT MAP###################################################
        var typeTimeDimesionHeatMap = ndx.dimension(function(d) {
          return [d.district.admin2_name_en, d.type];
        });
        var typgroupHeatMAp = typeTimeDimesionHeatMap
          .group()
          .reduceCount(d => 1);

        this.typgroupHeatMApArray = typgroupHeatMAp.all();
        // console.log("type group ",this.typgroupHeatMApArray)

        // .formatNumber(numberFormat);

        dc.renderAll(group);
        function remove_empty_bins(source_group) {
          return {
            all: function() {
              return source_group.all().filter(function(d) {
                //return Math.abs(d.value) > 0.00001; // if using floating-point numbers
                return d.value !== 0; // if integers only
              });
            }
          };
        }
      }),
        (this.loading = false);
    }, 3000);
  },
  methods: {
    async loadForms() {
      var response = await MongoReports.getUnapprovedDinrs().then(response => {
        let unsigneddata = response.data

          .filter(
            item =>
              !item ||
              ((!item.isApproved &&
                item.isApproved == false &&
                !item &&
                (!item.isRejected && item.isRejected == false)) ||
                (!item.approvalMetadata ||
                  !item.approvalMetadata.signature ||
                  item.approvalMetadata.signature.length == 0))
          )
          .map(obj => ({
            ...obj,
            hours: moment().diff(moment(obj.createdon), "hours"),
            createdon: new Date(obj.createdon.split("T")[0]),
            date: moment(obj.createdon).format("YYYY-MM-DD")
          }));
        unsigneddata = unsigneddata.filter(f => f.isRejected === false);
        this.pushUnsigned(unsigneddata);
        //format("DD-MM-YYYY")
        let unapprovedData = response.data

          .filter(
            item =>
              (!item.isApproved || item.isApproved == false) &&
              (!item.isRejected || item.isRejected == false) &&
              item.approvalMetadata &&
              item.approvalMetadata.signature &&
              item.approvalMetadata.signature.length > 0
          )
          .map(obj => ({
            ...obj,
            hours: moment().diff(moment(obj.createdon), "hours"),
            createdon: new Date(obj.createdon.split("T")[0]),
            date: moment(obj.createdon).format("YYYY-MM-DD")
          }));
        unapprovedData = unapprovedData.filter(f => f.isRejected === false);
        this.pushUnApproved(unapprovedData);
        return response;
      });
      var response2 = await MongoReports.getDinrs().then(response => {
        let submittedData = response.data.map(obj => ({
          ...obj,
          hours: moment().diff(moment(obj.createdon), "hours"),
          createdon: new Date(obj.createdon.split("T")[0]),
          date: moment(obj.createdon).format("YYYY-MM-DD")
        }));

        this.pushSubmitted(submittedData);
        return response;
      });
      return response;
    },
    pushUnsigned(data) {
      var type = "1-Unsigned";
      data.forEach(form => {
        if (form.hours <= 24) {
          this.data.push({ ...form, type: type, group: "1-last 24 Hours" });
        }
        if (form.hours <= 168) {
          this.data.push({ ...form, type: type, group: "2-last 7 day" });
        }
        if (form.hours <= 720) {
          this.data.push({ ...form, type: type, group: "3-last 30 day" });
        }
        if (form.hours <= 2160) {
          this.data.push({ ...form, type: type, group: "4-last 90 days" });
        }
        if (form.hours <= 8760) {
          this.data.push({ ...form, type: type, group: "5-last 12 Months" });
        }

        this.data.push({ ...form, type: type, group: "6-Lifetime" });
      });
      return;
    },
    pushUnApproved(data) {
      var type = "2-Unapproved";
      data.forEach(form => {
        if (form.hours <= 24) {
          this.data.push({ ...form, type: type, group: "1-last 24 Hours" });
        }
        if (form.hours <= 168) {
          this.data.push({ ...form, type: type, group: "2-last 7 day" });
        }
        if (form.hours <= 720) {
          this.data.push({ ...form, type: type, group: "3-last 30 day" });
        }
        if (form.hours <= 2160) {
          this.data.push({ ...form, type: type, group: "4-last 90 days" });
        }
        if (form.hours <= 8760) {
          this.data.push({ ...form, type: type, group: "5-last 12 Months" });
        }
        this.data.push({ ...form, type: type, group: "6-Lifetime" });
      });
      return;
    },
    pushSubmitted(data) {
      var type = "3-Submitted";
      data.forEach(form => {
        if (form.hours <= 24) {
          this.data.push({ ...form, type: type, group: "1-last 24 Hours" });
        }
        if (form.hours <= 168) {
          this.data.push({ ...form, type: type, group: "2-last 7 day" });
        }
        if (form.hours <= 720) {
          this.data.push({ ...form, type: type, group: "3-last 30 day" });
        }
        if (form.hours <= 2160) {
          this.data.push({ ...form, type: type, group: "4-last 90 days" });
        }
        if (form.hours <= 8760) {
          this.data.push({ ...form, type: type, group: "5-last 12 Months" });
        }

        this.data.push({ ...form, type: type, group: "6-Lifetime" });
      });
      return;
    }
  }
};
</script>

<style scoped>
.chartPaddingLeft {
  padding-left: 15px;
}
#barTrends,
#TableTrends {
  font-family: Arial, Helvetica, sans-serif;
  border-collapse: collapse;
  width: 100%;
  margin-left: 5px;
}
.dc-chart circle.dot {
  stroke-width: 9;
  stroke-opacity: 1 !important;
}
#trendsTable {
  font-family: Arial, Helvetica, sans-serif;
  border-collapse: collapse;
  width: 98%;
  margin-left: 5px;
}


.card-header {
  font-weight: bolder !important;
  color: black;
  /* background-color: #d9ecec; */
}
.cd-bg-color {
  /* background: #f0f2f5; */
}
.cb-bdr {
  border: 1px solid #bf6c00;
  margin-bottom: 5px;
}
#lineTrends {
  font-family: Arial, Helvetica, sans-serif;
  border-collapse: collapse;
  width: 90%;
  margin-left: 5px;
}
#heatTrends {
  font-family: Arial, Helvetica, sans-serif;
  border-collapse: collapse;
  width: 90%;
  margin-left: 5px;
}

#trendsTable td,
#trendsTable th {
  border: 1px solid #ddd;
  padding: 8px;
}

#trendsTable tr:nth-child(even) {
  background-color: #d9ecec;
}
#trendsTable tr:first-child th,
#trendsTable tr:last-child td {
  color: black !important;
  font-weight: bold;
}

#trendsTable tr:hover {
  background-color: #ddd;
}

#trendsTable th {
  padding-top: 12px;
  padding-bottom: 12px;
  text-align: left;
  color: #909399;
}
#identifier-c > select {
  padding-top: 12px !important;
  padding-bottom: 12px !important;
  background-color: #f7fafc !important;
}
.p0 {
  padding: 5px;
}
.fullwidth {
  width: 60%;
}
.card {
  border: 1px solid #dbddde;;
  border-radius: 7px;
}
.card {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
}
.table .thead-light th,
.el-table .thead-light th {
  background-color: #f6f9fc !important;
  color: #8898aa;
}
.el-table thead {
  color: #909399;
  font-weight: 500;
}

text.x-axis-label{
  margin-top:5px !important;
}
@import "../../../assets/css/nucleo/dc.css";
</style>
