import axios from 'axios';

const resource = "http://localhost:3000/api/session/";

const token = "";

const auth = {
    headers: { 'X-Metabase-Session': token, 'Content-Type': 'application/json' },
    useCredentails: true
}

const auth2 = {
    headers: { 'Content-Type': 'application/json' }
}





export class metabase {

    static async getToken() {
        axios.defaults.withCredentials = true;
        // Making a POST request using an axios instance from a connected library
        let token = axios.post("http://localhost:3000/api/session/", { "username": "michael<PERSON><EMAIL>", "password": "Cathybanda4" }, auth2)
            // Handle a successful response from the server
            .then(response => {
                // Getting a data object from response that contains the necessary data from the server
                const data = response.data;
                console.log(data);

                // Save the unique id that the server gives to our object
                //STUDENT_ID = data._id;
            })
            // Catch and print errors if any
            .catch(error => console.log(error));
        return token;
    }
    static async getDraData(token, filters) {
        var queryString = "select * from Data"
        if (filters != "") {
            queryString += " " + filters;
        }
        let response = axios.post(resource + "/api/dataset/", { "database": 65, "native": { "query": queryString }, "type": "native" }, auth).then(result => {
            return result
        })
        //console.log(axios)
        return response;
    }

}