$footer-font-size: 1.1rem;

.vgt-wrap__actions-footer{
  border: 1px solid $border-color;
}
.vgt-wrap__footer{
  color: $text-color;
  font-size:  $footer-font-size;
  padding:  1em;
  border: 1px solid $border-color;
  background: linear-gradient($thead-bg-color-1, $thead-bg-color-2);
  .footer__row-count{
    position: relative;
    padding-right: 3px;
    &__label, &__select{
      display: inline-block;
      vertical-align: middle;
    }
    &__label{
      font-size: $footer-font-size;
    }
    &__select{
      font-size: $footer-font-size;
      background-color: transparent;
      width: auto;
      padding: 0;
      border: 0;
      border-radius: 0;
      height: auto;
      margin-left: 8px;
      color:  $text-color;
      font-weight: bold;
      -webkit-appearance: none; 
      -moz-appearance: none;
      appearance: none;
      padding-right: 15px;
      padding-left: 5px;
      &::-ms-expand{
        display: none;
      }
      &:focus{
        outline: none;
        border-color: $link-color;
      }
    }
    &::after{
      content: '';
      display: block;
      position: absolute;
      height: 0px;
      width: 0px;
      right: 6px;
      top: 50%;
      margin-top: -1px;
      border-top:  6px solid $text-color;
      border-left: 6px solid transparent;
      border-right: 6px solid transparent;
      border-bottom: none;
      pointer-events: none
    }
  }
  .footer__navigation{
    > button:first-of-type {
      margin-right: 16px;
    } 
    font-size: $footer-font-size;
    &__page-btn, &__info, &__page-info{
      display: inline-block;
      vertical-align: middle;
      color: $secondary-text-color;
    }
    &__page-btn{
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      background: transparent;
      border: none;
      text-decoration: none;
      color: $text-color;
      font-weight: bold;
      white-space:nowrap;
      vertical-align: middle;
      &:hover{
        cursor: pointer;
      }
      &.disabled,
      &.disabled:hover {
        opacity: 0.5;
        cursor: not-allowed;
        .chevron.left:after{
          border-right-color: $text-color;
        }
        .chevron.right:after{
          border-left-color: $text-color;
        }
      }
      span{
        display: inline-block;
        vertical-align: middle;
        font-size: $footer-font-size;
      }
      .chevron{
        width:  24px;
        height:  24px;
        border-radius: 15%;
        position:  relative;
        margin:  0;
        display: inline-block;
        vertical-align: middle;
        &:after{
          content:  '';
          position:  absolute;
          display:  block;
          left:  50%;
          top:  50%;
          margin-top:  -6px;
          border-top: 6px solid transparent;
          border-bottom: 6px solid transparent;
        }
        &.left::after{
          border-right:  6px solid $link-color;
          margin-left:  -3px;
        }
      
        &.right::after{
          border-left:  6px solid $link-color;
          margin-left:  -3px;
        }
      }
    }
    &__info, &__page-info{
      display: inline-block;
      margin:  0px 16px;
    }
    &__page-info{
      span{
        display: inline-block;
        vertical-align: middle;
      }
      &__current-entry{
        width: 30px;
        text-align: center;
        vertical-align: middle;
        display: inline-block;
        margin: 0px 10px;
        font-weight: bold;
      }
    }
  
  
  }
}

@media only screen and (max-width: 750px) {
  /* on small screens hide the info */
  .vgt-wrap__footer .footer__navigation__info{
    display:  none;
  }
  .vgt-wrap__footer .footer__navigation__page-btn{
    margin-left: 16px;
  }
}