 
<template style="background: white">
  <div>
    <div>
      <p class="description col-12">
        <center>
          <grid-loader
            :loading="loading"
            :color="'#bf6c00'"
            :width="'100%'"
            size="40px"
            style="margin-top: 20%"
          ></grid-loader>
        </center>
      </p>
    </div>
    <div
      ref="printMe"
      style="background: white;"
      class="card mt-0 pt-0 px-4"
    >
      <div class="row mt-0 pt-0">
        <div class="col-xl-12 col-md-12  m-0 p-0" >
          <li class="list-group-item m-0 mx-1 px-3 mt-0">
            <div class="row mb-0 pb-0 mt-0">
              <div class="col-3 mx-0 mt-0 datarange">
                <span> START DATE </span>
                <span>
                  <base-input addon-left-icon="ni ni-calendar-grid-58">
                    <flat-picker
                      class="form-control datepicker bg-white"
                      :config="{ dateFormat: 'd-m-Y' }"
                      v-model="dates.start"
                    >
                    </flat-picker>
                  </base-input>
                </span>
              </div>
              <div class="col-3 mx-0 mt-0 datarange">
                <span> END DATE </span>
                <span>
                  <base-input addon-left-icon="ni ni-calendar-grid-58">
                    <flat-picker
                      class="form-control datepicker bg-white"
                      :config="{ dateFormat: 'd-m-Y' }"
                      v-model="dates.end"
                    >
                    </flat-picker>
                  </base-input>
                </span>
              </div>
              <div class="col-3 mx-0 mt-2 my-auto">
                <base-button
                  @click="filterByDate"
                  style="background:white;color:grey; border:1px solid #dbddde"
                >
                  FILTER
                </base-button>
              </div>
              <div class="col-3 mx-0 mt-4 text-right exports">
                <base-button
                  type="warning"
                  class="reset mx-1 py-2"
                  @click="resetAll"
                >
                  Reset all
                </base-button>

                <base-dropdown
                  menu-on-right
                  type="primary"
                  title-classes="btn btn-xl btn-primary pr-2 pl-2 mr-0 py-2"
                  :has-toggle="false"
                >
                  <a
                    slot="title"
                    class="dropdown-toggle"
                    type="neutral"
                    size="md"
                  >
                    <i class="text-white ni ni-cloud-download-95"></i> EXPORTS
                  </a>
                  <a class="dropdown-item" id="exportCSV">CSV</a>
                  <a class="dropdown-item" id="exportExcel">Excel</a>
                  <a class="dropdown-item" @click="printImage()">Screenshot</a>
                  <a class="dropdown-item" @click="exportToPDF">PDF</a>
                </base-dropdown>
                
              </div>
              <hr class="col-11 my-0 py-0 pl-4 pr-6"/>
              <div class="col-12 mx-0 px-0 ml--4 row text-dark mt-3">
                
                <span id="row-chart" class="p-0 m-0 mx-0 px-0 col-12" style="position:relative;"></span>
              </div>
            </div>
          </li>
        </div>
      </div>
        <div class="row mt-1">
         <div class="col-xl-3 m-0 p-0">
          <div class="card  ml-1 mt-1" style="border:1px solid #dbddde; padding-top: 0.5rem; margin-bottom: 0px">
            <div class="row">
              <div class="col-sm-6">
               <img class="icon" src="../../../../public/img/disasters/House-destroyed.svg" style="width: 65px; height:65px;">
              </div>
              <div class="col-sm-6" style="justify-content: right; display: flex; align-items: right;">
                 <strong id="hh-without-shelter" style="float:right; font-size: 40px; padding-right: 20px; position: absolute; bottom: 0px; color: #c77e1f"></strong>
              </div>
            </div>
              <hr class="" style="margin-top: 0.5rem; margin-bottom: 0.4rem; margin-right: 1rem; margin-left:1rem;">
            <div class="row">
              <div class="col-xl-12">
                 <h3 class="card-text" style="padding-left: 15px; padding-bottom:20px; color:#c77e1f">HH with shelter affected</h3>
              </div>
            </div>
          </div>
        </div>

        <div class="col-xl-3 m-0 p-1">
          <div class="card" style="border:1px solid #dbddde; padding-top: 0.5rem; margin-bottom: 0px">
            <div class="row">
              <div class="col-sm-6">
               <img class="icon" src="../../../../public/img/disasters/Injured.png" style="width: 45px; margin-left:10px; height:65px">
              </div>
              <div class="col-sm-6" style="justify-content: right; display: flex; align-items: right;">
                 <strong id="people-injured" style="float:right; font-size: 40px; padding-right: 20px; position: absolute; bottom: 0px; color: #999999"></strong>
              </div>
            </div>
              <hr class="" style="margin-top: 0.5rem; margin-bottom: 0.4rem; margin-right: 1rem; margin-left:1rem;">
            <div class="row">
              <div class="col-xl-12">
                 <h3 class="card-text" style="padding-left: 15px; padding-bottom:20px;color:#999999">People injured</h3>
              </div>
            </div>
          </div>
        </div>

        <div class="col-xl-3 m-0 p-1">
          <div class="card" style="border:1px solid #dbddde; padding-top: 0.5rem; margin-bottom: 0px">
            <div class="row">
              <div class="col-sm-6">
               <img class="icon" src="../../../../public/img/disasters/Dead.png" style="width: 59px; height:65px">
              </div>
              <div class="col-sm-6" style="justify-content: right; display: flex; align-items: right;">
                 <strong id="people-dead" style=" font-size: 40px; padding-right: 20px; position: absolute; bottom: 0px; color: #999999"></strong>
              </div>
            </div>
              <hr class="" style="margin-top: 0.5rem; margin-bottom: 0.4rem; margin-right: 1rem; margin-left:1rem;">
            <div class="row">
              <div class="col-xl-12">
                 <h3 class="card-text" style="padding-left: 15px; padding-bottom:20px;color:#999999">People dead</h3>
              </div>
            </div>
          </div>
        </div>

        <div class="col-xl-3 m-0 p-1">
          <div class="card" style="border:1px solid #dbddde; padding-top: 0.5rem; margin-bottom: 0px">
            <div class="row">
              <div class="col-sm-6">
               <img class="icon" src="../../../../public/img/disasters/Food-Security.svg" style="width: 65px; height:65px">
              </div>
              <div class="col-sm-6" style="justify-content: right; display: flex; align-items: right;">
                 <strong id="people-affected" style="float:right; font-size: 40px; padding-right: 20px; position: absolute; bottom: 0px; color: #999999"></strong>
              </div>
            </div>
              <hr class="" style="margin-top: 0.5rem; margin-bottom: 0.4rem; margin-right: 1rem; margin-left:1rem;">
            <div class="row">
              <div class="col-xl-12">
                 <h3 class="card-text" style="padding-left: 15px; padding-bottom:20px;color:#999999">HH with food lost</h3>
              </div>
            </div>           
          </div>
        </div>
        </div>
      <div class="row">
        <div class="col-xl-12 col-md-12 m-0 p-1">
          <div class="chart-wrapper chart-card">
            <div class="chart-title">
              HH with shelter affected by disaster in above districts
              <button
                class="reset small pull-right btn btn-sm mr-1"
                style="margin-top: -0.4%"
                id="resetserieschart"
              >
                reset
              </button>
            </div>
            <div
              style="width: 100%;  height: 93%"
              class="px-4"
            >
              <div id="series-chart" style="width: 100%"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-xl-4 col-md-6 m-0 p-1">
          <div class="chart-wrapper chart-card">
            <div class="chart-title">
             <span>
                    HH with shelter affected by disasters

              </span>
              <button
                class="reset small pull-right btn btn-sm mr-1"
                id="resetpiechart"
              >
                reset
              </button>
            </div>
            <div id="pie-chart" style="width: 100%;"></div>
            <div
              id="hh-total"
              style="
                position: absolute;
                top: 240px;
                bottom: 100px;
                right: 0px;
                left: 5px;
                font-size: 12px;
                font-weight: bold;
                color: #21986c;
              "
            ></div>
            <div
              id="hh-total-affected"
              style="
                position: absolute;
                top: 250px;
                bottom: 100px;
                right: 0px;
                left: 5px;
                font-size: 30px;
                font-weight: bold;
                color: #db6400;
              "
            ></div>
          </div>
        </div>
        <div class="col-xl-4 col-md-6 m-0 p-1">
          <div class="chart-wrapper chart-card">
            <div class="chart-title">
              <span >

                    HH with shelter affected per TA

              </span>
              <button
                class="reset small pull-right btn btn-sm mr-1"
                id="resetbarchart"
              >
                reset
              </button>
            </div>
            <div
              style="width: 100%;  height: 93%"
              class="px-4"
            >
              <div id="bar-chart" style="width: 100%"></div>
            </div>
          </div>
        </div>
        <div class="col-xl-4 col-md-6 m-0 p-1">
          <div class="chart-wrapper chart-card">
            <div class="chart-title">
              Map -
              <span>
               <i>
                    HH with shelter affected by disasters</i
                  >
              </span>
              <!-- <button class="reset small pull-right btn btn-sm mr-1"  @click="resetAll" id="resetmapchart">reset</button> -->
            </div>
            <div id="map-chart"></div>
          </div>
        </div>
      </div>

      <div class="row mx-0 px-0">
        <div class="col-8 mx-0 px-0">
          <h4 style="color:font-weight:bold mx-0 px-0">
            <br />
            <h3 class="font-weight-light text-justify m-0">
              Please note that boundaries and names shown and the designations
              used on this map do not imply official endorsement or acceptance
              by the United Nations.
            </h3>
            <br />

            <h3>
              <b>KEY</b>
            </h3>

            <b>HH = Household</b>
            <br />
            <i class="huma huma-person-2" style="font-size: 20pt !important"></i
            >= <b>Male or Male headed household</b> depending on where its
            placed
            <br />
            <i
              class="huma huma-person-1"
              style="font-size: 20pt !important"
            ></i>
            =
            <b>Female or Female headed</b> household depending where its placed
            <br />
            <br />
            <b class="text-warning">
              For more details of the icons used on this page please go to
              <a href="https://www.unocha.org/story/iconography-part-un%E2%80%99s-humanitarian-efforts-ocha-releases-new-humanitarian-icons" target="_blank">UN OCHA Website</a>
            </b>
          </h4>
        </div>
        <div
          class="col-4 text-right"
          style="float: right !important; margin-top: 18%"
        >
          <img
            src="../../../assets/logo.png"
            style="width: 80px"
            alt="Malawi govt Logo"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import flatPicker from "vue-flatpickr-component";
import gridLoader from "vue-spinner/src/GridLoader";
import "flatpickr/dist/flatpickr.css";
import components from "../../components/infographics/dashboard";
import RouteBreadCrumb from "@/components/Breadcrumb/RouteBreadcrumb";
import BaseHeader from "@/components/BaseHeader";
import StatsCard from "@/components/Cards/StatsCard";
import { Select, Option } from "element-ui";
import TagsInput from "@/components/Inputs/TagsInput";
import jspdf from "jspdf";
import domtoimage from "dom-to-image";
import { Colors } from "../../components/infographics/Colors";

import "leaflet";

import { dc, markerChart, d3 } from "dc.leaflet";
import "dc.leaflet/web/js/colorbrewer";

import "dc.leaflet/web/js/leaflet.markercluster";
import crossfilter from "crossfilter2";
window.jQuery = require("jquery");

import districtsLatlongData from "../../../data/latlondistricts.json";

import utils from "../../../util/dashboard";
import { generateExcel, generateTAGVHExcel } from "../../../util/generateExcel";

import { mapGetters, mapActions } from "vuex";
import moment from "moment";
import vSpinner from "v-spinner";
var groupname = "marker-select";
vSpinner();

var dcDateFilterHandler = {};

export default {
  props: ["dinrs", "dras", "flatData"],
  components: {
    gridLoader,
    flatPicker,
    StatsCard,
    BaseHeader,
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    TagsInput,
  },
  data() {
    return {
      loading: true,
      fullrange: "",
      isInline: false,
      dates: { start: "", end: "" },
    };
  },

  async mounted() {
    this.show();
    setTimeout(() => {
      this.dcCharts(this.flatData);
    }, 2000);
  },

  methods: {
    resetAll() {
      //chart.filterAll();
      dcDateFilterHandler.filter(null);
      this.dates = { start: "", end: "" };
      //chart.filterAll(group);
      dc.redrawAll(groupname);
    },
    filterByDate() {
      let date_end = new Date(
        moment(this.dates.end, "DD-MM-YYYY")
      );
      let date_start = new Date(
        moment(this.dates.start, "DD-MM-YYYY")
      );
      if (
        date_start < date_end
      ) {
        dcDateFilterHandler.filter(null);
        dcDateFilterHandler.filter([date_start, date_end]);
        dc.redrawAll(groupname);
      } else {
        alert("Date range should be filled in valid range");
      }
    },

    ...mapActions("dinrs", {
      getDinrsAction: "get",
    }),
    ...mapActions("dras", {
      getDrasAction: "get",
    }),
    async printImage() {
      let exportsButtons = document.getElementsByClassName("exports");
      let buttons = document.getElementsByTagName("button");
      let resetButtons = document.querySelectorAll('[type="reset"]');
      let dateRange = document.getElementsByClassName("datarange");
      let x = this.dates.start;
      let y = this.dates.end;

      if (x == "" || y == "") {
        dateRange = document.getElementsByClassName("datarange");

        await utils.Visibility.hide([
          ...exportsButtons,
          ...buttons,
          ...resetButtons,
          ...dateRange,
        ]);
      } else {
        await utils.Visibility.hide([
          ...exportsButtons,
          ...buttons,
          ...resetButtons,
        ]);
      }
      let dataUrl = await utils.Screenshot.take(this.$refs.printMe);
      utils.download(dataUrl);
      utils.Visibility.show([
        ...dateRange,
        ...exportsButtons,
        ...buttons,
        ...resetButtons,
      ]);
    },

    async exportToPDF() {
      let exportsButtons = document.getElementsByClassName("exports");
      let buttons = document.getElementsByTagName("button");
      let resetButtons = document.querySelectorAll('[type="reset"]');
      let dateRange = document.getElementsByClassName("datarange");
      let x = this.dates.start;
      let y = this.dates.end;

      if (x == "" || y == "") {
        dateRange = document.getElementsByClassName("datarange");

        await utils.Visibility.hide([
          ...exportsButtons,
          ...buttons,
          ...resetButtons,
          ...dateRange,
        ]);
      } else {
        dateRange = document.getElementsByClassName("datarange");
        await utils.Visibility.hide([
          ...exportsButtons,
          ...buttons,
          ...resetButtons,
          ...dateRange,
        ]);
      }

      domtoimage.toPng(await this.$refs.printMe).then(function (dataUrl) {
        var img = new Image();

        img.src = dataUrl;

        const doc = new jspdf({
          orientation: "portrait",
        });

        doc.addImage(img, "JPEG", 0, 15, 210, 265);

        if (x != "" || y != "") {
          doc.text(2, 7, "DRMIS Dashboard From (" + x + " To " + y + ")");
        } else {
          doc.text(2, 9, "DRMIS Dashboard");
        }

        const date = new Date();
        const filename =
          "DRMIS_dashboard" +
          date.getFullYear() +
          ("0" + (date.getMonth() + 1)).slice(-2) +
          ("0" + date.getDate()).slice(-2) +
          ("0" + date.getHours()).slice(-2) +
          ("0" + date.getMinutes()).slice(-2) +
          ("0" + date.getSeconds()).slice(-2) +
          ".pdf";
        doc.save(filename);

        utils.Visibility.show([
          ...dateRange,
          ...exportsButtons,
          ...buttons,
          ...resetButtons,
        ]);
      });
    },

    show() {
      this.spinner = this.loader || this.$spinner({ el: this.$refs.printMe });
      this.spinner.show();
    },
    hide() {
      this.spinner.hide();
    },
    dcCharts(data) {
      try {
        var xf = crossfilter(data);
        //let all = xf.groupAll();
        let deadPeople = xf
          .groupAll()
          .reduceSum((d) => (d.dead_females || 0) + (d.dead_males || 0));
        let injuredPeople = xf
          .groupAll()
          .reduceSum((d) => (d.injured_females || 0) + (d.injured_males || 0));
        let hhWithoutShelter = xf
          .groupAll()
          .reduceSum((d) => d.total_without_shelter_hh);
        let hhTotal = xf
          .groupAll()
          .reduceSum((d) => d.total_without_shelter_hh || 0);
        let foodlostHH = xf
          .groupAll()
          .reduceSum((d) => (d.foodlost_males || 0) + (d.foodlost_males || 0));
        var dateData = xf.dimension(dc.pluck("created_on"));

        dc.numberDisplay("#people-dead", groupname)
          .group(deadPeople)
          .valueAccessor((d) => {
            return d;
          })
          .formatNumber(d3.format(","))
          .render();

        dcDateFilterHandler = xf.dimension(function (d) {
          return d.created_on;
        });

        dc.numberDisplay("#people-injured", groupname)
          .group(injuredPeople)
          .valueAccessor((d) => {
            return d;
          })
          .formatNumber(d3.format(","));

        dc.numberDisplay("#people-affected", groupname)
          //.formatNumber(d3.format("d"))
          .group(foodlostHH)
          .valueAccessor((d) => {
            return d;
          })
          .formatNumber(d3.format(","));

        dc.numberDisplay("#hh-without-shelter", groupname)

          .group(hhWithoutShelter)
          .valueAccessor((d) => {
            return d;
          })
          .formatNumber(d3.format(","))
          .render();
        //.render();

        var totalHH = dc
          .numberDisplay("#hh-total-affected", groupname)
          .group(hhTotal)
          .valueAccessor((d) => {
            return d;
          })
          .formatNumber(d3.format(","))
          .render();

        dc.numberDisplay("#start-year", groupname)
          .group(this.fake_groupAll(dateData, "created_on", "bottom"))
          .formatNumber(d3.format(""))
          .valueAccessor((x) => x)
          .render();

        dc.numberDisplay("#end-year", groupname)
          .group(this.fake_groupAll(dateData, "created_on", "top"))
          .formatNumber(d3.format(""))
          .valueAccessor((x) => x)
          .render();

        var disasterDimension = xf.dimension(function (d) {
          return d.disaster;
        });
        
        var disasterDimension2 = xf.dimension(function (d) {
          return d.disaster;
        });
        var totalHHByDisasterTypeGroup = disasterDimension2
          .group()
          .reduceSum(function (d) {
            return d.total_without_shelter_hh;
          });
        var disasterGroup = xf.dimension(d => d.location.city).group().reduce(
          function (p, v) {
            // add
            p.data = v;
            p.geo = [v.location.lat, v.location.lng];
            ++p.count;
            return p;
          },
          function (p, v) {
            // remove
            --p.count;
            return p;
          },
          function () {
            // init
            return {
              count: 0,
            };
          }
        );
        var map = markerChart("#map-chart", groupname)
          .dimension(xf.dimension(d => d.location.city))
          .group(disasterGroup);

        
       
        map = components.mapOptions(map, data, 6, [-13.5, 35]);

        var districtGroup = xf.dimension((d) => d.location.city)
          .group()
          .reduceSum((d) => d.total_without_shelter_hh);

        var districtDimension3 = xf.dimension((d) => d.location.city);

        var districtGroup3 = districtDimension3
          .group()
          .reduceSum((d) => d.total_without_shelter_hh);

        var self = this;
        var row = new dc.CboxMenu("#row-chart", groupname);
        row
          .dimension(districtDimension3)
          .group(districtGroup3)
          .title(function (p) {
            return p.key;
          })

          .multiple(true);

        /*############## HORIZONTAL BAR CHART #############*/
        var chart = new dc.RowChart("#bar-chart", groupname)

          .x(d3.scaleLinear().domain(xf.dimension((d) => d.location.city)))
          .dimension(xf.dimension((d) => d.location.city))
          .group(districtGroup);

        var chart = components.barOptions(
          dc,
          chart.gap(8),
          xf.dimension((d) => d.location.city)
        );

        var xDateDimension = xf.dimension((d) => [d.disaster, d.created_on]);
       
        var minDate = d3.min(xf.all(), function (d) {
          return d.created_on;
        });
        var maxDate = d3.max(xf.all(), function (d) {
          return d.created_on;
        });

        var series = new dc.SeriesChart("#series-chart", groupname);
        series
          .dimension(
            xf.dimension((d) => {
              return d3.timeMonth(d.created_on);
            })
          )
          .group(
            xf
              .dimension((d) => d3.timeMonth(d.created_on))
              .group()
              .reduceSum((item) => {
                //console.log(item, "jjjjjjjjjjjjjjjjjjjjjjjjjjj");
                return item.total_without_shelter_hh;
              })
          )
          .x(d3.scaleTime().domain([minDate, maxDate]));

        series = components.lineOptions(d3, dc, series);

        var pie = dc
          .pieChart("#pie-chart", groupname)
          .dimension(disasterDimension2)
          .group(totalHHByDisasterTypeGroup)
          .on("pretransition", (chart) => {
            ///d3.select("svg").remove();
            var svg = d3.select("#pie-chart").select("svg");
            svg

              .append("text") // appent the "text" element
              .text(function () {
                document.getElementById("hh-total").innerHTML =
                  "HH affected";
              });
            svg
              .append("text") // appent the "text" element
              .text(function () {
                document.getElementById("hh-total-affected");
              });
             d3.selectAll(".tick").attr("color","black")
          });
        pie = components.pieOptions(pie, dc);

        dc.renderAll(groupname);

        dc.redrawAll();

        /*######## function to add x-axis label for rowChart #########*/
        function AddXAxis(chartToUpdate, displayText) {
          var textSelection = chartToUpdate
            .svg()
            .append("text")
            .attr("class", "x-axis-label")
            .attr("text-anchor", "middle")
            .attr("x", chartToUpdate.width() / 2)
            .attr("y", chartToUpdate.height() - 0.5)
            .text(displayText);
        }
        AddXAxis(chart, "HH affected");

        this.setResetChart("resetall", dc, groupname);
        this.setResetChart("resetbarchart", chart, groupname);
        this.setResetChart("resetmapchart", map, groupname);
        this.setResetChart("resetserieschart", series, groupname);
        this.setResetChart("resetpiechart", pie, groupname);
        this.hide();
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    fake_groupAll(dim, field, method) {
      return {
        value: function () {
          //get year
          return dim[method](1)[0]
            ? dim[method](1)[0][field].getFullYear()
            : "";
        },
      };
    },
    setResetChart(id, chart, group) {
      d3.select(`#${id}`).on("click", function () {
        chart.filterAll();
        if (id == "resetall") {
          chart.filterAll(group);
        }
        dc.redrawAll(group);
      });
    },
    getSum(total, num) {
      return total + parseFloat(num ? num : 0);
    },
  },
};
</script>

<style >
.dc-cbox-item-li {
  display: table;
  vertical-align: middle;
  color: white;
  border: 1px solid #2ec3a3;
  background: #2ec3a3 !important;
  width: 100%;
  padding-bottom: 5px;
}

.reset.small {
  background-color: #db6400;
  color: whitesmoke;
  margin-top: -1%;
}

#buble-chart,
#bar-chart,
#pie-chart,
#map-chart {
  height: 93%;
}
.dc-chart circle.dot {
  stroke-width: 9;
  stroke-opacity: 1 !important;
}
.dc-chart circle.dot:hover {
  stroke: #2ec3a3 !important;
  stroke-width: 9;
  stroke-opacity: 1 !important;
}
#buble-chart .x.axis text {
  text-anchor: end !important;
  transform: rotate(-45deg);
}

#pie-chart {
  padding-top: 1%;
}

.pie {
  margin-left: 30px;
}

.dc-chart li {
  float: left;
}

.dc-cbox-group {
  list-style-type: none;
}

.dc-cbox-item {
  width: 9em;
  white-space: nowrap;
}

.dc-cbox-group input[type="checkbox"],
.dc-cbox-group input[type="radio"] {
  margin: 0 4px 0 4px;
  transform: scale(1.5);
}
.chart-wrapper> .number-display {
  font-weight: bold !important;
  padding: 20px 20px 10px;
  font-family: "Calibri" !important;
  float: right; 
  }
.icon {
    padding:10px 10px 10px 10px; 
    float:left;
}

@import "../../../../node_modules/dc.leaflet/web/css/leaflet.css";
@import "../../../../node_modules/dc.leaflet/web/css/MarkerCluster.Default.css";
@import "../../../../node_modules/dc.leaflet/web/css/MarkerCluster.css";
@import "../../../../node_modules/dc.leaflet/web/css/dc.css";
@import "../../../../node_modules/dc.leaflet/web/css/leaflet-legend.css";
@import "../../../assets/stylenew.css";
@import "../../../assets/fonts/font-humanitarian.css";

input{
  color:#9696a3;
  border: #9696a3 solid 1px;
  background:#FFF;
  font-family: "Open Sans", sans-serif;
  font-weight: 500;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
}

input:hover{
  color:black;
  cursor: pointer;
}

li.dc-cbox-item > label {
  margin-left: 6px !important;
} 


</style>
