<template>
  <div>
    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Edit</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">Preliminary Disaster Reports</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">Edit</li>
            </ol>
          </nav>
        </div>
        <div class="col-lg-6 col-5 text-right"></div>
      </div>
    </base-header>
    <div class="container-fluid mt--6">
      <div class="row justify-content-center">
        <div class="col-lg-8 card-wrapper">
          <!-- Grid system -->
          <card class="card">
            <!-- Card header -->
            <form
              v-for="(item, index) in impact"
              :key="index"
              class="needs-validation"
              @submit.prevent="validate"
            >
              <h3 slot="header" class="mb-2" v-if="index == 0">
                Edit Preliminary Disaster Report
              </h3>

              <strong v-if="impact.length > 1"> GVH #{{ index + 1 }}</strong>
              <div class="row">
                <div class="col-lg-12">
                  <p class="mb-0 text-bold"><b>Disaster Details</b></p>
                </div>
              </div>

              <div class="form-row">
                <div class="col-md-4">
                  <base-input
                    label="Date of Occurence"
                    disabled
                    placeholder="Date of Occurence"
                    v-model="item.Date"
                  ></base-input>
                </div>

                <div class="col-md-4">
                  <base-input
                    label="TA Affected"
                    placeholder="TA Affected"
                    disabled
                    v-model="item.TA"
                  ></base-input>
                </div>

                <div class="col-md-4">
                  <base-input
                    label="GVH Affected"
                    placeholder="TA Affected"
                    disabled
                    v-model="item.gvh.name"
                  >
                  </base-input>
                </div>
              </div>

              <div class="row">
                <div class="col-lg-12">
                  <p class="mb-0 text-bold"><b>Injuries/Deaths/Missing</b></p>
                </div>
              </div>

              <div class="form-row">
                <div class="col-md-4">
                  <base-input
                    label="People Dead"
                    placeholder="People Dead"
                    type="number"
                    v-model="item.dead"
                  ></base-input>
                </div>
                <div class="col-md-4">
                  <base-input
                    label="People Missing"
                    placeholder="People Missing"
                    v-model="item.missing"
                    type="number"
                  ></base-input>
                </div>

                <div class="col-md-4">
                  <base-input
                    label="People Injured"
                    placeholder="People Injured"
                    v-model="item.injured"
                    type="number"
                  ></base-input>
                </div>
              </div>

              <div class="row">
                <div class="col-lg-12">
                  <p class="mb-0 text-bold"><b>Other Details</b></p>
                </div>
              </div>

              <div class="form-row">
                <div class="col-md-4">
                  <base-input
                    label="People disabled"
                    placeholder="People disabled"
                    type="number"
                    v-model="item.disabled"
                  ></base-input>
                </div>
                <div class="col-md-4">
                  <base-input
                    label="Pregnant women"
                    placeholder="Pregnant women"
                    type="number"
                    v-model="item.pregnant"
                  ></base-input>
                </div>

                <div class="col-md-4">
                  <base-input
                    label="Lactacting women"
                    placeholder="Lactacting women"
                    type="number"
                    v-model="item.lactactingwomen"
                  ></base-input>
                </div>

                <div class="col-md-4">
                  <base-input
                    label="Underfive"
                    placeholder="Underfive"
                    type="number"
                    v-model="item.underfive"
                  ></base-input>
                </div>
                <div class="col-md-4">
                  <base-input
                    label="MHH"
                    placeholder="MHH"
                    v-model="item.mhh"
                    type="number"
                  ></base-input>
                </div>

                <div class="col-md-4">
                  <base-input
                    label="FHH"
                    placeholder="FHH"
                    v-model="item.fhh"
                    type="number"
                  ></base-input>
                </div>
              </div>

              <base-button
                type="primary"
                native-type="submit"
                v-if="index == impact.length - 1"
                @click="updateData(item, impact)"
                >Update</base-button
              >
            </form>
          </card>
        </div>
      </div>
    </div>
  </div>
</template>

<script src="./prelim.data.edit.js" />
