<template>
  <div>
    <h2>ENVIRONMENT</h2>
    <h3>
      *
      <small>Hint &nbsp;</small>
      <b
        ><font color="primary"
          >(HH: Households, FHH : Female Headed Households, MHH : Male Headed
          Households)</font
        ></b
      >
    </h3>
    <hr class="mt-3 mb-3" />
    <h2>
      <b class="alert-suc">Impact on environment</b>
    </h2>
    <form>
      <div
        class="row row-example"
        v-for="(value, index) in impact_on_environment"
        v-bind:key="index"
      >
        <div class="col-md">
          <base-input label="Please choose a question">
            <select
              class="form-control"
              v-model="value.impact_question"
              data-toggle="tooltip"
              data-placement="top"
              title="Choose an impact question"
            >
              <option
                v-for="question in questions"
                :value="question.name"
                :key="question"
                >{{ question.name }}</option
              >
            </select>
          </base-input>
        </div>
        <div class="row pt-5">
          <div class="col-md">
            <base-radio
              name="yes"
              class="mb-3"
              v-model="value.response"
              data-toggle="tooltip"
              data-placement="top"
              title="Yes"
              v-bind:value="'yes'"
              :disabled="
                value.impact_question == null || value.impact_question === ''
              "
              >Yes</base-radio
            >
          </div>

          <div class="col-md">
            <base-radio
              name="no"
              class="mb-3"
              v-model="value.response"
              data-toggle="tooltip"
              data-placement="top"
              title="No"
              v-bind:value="'no'"
              :disabled="
                value.impact_question == null || value.impact_question === ''
              "
              >No</base-radio
            >
          </div>
        </div>

        <div class="col-md pr-5 pt-5">
          <base-button
            size="sm"
            type="warning"
            data-toggle="tooltip"
            data-placement="top"
            title="Delete impact on environment"
            class="btn-icon-only rounded-circle noprint"
            @click="
              removeItemRow(
                'impact_on_environment',
                impact_on_environment,
                questions,
                index,
                'impact_question'
              )
            "
            >X</base-button
          >
        </div>
      </div>
    </form>
    <base-button
      size="md"
      type="info"
      @click="
        addItemRow(
          'impact_on_environment',
          impact_on_environment,
          questions,
          'impact_question'
        )
      "
      class="btn-icon-only rounded-circle noprint"
      data-toggle="tooltip"
      data-placement="top"
      title="Add impact on environment"
    >
      <i class="ni ni-fat-add"></i>
    </base-button>

    <hr />
    <b>Response Needed for the Environment Cluster</b>
    <hr class="mt-3 mb-3" />
    <base-input label="General Response needed for Environment cluster">
      <textarea
        class="form-control"
        id="exampleFormControlTextarea3"
        data-toggle="tooltip"
        data-placement="top"
        title="Type the general response needed"
        placeholder="Type the response needed for the Environment cluster"
        v-model="response_needed"
        rows="3"
      ></textarea>
    </base-input>
    <base-input label="Urgent Response needed for Environment cluster">
      <textarea
        class="form-control"
        id="exampleFormControlTextarea3"
        data-toggle="tooltip"
        data-placement="top"
        title="Type the urgent response neeeded"
        placeholder="Type the urgent response needed for the Environment cluster"
        v-model="urgent_response_needed"
        rows="3"
      ></textarea>
    </base-input>
    <base-button
      size="lg"
      @click.stop="save"
      data-toggle="tooltip"
      data-placement="top"
      title="Save and goto next section"
      type="primary"
      class="noprint"
    >
      Save & Continue
    </base-button>
  </div>
</template>
<script src="./index.js"/>
