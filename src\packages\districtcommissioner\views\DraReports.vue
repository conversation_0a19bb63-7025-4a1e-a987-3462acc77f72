<template>
  <div class="content">
    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7" v-if="isDataFilled">
          <h6 class="h2 d-inline-block mb-0">{{draData[0].Disaster}}</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <a href="#">REPORTS</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">{{draData[0].District}}</li>
            </ol>
          </nav>
        </div>
        <div class="col-lg-6 col-5 text-right"></div>
      </div>
      <!-- Card stats -->
    </base-header>
    <div class="container-fluid mt--6">
      <div>
        <card class="no-border-card" body-classes="px-0 pb-1" footer-classes="pb-2">
          <template slot="header">
            <h3 class="mb-0">Disaster TA Reports</h3>
          </template>
          <div>
            <div class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap">
              <el-select
                class="select-primary pagination-select"
                v-model="pagination.perPage"
                placeholder="Per page"
              >
                <el-option
                  class="select-primary"
                  v-for="item in pagination.perPageOptions"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>

              <div>
                <base-input
                  v-model="searchQuery"
                  prepend-icon="fas fa-search"
                  placeholder="Search..."
                ></base-input>
              </div>
            </div>
            <el-table
              :data="queriedData"
              row-key="id"
              header-row-class-name="thead-light"
              @sort-change="sortChange"
            >
              <el-table-column
                v-for="column in tableColumns"
                :key="column.label"
                v-bind="column"
                id="reportForm"
              ></el-table-column>
              <el-table-column min-width="180px" align="right" label="Actions">
                <div slot-scope="{$index, row}" class="d-flex">
                  <base-button
                    @click.native="handleInfographics(row)"
                    class="edit"
                    type="primary"
                    size="sm"
                    icon
                  >
                    <i class="text-white ni ni-chart-bar-32"></i>
                  </base-button>
                </div>
              </el-table-column>
            </el-table>
          </div>
          <div
            slot="footer"
            class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
          >
            <div class>
              <p class="card-category">
                Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
                <span
                  v-if="selectedRows.length"
                >&nbsp; &nbsp; {{selectedRows.length}} rows selected</span>
              </p>
            </div>
            <base-pagination
              class="pagination-no-border"
              v-model="pagination.currentPage"
              :per-page="pagination.perPage"
              :total="total"
            ></base-pagination>
          </div>
        </card>
      </div>
    </div>
  </div>
</template>
<script>
import { Table, TableColumn, Select, Option } from "element-ui";
import RouteBreadCrumb from "@/components/Breadcrumb/RouteBreadcrumb";
import { BasePagination } from "@/components";
import clientPaginationMixin from "@/components/PaginatedTables/clientPaginationMixin";
import swal from "sweetalert2";
import { MongoReports } from "../api/MongoReports";

export default {
  mixins: [clientPaginationMixin],
  components: {
    BasePagination,
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn
  },
  data() {
    return {
      isDataFilled: false,
      propsToSearch: ["TA", "GVHS_affected"],
      tableColumns: [
        {
          prop: "TA",
          label: "TA",
          minWidth: 100,
          sortable: true
        },
        {
          prop: "GVHS_affected",
          label: "GVHs",
          minWidth: 200,
          sortable: true
        },
        {
          prop: "Villages",
          label: "Villages",
          minWidth: 250,
          sortable: true
        }
      ],
      tableData: [],
      selectedRows: []
    };
  },
  methods: {
    handleInfographics(row) {
      this.$router.push({ path: "/districtcommissioner/infographics/" + row.uuid });
    }
  },
  mounted() {
    MongoReports.getDras(this.$route.params.uuid).then(response => {
      this.draData = response.data;
      this.isDataFilled = true;

      for (let i = 0; i < this.draData.length; i++) {
        let dra = this.draData[i];
        var villages = "";
        var gvhs = "";
        for (let i = 0; i < dra.gvhs.length; i++) {
          gvhs += dra.gvhs[i].name + ",";
        }

        for (let i = 0; i < dra.villages.length; i++) {
          villages += dra.villages[i].name + ",";
        }

       

        this.tableData.push({
          uuid: dra._id,
          TA: dra.admin3.admin3Name_en,
          GVHS_affected: gvhs,
          Villages: villages
        });
      }
    });
  }
};
</script>
<style>
.no-border-card .card-footer {
  border-top: 0;
}
</style>
