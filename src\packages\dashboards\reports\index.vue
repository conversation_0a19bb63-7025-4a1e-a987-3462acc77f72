<template>
  <div class="no-padding no-margin">
    <div class="container-fluid row p-4">
      <div class="col-6">
        <b-form-radio-group v-model="checked" state="checked" :options="switchOptions" name="radio-validation">
        </b-form-radio-group>
      </div>

      <div class="col-6 pull-right text-right">

        <base-dropdown title-classes="btn btn-warning mx-1 py-2 btn-xl" menu-on-right :has-toggle="false">
          <a slot="title" class="dropdown-toggle" type="neutral" size="sm">
            <i class="text-black ni ni-cloud-download-95"></i> EXPORT ALL TO EXCEL
          </a>
          <a class="dropdown-item" @click="generateColorCodeSheet('TA')">All TA-Based data</a>
          <a class="dropdown-item" @click="generateColorCodeSheet('GVH')">All GVH-based data</a>
          <!-- <a class="dropdown-item" @click="generateColorCodeSheet('EXCEL_data')">All Disaster Summary</a> -->
        </base-dropdown>

        <!-- <base-dropdown  title-classes="btn btn-primary mx-1 py-2 btn-xl" menu-on-right
          :has-toggle="false">
          <a slot="title" class="dropdown-toggle" type="neutral" size="sm">
            <i class="text-black ni ni-cloud-download-95"></i> EXPORT TO CSV
          </a>
          <a class="dropdown-item" @click="generateColorCodeSheet('CSV_data')">All Disaster Summary</a>
        </base-dropdown> -->
      </div>
      </label>
    </div>
    <card header-classes="bg-transparent p-0 m-0">
      <div v-show="checked === 'list'">
        <div>
          <div class="
              col-12
              d-flex
              justify-content-center justify-content-sm-between
              flex-wrap
            ">
            <el-select class="select-primary pagination-select" v-model="pagination.perPage" placeholder="Per page">
              <el-option class="select-primary" v-for="item in pagination.perPageOptions" :key="item" :label="item"
                :value="item"></el-option>
            </el-select>

            <div>
              <base-input v-model="searchQuery" prepend-icon="fas fa-search" placeholder="Search..."></base-input>
            </div>
          </div>
          <div>
            <b-table responsive sticky-header :striped="striped" :bordered="bordered" :borderless="borderless"
              :outlined="outlined" :small="small" :hover="hover" :dark="dark" :sort-icon="true" :fixed="fixed"
              :foot-clone="footClone" :no-border-collapse="noCollapse" head-variant="light"
              :table-variant="tableVariant" :items="queriedData" :fields="tableColumns">
              <template #cell(actions)="row" tdClass="text-right">
                <b-button @click="review(row.item, row.index, $event.target)" class="edit bg-primary text-white "
                  type="primary" size="sm" icon>
                  <i class="ni ni-single-copy-04"></i> Detailed
                </b-button>
                <b-button @click="reviewSummary(row.item, row.index, $event.target)"
                  style="color: white; background: #ab6f00; border:#ab6f00;" class="edit text-white " type="primary"
                  size="sm" icon>
                  <i class="ni ni ni-badge"></i> Summary
                </b-button>
                <b-dropdown id="dropdown-grouped" size="sm" variant="outline-primary" text="Download" class="m-sm-0">
                  <b-dropdown-group id="dropdown-group-1" header="Exports"></b-dropdown-group>
                  <b-dropdown-item-button>
                    <a class="" @click="generateColorCodeSheetDisasterBased(row.item)"> Excel </a>
                  </b-dropdown-item-button>
                  <b-dropdown-item-button>
                    <a class="" :disabled="hasPictures(row.item, row.index)" @click="downloadDpictures(row.item, row.index, $event.target)">{{ buttonText }}</a>
                  </b-dropdown-item-button>
                </b-dropdown-group>
                  <b-dropdown-divider></b-dropdown-divider>
                  <b-dropdown-group id="dropdown-group-2" header="Reports">
                    <b-dropdown-item-button>
                      <a class="" @click="generateReport(row.item)"> Detailed </a>
                    </b-dropdown-item-button>
                    <b-dropdown-item-button> 
                      <a class="" @click="generateSummaryReport(row.item)"> Summary </a> 
                    </b-dropdown-item-button>
                  </b-dropdown-group>
                </b-dropdown>


              </template>
            </b-table>
          </div>


        </div>

      </div>
      <div slot="footer" class="
              col-12
              d-flex
              justify-content-center justify-content-sm-between
              flex-wrap
            ">
        <div class>
          <p class="card-category">
            Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
            <span v-if="selectedRows.length">&nbsp; &nbsp; {{ selectedRows.length }} rows selected</span>
          </p>
        </div>
        <base-pagination class="pagination-no-border" v-model="pagination.currentPage" :per-page="pagination.perPage"
          :total="total"></base-pagination>
      </div>
      <div class="
          card-body
          row
          d-flex
          justify-content-between
          p-0
          m-0
          no-padding no-margin
        ">
        <div v-show="checked === 'tiles'" class="col-2 border m-3 p-0" v-for="(row, index) in rows" :key="index">
          <div class="col-12 text-center">
            <div class="row p-1">
              <div class="imagebut col-12 pt-3">
                <img class="image" src="/img/theme/file-pic.gif" style="opacity: 0.6" />
                <div class="viewreports pt-3">
                  <base-button @click="review(row.disasterId)" class="edit-1 mt-3 mr-2" type="primary" size="sm">
                    Detailed</base-button>
                  <base-button class="edit-1 mt-3 ml-2" style="color: white; background: #bf6c00; border: #bf6c00"
                    size="sm" @click="reviewSummary(row.disasterId)">Summary</base-button>
                </div>
              </div>

              <div class="col-12 text-uppercase font-weight-bolder">
                <b>
                  <b>{{ row.district }}</b>
                </b>
              </div>
              <div class="col-12 font-weight-bold">{{ row.disaster }}</div>
              <div class="col-12 pb-3">{{ formatDate(row.created_on) }}</div>
            </div>
          </div>
        </div>
      </div>
    </card>
    <b-modal scrollable hide-backdrop size="xl" class="modal-lg" v-model="modalShow" hide-footer="true">
      <Review v-bind:data="id"></Review>
    </b-modal>
    <b-modal scrollable hide-backdrop size="xl" v-model="modalShow1" hide-footer="true">
      <Summary v-bind:data="id"></Summary>
    </b-modal>
  </div>
</template>

<script>
import { Table, TableColumn, Select, Option } from "element-ui";
import { BasePagination } from "@/components";
import clientPaginationMixin from "@/components/PaginatedTables/clientPaginationMixin";
import _ from "underscore";
import moment from "moment";
import { BModal, VBModal } from "bootstrap-vue";
import Review from "../components/review.vue";
import Summary from "../components/reviewSummary";
import utils from "../../../util/dashboard";
import { flatten } from "../../../store/flatdinrs/flatten";
import { admin2s } from "../../manager/api/location/admin2s";
import { generateFlatSectorData, processGVHExcel, exportAllDisasterData  } from "../../../util/generateFlatSectorData";
import EventBus from "../../../helpers/event-bus"
import {
  generateExcel,
  generateTAGVHExcel,
  generateDisasterSummaryExcel
} from "../../../util/generateExcel";
import { generateCSV } from "../../../util/generateCSV";
import {
  mapGetters,
  mapActions
} from "vuex";
export default {
  mixins: [clientPaginationMixin],
  components: {
    BasePagination,
    Review,
    Summary,
    [TableColumn.name]: TableColumn,
    [Select.name]: Select,
    [Option.name]: Option,
    [Table.name]: Table
  },
  props: ["dinrs", "dras", "flatData"],

  data() {
    return {
      propsToSearch: ["disaster", "district", "officer", "date"],
      id: "",
      checked: "list",
      modalShow: false,
      modalShow1: false,
      isLoaded: false,
      loading: true,
      rows: [],
      gridOptions: {},
      switchOptions: [
        { text: "Table", value: "list" },
        { text: "Tiles", value: "tiles" }
      ],
      tableColumns: [
        {
          key: "id",
          label: "ID",
          sortable: true
        },
        {
          key: "disaster",
          label: "Disaster",
          sortable: true
        },
        {
          key: "district",
          label: "District",
          sortable: true
        },
        {
          key: "officer",
          label: "Officer",
          sortable: true
        },
        {
          key: "date",
          label: "Submmited on",
          sortable: true
        },
        {
          key: "actions",
          label: "Actions"
        }
      ],
      tableData: [],
      selectedRows: [],
      allforms: [],
      districtsCopy: [],
      downloadData: [],
      download: [],
      villagesArray: [],
      campsArray: [],
      testarray: [],
      TaData: [],
      gvhDATA: [],
      admin2sData: [],

      AllDisasterSummaryExcel: [],
    };
  },
  computed: {
    ...mapGetters({
      getDinrs: "dinrs/get",
      getDraById: "dras/getById",
      getDinrById: "dinrs/getById"
    }),
    ...mapActions({
      loadFlatdinr: "loadFlatdinrs"
    })
  },

  methods: {
    // review(id) {
    //   this.$router.push({
    //     path: "/manager/detailsreport/" + id,
    //   });
    // },


    generateReport (index) {
      this.modalShow = !this.modalShow;
      this.id = index.uuid;

      setTimeout(()=>{
        EventBus.$emit("printPDF",this)
      },5000)
      
      
    },

    generateSummaryReport (index) {
      this.modalShow1 = !this.modalShow1;
      this.id = index.uuid;

      setTimeout(()=>{
        EventBus.$emit("printSummaryPDF",this)
      },5000)     
    },

    hasPictures(index) {
      let response = this.getDinrById(index.uuid);

      if (
        response.disasterImages == "undefined" ||
        response.disasterImages == null
      ) {
        this.buttonText = "No Photos";
        return true;
      }

      this.buttonText = "Photos";
      return false;
    },

    downloadDpictures(form) {
      const resource = process.env.VUE_APP_ENGINE_URL + "/forms/dinr";
      const url = `${resource}/${form.uuid}/archives`;
      axios
        .get(url, {
          responseType: "blob",
          onDownloadProgress: e => {
            console.log(e);
          }
        })
        .then(response => {
          const blob = new Blob([response.data]);
          const a = document.createElement("a");
          a.href = window.URL.createObjectURL(blob);
          a.download = `${form.disaster.split(" ").join("_")}_${moment(
            form.dodFrom
          ).format("DDMMYYYY")}.zip`;

          a.click();
        });
    },
    generateColorCodeSheet(type) {
      exportAllDisasterData(type, this.download, this.admin2sData, this.gvhDATA, this.allforms);
    },
    generateColorCodeSheetDisasterBased(row){
      exportAllDisasterData("TA", this.download.filter(x=>x.all.dinrFormId==row.uuid), this.admin2sData, this.gvhDATA.filter(x=>x.DistasterID==row.uuid), this.allforms.filter(x=>x._id==row.uuid));
    },
    ...mapActions("dinrs", {
      getDinrsAction: "get"
    }),
    ...mapActions("dras", {
      getDrasAction: "get"
    }),

    prepareData(downloadPayload, lists) {
      let data = JSON.parse(JSON.stringify(downloadPayload));

      lists.forEach((element, index) => {
        let header = element.header;
        data.forEach((el, i) => {
          delete el[header];
        });
      });

      return data;
    },

    async downloadExcel(index) {
      this.id = index.uuid;
      let data = (this.rows);
      let selectedRow = data.filter(x => x.disasterId === index.uuid)

      utils.generateExcel(selectedRow);

    },

    review(index) {
      this.modalShow = !this.modalShow;
      this.id = index.uuid;
    },
    reviewSummary(index) {
      this.modalShow1 = !this.modalShow1;
      this.id = index.uuid;
    },
    formatDate(date) {
      return moment(date).format("DD/MM/YYYY");
    }
  },

  async mounted() {

    let data = this.flatData;
    data.sort(function compare(a, b) {
      var dateA = new Date(a.created_on);
      var dateB = new Date(b.created_on);
      return dateB - dateA;
    });




    let dinrs = [...(await this.getDinrsAction())];
    let dras = [...(await this.getDrasAction())];

    let district = this.$session.get("user").admin2_name_en;

    //let DINRData = await dinrs;
    let DRASData = dras;
    let DINRData = dinrs.filter(x =>
      x.district ? x.district.admin2_name_en.includes(district) : false
    );

    this.dinrDataa = DINRData
    

    this.allforms = [...(await dinrs)];


    this.allforms.forEach((dinr, index) => {
      var draarr = [];
      DRASData.forEach((dra, point) => {
        if (dinr._id == dra.dinrFormId) {
          draarr.push({ ...dra });
        }
        dinr.dra = [...draarr];
      });
    });

    DINRData.sort(function (a, b) {
      return new Date(b.createdon) - new Date(a.createdon);
    });
    DINRData.forEach((dinr, index) => {

      
      let row = dinr._id;

      this.tableData.push({
        id: index + 1,
        disaster: dinr.disaster,
        district: dinr.district.admin2_name_en,
        date: this.formatDate(dinr.createdon),
        officer: dinr.account.firstName + " " + dinr.account.lastName,
        uuid: dinr._id
      });

      let dinformDataset = {
        disaster: dinr.disaster,
        district: dinr.district.admin2_name_en,
        date: this.formatedDate(dinr.createdon),
        doaAcpc: this.formatedDate(dinr.doaAcpc),
        doaDcpc: this.formatedDate(dinr.doaDcpc),
        dodFrom: this.formatedDate(dinr.dodFrom),
        dodTo: this.formatedDate(dinr.dodTo),
        officer:
          dinr.account.firstName +
          " " +
          dinr.account.lastName,
        uuid: dinr._id,
        country: "MALAWI",
        country_pcode: "MWI"
        //status:  DINRData[index].status
      };

    

      let dra = dras.filter(dra => dra.dinrFormId == row);

      this.draFormsData = [];

      this.downloadData.dinrform = {};

      dra.forEach((item, index, array) => {
        generateFlatSectorData(item, dinformDataset, this.downloadData, this.villagesArray, this.campsArray, this.download)
        //this.processExcel(item, dinformDataset);
      });
    });




    processGVHExcel(this.download, this.gvhDATA);

    admin2s.get().then(response => {
      
      let district = this.$session.get("user").admin2_name_en;
      this.admin2sData = response.data.filter(x=>x.admin2_name_en==district);
    });




    this.rows = data;
    this.isLoaded = false;
    this.loading = false;






  }
};
</script>

<style>
.no-border-card .card-footer {
  border-top: 0;
}

.imagebut {
  position: relative;
  width: 50%;
}

.image {
  opacity: 1;
  display: block;
  width: 100%;
  height: auto;
  transition: 0.5s ease;
  backface-visibility: hidden;
}

.viewreports {
  transition: 0.5s ease;
  opacity: 0;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
}

.imagebut:hover .image {
  opacity: 0.3;
}

.imagebut:hover .viewreports {
  opacity: 2;
}

.edit-1 {
  font-size: 15px;
  padding: 10px 20px;
}

.switch {
  margin-left: 20px;
}

input {
  opacity: 1;
}

.dc-cbox-item-li{
   background:white !important;
   border:none;
}

.modal-dialog {
  max-width: 80%;
  margin-left: 15%;
  z-index: 1000;
  height: 100vh;
  display: flex;
}
</style>
