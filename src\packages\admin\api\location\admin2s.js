import axios from 'axios';

const resource = process.env.VUE_APP_ENGINE_URL + '/admin-2s';

export class admin2s {

  static get(admin2Pcode = null) {
    if (admin2Pcode == null) {
      return axios.get(resource).then(response => { return response });
    }
    else {
      return axios.get(resource + '/' + admin2Pcode).then(response => { return response });
    }

  }
  static count() {
    return axios.get(resource + '/count').then(response => { return response });
  }
  static async create(data) {
    let response = await axios.post(resource, data).then(response => { return response });
    return response;
  }
  static async remove(admin2Pcode) {
    let response = await axios.delete(resource + '/' + admin2Pcode).then(response => { return response });
    return response;
  }
  static update(data) {
    return axios.patch(resource + '/' + data.admin2Pcode, data).then(response => { return response });
  }

}
