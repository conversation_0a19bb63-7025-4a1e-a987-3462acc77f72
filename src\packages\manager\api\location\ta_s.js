import axios from 'axios';

const resource = process.env.VUE_APP_ENGINE_URL + '/admin-3S';

export class ta_s {

  static get(admin3Pcode = null, admin2Pcode = null) {
    if (admin3Pcode == null) {
      if (admin2Pcode == null) {
        return axios.get(resource).then(response => { return response });
      } else {
        return axios.get(resource + '?filter[where][admin2Pcode]=' + admin2Pcode).then(response => { return response });
      }
    }
    else {
      return axios.get(resource + '/' + admin3Pcode).then(response => { return response });
    }

  }
  static async create(data) {
    let response = await axios.post(resource, data).then(response => { return response });
    return response;
  }
  static async remove(admin3Pcode) {
    let response = await axios.delete(resource + '/' + admin3Pcode).then(response => { return response });
    return response;
  }
  static update(data) {
    return axios.patch(resource + '/' + data.admin3Pcode, data).then(response => { return response });
  }

}
