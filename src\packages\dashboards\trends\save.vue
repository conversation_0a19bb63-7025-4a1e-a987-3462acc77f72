<template>
  <div
    ref="printOverview"
    class="row"
    style="margin-right: 1px; margin-left: 1px"
  >
    <div class="row p-0 m-0" style="width: 100%">
      <div class="col-12 pb-1 pl-0 pr-0">
        <li class="list-group-item">
          <div class="row">
            <div class="col-8 text-left">
              <h3 class="font-weight-light text-justify m-0">
                Please note that boundaries and names shown and the designations
                used on this map do not imply official endorsement or acceptance
                by the United Nations.
              </h3>
            </div>
            <div class="text-right col-4">
              <button
                class="reset btn btn-warning btn-sm mx-1"
                id="charts-resetall-o"
                ref="overviewResetAll"
                @click="resetAll"
              >
                Reset all
              </button>
              <button
                class="reset btn btn-primary btn-sm pull-right mx-1"
                @click="printImage()"
              >
                Screenshot
              </button>
            </div>
          </div>
          <div class="row date-filters row mt-4">
            <div class="col-3">
              <span>
                DATE RANGE
              </span>
              <!-- <span>
                Date From :
                <input
                  type="date"
                  ref="datefrom"
                  style="margin-left:15px;margin-right:5px;border:1px solid #008080;border-radius:4px;"
                />
              </span>

              <span>
                Date To :
                <input
                  type="date"
                  ref="dateto"
                  style="margin-left:15px;border:1px solid #008080;border-radius:4px;"
                />
              </span> -->
              <span ref="overviewDateRange">
                <base-input addon-left-icon="ni ni-calendar-grid-58">
                  <flat-picker
                    @on-close="ChangedDateRange"
                    :config="{ allowInput: true, mode: 'range' }"
                    class="form-control datepicker"
                    v-model="dates.range"
                  >
                  </flat-picker>
                </base-input>
              </span>

              <!-- <select
                class="btn btn-outline-secondary border p-2 text-left overview-date-filter-buttons-container-c"
              >
                <option selected>DATE CATEGORIES [Created on]</option>
              </select>
              <span style="color:black!important;font-size:18pt;font-weight:bold" class="mx-1">
                <span id="overview-start-c"></span> -
                <span id="overview-end-c"></span>
              </span>-->
            </div>
            <div class="col-4">
              <span>TA DISASTER DATE</span>
              <span id="identifier-overview"></span>
            </div>
          </div>
        </li>
      </div>
    </div>
    <div style="width:100%;border:1px solid red;" id="spenderContainer">
        <div id="test"></div>
    </div>
    <div class="col-12">
      <div class="col-8">
        <h4 style="color:font-weight:bold">
          <br />
          <br />

          <h3>
            <b>KEY</b>
          </h3>

          <b>HH = Household</b>
          <br />
          <i class="huma huma-person-2" style="font-size: 20pt !important"></i>=
          <b>Male or Male headed household</b> depending on where its placed
          <br />
          <i class="huma huma-person-1" style="font-size: 20pt !important"></i>
          =
          <b>Female or Female headed</b> household depending where its placed
          <br />
          <br />
          <b class="text-warning">
            For more details of the icons used on this page please go to
            <a href="https://www.unocha.org/">UN OCHA Website</a>
          </b>
        </h4>
      </div>
      <div
        class="col-4 text-right"
        style="float: right !important; margin-top: -6%"
      >
        <img
          src="../../../assets/logo.png"
          style="width: 80px"
          alt="Malawi govt Logo"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapGetters } from "vuex";
import * as d3 from "d3";
import * as dc from "dc";
import crossfilter from "crossfilter2";
import components from "../../components/infographics/dashboard";
import utils from "../../../util/dashboard";
import resize from "../../../util/dc-resizing";
var $ = require("jquery");
import flatPicker from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
var malawiAdmin2GeoJson = require("../../../data/malawi.features");
//var malawiAdmin2TopoJson = require("../../../data/admin2.topojson");
var malawiLakeGeoJson = require("../../../data/malawi-lake.features");
var malawDistrictsLatLng = require("../../../data/latlondistricts");
var _ = require("lodash");
var dcDateFilterHandler = {};
var checkboxDimesions = {};
let group = "chartsgroup";

export default {
  components: { flatPicker },
  props: ["dinrs", "dras", "flatData"],
  data() {
    return {
      barWidth: 765,
      barHeight: 381,
      fullrange: "",
      isInline: false,
      dates: { range: "2018-07-17 to 2018-07-19" },
      checkboxes: {
        balaka: false,
        chikwawa: false,
        mangochi: false,
        phalombe: true
      },

      dinrsData: [],
      drasData: [],
      flatRecords: []
    };
  },
  computed: {
    ...mapGetters({
      getDinrs: "getDinrs",
      getDras: "getDras",
      getFlatdinrs: "getFlatdinrs"
    }),
    updatedata() {
      var d = this.getFlatdinrs;
      alert("update");
    }
  },
  methods: {
    ...mapActions("dims", {
      addWidth: "addWidth"
    }),
    ...mapActions("dinrs", {
      loadDinrs: "get"
    }),
    ...mapActions("dras", {
      loadDras: "get"
    }),

    ...mapActions("flatdras", {
      loadFlatdras: "loadFlatdras"
    }),
    resetAll() {
      //chart.filterAll();
      dcDateFilterHandler.filter(null);
      checkboxDimesions.filter(null);
      this.dates.range = this.fullrange;
      //chart.filterAll(group);
      dc.redrawAll(group);
    },
    formatDate(date) {
      var d = new Date(date),
        month = "" + (d.getMonth() + 1),
        day = "" + d.getDate(),
        year = d.getFullYear();

      if (month.length < 2) month = "0" + month;
      if (day.length < 2) day = "0" + day;
      return [year, month, day].join("-");
    },
    ChangedDateRange() {
      dcDateFilterHandler.filter(null);
      dcDateFilterHandler.filter(null);

      var ranges = this.$refs.overviewDateRange
        .getElementsByClassName("datepicker")[0]
        .value.split("to");

      // var dateFrom =
      //   refs.datefrom.value === "" ? bottomDate : refs.datefrom.value;
      dcDateFilterHandler.filter([new Date(ranges[0]), new Date(ranges[1])]);
      dc.redrawAll(group);
    },
    getScreenSize() {
      let width = this.$refs.box.clientWidth;
      let height = this.$refs.box.clientHeight;
      this.addWidth(width);

      //console.log(this.$refs.printOverview.clientWidth, "box");
      return [width, height];
    },
    async printImage() {
      let exportsButtons = document.getElementsByClassName("exports");
      let buttons = document.getElementsByTagName("button");
      let resetButtons = document.querySelectorAll('[type="reset"]');
      await utils.Visibility.hide([
        ...exportsButtons,
        ...buttons,
        ...resetButtons
      ]);
      let dataUrl = await utils.Screenshot.take(this.$refs.printOverview);
      utils.download(dataUrl);
      utils.Visibility.show([...exportsButtons, ...buttons, ...resetButtons]);
    },
    getChartWidth(percentage, screenWidth) {
      return screenWidth * percentage;
    },
    calculateChartWidth(percentage) {
      let screenSize = this.getScreenSize()[0];
      return this.getChartWidth(percentage, screenSize);
    },
    dcCharts(data) {
      let self = this;

      var xf = crossfilter(data);

      var numberFormat = d3.format(",");

      checkboxDimesions = xf.dimension(function(d) {
        ///console.log(d.district);
        return d.ta;
      });

      var checkBoxGroup = checkboxDimesions.group();
      new dc.CboxMenu("#cbox1", group)
        .width(this.calculateChartWidth(16.7))
        .dimension(checkboxDimesions)
        .group(checkBoxGroup)
        .multiple(true)
        .controlsUseVisibility(true);
      //904
      //console.log(136, this.calculateChartWidth(16.7), "CBoxMenu");

      dc.numberDisplay("#withoutShelter", group)
        .group(
          xf
            .dimension(r => r)
            .group()
            .reduceSum(d => d.total_without_shelter_hh)
        )
        .formatNumber(numberFormat);

      dc.numberDisplay("#totalDead", group)
        .group(
          xf
            .dimension(r => r)
            .group()
            .reduceSum(d => d.dead_females + d.dead_males)
        )
        .formatNumber(numberFormat);

      dc.numberDisplay("#totalInjured", group)
        .group(
          xf
            .dimension(r => r)
            .group()
            .reduceSum(d => d.injured_females + d.injured_males)
        )
        .formatNumber(numberFormat);

      dc.numberDisplay("#totalDisplaced", group)
        .group(
          xf
            .dimension(r => r)
            .group()
            .reduceSum(d => d.displaced_fhh + d.displaced_mhh)
        )
        .formatNumber(numberFormat);

      let disasterDimension = xf.dimension(function(d) {
        return d.ta;
      });

      var disasterGroupMap = disasterDimension.group().reduce(
        function(p, v) {
          // add
          p.data = v;
          p.geo = [v.location.lat, v.location.lng];
          ++p.count;
          return p;
        },
        function(p, v) {
          // remove
          --p.count;
          return p;
        },
        function() {
          // init
          return {
            count: 0
          };
        }
      );

      var pieChartDimesions = xf.dimension(function(d) {
        return d.disaster;
      });
      var pieChartGroup = pieChartDimesions
        .group()
        .reduceSum(d => d.total_without_shelter_hh);
      const pie = dc.pieChart("#chart1", group);

      let tileboxHeight = document.getElementById("tilebox").clientHeight;

     

      document
        .getElementById("piebox")
        .setAttribute("style", "display:block;width:" + tileboxHeight + "px");
      document.getElementById("piebox").style.width = tileboxHeight + "px";

      //console.log(this.calculateChartWidth(0.28075052), "Pie Chart");

      let pieWidth = this.calculateChartWidth(0.28075052);
      let pieRadius = pieWidth * 0.74257426;
      let pieInnerRadius = pieWidth * 0.17326733;
      //let pieHeight = pieWidth * 0.63366337;
      let pieHeight = pieWidth * 0.6496939;
      //248
      let totalAmount = 0;
      pie
        //.width(pieWidth - percentpie)
        .width(pieWidth)
        .height(pieHeight)
        .radius(pieRadius)
        .innerRadius(pieInnerRadius)
        .dimension(pieChartDimesions)
        .group(pieChartGroup)
        .colorAccessor(d => {
          return d.key;
        })
        .ordinalColors(["#ff9f4a"])

        .on("filtered", function(chart, filter) {
          chart.selectAll("image").remove();
          var sel = filter;
          let percentage = 0,
            value = 0;
          let disastersBuffer = [];
          totalAmount = 0;

          pie.selectAll("text.pie-slice").text(d => {
            percentage = dc.utils.printSingleValue(
              ((d.endAngle - d.startAngle) / (2 * Math.PI)) * 100
            );
            disastersBuffer.push({
              ...d.data,
              percentage
            });
            totalAmount += parseFloat(d.data.value);
          });

          filterPiechart(sel, percentage, totalAmount, disastersBuffer, value);
          if (totalAmount === 1) {
            svg.selectAll("image").remove();
          }
        })
        .on("renderlet", chart => {
          if (!chart.selectAll("g.selected")._groups[0].length) {
            chart.filter(null);
            filterPiechart("", 100, totalAmount, [], 0);
          }
          var image_width = 24;
          var image_height = 24;

          chart.selectAll("image").remove();
          chart
            .select("svg")
            .attr("class", "donutclass")
            .attr("transform", "translate(-35, 0)");
          chart
            .select("svg")
            .style("padding", "0px")
            .style("margin", "auto");
          chart
            .select("svg")
            .attr("class", "donutclass")
            .attr("transform", "translate(-30, 10)");
          chart
            .select("svg")
            .style("padding", "0px")
            .style("margin", "auto");
          chart
            .select("svg")
            .select("g")
            .style("padding", "0px");
          //.attr("transform", "translate(200, 160)");
          let iRadius = pieWidth * 0.24752475;
          let oRadius = pieWidth * 0.2970297;

          chart
            .select("g.pie-slice-group")
            .selectAll("g.pie-slice")
            .append("svg:image")
            .attr("transform", function(d) {
              var arc = d3
                .arc()
                .innerRadius(iRadius)
                .outerRadius(oRadius)
                .startAngle(d.startAngle)
                .endAngle(d.endAngle);

              var x = arc.centroid(d)[0] - 10;
              var y = arc.centroid(d)[1] - 10;

              return "translate(" + x + "," + y + ")";
            })
            .attr("xlink:href", function(d) {
              if (d.data.value > 0) {
                var name = d.data.key;
                return "img/disasters/" + name + ".svg";
              }
            })
            .attr("width", image_width)
            .attr("height", image_height);
        })
        .addFilterHandler(function(filters, filter) {
          filters.length = 0; // empty the array
          filters.push(filter);
          return filters;
        });
      new dc.NumberDisplay("#chart1-count", group)
        .group(
          xf
            .dimension(r => r)
            .group()
            .reduceSum(d => d.total_without_shelter_hh)
        )
        .formatNumber(numberFormat);
      new dc.NumberDisplay("#chart2-count", group)
        .group(
          xf
            .dimension(r => r)
            .group()
            .reduceSum(d => d.total_without_shelter_hh)
        )
        .formatNumber(numberFormat);
      new dc.NumberDisplay("#chart3-count", group)
        .group(
          xf
            .dimension(r => r)
            .group()
            .reduceSum(d => d.total_without_shelter_hh)
        )
        .formatNumber(numberFormat);
      function filterPiechart(
        sel,
        percentage,
        totalAmount,
        disastersBuffer,
        value
      ) {
        var svg = pie.select("svg");
        if (sel && totalAmount > 1) {
          percentage = disastersBuffer.find(i => i.key == sel).percentage;
          value = disastersBuffer.find(i => i.key == sel).value;
        } else if (totalAmount > 1) {
          sel = "HH";
          value = totalAmount;
          percentage = 100;
        } else {
          sel = "HH";
          value = 0;
          percentage = 0;
        }

        pie.selectAll(".piechart-center-text").remove();
        let newY = pieHeight - 30;
        svg
          .append("svg")
          .attr("width", pieWidth)
          .attr("height", newY)
          .append("g")

          .attr("transform", "translate(" + pieWidth / 2 + "," + newY / 2 + ")")
          .append("text")
          .classed("piechart-center-text font-weight-bold", true)
          .attr("text-anchor", "middle")
          .text(numberFormat(value))
          .attr("font-size", "1.5em");

        newY = pieHeight;

        svg
          .append("svg")
          .attr("width", pieWidth)
          .attr("height", newY)
          .append("g")
          .attr("transform", "translate(" + pieWidth / 2 + "," + newY / 2 + ")")
          .append("text")
          .classed("piechart-center-text font-weight-bold", true)
          .attr("text-anchor", "middle")
          .text(sel);

        newY = pieHeight + 30;

        svg
          .append("svg")
          .attr("width", pieWidth)
          .attr("height", newY)
          .append("g")
          .attr("transform", "translate(" + pieWidth / 2 + "," + newY / 2 + ")")
          .append("text")
          .classed("piechart-center-text font-weight-bold", true)
          .attr("text-anchor", "middle")
          .text(percentage + "%");
      }

      var identifierDimension = xf.dimension(function(d) {
        return d.identifier.split(" ")[2];
      });

      new dc.SelectMenu("#overview-identifier-c", group)
        .dimension(identifierDimension)
        .group(identifierDimension.group())
        .promptText("DATE FROM")
        .on("postRender", function() {
          d3.select(".dc-select-menu").attr(
            "class",
            "btn btn-outline-secondary border p-2 mr-4 text-left"
          );
        });
      // var overviewChart = new dc.SeriesChart("#overview-identifier-c2");
      //         overviewChart
      //     .width(768)
      //     .height(100)
      //     .transitionDuration(0)
      //     .chart(function(c) { return new dc.LineChart(c).curve(d3.curveCardinal); })
      //     .x(d3.scaleLinear().domain([0,20]))
      //     .brushOn(true)
      //     .xAxisLabel("Run")
      //     .clipPadding(10)
      //     .dimension(runDimension)
      //     .group(runGroup)
      //     .seriesAccessor(function(d) {return "Expt: " + d.key[0];})
      //     .keyAccessor(function(d) {return +d.key[1];})
      //     .valueAccessor(function(d) {return +d.value - 500;});

      var dropdowns = d3
        .select(".overview-date-filter-buttons-container-c")
        .selectAll("option")
        .data([
          "This Week",
          "Last Week",
          "This Month",
          "Last Month",
          "Last 2 Months",
          "Last 3 Months",
          "Last 6 Months",
          "This Year",
          "Last Year",
          "Last 2 Years",
          "Last 3 Years",
          "Last 5 Years",
          "Last 10 Years"
        ]);

      dropdowns = dropdowns
        .enter()
        .append("option")
        .attr("class", "dc-select-option");

      // fill the buttons with the year from the data assigned to them
      dropdowns.each(function(d) {
        this.innerHTML = d;
      });
      dcDateFilterHandler = xf.dimension(function(d) {
        return d.created_on;
      });
      // dropdowns.on("click", dropdownsHandler);
      var refs = this.$refs;
      var maxdate = dcDateFilterHandler.top(1)[0].created_on;
      var bottomDate = dcDateFilterHandler.bottom(1)[0].created_on;
      // this.refs.daterange.addEventLister()
      // this.$refs.dateto.addEventListener(
      //   "input",
      //   function () {
      //     dcDateFilterHandler.filter(null);
      //     var dateFrom =
      //       refs.datefrom.value === "" ? bottomDate : refs.datefrom.value;
      //     dcDateFilterHandler.filter([
      //       new Date(dateFrom),
      //       new Date(refs.dateto.value),
      //     ]);
      //     dc.redrawAll(group);
      //   },
      //   false
      // );
      dcDateFilterHandler = xf.dimension(function(d) {
        return d.created_on;
      });

      function dropdownsHandler() {
        // our year will this.innerText

        let dateCategoryProperties = self.dateCategories[this.innerText];
        dateRangeHandler(
          dateCategoryProperties.type,
          dateCategoryProperties.number
        );

        function dateRangeHandler(type, number) {
          self.dateRangeMethods[type](number);
        }

        let startDateArray = $("#overview-start-c")
          .text()
          .split("/");
        let endDateArray = $("#overview-end-c")
          .text()
          .split("/");
        var start =
          startDateArray[2] + "/" + startDateArray[1] + "/" + startDateArray[0];
        var end =
          endDateArray[2] + "/" + endDateArray[1] + "/" + endDateArray[0];

        dcDateFilterHandler.filter(null);
        dcDateFilterHandler.filter([new Date(start), new Date(end)]);
        dc.redrawAll(group);
      }
      ///
      var identifierDimension = xf.dimension(function(d) {
        return d.identifier;
      });

      new dc.SelectMenu("#identifier-overview", group)
        .dimension(identifierDimension)
        .group(identifierDimension.group())
        .promptText("SELECT")
        .on("postRender", function() {
          d3.select(".dc-select-menu").attr(
            "class",
            "btn btn-outline-secondary border p-2 mr-4 text-left"
          );
        });

      let barchart = new dc.BarChart("#chart2", group)
        .dimension(
          xf.dimension(function(d) {
            return d.ta;
          })
        )
        .group(
          xf
            .dimension(function(d) {
              return d.ta;
            })
            .group()
            .reduceSum(item => item.total_without_shelter_hh)
        );

      //console.log(765, this.calculateChartWidth(0.56150104), "Barchart");
      let barWidth = this.calculateChartWidth(0.56150104);
      let barHeight = barWidth * 0.40777546;
      let difference = barWidth * 0.03712871;
      barchart
        //.width(difference > 200 ? difference:barWidth)
        .width(barWidth - difference)
        .height(barHeight)
        .on("renderlet", chart => {
          chart.selectAll("rect").attr("rx", "7");
          chart.selectAll("text.barLabel").attr("fill", "white");
          chart
            .selectAll("path.domain")
            .attr("display", "none")
            .attr("fill", "white");
          chart
            .selectAll("g.x")
            .selectAll("line")
            .attr("display", "none");
          chart
            .selectAll("g.y")
            .selectAll("line")
            .attr("display", "none");
        })
        .gap(50)
        .x(d3.scaleOrdinal().domain([...new Set(data.map(x => x.ta))]))
        .xUnits(dc.units.ordinal)
        .margins({
          top: 20,
          left: 30,
          right: 15,
          bottom: 30
        })
        .renderHorizontalGridLines(true)
        .transitionDuration(500)
        .renderLabel(true)
        .colorAccessor(d => {
          return d.key;
        })
        .ordinalColors(["#ff9f4a"])
        .label(function(d) {
          //console.log(d);
          return d.y;
        })
        .xAxisPadding(1)
        .ordering(function(d) {
          return d.value;
        })
        .elasticX(true)
        .yAxisLabel(" ")
        .yAxis()
        .ticks(5);

      let formatDate = d3.timeFormat("%b %Y");

      let withoutShelterHHDimension = xf.dimension(function(d) {
        return formatDate(d.created_on);
      });
      let withoutShelterHHGroup = withoutShelterHHDimension
        .group()
        .reduceSum(item => item.total_without_shelter_hh);

      var minDate = d3.min(xf.all(), function(d) {
        return d.created_on;
      });
      //console.log("min date y:", new Date(minDate).toISOString().slice(0, 10));
      var maxDate = d3.max(xf.all(), function(d) {
        return d.created_on;
      });
      //this.dates.range = new Date(minDate).toISOString().slice(0, 10) + " to " + new Date(maxDate).toISOString().slice(0, 10)
      var series = new dc.SeriesChart("#chart3", group);
      series
        .dimension(xf.dimension(d => d3.timeMonth(d.created_on)))
        .group(
          xf
            .dimension(d => d3.timeMonth(d.created_on))
            .group()
            .reduceSum(item => item.total_without_shelter_hh)
        )
        .x(d3.scaleTime().domain([minDate, maxDate]));

      //console.log(this.calculateChartWidth(0.84294649), "series");
      let seriesWidth = this.calculateChartWidth(0.84294649);
      let seriesHeight = seriesWidth * 0.28517186;
      let diffr = seriesWidth * 0.004712871;
      series
        .chart(function(c) {
          return new dc.LineChart(c);
        })
        .height(seriesHeight)
        .width(seriesWidth - diffr)
        .brushOn(false)
        .xAxisLabel("Month, Year")
        .yAxisLabel(" ")
        .clipPadding(10)
        .renderHorizontalGridLines(true)
        .seriesAccessor(function(d) {
          return d.key[0];
        })
        .keyAccessor(function(d) {
          return d.key;
        })
        .valueAccessor(function(d) {
          return d.value;
        })
        .turnOnControls(true)
        .colorAccessor(d => {
          return d.value;
        })
        .ordinalColors(["#ff9f4a"])
        .title(d => `${d.value} HH with shelter affected`);

      series.margins().left += 20;
      series.margins().bottom += 20;
      series.margins().top += 20;

      series.clipPadding(10).elasticY(true);

      series.xAxis().tickFormat(d3.timeFormat("%b,%Y"));

      //Map
      var minDate = d3.min(xf.all(), function(d) {
        return d.created_on;
      });
      //console.log("Min Date :", minDate);
      var maxDate = d3.max(xf.all(), function(d) {
        return d.created_on;
      });
      var map = new dc.GeoChoroplethChart("#map-chart", group);

      let diff = this.calculateChartWidth(0.28005559) * 0.16873449;
      let width = this.calculateChartWidth(0.28005559) - diff,
        height = 642;

      //console.log(350, this.calculateChartWidth(0.28005559), "Map");
      var projection = d3
        .geoMercator()
        .fitSize([width - 10, height - 10], malawiAdmin2GeoJson);

      map
        .projection(projection)
        .dimension(disasterDimension)
        .group(disasterGroupMap)
        .width(width)
        .height(height + 10)
        .transitionDuration(1000)
        .colorDomain([1, 100000])
        .colorCalculator(function(d) {
          return "white";
        })

        .overlayGeoJson(malawiAdmin2GeoJson.features, "districts", function(d) {
          return d.properties.ADMIN3;
        })
        .overlayGeoJson(malawiLakeGeoJson.features, "water", function(d) {
          return d.properties.ADMIN3;
        })
        .valueAccessor(d => {
          return d.value.data.total_without_shelter_hh;
        })
        .on("renderlet", function(chart) {
          d3.selectAll("g.water.malawi path")
            .attr("stroke", "#dbddde")
            .style("fill", function(d) {
              return "#409ffb";
            });

          d3.selectAll("#map-chart g.districts path").attr("stroke", "#777");

          var svg = chart.svg();

          let dim = chart._dimension.top(Infinity).map(val => {
            return {
              district: val.district,
              ta: val.ta,
              location: val.location,
              total_without_shelter_hh: val.total_without_shelter_hh,
              without_shelter_mhh: val.without_shelter_mhh,
              without_shelter_fhh: val.without_shelter_fhh
            };
          });

          let reducedDim = _(dim)
            .groupBy("district")
            .map((objs, key) => ({
              district: key,
              total_without_shelter_hh: _.sumBy(
                objs,
                "total_without_shelter_hh"
              ),
              without_shelter_mhh: _.sumBy(objs, "without_shelter_mhh"),
              without_shelter_fhh: _.sumBy(objs, "without_shelter_fhh")
            }))
            .value();

          var radius = d3
            .scaleSqrt()
            .domain([0, 100000])
            .range([0, 100]);

          let nodes = svg
            .append("g")
            .attr("class", "bubble")
            .selectAll("circle")
            .data(reducedDim)
            .enter()
            .append("circle")
            .attr("transform", function(d) {
              let loc = malawDistrictsLatLng.find(
                item => item.admin == d.district
              );
              let xY = projection([loc.lng, loc.lat]);
              return "translate(" + xY[0] + "," + xY[1] + ")";
            })
            .attr("r", function(d) {
              return radius(d.total_without_shelter_hh);
            })
            .style("fill", "#fdae6b")

            .append("svg:title")
            .text(function(d) {
              if (d)
                return (
                  d.district +
                  "\n" +
                  numberFormat(d.total_without_shelter_hh) +
                  " HH with shelter affected\nMHH: " +
                  numberFormat(d.without_shelter_mhh) +
                  ", FHH: " +
                  numberFormat(d.without_shelter_fhh)
                );
            });

          svg
            .append("g")
            .selectAll("text")
            .data(reducedDim)
            .enter()
            .append("text")
            .attr("transform", function(d) {
              let loc = malawDistrictsLatLng.find(
                item => item.admin == d.district
              );
              let xY = projection([loc.lng, loc.lat]);
              return "translate(" + xY[0] + "," + xY[1] + ")";
            })
            .text(function(d) {
              return d.district;
            })
            .attr("font-size", 14) //font size
            .attr("dx", -20) //positions text towards the left of the center of the circle
            .attr("dy", 4)
            .append("svg:title")
            .text(function(d) {
              if (d)
                return (
                  d.district +
                  "\n" +
                  numberFormat(d.total_without_shelter_hh) +
                  " HH with shelter affected\nMHH: " +
                  numberFormat(d.without_shelter_mhh) +
                  ", FHH: " +
                  numberFormat(d.without_shelter_fhh)
                );
            });

          var legend = svg
            .append("g")
            .attr("class", "legend")
            .attr("transform", "translate(" + (width - 50) + "," + 90 + ")")
            .selectAll("g")
            .data([0, 2000, 5000, 10000, 20000])
            .enter()
            .append("g");

          legend
            .append("circle")
            .attr("cy", function(d) {
              return -radius(d);
            })
            .attr("r", radius);

          legend
            .append("text")
            .attr("y", function(d) {
              return -2 * radius(d);
            })
            .attr("dy", "1.3em")
            .text(d3.format(".1s"));
        });

      map.title(function(d) {
        if (d.value && d.value.value)
          return (
            d.value.value.data.district +
            " has " +
            d.value.value.data.total_without_shelter_hh +
            " HH with shelter affected\nMHH: " +
            d.value.value.data.without_shelter_mhh +
            ", FHH: " +
            d.value.value.data.without_shelter_fhh +
            "\nCause: " +
            d.value.value.data.disaster
          );

        return d.key;
      });

      dc.renderAll(group);
      self.setResetChart(dc, group);
    },
    setResetChart(chart, groupname) {
      var refs = this.$refs;
      d3.select(`#charts-resetall-o`).on("click", function() {
        chart.filterAll();
        dcDateFilterHandler.filter(null);
        checkboxDimesions.filter(null);
        chart.filterAll(groupname);
        dc.redrawAll(groupname);
      });
    },

    getColor(d) {
      return d > 1000
        ? "#ff9e20"
        : d > 5000
        ? "#ffae4f"
        : d > 1000
        ? "#ffb663"
        : d > 500
        ? "#ffbd75"
        : d > 100
        ? "#ffc588"
        : d > 10
        ? "#ffcd9a"
        : d > 0
        ? "#ffd5ad"
        : "#f2efe9";
    }
  },
  mounted() {
   
var spenderRowChart = new dc.RowChart("#test");
    
var data1 = [
    {Name: 'last 24 Hours', Spent: 40, count: 20},
    {Name: 'last 7 day', Spent: 10, count: 40},
     {Name: 'last 30 day', Spent: 10, count: 40},
      {Name: 'last 90 days', Spent: 10, count: 40},
       {Name: 'last 12 Months', Spent: 10, count: 40},
    
];
var ndx = crossfilter(data1),
    yearDim  = ndx.dimension(function(d) {return +d.Year;}),
    spendDim = ndx.dimension(function(d) {return Math.floor(d.Spent/10);}),
    nameDim  = ndx.dimension(function(d) {return d.Name;}),
    spendPerYear = yearDim.group().reduceSum(function(d) {return +d.Spent;}),
    spendPerName = nameDim.group().reduceSum(d => d.count),
    spendHist    = spendDim.group().reduceCount();
     var width = document.querySelector("#spenderContainer").clientWidth -2
    spenderRowChart
        .width(width).height(400)
        .dimension(nameDim)
        .group(spendPerName)
        .elasticX(true);

    dc.renderAll();
    
  }
};
</script>

<style>
g.water.malawi path {
  fill: "#409ffb" !important;
}

.donutclass {
  z-index: 1000;
}

.darkcolor {
  background-color: #172b4d !important;
}

.dot {
  fill: #ff9f4a !important;
}

.line {
  stroke: #ff9f4a !important;
}

.dc-chart .pie-slice {
  cursor: pointer;
  stroke: #f0f0f0;
  stroke-width: 1 px;
}

.changecolor {
  stroke: #ccc !important;
}

.pie-label-group {
  display: none !important;
}

.dc-leaflet.leaflet-container {
  height: 840px !important;
  width: 100% !important;
}

.dc-chart th {
  text-align: left;
}

.dc-chart th,
.dc-chart td {
  padding-left: 0px;
}

.dc-chart li {
  width: 15rem;
}
#identifier-overview select {
  padding-top: 12px !important;
  padding-bottom: 12px !important;
}
.dc-cbox-group {
  list-style-type: none;
  margin-left: 0;
  max-width: 150px;
}

.dc-cbox-group label {
  display: inline-block;
}

.dc-cbox-group input[type="checkbox"],
.dc-cbox-group input[type="radio"] {
  margin: 0 5px 5px 5px;
}

li.dc-cbox-item {
  margin: 0;
  padding: 0;
  max-width: 140px;
}

ul.dc-cbox-group {
  margin: 4px !important;
  padding: 0 !important;
}

circle {
  fill-opacity: 0.7 !important;
  stroke-opacity: 0.7 !important;
  stroke: #ff9f4a !important;
}

circle:hover {
  fill-opacity: 0.8 !important;
  stroke-opacity: 0.8 !important;
  cursor: pointer;
}

text:hover {
  fill-opacity: 0.8 !important;
  stroke-opacity: 0.8 !important;
  cursor: pointer;
}

.legend circle {
  fill: none;
  stroke: #ccc;
}

.legend text {
  fill: #777;
  font: 10px sans-serif;
  text-anchor: middle;
}

text.district-name {
  fill: blue !important;
}
text:hover .district-name {
  fill: red;
}
@import "../../../assets/css/nucleo/dc.css";
@import "../../../assets/fonts/font-humanitarian.css";
@import "../../../assets/dc-resizing.css";
</style>
<style lang="sccs"></style>
