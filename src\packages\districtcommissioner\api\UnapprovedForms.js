import axios from "axios";

const resource = process.env.VUE_APP_ENGINE_URL + "/forms";

export class UnapprovedForms {
  static getDinrs() {
    return axios.get(resource + "/dinr").then(response => {
      return response;
    });
  }
  static getOneDinr(id) {
    //console.log(id)
    return axios.get(resource + "/dinr/" + id).then(response => {
      return response;
    });
  }
  static getDras(id) {
    return axios
      .post(resource + "/dra/query", { dinrFormId: id })
      .then(response => {
        return response;
      });
  }

  static update(data) {
    return axios.patch(resource + "/dinr/" + data._id, data).then(response => {
      return response;
    });
  }

  static updateDra(data) {
    return axios.patch(resource + "/dra/" + data._id, data).then(response => {
      return response;
    });
  }
}
