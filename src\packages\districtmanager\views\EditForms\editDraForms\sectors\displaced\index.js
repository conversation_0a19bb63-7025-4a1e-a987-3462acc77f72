import RouteBreadcrumb from '@/components/Breadcrumb/RouteBreadcrumb'
import BaseHeader from '@/components/BaseHeader'
import HtmlEditor from '@/components/Inputs/HtmlEditor'
import TagsInput from '@/components/Inputs/TagsInput'
import DropzoneFileUpload from '@/components/Inputs/DropzoneFileUpload'
import BaseSlider from '@/components/BaseSlider'
import { Select, Option } from 'element-ui'

import { draforms } from '../../../../../api/forms/draforms'

export default {
  components: {
    BaseHeader,
    RouteBreadcrumb,
    HtmlEditor,
    TagsInput,
    DropzoneFileUpload,
    BaseSlider,
    [Select.name]: Select,
    [Option.name]: Option,
    validator: 'new'
  },
  props: ['displaced', 'max'],
  data () {
    return {
      /*model definitions*/
      PeopleAffectedrows: [],
      hummanitarian_assistance: [{}],
      gvhdata: [],
      response_needed: '',
      dataset: {},
      valid: false,
      maxValue: this.max,
      labelValue: '',
      errorsDisplaced: false,
      errorsDisplaced2: false,
      displaced_households_accommodated: [],
      displaced_individuals_accommodated: [],
      displaced_disaggregated: [],
      number_displaced_by_gender_mhh: 0,
      number_displaced_by_gender_fhh: 0,
      displaced_females: 0,
      displaced_males: 0,
      accomodated_females_hh: 0,
      accomodated_males_hh: 0,
      accomodated_females: 0,
      accomodated_males: 0,

      /*Static datasets definitions*/
      structures: [
        { name: 'School' },
        { name: 'Church' },
        { name: 'Mosques' },
        { name: 'Evacuation Centers' },
        { name: 'Host Community' }
      ],
      peoples_vulnerable: [
        { name: 'Children (0 - 18 Years)' },
        { name: 'Adults (18 and over)' },
        { name: 'Elderly (65 Years and over)' },
        { name: 'Chronically ill' }
      ],
      peoples_disagregated: [
        { name: 'Children (0 - 18 Years)' },
        { name: 'Adults (18 and over)' },
        { name: 'Elderly (65 Years and over)' },
        { name: 'Chronically ill' }
      ]
    }
  },

  beforeMount () {
    //retrive component dataset from DB

    typeof this.displaced !== 'undefined' ? this.displaced : {}
    setInterval(this.autosave, 1200)

    this.TA = this.$session.get('TA')
    draforms.get(this.$route.params.id).then(response => {
      try {
        this.dataset = JSON.parse(localStorage.getItem('displaced').data)
      } catch (error) {
        this.dataset = this.displaced
      }

      var displacedCopy = this.dataset
      this.gvhdata = response.data.gvhs
      if (
        displacedCopy.PeopleAffectedrows === undefined ||
        displacedCopy.PeopleAffectedrows.length == 0
      ) {
        for (var data in this.gvhdata) {
          this.PeopleAffectedrows.push({
            name: this.gvhdata[data].name,
            number_displaced_by_gender_fhh: '',
            number_displaced_by_gender_mhh: ''
          })
        }
      } else {
        this.PeopleAffectedrows = displacedCopy.PeopleAffectedrows
      }

      this.displaced_households_accommodated =
        typeof displacedCopy.displaced_households_accommodated === 'undefined'
          ? this.displaced_households_accommodated
          : displacedCopy.displaced_households_accommodated

      this.hummanitarian_assistance =
        displacedCopy.hummanitarian_assistance.length === 0
          ? this.hummanitarian_assistance
          : displacedCopy.hummanitarian_assistance

      this.displaced_individuals_accommodated =
        typeof displacedCopy.displaced_individuals_accommodated.length ===
        'undefined'
          ? this.displaced_individuals_accommodated
          : displacedCopy.displaced_individuals_accommodated

      this.displaced_disaggregated =
        typeof displacedCopy.displaced_disaggregated === 'undefined'
          ? this.displaced_disaggregated
          : displacedCopy.displaced_disaggregated

      this.response_needed =
        typeof this.dataset.response_needed === 'undefined'
          ? this.response_needed
          : this.dataset.response_needed

      this.urgent_response_needed =
        typeof this.dataset.urgent_response_needed === 'undefined'
          ? this.urgent_response_needed
          : this.dataset.urgent_response_needed

      this.PeopleAffectedrows.length > 0
        ? this.init_totals(
            'number_displaced_by_gender_fhh',
            this.PeopleAffectedrows,
            'total_displaced_fhh'
          )
        : ''

      this.PeopleAffectedrows.length > 0
        ? this.init_totals(
            'number_displaced_by_gender_mhh',
            this.PeopleAffectedrows,
            'total_displaced_mhh'
          )
        : ''

      this.displaced_households_accommodated.length > 0
        ? this.init_totals(
            'accomodated_males_hh',
            this.displaced_households_accommodated,
            'accomodated_males_hh'
          )
        : ''
      this.displaced_households_accommodated.length > 0
        ? this.init_totals(
            'accomodated_females_hh',
            this.displaced_households_accommodated,
            'accomodated_females_hh'
          )
        : ''

      this.displaced_individuals_accommodated.length > 0
        ? this.init_totals(
            'accomodated_males',
            this.displaced_individuals_accommodated,
            'accomodated_males'
          )
        : ''
      this.displaced_individuals_accommodated.length > 0
        ? this.init_totals(
            'accomodated_females',
            this.displaced_individuals_accommodated,
            'accomodated_females'
          )
        : ''

      this.displaced_disaggregated.length > 0
        ? this.init_totals(
            'displaced_males',
            this.displaced_disaggregated,
            'displaced_males'
          )
        : ''

      this.displaced_disaggregated.length > 0
        ? this.init_totals(
            'displaced_females',
            this.displaced_disaggregated,
            'displaced_females'
          )
        : ''
    })
  },

  methods: {
    addItemRow (member, array_name, static_data = [], key_value) {
      //add html row
      this.$emit(
        'addItemRow',
        'displaced',
        member,
        array_name,
        static_data,
        key_value
      )
    },
    removeItemRow ( //add html row
      member,
      array_name,
      static_data = [],
      index,
      key_value,
      arrayTotals
    ) {
      this.$emit(
        'removeItemRow',
        'displaced',
        member,
        array_name,
        static_data,
        index,
        key_value
      )

      let arrayCopy = []
      for (let i = 0; i < arrayTotals.length; i++) {
        if (array_name == '') {
          let dynamic_element = arrayTotals[i]
          //alert(dynamic_element+"mia")
          arrayCopy.push({ dynamic_element: 0 })
          this.init_totals(arrayTotals[i], arrayCopy, arrayTotals[i])
        } else {
          this.init_totals(arrayTotals[i], array_name, arrayTotals[i])
        }
      }
    },
    init_totals (key, array_name, member) {
      //initialize total computations onload
      this[member] = array_name
        .map(function (item) {
          return parseInt(item[key]) ? parseInt(item[key]) : 0
        })
        .reduce((sum, value) => sum + value, 0)
    },
    save () {
      this.displaced.response_needed = this.response_needed
      this.displaced.urgent_response_needed = this.urgent_response_needed
      this.displaced.displaced_households_accommodated = this.displaced_households_accommodated.filter(
        value => Object.keys(value).length !== 0
      )
      this.displaced.displaced_disaggregated = this.displaced_disaggregated.filter(
        value => Object.keys(value).length !== 0
      )
      this.displaced.displaced_individuals_accommodated = this.displaced_individuals_accommodated.filter(
        value => Object.keys(value).length !== 0
      )
      this.displaced.PeopleAffectedrows = this.PeopleAffectedrows.filter(
        value => Object.keys(value).length !== 0
      )
      this.displaced.hummanitarian_assistance = this.hummanitarian_assistance.filter(
        value => Object.keys(value).length !== 0
      )
      this.$emit('save', this.displaced, 'displaced', this.errorsDisplaced)
    },

    autosave () {
      this.displaced.response_needed = this.response_needed
      this.displaced.urgent_response_needed = this.urgent_response_needed
      this.displaced.displaced_households_accommodated = this.displaced_households_accommodated.filter(
        value => Object.keys(value).length !== 0
      )
      this.displaced.displaced_disaggregated = this.displaced_disaggregated.filter(
        value => Object.keys(value).length !== 0
      )
      this.displaced.displaced_individuals_accommodated = this.displaced_individuals_accommodated.filter(
        value => Object.keys(value).length !== 0
      )
      this.displaced.PeopleAffectedrows = this.PeopleAffectedrows.filter(
        value => Object.keys(value).length !== 0
      )
      this.displaced.hummanitarian_assistance = this.hummanitarian_assistance.filter(
        value => Object.keys(value).length !== 0
      )
      this.$emit('autosave', this.displaced, 'displaced', this.errorsDisplaced)
    },

    testIfgreater (value) {
      if (
        this[value] > this.total_displaced_hh ||
        this.total_accomodated_hh > this.total_displaced_hh
      ) {
        this.errorsDisplaced = true
      } else {
        this.errorsDisplaced = false
      }
    },

    testIfgreater2 (value) {
      if (
        this[value] > this.total_displaced ||
        this.total_accomodated > this.total_displaced ||
        this.total_displaced_hh > this.total_displaced
      ) {
        this.errorsDisplaced2 = true
      } else {
        this.errorsDisplaced2 = false
      }
    }
  },

  computed: {
    total_displaced_males: {
      get () {
        return this.displaced_males
      },
      set (new_total) {
        this.displaced_males = new_total
      }
    },
    total_displaced_females: {
      get () {
        return this.displaced_females
      },
      set (new_total) {
        this.displaced_females = new_total
      }
    },
    total_displaced () {
      return this.total_displaced_females + this.total_displaced_males
    },
    total_accomodated_males: {
      get () {
        return this.accomodated_males
      },
      set (new_total) {
        this.accomodated_males = new_total
      }
    },
    total_accomodated_females: {
      get () {
        return this.accomodated_females
      },
      set (new_total) {
        this.accomodated_females = new_total
      }
    },
    total_accomodated () {
      let sum = this.total_accomodated_females + this.total_accomodated_males

      return sum
    },
    total_accomodated_males_hh: {
      get () {
        return this.accomodated_males_hh
      },
      set (new_total) {
        this.accomodated_males_hh = new_total
      }
    },
    total_accomodated_females_hh: {
      get () {
        return this.accomodated_females_hh
      },
      set (new_total) {
        this.accomodated_females_hh = new_total
      }
    },
    total_accomodated_hh () {
      return this.total_accomodated_females_hh + this.total_accomodated_males_hh
    },
    total_displaced_mhh: {
      get () {
        return this.number_displaced_by_gender_mhh
      },
      set (new_total) {
        this.number_displaced_by_gender_mhh = new_total
      }
    },
    total_displaced_fhh: {
      get () {
        return this.number_displaced_by_gender_fhh
      },
      set (new_total) {
        this.number_displaced_by_gender_fhh = new_total
      }
    },
    total_displaced_hh () {
      return this.total_displaced_fhh + this.total_displaced_mhh
    }
  }
}
