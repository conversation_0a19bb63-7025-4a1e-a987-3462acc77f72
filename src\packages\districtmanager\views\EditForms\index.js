import { dinrforms } from '../../api/forms/dinrforms'
import Swal from 'sweetalert2'
import moment from 'moment'
import swal from 'sweetalert2'
import Loading from 'vue-loading-overlay'
import 'vue-loading-overlay/dist/vue-loading.css'
import { Table, TableColumn, Select, Option } from 'element-ui'
import flatPicker from 'vue-flatpickr-component'
import 'flatpickr/dist/flatpickr.css'
import HtmlEditor from '@/components/Inputs/HtmlEditor'
import TagsInput from '@/components/Inputs/TagsInput'
import DropzoneFileUpload from '@/components/Inputs/DropzoneFileUpload'
import BaseSlider from '@/components/BaseSlider'
import RouteBreadCrumb from '@/components/Breadcrumb/RouteBreadcrumb'
import { BasePagination } from '@/components'
import clientPaginationMixin from '@/components/PaginatedTables/clientPaginationMixin'
import configs from './mixins/configs'
import dataformmatting from './mixins/dataFormatting'
import { draforms } from '../../api/forms/draforms'

export default {
  mixins: [clientPaginationMixin, configs, dataformmatting],
  components: {
    Loading,
    HtmlEditor,
    TagsInput,
    DropzoneFileUpload,
    BaseSlider,
    flatPicker,
    [Select.name]: Select,
    [Option.name]: Option,
    BasePagination,
    RouteBreadCrumb,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn
  },
  $_veeValidate: {
    validator: 'new'
  },
  data: () => ({
    uuid: null,
    max: '',
    status: false,
    dodFromMenu: false,
    dodToMenu: false,
    doaAcpcMenu: false,
    doaDcpcMenu: false,
    DisasterProfile: false,
    loading: false,
    formModel: {},
    draForms: [],
    disastername: null,
    roleData: [],
    loading: false,
    valid: true,
    basic: {
      headers: [
        { text: 'TA', value: 'ta' },
        { text: 'GVHs', value: 'gvhs' },
        { text: 'Villages', value: 'villages' },
        { text: 'Options', value: 'options' }
      ]
    },
    selectedRows: [],
    propsToSearch: ['ta', 'gvhs', 'villages'],
    dinrFormsData: [],
    tableColumns: [],
    tableData: []
  }),
  mounted () {
    this.maxDate()
    this.disasters.sort(this.dynamicSort('name'))
    this.$validator.localize('en', this.dictionary)
    this.uuid = this.$route.params.id

    dinrforms.get(this.uuid).then(response => {
      this.draForms = response.data.dra

      this.formModel = response
      this.formModel.doaDcpc = response.data.doaDcpc
      this.formModel.doaAcpc = response.data.doaAcpc
      this.formModel.dodTo = response.data.dodTo
      this.formModel.dodFrom = response.data.dodFrom
      this.disastername = response.data.disaster
      this.status = this.formModel.statusId == '1' ? true : false
    })
  },
  methods: {
    controlDateValue () {
      let isafter =
        moment(this.formModel.dodFrom).format('YYYY-MM-DD') >
        moment(this.formModel.dodTo).format('YYYY-MM-DD')
      if (isafter == true) {
        this.formModel.dodTo = this.formModel.dodFrom
        Swal.fire({
          title: 'Date Range Error',
          text: 'Start date should be less or equal to end date',
          type: 'error',
          toast: true,
          animation: false,
          position: 'top',
          timer: 4000,
          showConfirmButton: false
        })
      }
    },

    controlDCPCDateValue () {
      let isafter =
        moment(this.formModel.doaDcpc).format('YYYY-MM-DD') <
        moment(this.formModel.dodTo).format('YYYY-MM-DD')
      if (isafter == true) {
        this.formModel.doaDcpc = this.formModel.dodTo
        Swal.fire({
          title: 'Date Range Error',
          text: 'DCPC should be greater or equal to end date',
          type: 'error',
          toast: true,
          animation: false,
          position: 'top',
          timer: 4000,
          showConfirmButton: false
        })
      }
    },
    controlACPCDateValue () {
      let isafter =
        moment(this.formModel.doaAcpc).format('YYYY-MM-DD') <
        moment(this.formModel.dodTo).format('YYYY-MM-DD')
      if (isafter == true) {
        this.formModel.doaAcpc = this.formModel.dodTo
        Swal.fire({
          title: 'Date Range Error',
          text: 'ACPC should be greater or equal to end date',
          type: 'error',
          toast: true,
          animation: false,
          position: 'top',
          timer: 4000,
          showConfirmButton: false
        })
      }
    },
    handleEdit (index, row) {
      this.$session.set('TA', row.admin3.admin3Name_en)

      localStorage.clear()

      this.$router.push({
        name: 'EditDra',
        params: { id: row._id, dinrId: this.$route.params.id }
      })
    },

    //NAVIGATION
    handleNavigation (params) {
      this.$router.push(params)
    },

    handleDelete (index, row) {
      swal({
        title: 'Are you sure?',
        text: `You won't be able to revert this!`,
        type: 'warning',
        showCancelButton: true,
        confirmButtonClass: 'btn btn-success btn-fill',
        cancelButtonClass: 'btn btn-danger btn-fill',
        confirmButtonText: 'Yes, delete it!',
        buttonsStyling: false
      }).then(result => {
        if (result.value) {
          draforms.remove(row._id).then(
            response => {
              swal({
                title: 'Deleted!',
                text: `DRA Form deleted`,
                type: 'success',
                confirmButtonClass: 'btn btn-success btn-fill',
                buttonsStyling: false
              })
              this.deleteRow(row)
              this.reloadData()
            },
            reason => {
              this.loading = false
              this.$swal({
                title: 'Failed to delete DRA Form',
                text: 'possible (' + reason + ')',
                type: 'error',
                confirmButtonClass: 'btn btn-success btn-fill',
                buttonsStyling: false
              })
            }
          )
        }
      })
    },
    deleteRow (row) {
      let indexToDelete = this.tableData.findIndex(
        tableRow => tableRow.id === row.id
      )
      if (indexToDelete >= 0) {
        this.tableData.splice(indexToDelete, 1)
      }
    },
    selectionChange (selectedRows) {
      this.selectedRows = selectedRows
    },
    reloadData () {
      draforms.query({ dinrFormId: this.$route.params.id }).then(response => {
        this.draForms = response.data
      })
    },
    createDisasterProfile () {
      dinrforms
        .update({
          dodFrom: this.formModel.dodFrom,
          dodTo: this.formModel.dodTo,
          doaAcpc: this.formModel.doaAcpc,
          doaDcpc: this.formModel.doaDcpc,
          disaster: this.disastername,
          _id: this.$route.params.id
        })
        .then(
          response => {
            this.DisasterProfile = true
          },
          reason => {
            swal({
              title: 'Failed to update DINR Form',
              text: 'possible  (' + reason + ')',
              type: 'error',
              confirmButtonClass: 'btn btn-success btn-fill',
              buttonsStyling: false
            })
          }
        )
    }
  }
}
