
<template>
  <div>
    <h2>EDUCATION</h2>
     <h3>
      *
      <small>Hint &nbsp;</small>
      <b><font color="primary">(HH: Households, FHH : Female Headed Households, MHH : Male Headed Households)</font></b>
    </h3>
<hr class="mt-3 mb-3"/>
    <h2>
      <b class="alert-suc">Impact on schools and school goers</b>
    </h2>
    <form>
      <div class="row row-example" v-for="(value, index) in impact_on_schools" v-bind:key="index">
        <div class="col-md-6">
          <base-input label="Please choose a school type">
            <select class="form-control" v-model="value.school_type"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Choose a school type">
            <option v-for="item in school_buildings_school_types">{{item.name}}</option>
          </select>
          </base-input>
        </div>
          <div class="col-md-6">
             <base-input
             v-model="value.school_name"
            :disabled="value.school_type == null || value.school_type === ''"
            :rules="[(v) => !!v || 'school name is required']"
             placeholder="Please specify school name"
             data-toggle="tooltip"
                    data-placement="top"
                    title="Specify school name"
             label="Please specify school name" />
        </div>
        <div class="col-md-12">
              <h3>
      <b class="alert-suc">Impact on school buildings at </b><br/>  {{
              typeof value.school_name !== "undefined" || typeof value.school_type !== "undefined" ? '(' + value.school_name + ' ' + value.school_type + ')' : '(Specify school)'}}
    </h3>
        </div>

    <div class="col-md-12">
        <div class="col-md">
          <base-input label="Please choose school structure affected" >
            <select class="form-control" v-model="value.structure_type" data-toggle="tooltip"
                    data-placement="top"
                    title="Choose the structure affected">
              <option  v-for="structure in structures" :value="structure.name">{{structure.name}}</option>
            </select>
          </base-input>
        </div>
        <div class="col-md">
          <base-input label="Roof affected " placeholder="# of roofs affected" type="number"
           oninput="validity.valid||(value='');"
            min="0"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Number of roofs affected"
           v-bind:max="maxValue"
            v-model="value.roofs_affected"
            :disabled="value.school_name == null || value.school_name === '' || value.school_type == null || value.school_type === ''"
            @input="init_totals('roofs_affected', impact_on_schools, 'roofs_affected')"
       />
        </div>
        <div class="col-md">
          <base-input label="Partly damaged" placeholder="Partly damaged" type="number"
            v-model="value.partially_functioning"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Number partly damaged buildings"
            oninput="validity.valid||(value='');"
            min="0"
            :disabled="value.school_name == null || value.school_name === '' || value.school_type == null || value.school_type === ''"
            @input="init_totals('partially_functioning', impact_on_schools, 'partially_functioning')"
            v-bind:max="maxValue" />
        </div>
        <div class="col-md">
            <base-input label="Underwater"
             v-model="value.underwater"
            oninput="validity.valid||(value='');"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Number of buildings underwater"
            type="number"
            min="0"
            placeholder="0"
            :disabled="value.school_name == null || value.school_name === '' || value.school_type == null || value.school_type === ''"
            @input="init_totals('underwater', impact_on_schools, 'underwater')"
            v-bind:max="maxValue"
        />

        </div>
        <div class="col-md">
          <base-input label="Completely damaged " v-model="value.completely_damaged"
          data-toggle="tooltip"
                    data-placement="top"
                    title="Number of completely damaged buildings"
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            :disabled="value.school_name == null || value.school_name === '' || value.school_type == null || value.school_type === ''"
            @input="init_totals('completely_damaged', impact_on_schools, 'completely_damaged')"
            placeholder="0"
            v-bind:max="maxValue"
           />
        </div>
        <div class="col-md">
          <base-input label="Functioning"
           v-model="value.functioning_buildings"
           data-toggle="tooltip"
                    data-placement="top"
                    title="Number of functioning buildings"
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            :disabled="value.school_name == null || value.school_name === '' || value.school_type == null || value.school_type === ''"
            @input="init_totals('functioning_buildings', impact_on_schools, 'functioning_buildings')"
            placeholder="0"
            v-bind:max="maxValue"
       />
        </div>

        <div class="col-md">
            <base-input label="Partly functioning" v-model="value.partially_functioning_buildings"
            oninput="validity.valid||(value='');"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Number of partially functioning buildings"
            type="number"
            min="0"
            :disabled="value.school_name == null || value.school_name === '' || value.school_type == null || value.school_type === ''"
            placeholder="0"
            @input="init_totals('partially_functioning_buildings', impact_on_schools, 'partially_functioning_buildings')"
            v-bind:max="maxValue"
         />

        </div>
          <div class="col-md">
            <base-input label="Closed"
            data-toggle="tooltip"
                    data-placement="top"
                    title="NUmber of closed buildings"
             v-model="value.closed_buildings"
            placeholder="0"
            min="0"
            type="number"
            :disabled="value.school_name == null || value.school_name === '' || value.school_type == null || value.school_type === ''"
            @input="init_totals('closed_buildings', impact_on_schools, 'closed_buildings')"
        />

        </div>
      </div>
        <div class="col-md-12">
              <h3>
      <b class="alert-suc">Impact on school goers at</b><br/>   {{
              typeof value.school_name !== "undefined" || typeof value.school_type !== "undefined" ? '(' + value.school_name + ' ' + value.school_type + ')' : '(Specify school)'}}

    </h3>
    </div>

    <div class="col-md-12">
        <div class="col-md">
          <base-input label="Females out of school" placeholder="# of females out of school"
            v-model="value.females_out_of_school"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Number of females out of school"
            :disabled="value.school_name == null || value.school_name === '' || value.school_type == null || value.school_type === ''"
            @input="init_totals('females_out_of_school', impact_on_schools, 'females_out_of_school')"
        type="number" />
        </div>
        <div class="col-md">
          <base-input label="Males out of school" placeholder="# of males out of school"
             v-model="value.males_out_of_school"
            oninput="validity.valid||(value='');"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Number of males out of school"
            type="number"
            min="0"
            :disabled="value.school_name == null || value.school_name === '' || value.school_type == null || value.school_type === ''"
            v-bind:max="maxValue"
            @input="init_totals('males_out_of_school', impact_on_schools, 'males_out_of_school')" />
        </div>
        <div class="col-md pr-5 pt-5">

        </div>


      </div>
      </div>
      <hr/>


       <base-button size="md" type="info" class="btn-icon-only rounded-circle noprint"
          @click="addItemRow('impact_on_schools', impact_on_schools, '', '')"
          data-toggle="tooltip"
                    data-placement="top"
                    title="Add impact on schools"
      >
      <i class="ni ni-fat-add"></i>
    </base-button>

        <base-button size="md" type="warning" class="btn-icon-only rounded-circle noprint"
                  v-if="impact_on_schools.length > 0"
                  data-toggle="tooltip"
                    data-placement="top"
                    title="Delete impact on schools"
            @click="removeItemRow('impact_on_schools',impact_on_schools,  school_buildings_school_types, index, 'school_type', ['completely_damaged', 'partially_functioning', 'females_out_of_school', 'males_out_of_school'] )"
       >X</base-button>

      <hr />
    <div class="row row-example">
      <div class="col-sm-3">
        <h5 slot="header" class="mb-0">Total females out of school: {{ tt_female_learners_out_of_school }}</h5>
      </div>
      <div class="col-sm-3">
        <h5 slot="header" class="mb-0">Total males out of school: {{ tt_male_learners_out_of_school }}</h5>
      </div>
       <div class="col-sm-3">
        <h5 slot="header" class="mb-0">Total learners out of school: {{ tt_learners_out_of_school }}</h5>
      </div>
      <div class="col-sm-3">
        <h5 slot="header" class="mb-0">Total buildings damaged: {{ tt_school_buildings_damaged }}</h5>
      </div>

    </div>

    <hr />


    </form>


    <hr />
   <b>Response Needed for the Education Cluster</b>
    <hr class="mt-3 mb-3">
    <base-input label="General Response needed for Education cluster">
      <textarea
        class="form-control"
        id="exampleFormControlTextarea3"
        data-toggle="tooltip"
                    data-placement="top"
                    title="Type the general response needed"
        placeholder="Type the response needed for the Education cluster"
        v-model="response_needed"
        rows="3"
      ></textarea>
    </base-input>
    <base-input label="Urgent Response needed for Education cluster">
      <textarea
        class="form-control"
        id="exampleFormControlTextarea3"
        data-toggle="tooltip"
                    data-placement="top"
                    title="Type the urgent response needed"
        placeholder="Type the urgent response needed for the Education cluster"
        v-model="urgent_response_needed"
        rows="3"
      ></textarea>
    </base-input>

      <base-button size="lg" type="primary"  data-toggle="tooltip"
                    data-placement="top"
                    title="Save and goto next section" @click.stop="save" class="noprint">
      Save & Continue
    </base-button>

  </div>
</template>
<script src="./index.js"/>
<style scoped>
@media print {
  .page-break {
    overflow-y: visible;
    display: block;
  }
  .not-active {
    pointer-events: none;
    cursor: default;
    text-decoration: none;
    color: black;
  }
  .noprint {
    visibility: hidden;
  }
}
</style>
