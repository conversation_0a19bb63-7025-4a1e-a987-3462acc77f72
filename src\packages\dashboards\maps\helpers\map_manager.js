var malawiAdmin2GeoJson = require('../../../../data/malawi.features')
var malawiLakeGeoJson = require('../../../../data/malawi-lake.features')
var malawDistrictsLatLng = require('../../../../data/latlondistricts')
//let malawiAdmin3GeoJson = require("../../../../data/malawi.tas");
var malawDistrictsLatLng = require("../../../../data/latlondistricts");

const buildMap = (
  id,
  dc,
  d3,
  districtDimension,
  districtGroup,
  groupname,
  getColor,
  numberFormat,
  data,
  width
) => {
  var projection = d3
    .geoMercator()
    .fitSize([width - 30, 900], malawiAdmin2GeoJson)

  let choroplethMap = dc.geoChoroplethChart(id, groupname)

  choroplethMap
    .width(width - 30)
    .height(960)
    .transitionDuration(1000)
    .dimension(districtDimension)
    .group(districtGroup)
    .projection(projection)
    .colorDomain([1, 80])
    .colorCalculator(function (d) {
     
      let color = getColor(d)
      return d && color ? color : '#dbddde'
    })
    .overlayGeoJson(malawiAdmin2GeoJson.features, 'district', function (d) {
      return d.properties.ADMIN3
    })
    .overlayGeoJson(malawiLakeGeoJson.features, 'water', function (d) {
      return d.properties.ADMIN3
    })
    .title(function (d) {
     
      const counts = {};
      data.forEach((x) => {
        counts[x.district] = (counts[x.district] || 0) + 1;
      });
 // Property name stored in JS variable
     var prop = d.key;
     
    // var allStats = `District: ${d.key}\nTotal Disasters: ${numberFormat(counts[prop] ? counts[prop] : 0)} \nTotal HH affected: ${numberFormat(d.value ? d.value : 0)}`
     var allStats = `Total HH affected: ${numberFormat(d.value ? d.value : 0)}`
 
     return allStats;  
    })
    .on('renderlet', function (chart) {
      d3.selectAll('g.water.malawi path')
        .attr('stroke', 'white')
        .style('fill', function (d) {
          return '#409ffb'
        })
      d3
        .select("#labelG")
        .remove()
      var labelG = d3
        .select('#mapChartId svg')
        .append('svg:g')
        .attr('id', 'labelG')
        .attr('class', 'Title')

      labelG
        .selectAll('text')
        .data(malawDistrictsLatLng)

        .enter()
        .append('svg:text')
        .text(function (d) {
          if (data.some(item => item.district == d.admin)) {
            return d.admin
          }
        })
        .attr('x', function (d) {
          return projection([d.lng, d.lat])[0]
        })
        .attr('y', function (d) {
          return projection([d.lng, d.lat])[1]
        })
        .attr('dx', '-1em')

      d3.selectAll('#mapChartId g.district path')
        .attr('stroke', 'white')
        .style('cursor', 'pointer')
    })

  choroplethMap.legendables = function () {
    let categories = [0, 2000, 4000, 6000, 8000, 10000, 12000]
    return categories.map(function (d, i) {
      
      var legendable = {
        name:
          categories[i] === 0
            ? categories[i]
            : categories[i + 1]
              ? categories[i] + ' - ' + categories[i + 1]
              : '> ' + categories[i],
        chart: choroplethMap
      }

      legendable.color = getColor(categories[i])
      return legendable
    })
  }

  choroplethMap.legend(
    dc
      .legend()
      .x(0)
      .y(6)
      .itemHeight(500 / 30)
      .gap(5)
  )

  return choroplethMap
}

export { buildMap }
