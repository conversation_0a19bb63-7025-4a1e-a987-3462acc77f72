<template>
  <div class="content">
    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Disaster</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">Trends</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">All</li>
            </ol>
          </nav>
        </div>
        <div class="col-lg-6 col-5 text-right"></div>
      </div>
      <!-- Card stats -->
    </base-header>
    <div class="container-fluid mt--6">
      <div>
        <card
          class="no-border-card"
          body-classes="px-0 pb-1"
          footer-classes="pb-2"
        >
          <template slot="header">
            <h3 class="mb-0">TRENDS OF DISASTER REPORTS</h3>
          </template>
          <div>
            <div style="text-align:center">
              <h2>{{ filter.toUpperCase() }}</h2>
            </div>
            <div
              class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
            >
              <div class="row col-12 mt-4">
                <div class="col-6">
                  <span style="margin-right:10px;">Trend :</span>
                  <span id="identifier-c"></span>
                </div>
                <div class="col-1"></div>
              </div>
            </div>
            <div style="width:100%;text-algin:center;" id="trends"></div>
          </div>
          <div
            slot="footer"
            class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
          ></div>
        </card>
      </div>
    </div>
  </div>
</template>
<script>
import { Table, TableColumn, Select, Option } from "element-ui";
import RouteBreadCrumb from "@/components/Breadcrumb/RouteBreadcrumb";
import * as d3 from "d3";
import * as dc from "dc";
import crossfilter from "crossfilter2";
var globalData = [];
var group = "trendCharts"
var parent = {};
import { MongoReports } from "../../districtmanager/api/MongoReports";
var moment = require("moment");

export default {
  components: {
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn
  },
  data() {
    return {
      filter: "6-Lifetime",
      data: [
        {
          group: "1-last 24 Hours",
          forms: []
        },
        {
          group: "2-last 7 day",
          forms: []
        },
        {
          group: "3-last 30 day",
          forms: []
        },
        {
          group: "4-last 90 days",
          forms: []
        },
        {
          group: "5-last 12 Months",
          forms: []
        },
        {
          group: "6-Lifetime",
          forms: []
        }
      ],
      Graphtitle: ""
    };
  },
  computed: {
    drawGraph1() {
      ///tooltip
    },
    getData() {
      return this.data;
    }
  },
  async mounted() {
    parent = this;
    await this.loadForms().then(response => {
      var chart = new dc.BarChart("#trends",group);
      var ndx = crossfilter(this.data.find(d => d.group === this.filter).forms),
        typeDimension = ndx.dimension(function(d) {
          return d.type;
        }),
        typeCountGroup = typeDimension.group().reduceCount(d => 1)
    var containerWidth =
        document.querySelector("#trends").clientWidth - 300
       
      chart
    .width(containerWidth)
    .height(480)
    .x(d3.scaleBand())
    .xUnits(dc.units.ordinal)
    .brushOn(true)
    .yAxisLabel("Total number of forms")
    .dimension(typeDimension)
    .colorAccessor(d => d.key)
    .ordinalColors(["#a82a2a", "#bf6c00", "#ff9000"])
    .group(typeCountGroup)
    .controlsUseVisibility(true)
    .addFilterHandler(function(filters, filter) {return [filter];})
    .on("renderlet",chart => {
      chart.selectAll("rect").attr("rx", "7");
       chart.selectAll("rect").selectAll("title").text(function(d) {
         var key = d.data.key
         var value = d.data.value
         return key.split('-')[1] + " : " + value
       })
        chart.selectAll(".tick").selectAll("text").text(function(d) {
         
         return d.toString().split('-')[1]
       })
    }); 
//##################################
  var xf = crossfilter(this.data);
      var identifierDimension = xf.dimension(function(d) {
        return d.group;
      });
     

      new dc.SelectMenu("#identifier-c",group)
        .dimension(identifierDimension)
        .group(identifierDimension.group())

        .promptText("SELECT")
        .on("postRender", function() {
          d3.select(".dc-select-menu").attr(
            "class",
            "btn btn-outline-secondary border p-2 mr-4 text-left"
          );
        })
        .on("renderlet",chart => {
          d3.selectAll('.dc-select-option').text(function (d) {
                   var key = d.key
         return key.split('-')[1]
          })
      //chart.selectAll("rect").attr("rx", "7");
      //  chart.selectAll("rect").selectAll("title").text(function(d) {
      //    var key = d.data.key
      //    var value = d.data.value
      //    return key.split('-')[1] + " : " + value
      //  })
      //   chart.selectAll(".tick").selectAll("text").text(function(d) {
      //     console.log(typeof d)
      //    return d.split('-')[1]
      //  })
    })
        .on("filtered", function(chart, filter) {
         parent.drawGraph(filter)
        });

   dc.renderAll(group)

    });
  },
  methods: {
    async loadForms() {
      var response = await MongoReports.getUnapprovedDinrs().then(response => {
        let unsigneddata = response.data
          .filter(data =>
            data.district.admin2_name_en.includes(
              this.$session.get("user").admin2_name_en
            )
          )
          .filter(
            item =>
              !item ||
              ((!item.isApproved &&
                item.isApproved == false &&
                !item &&
                (!item.isRejected && item.isRejected == false)) ||
                (!item.approvalMetadata ||
                  !item.approvalMetadata.signature ||
                  item.approvalMetadata.signature.length == 0))
          )
          .map(obj => ({
            ...obj,
            hours: moment().diff(moment(obj.createdon), "hours"),
            date: moment(obj.createdon).format("DD-MM-YYYY HH:mm:ss")
          }));
        this.pushUnsigned(unsigneddata);

        let unapprovedData = response.data
          .filter(data =>
            data.district.admin2_name_en.includes(
              this.$session.get("user").admin2_name_en
            )
          )
          .filter(
            item =>
              (!item.isApproved || item.isApproved == false) &&
              (!item.isRejected || item.isRejected == false) &&
              item.approvalMetadata &&
              item.approvalMetadata.signature &&
              item.approvalMetadata.signature.length > 0
          )
          .map(obj => ({
            ...obj,
            hours: moment().diff(moment(obj.createdon), "hours"),
            date: moment(obj.createdon).format("DD-MM-YYYY HH:mm:ss")
          }));
        this.pushUnApproved(unapprovedData);
        return response;
      });
      var response2 = await MongoReports.getDinrs().then(response => {
        let submittedData = response.data.map(obj => ({
          ...obj,
          hours: moment().diff(moment(obj.createdon), "hours"),
          date: moment(obj.createdon).format("DD-MM-YYYY HH:mm:ss")
        }));
        let district = this.$session.get("user").admin2_name_en;
        this.pushSubmitted(submittedData);
        return response;
      });
      return response;
    },
    pushUnsigned(data) {
      var type = "1-Unsigned"
      data.forEach(form => {
        if (form.hours <= 24) {
          this.data
            .find(obj => obj.group === "1-last 24 Hours")
            .Unsigned.push(form);
        } else if (form.hours <= 168) {
          this.data
            .find(obj => obj.group === "2-last 7 day")
            .forms.push({ ...form, type: type });
        } else if (form.hours <= 720) {
          this.data
            .find(obj => obj.group === "3-last 30 day")
            .forms.push({ ...form, type: type });
        } else if (form.hours <= 2160) {
          this.data
            .find(obj => obj.group === "4-last 90 days")
            .forms.push({ ...form, type: type });
        } else if (form.hours <= 8760) {
          this.data
            .find(obj => obj.group === "5-last 12 Months")
            .forms.push({ ...form, type: type });
        }
        this.data
          .find(obj => obj.group === "6-Lifetime")
          .forms.push({ ...form, type: type });
      });
      return;
    },
    pushUnApproved(data) {
      var type = "2-UnApproved"
      data.forEach(form => {
        if (form.hours <= 24) {
          this.data
            .find(obj => obj.group === "1-last 24 Hours")
            .forms.push({ ...form, type: type });
        } else if (form.hours <= 168) {
          this.data
            .find(obj => obj.group === "2-last 7 day")
            .forms.push({ ...form, type: type });
        } else if (form.hours <= 720) {
          this.data
            .find(obj => obj.group === "3-last 30 day")
            .forms.push({ ...form, type: type });
        } else if (form.hours <= 2160) {
          this.data
            .find(obj => obj.group === "4-last 90 days")
            .forms.push({ ...form, type: type });
        } else if (form.hours <= 8760) {
          this.data
            .find(obj => obj.group === "5-last 12 Months")
            .forms.push({ ...form, type: type });
        }
        this.data
          .find(obj => obj.group === "6-Lifetime")
          .forms.push({ ...form, type: type });
      });
      return;
    },
    pushSubmitted(data) {
      var type = "3-Submitted"
      data.forEach(form => {
        if (form.hours <= 24) {
          this.data
            .find(obj => obj.group === "1-last 24 Hours")
            .forms.push({ ...form, type: type });
        } else if (form.hours <= 168) {
          this.data
            .find(obj => obj.group === "2-last 7 day")
            .forms.push({ ...form, type: type });
        } else if (form.hours <= 720) {
          this.data
            .find(obj => obj.group === "3-last 30 day")
            .forms.push({ ...form, type: type });
        } else if (form.hours <= 2160) {
          this.data
            .find(obj => obj.group === "4-last 90 days")
            .forms.push({ ...form, type: type });
        } else if (form.hours <= 8760) {
          this.data
            .find(obj => obj.group === "5-last 12 Months")
            .forms.push({ ...form, type: type });
        }
        this.data
          .find(obj => obj.group === "6-Lifetime")
          .forms.push({ ...form, type: type });
      });
      return;
    },
    drawGraph (filter) {
      
    }
  }
};
</script>

<style>
#trends {
  font-family: Arial, Helvetica, sans-serif;
  border-collapse: collapse;
  width: 90%;
  margin-left: 5%;
}

#trends td,
#trends th {
  border: 1px solid #ddd;
  padding: 8px;
}

#trends tr:nth-child(even) {
  background-color: #f2f2f2;
}

#trends tr:hover {
  background-color: #ddd;
}

#trends th {
  padding-top: 12px;
  padding-bottom: 12px;
  text-align: left;
  background-color: #04aa6d;
  color: white;
}
#identifier-c select {
  padding-top: 12px !important;
  padding-bottom: 12px !important;
}
</style>
