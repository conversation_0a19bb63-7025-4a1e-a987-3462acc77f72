<template>
  <div>
    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Edit</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a
                  href="#"
                  data-toggle="tooltip"
                  data-placement="top"
                  title="Goto DINR forms"
                  >Forms</a
                >
              </li>
              <li class="breadcrumb-item">
                <router-link
                  :to="{ name: 'DINR Forms Edit', params: { id: dinrId } }"
                  data-toggle="tooltip"
                  data-placement="top"
                  title="Back to DINR form page"
                  >DINR</router-link
                >
              </li>

              <li class="breadcrumb-item">
                <a href="#">DRA</a>
              </li>
              <li class="breadcrumb-item active">
                <a href="#">Edit</a>
              </li>
            </ol>
          </nav>
        </div>
        <div class="col-lg-6 col-6 text-right pr-5">
          <!--   <base-button size="sm" type="neutral" @click="saveAllSections">
            <span class="btn-inner--icon">
            </span>
            <span class="btn-inner--text">SAVE ALL DATA</span>
          </base-button>
 -->
          <base-button size="sm" type="neutral" @click.native="gotoPrint()">
            <span class="btn-inner--icon">
              <!--  <i class="ni ni-fat-add"></i> -->
            </span>
            <span
              class="btn-inner--text"
              data-toggle="tooltip"
              data-placement="top"
              title="Preview this form"
              >PREVIEW FORM</span
            >
          </base-button>
        </div>
      </div>
    </base-header>

    <div class="container-fluid mt--6">
      <div class="row justify-content-center">
        <div class="col-md-11" style="margin:auto">
          <b
            >DISASTER IMPACT NEEDS REPORTING (Traditional Authority:
            {{ TA }})</b
          >
          <tabs
            tabNavClasses="nav-fill flex-column flex-sm-row nav-wrapper"
            tabContentClasses="card shadow"
            :value="model"
            v-model="model"
          >
            <tabpane :id="1" title="1" :key="1">
              <span slot="title" @click="setActive('1')">
                <center>
                  <span
                    style="font-size:400%;"
                    data-toggle="tooltip"
                    data-placement="top"
                    title="SHELTER"
                    class="humanitarianicons-Shelter"
                  ></span>
                </center>
              </span>
              <div class="card-body">
                <shelter
                  v-on:save="save"
                  v-on:autosave="autosave"
                  v-bind:shelter="sector.shelter"
                  v-on:addItemRow="addItemRow"
                  v-on:removeItemRow="removeItemRow"
                  v-bind:max="max"
                  v:bind:value="value"
                  v:bind:isVisible="isVisible"
                ></shelter>
              </div>
            </tabpane>

            <tabpane :id="2" title="2" :key="2">
              <span slot="title" @click="setActive('2')">
                <center>
                  <span
                    style="font-size:400%;"
                    data-toggle="tooltip"
                    data-placement="top"
                    title="DISPLACED"
                    class="humanitarianicons-Internally-displaced"
                  ></span>
                </center>
              </span>
              <div class="card-body">
                <displaced
                  v-on:save="save"
                  v-on:addItemRow="addItemRow"
                  v-on:autosave="autosave"
                  v-on:removeItemRow="removeItemRow"
                  v-bind:displaced="sector.displaced"
                  v-bind:max="max"
                ></displaced>
              </div>
            </tabpane>

            <tabpane :id="3" title="3" :key="3">
              <span slot="title" @click="setActive('3')">
                <center>
                  <span
                    style="font-size:400%;"
                    data-toggle="tooltip"
                    data-placement="top"
                    title="AGRICULTURE"
                    class="humanitarianicons-Agriculture"
                  ></span>
                </center>
              </span>
              <div class="card-body">
                <agriculture
                  v-on:save="save"
                  id="content"
                  v-on:addItemRow="addItemRow"
                  :key="sector.agriculture"
                  v-if="childDataLoaded === true"
                  v-on:autosave="autosave"
                  v-on:removeItemRow="removeItemRow"
                  v-bind:agriculture="sector.agriculture"
                  v-bind:max="max"
                ></agriculture>
              </div>
            </tabpane>
            <tabpane :id="4" title="4" :key="4">
              <span slot="title" @click="setActive('4')">
                <center>
                  <span
                    style="font-size:400%;"
                    data-toggle="tooltip"
                    data-placement="top"
                    title="WASH"
                    class="humanitarianicons-Water-Sanitation-and-Hygiene"
                  ></span>
                </center>
              </span>
              <div class="card-body">
                <wash
                  v-on:save="save"
                  v-on:addItemRow="addItemRow"
                  v-on:removeItemRow="removeItemRow"
                  v-on:autosave="autosave"
                  v-if="childDataLoaded === true"
                  v-bind:wash="sector.wash"
                  v-bind:max="max"
                ></wash>
              </div>
            </tabpane>
            <tabpane :id="5" title="5" :key="5">
              <span slot="title" @click="setActive('5')">
                <center>
                  <span
                    style="font-size:400%;"
                    data-toggle="tooltip"
                    data-placement="top"
                    title="HEALTH"
                    class="humanitarianicons-Health"
                  ></span>
                </center>
              </span>
              <div class="card-body">
                <health
                  v-on:save="save"
                  v-bind:health="sector.health"
                  v-if="childDataLoaded === true"
                  v-on:autosave="autosave"
                  v-on:addItemRow="addItemRow"
                  v-on:removeItemRow="removeItemRow"
                  v-bind:max="max"
                ></health>
              </div>
            </tabpane>
            <tabpane :id="6" title="6" :key="6">
              <span slot="title" @click="setActive('6')">
                <center>
                  <span
                    style="font-size:400%;"
                    data-toggle="tooltip"
                    data-placement="top"
                    title="FOOD"
                    class="humanitarianicons-Food-Security"
                  ></span>
                </center>
              </span>
              <div class="card-body">
                <food
                  v-on:save="save"
                  v-bind:food="sector.food"
                  v-on:addItemRow="addItemRow"
                  v-if="childDataLoaded === true"
                  v-on:autosave="autosave"
                  v-on:removeItemRow="removeItemRow"
                  v-bind:max="max"
                ></food>
              </div>
            </tabpane>
            <tabpane :id="7" title="7" :key="7">
              <span slot="title" @click="setActive('7')">
                <center>
                  <span
                    style="font-size:400%;"
                    data-toggle="tooltip"
                    data-placement="top"
                    title="LOGISTICS"
                    class="humanitarianicons-Logistics"
                  ></span>
                </center>
              </span>
              <div class="card-body">
                <logistics
                  v-on:save="save"
                  v-bind:logistics="sector.logistics"
                  v-on:autosave="autosave"
                  v-if="childDataLoaded === true"
                  v-on:addItemRow="addItemRow"
                  v-on:removeItemRow="removeItemRow"
                  v-bind:max="max"
                ></logistics>
              </div>
            </tabpane>
            <tabpane :id="8" title="8" :key="8">
              <span slot="title" @click="setActive('8')">
                <center>
                  <span
                    style="font-size:400%;"
                    data-toggle="tooltip"
                    data-placement="top"
                    title="PROTECTION"
                    class="humanitarianicons-Protection"
                  ></span>
                </center>
              </span>
              <div class="card-body">
                <protection
                  v-on:save="save"
                  v-bind:protection="sector.protection"
                  v-on:autosave="autosave"
                  v-on:addItemRow="addItemRow"
                  v-if="childDataLoaded === true"
                  v-on:removeItemRow="removeItemRow"
                  v-bind:max="max"
                ></protection>
              </div>
            </tabpane>
            <tabpane :id="9" title="9" :key="9">
              <span slot="title" @click="setActive('9')">
                <center>
                  <span
                    style="font-size:400%;"
                    data-toggle="tooltip"
                    data-placement="top"
                    title="NUTRITION"
                    class="humanitarianicons-Nutrition"
                  ></span>
                </center>
              </span>
              <div class="card-body">
                <nutrition
                  v-on:save="save"
                  v-bind:nutrition="sector.nutrition"
                  v-on:addItemRow="addItemRow"
                  v-on:autosave="autosave"
                  v-if="childDataLoaded === true"
                  v-on:removeItemRow="removeItemRow"
                  v-bind:max="max"
                ></nutrition>
              </div>
            </tabpane>
            <tabpane :id="10" title="10" :key="10">
              <span slot="title" @click="setActive('10')">
                <center>
                  <span
                    style="font-size:400%;"
                    data-toggle="tooltip"
                    data-placement="top"
                    title="EDUCATION"
                    class="humanitarianicons-Education"
                  ></span>
                </center>
              </span>
              <div class="card-body">
                <education
                  v-on:save="save"
                  v-bind:education="sector.education"
                  v-on:addItemRow="addItemRow"
                  v-if="childDataLoaded === true"
                  v-on:autosave="autosave"
                  v-on:removeItemRow="removeItemRow"
                  v-bind:max="max"
                ></education>
              </div>
            </tabpane>
            <tabpane :id="11" title="11" :key="11">
              <span slot="title" @click="setActive('11')">
                <center>
                  <span
                    style="font-size:400%;"
                    data-toggle="tooltip"
                    data-placement="top"
                    title="LIVELIHOODS"
                    class="humanitarianicons-Livelihood"
                  ></span>
                </center>
              </span>
              <div class="card-body">
                <livelihoods
                  v-on:save="save"
                  v-bind:livelihoods="sector.livelihoods"
                  v-on:addItemRow="addItemRow"
                  v-if="childDataLoaded === true"
                  v-on:autosave="autosave"
                  v-on:removeItemRow="removeItemRow"
                  v-bind:max="max"
                ></livelihoods>
              </div>
            </tabpane>
            <tabpane :id="12" title="12" :key="12">
              <span slot="title" @click="setActive('12')">
                <center>
                  <span
                    style="font-size:400%;"
                    data-toggle="tooltip"
                    data-placement="top"
                    title="ENVIRONMENT"
                    class="humanitarianicons-Environment"
                  ></span>
                </center>
              </span>
              <div class="card-body">
                <environment
                  v-on:save="save"
                  v-bind:environment="sector.environment"
                  v-if="childDataLoaded === true"
                  v-on:autosave="autosave"
                  v-on:addItemRow="addItemRow"
                  v-on:removeItemRow="removeItemRow"
                  v-bind:max="max"
                ></environment>
              </div>
            </tabpane>
          </tabs>
        </div>
      </div>
    </div>
  </div>
</template>
<script src="./index.js" />

<style scoped>
.alert-suc {
  color: #006666;
}

@import "../../../../../assets/style.css";
@import ".../../../../../../../assets/ie7/ie7.css";
</style>
