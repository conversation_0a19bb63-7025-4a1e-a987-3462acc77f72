import axios from 'axios'

const jet = 'http://localhost:3344';
//const resource = process.env.VUE_APP_ENGINE_URL + '/signatures'
const resource = process.env.VUE_APP_ENGINE_URL + '/files'
//onst resource = process.env.VUE_APP_AUTH_URL + '/api/Uploads';
export class uploads {
  static get (token) {
    return axios.get(resource + '?access_token=' + token).then(response => {
      return response
    })
  }
  static async create (upload) {
    let response = await axios.post(resource, upload).then(response => {
      return response.data
    })
    return response
  }
  static async count (id) {
    let response = await axios
      .get(resource + '/count' + '?access_token=' + id)
      .then(response => {
        return response.data
      })
    return response
  }
  static async upload (data, progressCallback = null) {
    if (progressCallback) {
      const response = await axios.post(`${resource}/upload`, data, {
        headers: { 'Content-Type': 'multipart/form-data' },
        onUploadProgress: progressCallback
      })

      return response
    } else {
      let response = await axios
      .post(resource + '/upload/', data, {
        headers: { 'Content-Type': 'multipart/form-data' }
      })
      .then(response => {
        return response
      })
      return response
    }
  }
  static async test (data, progressCallback = null) {
    if (progressCallback) {
      const response = await axios.post(`${resource}/driveUpload`, data, {
        headers: { 'Content-Type': 'multipart/form-data' },
        onUploadProgress: progressCallback
      })

      return response
    } else {
      let response = await axios
      .post(resource + '/driveUpload/', data, {
        headers: { 'Content-Type': 'multipart/form-data' }
      })
      .then(response => {
        return response
      })
      return response
    }
  }
  static async remove (data) {
    let response = await axios
      .post(resource + '/remove/', data)
      .then(response => {
        return response
      })
    return response
  }
}
