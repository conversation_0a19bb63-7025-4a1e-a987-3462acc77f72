<template>
  <div class="content">
    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Disaster</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">Trends</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">All</li>
            </ol>
          </nav>
        </div>
        <div class="col-lg-6 col-5 text-right"></div>
      </div>
      <!-- Card stats -->
    </base-header>
    <div class="container-fluid mt--6">
      <div>
        <card
          class="no-border-card"
          body-classes="px-0 pb-1"
          footer-classes="pb-2"
        >
          <template slot="header">
            <h3 class="mb-0">TRENDS OF DISASTER REPORTS</h3>
          </template>
          <div>
            <div
              class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
            >
              

              <div style="display:none;">
                <base-input
                  v-model="searchQuery"
                  prepend-icon="fas fa-search"
                  placeholder="Search..."
                ></base-input>
              </div>
            </div>
            <div style="width:100%;text-algin:center;" id="spenderContainer">
        
<table id="trends">
  <tr>
    <th style="background-color:white;"></th>
    <th>Unsigned</th>
    <th>unApproved</th>
    <th>Approved</th>
    <th>Submitted</th>
  </tr>
  <tr>
    <td>last 24 Hours</td>
    <td>{{unsigned.lessThan24Hours.length}}</td>
    <td>{{unApproved.lessThan24Hours.length}}</td>
    <td>{{approved.lessThan24Hours.length}}</td>
    <td>{{submitted.lessThan24Hours.length}}</td>
  </tr>
  <tr>
    <td>last 7 day</td>
    <td>{{unsigned.lessThan7Days.length}}</td>
     <td>{{unApproved.lessThan7Days.length}}</td>
    <td>{{approved.lessThan7Days.length}}</td>
    <td>{{submitted.lessThan7Days.length}}</td>
  </tr>
  <tr>
    <td>last 30 day</td>
    <td>{{unsigned.lessThan30Days.length}}</td>
    <td>{{unApproved.lessThan30Days.length}}</td>
    <td>{{approved.lessThan30Days.length}}</td>
    <td>{{submitted.lessThan30Days.length}}</td>
  </tr>
  <tr>
    <td>last 90 days</td>
    <td>{{unsigned.lessThan90Days.length}}</td>
    <td>{{unApproved.lessThan90Days.length}}</td>
    <td>{{approved.lessThan90Days.length}}</td>
    <td>{{submitted.lessThan90Days.length}}</td>
  </tr>
  <tr>
    <td>last 12 Months</td>
    <td>{{unsigned.lessThan12Months.length}}</td>
    <td>{{unApproved.lessThan12Months.length}}</td>
    <td>{{approved.lessThan12Months.length}}</td>
    <td>{{submitted.lessThan12Months.length}}</td>
  </tr>
  <tr>
   <td>Lifetime</td>
    <td>{{unsigned.lifeTime.length}}</td>
    <td>{{unApproved.lifeTime.length}}</td>
    <td>{{approved.lifeTime.length}}</td>
    <td>{{submitted.lifeTime.length}}</td>
  </tr>

</table>
    </div>
          </div>
          <div
            slot="footer"
            class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
          >
            
            
          </div>
        </card>
      </div>
    </div>
  </div>
</template>
<script>
import { Table, TableColumn, Select, Option } from "element-ui";
import RouteBreadCrumb from "@/components/Breadcrumb/RouteBreadcrumb";

import { MongoReports } from "../../districtmanager/api/MongoReports";
var moment = require("moment");

export default {
  components: {
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn
  },
  data() {
    return {
      unsigned: {
          lessThan24Hours: [],
          lessThan7Days: [],
          lessThan30Days: [],
          lessThan90Days: [],
          lessThan12Months: [],
          lifeTime: []
      },
       unApproved: {
          lessThan24Hours: [],
          lessThan7Days: [],
          lessThan30Days: [],
          lessThan90Days: [],
          lessThan12Months: [],
          lifeTime: []
      },
       approved: {
          lessThan24Hours: [],
          lessThan7Days: [],
          lessThan30Days: [],
          lessThan90Days: [],
          lessThan12Months: [],
          lifeTime: []
      },
       submitted: {
          lessThan24Hours: [],
          lessThan7Days: [],
          lessThan30Days: [],
          lessThan90Days: [],
          lessThan12Months: [],
          lifeTime: []
      }
    };
  },
  methods: {
    review(row) {
      this.$router.push({
        path: "/districtcommissioner/unapproveddetailsreport/" + row.uuid
      });
    },
    reviewSummary(row) {
      this.$router.push({
        path: "/districtcommissioner/summaryreport/" + row.uuid
      });
    },
    handleInfographics(row) {
      this.$router.push({
        path: "/districtcommissioner/infographics/" + row.uuid
      });
    }
  },
   mounted() {
   
MongoReports.getUnapprovedDinrs().then(response => {
     
      let data = response.data
        .filter(data =>
          data.district.admin2_name_en.includes(
            this.$session.get("user").admin2_name_en
          )
        )
        .filter(
          item =>
            !item ||
            ((!item.isApproved && item.isApproved == false) &&
              !item &&
              (!item.isRejected && item.isRejected == false) ||
              (!item.approvalMetadata ||
                !item.approvalMetadata.signature ||
                item.approvalMetadata.signature.length == 0))
        );

     

      for (let i = 0; i < data.length; i++) {
        let row = data[i]._id;
        var form = {
          id: i + 1,
          disaster: data[i].disaster,
          district: data[i].district.admin2_name_en,
          date: moment(data[i].createdon).format("MM-DD-YYYY HH:mm:ss"),
          hours: moment().diff(moment(data[i].createdon), 'hours'),
          officer: data[i].account.firstName + " " + data[i].account.lastName,
          uuid: data[i]._id
        }
       if (form.hours <= 24) {
this.unsigned.lessThan24Hours.push(form)
       }
       if (form.hours <= 168) {
this.unsigned.lessThan7Days.push(form)
       }
       if (form.hours <= 720) {
this.unsigned.lessThan30Days.push(form)
       }
        if (form.hours <= 2160) {
this.unsigned.lessThan90Days.push(form)
       }
        if (form.hours <= 8760) {
this.unsigned.lessThan12Months.push(form)
       } 
       this.unsigned.lifeTime.push(form)
      }
    });

     MongoReports.getUnapprovedDinrs().then(response => {
      let data = response.data
        .filter(data =>
          data.district.admin2_name_en.includes(
            this.$session.get("user").admin2_name_en
          )
        )
        .filter(item => item.isApproved && item.isApproved == true);

      data.sort(function(a, b) {
        return new Date(b.createdon) - new Date(a.createdon);
      });

      for (let i = 0; i < data.length; i++) {
        let row = data[i]._id;
       var form = {
          id: i + 1,
          disaster: data[i].disaster,
          district: data[i].district.admin2_name_en,
          date: data[i].createdon,
          hours: moment().diff(moment(data[i].createdon), 'hours'),
          officer: data[i].account.firstName + " " + data[i].account.lastName,
          uuid: data[i]._id
        }
        if (form.hours <= 24) {
this.approved.lessThan24Hours.push(form)
       }
       if (form.hours <= 168) {
this.approved.lessThan7Days.push(form)
       }
       if (form.hours <= 720) {
this.approved.lessThan30Days.push(form)
       }
        if (form.hours <= 2160) {
this.approved.lessThan90Days.push(form)
       }
        if (form.hours <= 8760) {
this.approved.lessThan12Months.push(form)
       } 
       this.approved.lifeTime.push(form)
      }
    });
       MongoReports.getUnapprovedDinrs().then(response => {
      let data = response.data
        .filter(data =>
          data.district.admin2_name_en.includes(
            this.$session.get("user").admin2_name_en
          )
        )
        .filter(
          item =>
            (!item.isApproved || item.isApproved == false) &&
            (!item.isRejected || item.isRejected == false) &&
            item.approvalMetadata &&
            item.approvalMetadata.signature &&
            item.approvalMetadata.signature.length > 0
        );

      data.sort(function(a, b) {
        return new Date(b.createdon) - new Date(a.createdon);
      });

      for (let i = 0; i < data.length; i++) {
        let row = data[i]._id;
       var form = {
          id: i + 1,
          disaster: data[i].disaster,
          district: data[i].district.admin2_name_en,
          date: moment(data[i].createdon).format("MM-DD-YYYY HH:mm:ss"),
          hours: moment().diff(moment(data[i].createdon), 'hours'),
          officer: data[i].account.firstName + " " + data[i].account.lastName,
          uuid: data[i]._id
        }
        if (form.hours <= 24) {
this.unApproved.lessThan24Hours.push(form)
       }
       if (form.hours <= 168) {
this.unApproved.lessThan7Days.push(form)
       }
       if (form.hours <= 720) {
this.unApproved.lessThan30Days.push(form)
       }
        if (form.hours <= 2160) {
this.unApproved.lessThan90Days.push(form)
       }
        if (form.hours <= 8760) {
this.unApproved.lessThan12Months.push(form)
       } 
       this.unApproved.lifeTime.push(form)
      }
    });

       MongoReports.getDinrs().then((response) => {
      let data = response.data;
      let district = this.$session.get("user").admin2_name_en;

     

      data = data.filter((x) =>
        x.district ? x.district.admin2_name_en.includes(district) : false
      );


      for (let i = 0; i < data.length; i++) {
        let row = data[i]._id;

       var form = {
          id: i + 1,
          disaster: data[i].disaster,
          district: data[i].district.admin2_name_en,
          date: moment(data[i].createdon).format("DD-MM-YYYY HH:mm:ss"),
           hours: moment().diff(moment(data[i].createdon), 'hours'),
          officer: data[i].account.firstName + " " + data[i].account.lastName,
          uuid: data[i]._id,
        }
         if (form.hours <= 24) {
this.submitted.lessThan24Hours.push(form)
       }
       if (form.hours <= 168) {
this.submitted.lessThan7Days.push(form)
       }
       if (form.hours <= 720) {
this.submitted.lessThan30Days.push(form)
       }
        if (form.hours <= 2160) {
this.submitted.lessThan90Days.push(form)
       }
        if (form.hours <= 8760) {
this.submitted.lessThan12Months.push(form)
       } 
       this.submitted.lifeTime.push(form)
      }
    });
    
  }
};
</script>

<style>
#trends {
  font-family: Arial, Helvetica, sans-serif;
  border-collapse: collapse;
  width: 90%;
  margin-left:5%;
}

#trends td, #trends th {
  border: 1px solid #ddd;
  padding: 8px;
}

#trends tr:nth-child(even){background-color: #f2f2f2;}

#trends tr:hover {background-color: #ddd;}

#trends th {
  padding-top: 12px;
  padding-bottom: 12px;
  text-align: left;
  background-color: #04AA6D;
  color: white;
}
</style>
