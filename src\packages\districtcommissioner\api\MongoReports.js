import axios from 'axios'

const endpoint = process.env.VUE_APP_ENGINE_URL + '/formsFinal'
const endpoint2 = process.env.VUE_APP_ENGINE_URL + '/forms'

export class MongoReports {
  static getDinrs () {
    return axios.get(endpoint + '/dinr').then(response => {
      return response
    })
  }

  static getUnapprovedDinrs () {
    return axios.get(endpoint2 + '/dinr').then(response => {
      return response
    })
  }
  static getOneDinr (id) {
    //console.log(id)
    if (id != '')
      return axios.get(endpoint + '/dinr/' + id).then(response => {
        return response
      })
    else
      return axios.get(endpoint + '/dinr/').then(response => {
        return response
      })
  }

  static getOneUnApprovedDinr (id) {
    //console.log(id)
    if (id != '')
      return axios.get(endpoint2 + '/dinr/' + id).then(response => {
        return response
      })
    else
      return axios.get(endpoint2 + '/dinr/').then(response => {
        return response
      })
  }
  static getDras (id) {
    if (id != '')
      return axios
        .post(endpoint + '/dra/query', { dinrFormId: id })
        .then(response => {
          return response
        })
    else
      return axios.get(endpoint + '/dra').then(response => {
        return response
      })
  }

  static getUnapprovedDras (id) {
    if (id != '')
      return axios
        .post(endpoint2 + '/dra/query', { dinrFormId: id })
        .then(response => {
          return response
        })
    else
      return axios.get(endpoint2 + '/dra').then(response => {
        return response
      })
  }
}
