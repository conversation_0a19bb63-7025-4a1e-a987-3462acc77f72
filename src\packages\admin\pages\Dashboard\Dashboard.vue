<template>
  <div>
    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Dashboard</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item active">
                <a href="#">Dashboards</a>
              </li>
            </ol>
          </nav>
        </div>
      </div>
      <!-- Card stats -->
      <div class="row">
        <div class="col-xl-3 col-md-6">
          <div class="card bg-primary border-0">
            <!-- Card body -->
            <div class="card-body">
              <div class="row">
                <div class="col">
                  <h5
                    class="card-title text-uppercase text-muted mb-0 text-white"
                  >
                    District offices
                  </h5>
                  <span class="h2 font-weight-bold mb-0 text-white">{{
                    offices
                  }}</span>
                  <base-progress
                    class="progress-xs mt-3 mb-0"
                    type="success"
                    :value="offices"
                  ></base-progress>
                </div>
              </div>
              <p class="mt-3 mb-0 text-sm">
                <a href="#!" class="text-nowrap text-white font-weight-600"></a>
              </p>
            </div>
          </div>
        </div>
        <div class="col-xl-3 col-md-6">
          <div class="card bg-primary border-0">
            <!-- Card body -->
            <div class="card-body">
              <div class="row">
                <div class="col">
                  <h5
                    class="card-title text-uppercase text-muted mb-0 text-white"
                  >
                    Officers
                  </h5>
                  <span class="h2 font-weight-bold mb-0 text-white">{{
                    officer
                  }}</span>
                  <base-progress
                    class="progress-xs mt-3 mb-0"
                    type="success"
                    :value="officer"
                  ></base-progress>
                </div>
              </div>
              <p class="mt-3 mb-0 text-sm">
                <a href="#!" class="text-nowrap text-white font-weight-600"></a>
              </p>
            </div>
          </div>
        </div>
        <div class="col-xl-3 col-md-6">
          <div class="card bg-primary border-0">
            <!-- Card body -->
            <div class="card-body">
              <div class="row">
                <div class="col">
                  <h5
                    class="card-title text-uppercase text-muted mb-0 text-white"
                  >
                    Admins
                  </h5>
                  <span class="h2 font-weight-bold mb-0 text-white">{{
                    admin
                  }}</span>
                  <base-progress
                    class="progress-xs mt-3 mb-0"
                    type="success"
                    :value="admin"
                  ></base-progress>
                </div>
              </div>
              <p class="mt-3 mb-0 text-sm">
                <a href="#!" class="text-nowrap text-white font-weight-600"></a>
              </p>
            </div>
          </div>
        </div>
        <div class="col-xl-3 col-md-6">
          <div class="card bg-primary border-0">
            <!-- Card body -->
            <div class="card-body">
              <div class="row">
                <div class="col">
                  <h5
                    class="card-title text-uppercase text-muted mb-0 text-white"
                  >
                    Managers
                  </h5>
                  <span class="h2 font-weight-bold mb-0 text-white">{{
                    manager
                  }}</span>
                  <base-progress
                    class="progress-xs mt-3 mb-0"
                    type="success"
                    :value="manager"
                  ></base-progress>
                </div>
              </div>
              <p class="mt-3 mb-0 text-sm">
                <a href="#!" class="text-nowrap text-white font-weight-600"></a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </base-header>
  </div>
</template>
<script>
// Charts
import { accounts } from "../../api/accounts/accounts";
import { admin2s } from "../../api/location/admin2s";
export default {
  components: {},
  data() {
    return {
      admin: 0,
      officer: 0,
      manager: 0,
      offices: 0
    };
  },
  mounted() {
    accounts.get(this.$session.get("jwt")).then(response => {
      let data = response.data;
      this.admin = data.filter(obj => {
        return obj.roleName === "admin";
      }).length;

      this.manager = data.filter(obj => {
        return obj.roleName === "manager";
      }).length;

      this.officer = data.filter(obj => {
        return obj.roleName === "officer";
      }).length;
    });

    admin2s.get().then(response => {
      this.offices = response.data.length;
    });
  }
};
</script>
<style></style>
