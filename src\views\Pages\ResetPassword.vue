<template>
  <div>
    <div class="row-col">
      <!-- Header -->
      <div style="position: center;">
        <div class="header mt-3">
          <div class="container">
            <div class="header-body text-center">
              <div class="row justify-content-center">
                <div class="col-12 text-center">
                  <p align="center" class="mt-1">
                    <img width="80" src="../../../static/logo.png" />
                  </p>
                </div>
                <div class="col-12">
                  <h1 class="text-lead text-primary display-4">
                    <b>DRMIS : WEB APP</b>
                  </h1>
                  <p class="text-lead text-primary">
                    (Disaster Risk Management Information System)
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="container-fluid bg-image-container">
          <div class="row justify-content-center align-items-center">
            <div class="col-lg-10 col-md-12 col-sm-12 col-12">
              <div class="card bg-secondary border-0 mb-0 my-0 py-0">
                <div class="card-body px-3 pb-1">
                  <form role="form" @submit.prevent="resetPassword" class="my-0 py-1">
                    <div class="text-center text-muted mb-4 my-0 py-0">
                      <small><b class="text-primary">Reset your password</b></small>
                    </div>

                    <base-input
                    class="round"
                    alternative
                     prepend-icon="ni ni-email-83"

                    id="newPassword"
                    placeholder="Email"
                    v-model="email"
                    required
                  ></base-input>


                    <base-input
                      alternative
                      class="round"
                      type="text"
                      prepend-icon="ni ni-lock-circle-open"
                      placeholder="OTP"
                      v-model="otp"
                      required
                    ></base-input>



                    <base-input
                      class="round"
                      alternative
                      prepend-icon="ni ni-lock-circle-open"
                      :type="showPassword ? 'text' : 'password'"
                      id="confirmPassword"
                      placeholder="New Password"
                      v-model="newPassword"
                      required
                    ></base-input>

                    <input
                      type="checkbox"
                      class="pt-2 ml-3 mb-3"
                      @change="togglePasswordVisibility"
                    />
                    <span class="pl-0 ml-2">Show Password</span>

                    <div class="round" alternative>
                      <base-button type="primary" native-type="submit" class="button">Reset Password</base-button>
                    </div>
                  </form>
                  <p v-if="message" class="text-success mt-3 text-center">{{ message }}</p>
                  <p v-if="error" class="text-danger mt-3 text-center">{{ error }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="pb-4"></div>
        <!-- Footer -->
        <footer style="position: center;">
          <div class="container-fluid">
            <div class="row pb-0 mb-0">
              <div class="col-8">
                <ul class="nav nav-footer">
                  <li class="nav-item"></li>
                  <li class="nav-item">
                    <a
                      v-bind:href="drmisURL"
                      class="nav-link font-weight-bold text-primary"
                      target="_blank"
                      >© {{ year }} DRMIS</a
                    >
                  </li>
                </ul>
              </div>
              <div class="col-4">
                <div class="d-flex align-items-end justify-content-end">
                  <div class="border-right pr-2 mr-0">
                    <img width="80" src="../../../static/donor_logo.png" />
                  </div>
                  <div>
                    <img width="23" class="ml-0" src="../../../static/undp.png" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
  </div>
</template>

<script>
import swal from 'sweetalert2';
export default {
  data() {
    return {
      otp: '',
      newPassword: '',
       email: '',
      showPassword: false,
      message: '',
      error: '',
      year: new Date().getFullYear(),
      drmisURL: process.env.VUE_APP_DRMIS_URL,
    };
  },
  methods: {
    togglePasswordVisibility() {
      this.showPassword = !this.showPassword;
    },
    async resetPassword() {
  try {
    const response = await axios.post(
      process.env.VUE_APP_AUTH_URL + '/api/Accounts/reset-password-otp',
      {
        email:this.email,
        otp: this.otp,
        newPassword: this.newPassword,
      }
    );

    console.log('Backend response:', response);

    // Extract the message from the response or provide a default
    const message =
      (response.data &&
        response.data.status &&
        response.data.status.message) ||
      'Password reset successfully.';

    this.message = message; // Show success message
    this.error = ''; // Clear any previous errors

    // Display success alert and redirect to login page
    swal
      .fire({
        text: this.message,
        icon: 'success',
      })
      .then(() => {
        this.$router.push('/Login');
      });
  } catch (err) {
  console.error('Error occurred during password reset:', err); // Log the error for debugging purposes

  // Set a generic error message
  this.error = 'Failed to reset password. Please try again later.';
  this.message = ''; // Clear any previous success messages

  // Display a generic error alert
  swal.fire({
    text: this.error,
    icon: 'error',
  });
}
}


  },
};
</script>

<style lang="scss" scoped>
.reset-password .card {
  background-color: #fff;
  border-radius: 15px;
}

.reset-password {
  margin-top: 30px;
}

.round {
  border-radius: 15px;
  background: #fff;
}

.button {
  width: 100%;
  background-color: #2ec3a3;
  border: none;
  color: #fff;
  padding: 10px;
  margin-top: 10px;
  border-radius: 5px;
  cursor: pointer;
}
</style>
