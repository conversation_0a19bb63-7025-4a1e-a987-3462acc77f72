 <template>
  <div>
    <h2>SHELTER</h2>
     <h3>
      *
      <small>Hint &nbsp;</small>
      <b><font color="primary">(HH: Households, FHH : Female Headed Households, MHH : Male Headed Households)</font></b>
    </h3>
    <div class="basic">
      <hr class="mt-3 mb-3">


      <div class="row row-example" v-for="(value, index) in PeopleAffectedrows" v-bind:key="index">

        <div class="col-md-12">
          <b>
            Impact on households in
            <h3>(GVH : {{ value.name }})</h3>
          </b>
        </div>
        <div class="col-md">
          <base-input
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of damaged female-headed households"
            placeholder="0"
            v-bind:max="maxValue"
            @input="init_totals('damaged_fhh', PeopleAffectedrows, 'total_houses_damaged_fhh')"
            v-model="value.damaged_fhh"
            label="damaged FHH"
          ></base-input>
        </div>
        <div class="col-md">
          <base-input
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            placeholder="0"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of damaged male-headed households"
            v-bind:max="maxValue"
            @input="init_totals('damaged_mhh', PeopleAffectedrows, 'total_houses_damaged_mhh')"
            v-model="value.damaged_mhh"
            label="damaged MHH"
          ></base-input>
        </div>

      </div>

      <div class="row row-example" v-for="(value, index) in PeopleAffectedrows" v-bind:key="index">

        <div class="col-md-12">
          <b>
            Impact on houses in
            <h3>(GVH : {{ value.name }})</h3>
          </b>
        </div>
        <div class="col-md">
          <base-input
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of partly blown roofs"
            placeholder="0"
            v-bind:max="maxValue"
            v-model="value.partly_blown_roof"
            label="partly blown roofs"
          ></base-input>
        </div>
        <div class="col-md">
          <base-input
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            placeholder="0"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of walls damaged"
            v-bind:max="maxValue"
            v-model="value.wall_damaged"
            label="with walls damaged"
          ></base-input>
        </div>

        <div class="col-md">
          <base-input
            onwinput="validity.valid||(value='');"
            type="number"
            min="0"
            placeholder="0"
            v-bind:max="maxValue"
            v-model="value.fully_blown_roof"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of fully blown roofs"
            label="with fully blown roofs"
          ></base-input>
        </div>

          <div class="col-md">
          <base-input
            onwinput="validity.valid||(value='');"
            type="number"
            min="0"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of burnt"
            placeholder="0"
            v-bind:max="maxValue"
            v-model="value.burnt"
            label="Burnt"
          ></base-input>
        </div>
      </div>

      <div class="row">
        <div class="col-md-12">
          <b>Total Households affected( {{TA}} )</b>
        </div>
        <div class="col-md-4">
          <base-input
            v-model.number="total_houses_damaged_fhh"
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            placeholder="0"
            v-bind:max="maxValue"
            label="Total FHH"
            disabled
          ></base-input>
        </div>
        <div class="col-md-4">
          <base-input
            v-model.number="total_houses_damaged_mhh"
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            placeholder="0"
            v-bind:max="maxValue"
            label="Total MHH"
            disabled
          ></base-input>
        </div>
        <div class="col-md-4">
          <base-input
            v-model.number="total_damaged"
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            placeholder="0"
            v-bind:max="maxValue"
            label="Total HH"
            disabled
          ></base-input>
        </div>
      </div>
      <hr>

      <div class="row">

        <div class="col-md-12">
        <b>
          EXTENT OF DAMAGE (How many of the
          <b><font color="red">{{ extent }}</font></b> households were affected in each of the following ways)
        </b>
        </div>
         <p v-if="errorAll === true" class="pl-3">
        <font color="red"><b>(Total of Partially damaged, Completely damaged and Under water</b> cannot exceed total extent of damage please check)</font>
      </p>
      </div><br/>

      <div class="row">
        <div class="col-md-4">
          <base-input
            v-model.number="femalecounter"
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            placeholder="0"
            v-bind:max="maxValue"
            @change="testIfPDgreater"
            @focus="testIfPDgreater"
            v-model="shelter.partly_damaged"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number partly damaged"
            label="Partly damaged"
          ></base-input>
          <p v-if="errorPD === true">
            <font color="red"><b>Partially damaged</b> cannot exceed total extent of damage</font>
          </p>
        </div>
        <div class="col-md-4">
          <base-input
            v-model.number="femalecounter"
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            placeholder="0"
            v-bind:max="maxValue"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number underwater"
            @change="testIfUWgreater"
            @focus="testIfPDgreater"
            v-model="shelter.under_water"
            label="Under water (submerged)"
          ></base-input>
          <p v-if="errorUW === true">
            <font color="red"><b>Under water</b> cannot exceed total extent of damage</font>
          </p>
        </div>
        <div class="col-md-4">
          <base-input
            v-model.number="femalecounter"
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            placeholder="0"
            v-bind:max="maxValue"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number completely damaged"
            @change="testIfCDgreater"
            @focus="testIfPDgreater"
            v-model="shelter.completely_damaged"
            label="Completely damaged"
          ></base-input>
          <p v-if="errorCD === true">
            <font color="red"><b>Completely damaged</b> cannot exceed total extent of damage</font>
          </p>
        </div>
        <div class="col-md">
          <base-input
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            placeholder="0"
            v-bind:max="maxValue"
            v-show="hide === false"
            disabled
            v-model="TestValidity"
            label="Total damage"
          ></base-input>
        </div>
      </div>
      <hr>
      <div class="pa-0 mt-0">
        <h2>
          <b>Other structure types damaged</b>
        </h2>
      </div>
      <form ref="form">
        <div class="row" v-for="(value, index) in other_structures_damaged" v-bind:key="index">

          <div class="col-md">
          <base-input label="Please choose structure" >
            <select class="form-control" v-model="value.name" data-toggle="tooltip"
                    data-placement="top"
                    title="Choose the structure affected">
              <option  v-for="structure in structures" :value="structure.name">{{structure.name}}</option>
            </select>
          </base-input>
        </div>

<div class="col-md pt-5">
        <base-radio
          name="burnt"
          class="mb-3"
          v-bind:value="'burnt'"
          data-toggle="tooltip"
                    data-placement="top"
                    title="Burnt"
          v-model="value.damage"
        >Burnt</base-radio>
      </div>

      <div class="col-md pt-5">
        <base-radio
          name="damaged"
          class="mb-3"
          v-bind:value="'damaged'"
          data-toggle="tooltip"
                    data-placement="top"
                    title="roofs blown-off"
          v-model="value.damage"
        >Blown-off roof</base-radio>
      </div>

       <div class="col-md pt-5">
            <base-button
              size="sm" type="warning" class="btn-icon-only rounded-circle noprint"
              data-toggle="tooltip"
                    data-placement="top"
                    title="Remove other structures damaged"
              v-if="other_structures_damaged.length > 0"
              @click="removeItemRow('other_structures_damaged',other_structures_damaged,  structures, index, 'name' )"
            >
              X
            </base-button>
          </div>

       <div class="col-md-12">
           <div class="col-md">
            <base-input
              v-model="value.partly_damaged"
              oninput="validity.valid||(value='');"
              data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number partly damaged"
              :rules="[(v) => !!v || 'value is required']"
              required
              type="number"
              min="0"
              placeholder="0"
              v-bind:max="maxValue"
              label="Partly damaged"
            ></base-input>
          </div>
          <div class="col-md">
            <base-input
              v-model="value.underwater"
              oninput="validity.valid||(value='');"
              data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number underwater"
              type="number"
              :rules="[(v) => !!v || 'value is required']"
              required
              min="0"
              placeholder="0"
              v-bind:max="maxValue"
              label="Under water"
            ></base-input>
          </div>
          <div class="col-md">
            <base-input
              v-model="value.completely_damaged"
              oninput="validity.valid||(value='');"
              type="number"
              :rules="[(v) => !!v || 'value is required']"
              required
              min="0"
              placeholder="0"
              v-bind:max="maxValue"
              data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number completely damaged"
              label="Completely damaged"
            ></base-input>
          </div>
       </div>

        </div>
      </form>

      <base-button
        size="md" type="info" class="btn-icon-only rounded-circle noprint" data-toggle="tooltip"
                    data-placement="top"
                    title="Add other structures damaged"
        @click="addItemRow('other_structures_damaged', other_structures_damaged, structures, 'name')"
      >
         <i class="ni ni-fat-add"></i>
      </base-button>
      <hr>
      <div class="pa-0 mt-0">
        <h2>
          <b>People without shelter</b>
        </h2>
      </div>
      <form  ref="form">
        <div class="row" v-for="(value, index) in people_without_shelter" v-bind:key="index">

              <div class="col-md">
          <base-input label="Please choose category">
            <select class="form-control" v-model="value.name" data-toggle="tooltip"
                    data-placement="top"
                    title="Choose a population type" @change="TestPopulationType(value.name, people_without_shelter, 'males')"
           >
              <option v-for="type in people_types" :value="type.name">{{type.name}}</option>
            </select>
          </base-input>
        </div>

          <div class="col-md">
            <base-input
              v-model.number="value.males"
              @input="init_totals('males', people_without_shelter, 'males')"
              oninput="validity.valid||(value='');"
              data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of males without shelter"
              type="number"
              :disabled="value.name === 'Pregnant Women'"
              min="0"
              :rules="[(v) => !!v || 'value is required']"
              required
              placeholder="0"
              v-bind:max="maxValue"
              label="Male"
            ></base-input>
          </div>
          <div class="col-md">
            <base-input
              oninput="validity.valid||(value='');"
              type="number"
              min="0"
              :rules="[(v) => !!v || 'value is required']"
              data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of females without shelter"
              required
              placeholder="0"
              @input="init_totals('females', people_without_shelter, 'females')"
              v-bind:max="maxValue"
              v-model="value.females"
              label="Female"
            ></base-input>
          </div>
          <div class="col-md pt-5">
            <base-button
               size="sm" type="warning" class="btn-icon-only rounded-circle noprint"
               data-toggle="tooltip"
                    data-placement="top"
                    title="Remove people without shelter"
               v-if="people_without_shelter.length > 0"
              @click="removeItemRow('people_without_shelter',people_without_shelter,  people_types, index, 'name', ['females','males'] )"
            >
              X
            </base-button>
          </div>
        </div>
      </form>
      <base-button
       size="md" type="info" class="btn-icon-only rounded-circle noprint"
       data-toggle="tooltip"
                    data-placement="top"
                    title="Add people without shelter"
          @click="addItemRow('people_without_shelter',  people_without_shelter, people_types, 'name')"
      >
            <i class="ni ni-fat-add"></i>

      </base-button>
      <hr>

      <div class="row" v-if="people_without_shelter.length > 0">
        <div class="col-md"></div>
        <div class="col-md">Total males: {{ males_without_shelter }}</div>
        <div class="col-md">Total females: {{ females_without_shelter }}</div>

        <div class="col-md">Total : {{ total_people_without_shelter }}</div>
      </div>
      <hr>
      <div class="row">
        <div class="col-md-12">
          <div slot="label">
            <h2>
              <b>People injured</b>
            </h2>
          </div>
        </div>
      </div>

      <div class="row" v-for="(row, index) in PeopleInjuredrows" v-bind:key="index">

        <div class="col-md-12">
        <b>
            Number of injured people in
            <h3>(GVH : {{ row.name }})</h3>
          </b>
      </div>


        <div class="col-md">
          <base-input
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            placeholder="0"
            @mousedown="checkIfValueExceeds"
            @keyup="checkIfValueExceeds"
            @focus="checkIfValueExceeds"
            @change="checkIfValueExceeds"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of injured males"
            v-bind:max="maxValue"
            @input="init_totals('people_injured_males', PeopleInjuredrows, 'total_people_injured_males')"
            v-model="row.people_injured_males"
            label="Males"
          ></base-input>
        </div>
        <div class="col-md">
          <base-input
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            placeholder="0"
            @mousedown="checkIfValueExceeds"
            @keyup="checkIfValueExceeds"
            @focus="checkIfValueExceeds"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of injured females"
            @change="checkIfValueExceeds"
            v-bind:max="maxValue"
            @input="init_totals('people_injured_females', PeopleInjuredrows, 'total_people_injured_females')"
            v-model="row.people_injured_females"
            label="Female"
          ></base-input>
        </div>
      </div>
      <hr>
      <div class="row">
        <div class="col-md-12">
          <div class="pa-0 mt-0">
            <b>Total number of people injured ({{TA}})</b>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-4">
          <base-input
            v-model.number="total_people_injured_males"
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            placeholder="0"
            v-bind:max="maxValue"
            label="Total males"
            disabled
          ></base-input>
        </div>
        <div class="col-md-4">
          <base-input
            v-model.number="total_people_injured_females"
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            placeholder="0"
            v-bind:max="maxValue"
            label="Total females"
            disabled
          ></base-input>
        </div>

        <div class="col-md-4">
          <base-input
            v-model.number="total_people_injured"
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            placeholder="0"
            v-bind:max="maxValue"
            label="Total"
            disabled
          ></base-input>
        </div>
      </div>

    <!--   <p v-if="errorPDI === true">
        <b>
          People dead and Injured
          <font
            color="red"
          >({{ total_people_dead + total_people_injured }})</font>
        </b> cannot exceed total
        <b>
          people affected
          <font color="red">({{ total_damaged }})</font>
        </b> (NB: Please resolve these discrepancies)
      </p>
 -->
      <hr>
      <div class="row">
        <div class="col-md">
          <div slot="label">
            <h2>
              <b>People dead</b>
            </h2>
          </div>
        </div>
      </div>

      <div class="row" v-for="(row, index) in PeopleDeadrows" v-bind:key="index">
        <div class="col-md-12">
          <b>
            Number of dead people in
            <h3>(GVH : {{ row.name }})</h3>
          </b>
        </div>
        <div class="col-md">
          <base-input
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            placeholder="0"
            v-bind:max="maxValue"
            @mousedown="checkIfValueExceeds"
            @keyup="checkIfValueExceeds"
            @focus="checkIfValueExceeds"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of females dead"
            @change="checkIfValueExceeds"
            @input="init_totals('females_dead', PeopleDeadrows, 'total_people_dead_females')"
            v-model="row.females_dead"
            label="Female"
          ></base-input>
        </div>
        <div class="col-md">
          <base-input
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            placeholder="0"
            @mousedown="checkIfValueExceeds"
            @keyup="checkIfValueExceeds"
            @focus="checkIfValueExceeds"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Enter the number of males dead"
            @change="checkIfValueExceeds"
            v-bind:max="maxValue"
            @input="init_totals('males_dead', PeopleDeadrows, 'total_people_dead_males')"
            v-model="row.males_dead"
            label="Male"
          ></base-input>
        </div>
        <div class="col-md">
          <base-input
            v-model="row.cause_of_death"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Type the cause of death"
            oninput="validity.valid||(value='');"
            label="Cause of death"
          ></base-input>
        </div>
      </div>
      <hr>
      <div class="row">
        <div class="col-md-12">
          <div class="pa-0 mt-0">
            <b>Total Number of people dead ( {{TA}})</b>
          </div>
        </div>
        <div class="col-md">
          <base-input
            v-model.number="total_people_dead_females"
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            placeholder="0"
            v-bind:max="maxValue"
            label="Total females"
            disabled
          ></base-input>
        </div>
        <div class="col-md">
          <base-input
            v-model.number="total_people_dead_males"
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            placeholder="0"
            v-bind:max="maxValue"
            label="Total males"
            disabled
          ></base-input>
        </div>

        <div class="col-md">
          <base-input
            v-model.number="total_people_dead"
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            placeholder="0"

            v-bind:max="maxValue"
            label="Total"
            disabled
          ></base-input>
        </div>
      </div>

    <!--   <p v-if="errorPDI === true">
        <b>
          People dead and Injured
          <font
            color="red"
          >({{ total_people_dead + total_people_injured }})</font>
        </b> cannot exceed total
        <b>
          people affected
          <font color="red">({{ total_damaged }})</font>
        </b> (NB: Please resolve these discrepancies)
      </p> -->
    </div>
    <hr>
    <b>Response Needed for the Shelter Cluster</b>
    <hr class="mt-3 mb-3">
    <base-input label="General Response needed for shelter cluster">
      <textarea
        class="form-control"
        id="exampleFormControlTextarea3"
        data-toggle="tooltip"
                    data-placement="top"
                    title="Type the general response neeeded"
        placeholder="Type the response needed for the shelter cluster"
        v-model="response_needed"
        rows="3"
      ></textarea>
    </base-input>
    <base-input label="Urgent Response needed for shelter cluster">
      <textarea
        class="form-control"
        id="exampleFormControlTextarea3"
        data-toggle="tooltip"
                    data-placement="top"
                    title="Type the urgent response needed"
        placeholder="Type the urgent response needed for the shelter cluster"
        v-model="urgent_response_needed"
        rows="3"
      ></textarea>
    </base-input>
    <base-button size="lg" type="primary"  data-toggle="tooltip"
                    data-placement="top"
                    title="Save and goto next section" @click.stop="save" class="noprint">Save & Continue</base-button>

  </div>

</template>
<script src="./index.js"/>

<style scoped>
.alert-danger {
  color: red;
}
.has-error {
  border-color: red;
}
@media print {
  .page-break {
    overflow-y: visible;
    display: block;
  }
  .not-active {
    pointer-events: none;
    cursor: default;
    text-decoration: none;
    color: black;
  }
  .noprint {
    visibility: hidden;
  }
}
</style>
