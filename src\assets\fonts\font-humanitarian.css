/*!
 *  Humanitarian Font 
 *  the iconic font designed for the Humanitarian
 *  -------------------------------------------------------
 *  The full suite of pictographic icons, examples, and documentation
 *  can be found at: https://github.com/unhcr/Humanitarian-Font
 *
 *  License
 *  -------------------------------------------------------
 *  - The Humanitarian Font font is licensed under the SIL Open Font License - http://scripts.sil.org/OFL
 *  - Humanitarian Font CSS, LESS, and SASS files are licensed under the MIT License -
 *    http://opensource.org/licenses/mit-license.html
 *  - The Humanitarian Font pictograms are licensed under the CC BY 3.0 License - http://creativecommons.org/licenses/by/3.0/
 *  - Attribution is not required in Humanitarian Font  but much appreciated:
 *    "https://unhcr.github.io/Humanitarian-Font"

 *  Credit
 *  -------------------------------------------------------
 *  Email: <EMAIL>
 *  Twitter: http://twitter.com/fortaweso_me
 *  Work: Lead Product Designer @ http://kyruus.com
 */
@font-face {
  font-family: 'FontAwesome';
  src: url('humanitarian-webfont.eot');
  src: url('humanitarian-webfont.eot') format('embedded-opentype'),
    url('humanitarian-webfont.woff') format('woff'),
    url('humanitarian-webfont.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}
/*  Humanitarian Font styles
    ------------------------------------------------------- */
[class^="icon-"],
[class*=" icon-"] {
  font-family: FontAwesome;
  font-weight: normal;
  font-style: normal;
  text-decoration: inherit;
  -webkit-font-smoothing: antialiased;

  /* sprites.less reset */
  display: inline;
  width: auto;
  height: auto;
  line-height: normal;
  vertical-align: baseline;
  background-image: none;
  background-position: 0% 0%;
  background-repeat: repeat;
  margin-top: 0;
}
/* more sprites.less reset */
.icon-white,
.nav-pills > .active > a > [class^="icon-"],
.nav-pills > .active > a > [class*=" icon-"],
.nav-list > .active > a > [class^="icon-"],
.nav-list > .active > a > [class*=" icon-"],
.navbar-inverse .nav > .active > a > [class^="icon-"],
.navbar-inverse .nav > .active > a > [class*=" icon-"],
.dropdown-menu > li > a:hover > [class^="icon-"],
.dropdown-menu > li > a:hover > [class*=" icon-"],
.dropdown-menu > .active > a > [class^="icon-"],
.dropdown-menu > .active > a > [class*=" icon-"],
.dropdown-submenu:hover > a > [class^="icon-"],
.dropdown-submenu:hover > a > [class*=" icon-"] {
  background-image: none;
}
[class^="icon-"]:before,
[class*=" icon-"]:before {
  text-decoration: inherit;
  display: inline-block;
  speak: none;
}
/* makes sure icons active on rollover in links */
a [class^="icon-"],
a [class*=" icon-"] {
  display: inline-block;
}
/* makes the font 33% larger relative to the icon container */
.icon-large:before {
  vertical-align: -10%;
  font-size: 1.3333333333333333em;
}
.btn [class^="icon-"],
.nav [class^="icon-"],
.btn [class*=" icon-"],
.nav [class*=" icon-"] {
  display: inline;
  /* keeps button heights with and without icons the same */

}
.btn [class^="icon-"].icon-large,
.nav [class^="icon-"].icon-large,
.btn [class*=" icon-"].icon-large,
.nav [class*=" icon-"].icon-large {
  line-height: .9em;
}
.btn [class^="icon-"].icon-spin,
.nav [class^="icon-"].icon-spin,
.btn [class*=" icon-"].icon-spin,
.nav [class*=" icon-"].icon-spin {
  display: inline-block;
}
.nav-tabs [class^="icon-"],
.nav-pills [class^="icon-"],
.nav-tabs [class*=" icon-"],
.nav-pills [class*=" icon-"] {
  /* keeps button heights with and without icons the same */

}
.nav-tabs [class^="icon-"],
.nav-pills [class^="icon-"],
.nav-tabs [class*=" icon-"],
.nav-pills [class*=" icon-"],
.nav-tabs [class^="icon-"].icon-large,
.nav-pills [class^="icon-"].icon-large,
.nav-tabs [class*=" icon-"].icon-large,
.nav-pills [class*=" icon-"].icon-large {
  line-height: .9em;
}
li [class^="icon-"],
.nav li [class^="icon-"],
li [class*=" icon-"],
.nav li [class*=" icon-"] {
  display: inline-block;
  width: 1.25em;
  text-align: center;
}
li [class^="icon-"].icon-large,
.nav li [class^="icon-"].icon-large,
li [class*=" icon-"].icon-large,
.nav li [class*=" icon-"].icon-large {
  /* increased font size for icon-large */

  width: 1.5625em;
}
ul.icons {
  list-style-type: none;
  text-indent: -0.75em;
}
ul.icons li [class^="icon-"],
ul.icons li [class*=" icon-"] {
  width: .75em;
}
.icon-muted {
  color: #eeeeee;
}
.icon-border {
  border: solid 1px #eeeeee;
  padding: .2em .25em .15em;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}
.icon-2x {
  font-size: 2em;
}
.icon-2x.icon-border {
  border-width: 2px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.icon-3x {
  font-size: 3em;
}
.icon-3x.icon-border {
  border-width: 3px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
}
.icon-4x {
  font-size: 4em;
}
.icon-4x.icon-border {
  border-width: 4px;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  border-radius: 6px;
}
.pull-right {
  float: right;
}
.pull-left {
  float: left;
}
[class^="icon-"].pull-left,
[class*=" icon-"].pull-left {
  margin-right: .3em;
}
[class^="icon-"].pull-right,
[class*=" icon-"].pull-right {
  margin-left: .3em;
}
.btn [class^="icon-"].pull-left.icon-2x,
.btn [class*=" icon-"].pull-left.icon-2x,
.btn [class^="icon-"].pull-right.icon-2x,
.btn [class*=" icon-"].pull-right.icon-2x {
  margin-top: .18em;
}
.btn [class^="icon-"].icon-spin.icon-large,
.btn [class*=" icon-"].icon-spin.icon-large {
  line-height: .8em;
}
.btn.btn-small [class^="icon-"].pull-left.icon-2x,
.btn.btn-small [class*=" icon-"].pull-left.icon-2x,
.btn.btn-small [class^="icon-"].pull-right.icon-2x,
.btn.btn-small [class*=" icon-"].pull-right.icon-2x {
  margin-top: .25em;
}
.btn.btn-large [class^="icon-"],
.btn.btn-large [class*=" icon-"] {
  margin-top: 0;
}
.btn.btn-large [class^="icon-"].pull-left.icon-2x,
.btn.btn-large [class*=" icon-"].pull-left.icon-2x,
.btn.btn-large [class^="icon-"].pull-right.icon-2x,
.btn.btn-large [class*=" icon-"].pull-right.icon-2x {
  margin-top: .05em;
}
.btn.btn-large [class^="icon-"].pull-left.icon-2x,
.btn.btn-large [class*=" icon-"].pull-left.icon-2x {
  margin-right: .2em;
}
.btn.btn-large [class^="icon-"].pull-right.icon-2x,
.btn.btn-large [class*=" icon-"].pull-right.icon-2x {
  margin-left: .2em;
}
.icon-spin {
  display: inline-block;
  -moz-animation: spin 2s infinite linear;
  -o-animation: spin 2s infinite linear;
  -webkit-animation: spin 2s infinite linear;
  animation: spin 2s infinite linear;
}
@-moz-keyframes spin {
  0% { -moz-transform: rotate(0deg); }
  100% { -moz-transform: rotate(359deg); }
}
@-webkit-keyframes spin {
  0% { -webkit-transform: rotate(0deg); }
  100% { -webkit-transform: rotate(359deg); }
}
@-o-keyframes spin {
  0% { -o-transform: rotate(0deg); }
  100% { -o-transform: rotate(359deg); }
}
@-ms-keyframes spin {
  0% { -ms-transform: rotate(0deg); }
  100% { -ms-transform: rotate(359deg); }
}
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(359deg); }
}
@-moz-document url-prefix() {
  .icon-spin {
    height: .9em;
  }
  .btn .icon-spin {
    height: auto;
  }
  .icon-spin.icon-large {
    height: 1.25em;
  }
  .btn .icon-spin.icon-large {
    height: .75em;
  }
}
/*  Humanitarian Font uses the Unicode Private Use Area (PUA) to ensure screen
    readers do not read off random characters that represent icons */
.icon-glass:before                { content: "\f000"; }
.icon-music:before                { content: "\f001"; }
.icon-search:before               { content: "\f002"; }
.icon-envelope:before             { content: "\f003"; }
.icon-heart:before                { content: "\f004"; }
.icon-star:before                 { content: "\f005"; }
.icon-star-empty:before           { content: "\f006"; }
.icon-user:before                 { content: "\f007"; }
.icon-film:before                 { content: "\f008"; }
.icon-th-large:before             { content: "\f009"; }
.icon-th:before                   { content: "\f00a"; }
.icon-th-list:before              { content: "\f00b"; }
.icon-ok:before                   { content: "\f00c"; }
.icon-remove:before               { content: "\f00d"; }
.icon-zoom-in:before              { content: "\f00e"; }

.icon-zoom-out:before             { content: "\f010"; }
.icon-off:before                  { content: "\f011"; }
.icon-signal:before               { content: "\f012"; }
.icon-cog:before                  { content: "\f013"; }
.icon-trash:before                { content: "\f014"; }
.icon-home:before                 { content: "\f015"; }
.icon-file:before                 { content: "\f016"; }
.icon-time:before                 { content: "\f017"; }
.icon-road:before                 { content: "\f018"; }
.icon-download-alt:before         { content: "\f019"; }
.icon-download:before             { content: "\f01a"; }
.icon-upload:before               { content: "\f01b"; }
.icon-inbox:before                { content: "\f01c"; }
.icon-play-circle:before          { content: "\f01d"; }
.icon-repeat:before               { content: "\f01e"; }

/* \f020 doesn't work in Safari. all shifted one down */
.icon-refresh:before              { content: "\f021"; }
.icon-list-alt:before             { content: "\f022"; }
.icon-lock:before                 { content: "\f023"; }
.icon-flag:before                 { content: "\f024"; }
.icon-headphones:before           { content: "\f025"; }
.icon-volume-off:before           { content: "\f026"; }
.icon-volume-down:before          { content: "\f027"; }
.icon-volume-up:before            { content: "\f028"; }
.icon-qrcode:before               { content: "\f029"; }
.icon-barcode:before              { content: "\f02a"; }
.icon-tag:before                  { content: "\f02b"; }
.icon-tags:before                 { content: "\f02c"; }
.icon-book:before                 { content: "\f02d"; }
.icon-bookmark:before             { content: "\f02e"; }
.icon-print:before                { content: "\f02f"; }

.icon-camera:before               { content: "\f030"; }
.icon-font:before                 { content: "\f031"; }
.icon-bold:before                 { content: "\f032"; }
.icon-italic:before               { content: "\f033"; }
.icon-text-height:before          { content: "\f034"; }
.icon-text-width:before           { content: "\f035"; }
.icon-align-left:before           { content: "\f036"; }
.icon-align-center:before         { content: "\f037"; }
.icon-align-right:before          { content: "\f038"; }
.icon-align-justify:before        { content: "\f039"; }
.icon-list:before                 { content: "\f03a"; }
.icon-indent-left:before          { content: "\f03b"; }
.icon-indent-right:before         { content: "\f03c"; }
.icon-facetime-video:before       { content: "\f03d"; }
.icon-picture:before              { content: "\f03e"; }

.icon-pencil:before               { content: "\f040"; }
.icon-map-marker:before           { content: "\f041"; }
.icon-adjust:before               { content: "\f042"; }
.icon-tint:before                 { content: "\f043"; }
.icon-edit:before                 { content: "\f044"; }
.icon-share:before                { content: "\f045"; }
.icon-check:before                { content: "\f046"; }
.icon-move:before                 { content: "\f047"; }
.icon-step-backward:before        { content: "\f048"; }
.icon-fast-backward:before        { content: "\f049"; }
.icon-backward:before             { content: "\f04a"; }
.icon-play:before                 { content: "\f04b"; }
.icon-pause:before                { content: "\f04c"; }
.icon-stop:before                 { content: "\f04d"; }
.icon-forward:before              { content: "\f04e"; }

.icon-fast-forward:before         { content: "\f050"; }
.icon-step-forward:before         { content: "\f051"; }
.icon-eject:before                { content: "\f052"; }
.icon-chevron-left:before         { content: "\f053"; }
.icon-chevron-right:before        { content: "\f054"; }
.icon-plus-sign:before            { content: "\f055"; }
.icon-minus-sign:before           { content: "\f056"; }
.icon-remove-sign:before          { content: "\f057"; }
.icon-ok-sign:before              { content: "\f058"; }
.icon-question-sign:before        { content: "\f059"; }
.icon-info-sign:before            { content: "\f05a"; }
.icon-screenshot:before           { content: "\f05b"; }
.icon-remove-circle:before        { content: "\f05c"; }
.icon-ok-circle:before            { content: "\f05d"; }
.icon-ban-circle:before           { content: "\f05e"; }

.icon-arrow-left:before           { content: "\f060"; }
.icon-arrow-right:before          { content: "\f061"; }
.icon-arrow-up:before             { content: "\f062"; }
.icon-arrow-down:before           { content: "\f063"; }
.icon-share-alt:before            { content: "\f064"; }
.icon-resize-full:before          { content: "\f065"; }
.icon-resize-small:before         { content: "\f066"; }
.icon-plus:before                 { content: "\f067"; }
.icon-minus:before                { content: "\f068"; }
.icon-asterisk:before             { content: "\f069"; }
.icon-exclamation-sign:before     { content: "\f06a"; }
.icon-gift:before                 { content: "\f06b"; }
.icon-leaf:before                 { content: "\f06c"; }
.icon-fire:before                 { content: "\f06d"; }
.icon-eye-open:before             { content: "\f06e"; }

.icon-eye-close:before            { content: "\f070"; }
.icon-warning-sign:before         { content: "\f071"; }
.icon-plane:before                { content: "\f072"; }
.icon-calendar:before             { content: "\f073"; }
.icon-random:before               { content: "\f074"; }
.icon-comment:before              { content: "\f075"; }
.icon-magnet:before               { content: "\f076"; }
.icon-chevron-up:before           { content: "\f077"; }
.icon-chevron-down:before         { content: "\f078"; }
.icon-retweet:before              { content: "\f079"; }
.icon-shopping-cart:before        { content: "\f07a"; }
.icon-folder-close:before         { content: "\f07b"; }
.icon-folder-open:before          { content: "\f07c"; }
.icon-resize-vertical:before      { content: "\f07d"; }
.icon-resize-horizontal:before    { content: "\f07e"; }

.icon-bar-chart:before            { content: "\f080"; }
.icon-twitter-sign:before         { content: "\f081"; }
.icon-facebook-sign:before        { content: "\f082"; }
.icon-camera-retro:before         { content: "\f083"; }
.icon-key:before                  { content: "\f084"; }
.icon-cogs:before                 { content: "\f085"; }
.icon-comments:before             { content: "\f086"; }
.icon-thumbs-up:before            { content: "\f087"; }
.icon-thumbs-down:before          { content: "\f088"; }
.icon-star-half:before            { content: "\f089"; }
.icon-heart-empty:before          { content: "\f08a"; }
.icon-signout:before              { content: "\f08b"; }
.icon-linkedin-sign:before        { content: "\f08c"; }
.icon-pushpin:before              { content: "\f08d"; }
.icon-external-link:before        { content: "\f08e"; }

.icon-signin:before               { content: "\f090"; }
.icon-trophy:before               { content: "\f091"; }
.icon-github-sign:before          { content: "\f092"; }
.icon-upload-alt:before           { content: "\f093"; }
.icon-lemon:before                { content: "\f094"; }
.icon-phone:before                { content: "\f095"; }
.icon-check-empty:before          { content: "\f096"; }
.icon-bookmark-empty:before       { content: "\f097"; }
.icon-phone-sign:before           { content: "\f098"; }
.icon-twitter:before              { content: "\f099"; }
.icon-facebook:before             { content: "\f09a"; }
.icon-github:before               { content: "\f09b"; }
.icon-unlock:before               { content: "\f09c"; }
.icon-credit-card:before          { content: "\f09d"; }
.icon-rss:before                  { content: "\f09e"; }

.icon-hdd:before                  { content: "\f0a0"; }
.icon-bullhorn:before             { content: "\f0a1"; }
.icon-bell:before                 { content: "\f0a2"; }
.icon-certificate:before          { content: "\f0a3"; }
.icon-hand-right:before           { content: "\f0a4"; }
.icon-hand-left:before            { content: "\f0a5"; }
.icon-hand-up:before              { content: "\f0a6"; }
.icon-hand-down:before            { content: "\f0a7"; }
.icon-circle-arrow-left:before    { content: "\f0a8"; }
.icon-circle-arrow-right:before   { content: "\f0a9"; }
.icon-circle-arrow-up:before      { content: "\f0aa"; }
.icon-circle-arrow-down:before    { content: "\f0ab"; }
.icon-globe:before                { content: "\f0ac"; }
.icon-wrench:before               { content: "\f0ad"; }
.icon-tasks:before                { content: "\f0ae"; }

.icon-filter:before               { content: "\f0b0"; }
.icon-briefcase:before            { content: "\f0b1"; }
.icon-fullscreen:before           { content: "\f0b2"; }

.icon-group:before                { content: "\f0c0"; }
.icon-link:before                 { content: "\f0c1"; }
.icon-cloud:before                { content: "\f0c2"; }
.icon-beaker:before               { content: "\f0c3"; }
.icon-cut:before                  { content: "\f0c4"; }
.icon-copy:before                 { content: "\f0c5"; }
.icon-paper-clip:before           { content: "\f0c6"; }
.icon-save:before                 { content: "\f0c7"; }
.icon-sign-blank:before           { content: "\f0c8"; }
.icon-reorder:before              { content: "\f0c9"; }
.icon-list-ul:before              { content: "\f0ca"; }
.icon-list-ol:before              { content: "\f0cb"; }
.icon-strikethrough:before        { content: "\f0cc"; }
.icon-underline:before            { content: "\f0cd"; }
.icon-table:before                { content: "\f0ce"; }

.icon-magic:before                { content: "\f0d0"; }
.icon-truck:before                { content: "\f0d1"; }
.icon-pinterest:before            { content: "\f0d2"; }
.icon-pinterest-sign:before       { content: "\f0d3"; }
.icon-google-plus-sign:before     { content: "\f0d4"; }
.icon-google-plus:before          { content: "\f0d5"; }
.icon-money:before                { content: "\f0d6"; }
.icon-caret-down:before           { content: "\f0d7"; }
.icon-caret-up:before             { content: "\f0d8"; }
.icon-caret-left:before           { content: "\f0d9"; }
.icon-caret-right:before          { content: "\f0da"; }
.icon-columns:before              { content: "\f0db"; }
.icon-sort:before                 { content: "\f0dc"; }
.icon-sort-down:before            { content: "\f0dd"; }
.icon-sort-up:before              { content: "\f0de"; }

.icon-envelope-alt:before         { content: "\f0e0"; }
.icon-linkedin:before             { content: "\f0e1"; }
.icon-undo:before                 { content: "\f0e2"; }
.icon-legal:before                { content: "\f0e3"; }
.icon-dashboard:before            { content: "\f0e4"; }
.icon-comment-alt:before          { content: "\f0e5"; }
.icon-comments-alt:before         { content: "\f0e6"; }
.icon-bolt:before                 { content: "\f0e7"; }
.icon-sitemap:before              { content: "\f0e8"; }
.icon-umbrella:before             { content: "\f0e9"; }
.icon-paste:before                { content: "\f0ea"; }
.icon-lightbulb:before            { content: "\f0eb"; }
.icon-exchange:before             { content: "\f0ec"; }
.icon-cloud-download:before       { content: "\f0ed"; }
.icon-cloud-upload:before         { content: "\f0ee"; }

.icon-user-md:before              { content: "\f0f0"; }
.icon-stethoscope:before          { content: "\f0f1"; }
.icon-suitcase:before             { content: "\f0f2"; }
.icon-bell-alt:before             { content: "\f0f3"; }
.icon-coffee:before               { content: "\f0f4"; }
.icon-food:before                 { content: "\f0f5"; }
.icon-file-alt:before             { content: "\f0f6"; }
.icon-building:before             { content: "\f0f7"; }
.icon-hospital:before             { content: "\f0f8"; }
.icon-ambulance:before            { content: "\f0f9"; }
.icon-medkit:before               { content: "\f0fa"; }
.icon-fighter-jet:before          { content: "\f0fb"; }
.icon-beer:before                 { content: "\f0fc"; }
.icon-h-sign:before               { content: "\f0fd"; }
.icon-plus-sign-alt:before        { content: "\f0fe"; }

.icon-double-angle-left:before    { content: "\f100"; }
.icon-double-angle-right:before   { content: "\f101"; }
.icon-double-angle-up:before      { content: "\f102"; }
.icon-double-angle-down:before    { content: "\f103"; }
.icon-angle-left:before           { content: "\f104"; }
.icon-angle-right:before          { content: "\f105"; }
.icon-angle-up:before             { content: "\f106"; }
.icon-angle-down:before           { content: "\f107"; }
.icon-desktop:before              { content: "\f108"; }
.icon-laptop:before               { content: "\f109"; }
.icon-tablet:before               { content: "\f10a"; }
.icon-mobile-phone:before         { content: "\f10b"; }
.icon-circle-blank:before         { content: "\f10c"; }
.icon-quote-left:before           { content: "\f10d"; }
.icon-quote-right:before          { content: "\f10e"; }

.icon-spinner:before              { content: "\f110"; }
.icon-circle:before               { content: "\f111"; }
.icon-reply:before                { content: "\f112"; }
.icon-github-alt:before           { content: "\f113"; }
.icon-folder-close-alt:before     { content: "\f114"; }
.icon-folder-open-alt:before      { content: "\f115"; }


/* Here start the humanitarian ones */

/* Sector */
.icon-basicneeds:before              { content: "\f5e4"; }
.icon-ocha-sector-registration:before              { content: "\f300"; }
.icon-ocha-sector-security:before             { content: "\f301"; }
.icon-ocha-sector-recovery:before             { content: "\f361"; }
.icon-ocha-sector-education:before             { content: "\f362"; }
.icon-ocha-sector-telecommunication:before             { content: "\f367"; }
.icon-ocha-sector-foodsecurity:before             { content: "\f366"; }
.icon-ocha-sector-health:before             { content: "\f364"; }
.icon-ocha-sector-logistic:before             { content: "\f365"; }
.icon-ocha-sector-wash:before             { content: "\f369"; }
.icon-ocha-sector-agriculture:before             { content: "\f359"; }
.icon-ocha-sector-coordination:before             { content: "\f371"; }
.icon-ocha-sector-protection:before             { content: "\f368"; }
.icon-ocha-sector-campmanagement:before             { content: "\f342"; }
.icon-ocha-sector-livelihood:before             { content: "\f36a"; }
.icon-ocha-sector-sgbv:before             { content: "\f3c8"; }
.icon-ocha-sector-community:before             { content: "\f55c"; }
.icon-ocha-sector-shelter:before             { content: "\f363"; }
.icon-ocha-sector-childprotection:before             { content: "\f334"; }
.icon-ocha-sector-cash:before             { content: "\f32a"; }


/* New Sector */
.icon-new-basicneeds:before              { content: "\f5e5"; }
.icon-new-sector-education:before             { content: "\f5e8"; }
.icon-new-sector-foodsecurity:before             { content: "\f5e7"; }
.icon-new-sector-health:before             { content: "\e7a1"; }
.icon-new-sector-wash:before             { content: "\e7a6"; }
.icon-new-sector-coordination:before             { content: "\e7a7"; }
.icon-new-sector-protection:before             { content: "\e7a3"; }
.icon-new-sector-livelihood:before             { content: "\e7a2"; }
.icon-new-sector-community:before             { content: "\e7a5"; }
.icon-new-sector-shelter:before             { content: "\e7a4"; }
.icon-new-refugee:before             { content: "\e7a9"; }
.icon-new-program:before             { content: "\e7a8"; }




/* Affected population */
.icon-ocha-affected-population:before              { content: "\f3b0"; }
.icon-ocha-affected-man:before             { content: "\f30c"; }
.icon-ocha-affected-woman:before             { content: "\f30d"; }
.icon-ocha-affected-elderly:before             { content: "\f30e"; }
.icon-ocha-affected-baby:before             { content: "\f30f"; }
.icon-ocha-affected-child:before             { content: "\f31a"; }
.icon-ocha-affected-disabled:before             { content: "\f40d"; }
.icon-ocha-affected-dead:before             { content: "\f40a"; }
.icon-child_soldier:before              { content: "\f54f"; }
.icon-individual:before              { content: "\f5a8"; }

/* Relief Item */
.icon-ocha-item-blanket:before             { content: "\f401"; }
.icon-ocha-item-bucket:before             { content: "\f402"; }
.icon-ocha-item-clothing:before             { content: "\f403"; }
.icon-ocha-item-food:before             { content: "\f404"; }
.icon-ocha-item-kitchenset:before             { content: "\f405"; }
.icon-ocha-item-mattress:before             { content: "\f406"; }
.icon-ocha-item-vaccine:before             { content: "\f407"; }
.icon-ocha-item-plasticsheeting:before             { content: "\f411"; }
.icon-ocha-item-reliefgood:before             { content: "\f412"; }
.icon-ocha-item-stove:before             { content: "\f413"; }
.icon-ocha-item-tarpaulin:before             { content: "\f414"; }
.icon-ocha-item-tent:before             { content: "\f415"; }
.icon-ocha-item-mosquitonet:before             { content: "\f409"; }
.icon-ocha-item-nonfooditem:before             { content: "\f410"; }
.icon-ocha-cash:before             { content: "\f533"; }

/* Event */
.icon-event-bluehelmet:before             { content: "\f39f"; }
.icon-event-force:before             { content: "\f40e"; }
.icon-event-collaborate:before             { content: "\f41b"; }
.icon-event-foodshortage:before             { content: "\f57a"; }
.icon-event-judge:before             { content: "\f564"; }
.icon-event-protest:before             { content: "\f569"; }
.icon-event-cashwork:before             { content: "\f573"; }
.icon-event-handshake:before             { content: "\f557"; }
.icon-event-abduction:before             { content: "\f554"; }
.icon-event-papercontrol:before             { content: "\f559"; }
.icon-event-lecturer:before             { content: "\f566"; }
.icon-event-hit:before             { content: "\f548"; }
.icon-event-gardening:before             { content: "\f574"; }
.icon-event-register:before             { content: "\f560"; }
.icon-event-violence:before             { content: "\f55e"; }

/* Relief Item 
.icon-ocha-security:before             { content: "\f327"; }
.icon-ocha-security:before             { content: "\f328"; }
.icon-ocha-security:before             { content: "\f329"; }
.icon-ocha-security:before             { content: "\f330"; }
.icon-ocha-security:before             { content: "\f331"; }
.icon-ocha-security:before             { content: "\f332"; }
.icon-ocha-security:before             { content: "\f333"; }
.icon-ocha-security:before             { content: "\f334"; }
.icon-ocha-security:before             { content: "\f335"; }
.icon-ocha-security:before             { content: "\f336"; }
.icon-ocha-security:before             { content: "\f337"; }
.icon-ocha-security:before             { content: "\f338"; }
.icon-ocha-security:before             { content: "\f339"; }
.icon-ocha-security:before             { content: "\f340"; }
.icon-ocha-security:before             { content: "\f341"; }
.icon-ocha-security:before             { content: "\f342"; }
.icon-ocha-security:before             { content: "\f343"; }
.icon-ocha-security:before             { content: "\f344"; }
.icon-ocha-security:before             { content: "\f345"; }
.icon-ocha-security:before             { content: "\f346"; }
.icon-ocha-security:before             { content: "\f347"; }
.icon-ocha-security:before             { content: "\f348"; }
.icon-ocha-security:before             { content: "\f349"; }
.icon-ocha-security:before             { content: "\f350"; }
.icon-ocha-security:before             { content: "\f351"; }
.icon-ocha-security:before             { content: "\f352"; }
.icon-ocha-security:before             { content: "\f353"; }
.icon-ocha-security:before             { content: "\f354"; }
.icon-ocha-security:before             { content: "\f355"; }
.icon-ocha-security:before             { content: "\f356"; }
.icon-ocha-security:before             { content: "\f357"; }
.icon-ocha-security:before             { content: "\f358"; }
.icon-ocha-security:before             { content: "\f359"; }
.icon-ocha-security:before             { content: "\f360"; }
.icon-ocha-security:before             { content: "\f361"; }
.icon-ocha-security:before             { content: "\f362"; }
.icon-ocha-security:before             { content: "\f363"; }
.icon-ocha-security:before             { content: "\f364"; }
.icon-ocha-security:before             { content: "\f365"; }
.icon-ocha-security:before             { content: "\f366"; }
.icon-ocha-security:before             { content: "\f367"; }
.icon-ocha-security:before             { content: "\f368"; }
.icon-ocha-security:before             { content: "\f369"; }
.icon-ocha-security:before             { content: "\f370"; }
.icon-ocha-security:before             { content: "\f371"; }
.icon-ocha-security:before             { content: "\f372"; }
.icon-ocha-security:before             { content: "\f373"; }
.icon-ocha-security:before             { content: "\f374"; }
.icon-ocha-security:before             { content: "\f375"; }
.icon-ocha-security:before             { content: "\f376"; }
.icon-ocha-security:before             { content: "\f377"; }
.icon-ocha-security:before             { content: "\f378"; }
.icon-ocha-security:before             { content: "\f379"; }
.icon-ocha-security:before             { content: "\f380"; }
.icon-ocha-security:before             { content: "\f381"; }
.icon-ocha-security:before             { content: "\f382"; }
.icon-ocha-security:before             { content: "\f383"; }
.icon-ocha-security:before             { content: "\f384"; }
.icon-ocha-security:before             { content: "\f385"; }
.icon-ocha-security:before             { content: "\f386"; }
.icon-ocha-security:before             { content: "\f387"; }
.icon-ocha-security:before             { content: "\f388"; }
.icon-ocha-security:before             { content: "\f389"; }
.icon-ocha-security:before             { content: "\f390"; }
.icon-ocha-security:before             { content: "\f391"; }
.icon-ocha-security:before             { content: "\f392"; }
.icon-ocha-security:before             { content: "\f393"; }
.icon-ocha-security:before             { content: "\f394"; }
.icon-ocha-security:before             { content: "\f395"; }
.icon-ocha-security:before             { content: "\f396"; }
.icon-ocha-security:before             { content: "\f397"; }
.icon-ocha-security:before             { content: "\f398"; }
.icon-ocha-security:before             { content: "\f399"; }
.icon-ocha-security:before             { content: "\f400"; }
.icon-ocha-security:before             { content: "\f401"; }
.icon-ocha-security:before             { content: "\f402"; }
.icon-ocha-security:before             { content: "\f403"; }
.icon-ocha-security:before             { content: "\f404"; }
.icon-ocha-security:before             { content: "\f405"; }
.icon-ocha-security:before             { content: "\f406"; }
.icon-ocha-security:before             { content: "\f407"; }
.icon-ocha-security:before             { content: "\f408"; }
.icon-ocha-security:before             { content: "\f409"; }
.icon-ocha-security:before             { content: "\f410"; }
.icon-ocha-security:before             { content: "\f411"; }
.icon-ocha-security:before             { content: "\f412"; }
.icon-ocha-security:before             { content: "\f413"; }
.icon-ocha-security:before             { content: "\f414"; }
.icon-ocha-security:before             { content: "\f415"; }
.icon-ocha-security:before             { content: "\f416"; }
.icon-ocha-security:before             { content: "\f417"; }
.icon-ocha-security:before             { content: "\f418"; }
.icon-ocha-security:before             { content: "\f419"; }
.icon-ocha-security:before             { content: "\f420"; }

*/

/* Information management */
.icon-im-coord:before             { content: "\e69f"; }
.icon-im-pie:before             { content: "\e615"; }
.icon-im-line:before             { content: "\e60f"; }
.icon-im-increase:before             { content: "\e611"; }
.icon-im-bar:before             { content: "\e6c0"; }
.icon-im-pin:before             { content: "\e6c4"; }

/* UNHCR map */
.icon-unhcr-office:before             { content: "\f500"; }
.icon-unhcr-locations-camp:before             { content: "\f501"; }
.icon-unhcr-locations-settlement:before             { content: "\f50a"; }
.icon-unhcr-location-accommodation:before             { content: "\f50b"; }
.icon-unhcr-locations-dispersed-population:before             { content: "\f502"; }
.icon-unhcr-locations-centre:before             { content: "\f503"; }
.icon-unhcr-locations-urban:before             { content: "\f504"; }
.icon-unhcr-location:before             { content: "\f505"; }
.icon-unhcr-reception-centre:before             { content: "\f506"; }
.icon-unhcr-detention-centre:before             { content: "\f507"; }
.icon-unhcr-entry-facility-centre:before             { content: "\f508"; }
.icon-unhcr-separated-children-centre:before             { content: "\f509"; }
.icon-unhcr-open-shelter:before             { content: "\f510"; }
.icon-unhcr-governement-office-linked-to-rsd:before             { content: "\f511"; }
.icon-unhcr-government-office-linked-to-reception-centre:before             { content: "\f512"; }


.icon-office-government:before             { content: "\f5e1"; }
.icon-office-diplomatic:before             { content: "\f5e2"; }
.icon-office-united-nations:before             { content: "\f57d"; }
.icon-office-ngo:before             { content: "\f57c"; }
 
 /* Humanitarian Organisation */
.icon-orga-acp:before             { content: "\f800"; }
.icon-orga-amnesty-international:before             { content: "\f801"; }
.icon-orga-care-international:before             { content: "\f802"; }
.icon-orga-caritas-internationalis:before             { content: "\f803"; }
.icon-orga-civil-defence:before             { content: "\f804"; }
.icon-orga-ecowas:before             { content: "\f806"; }
.icon-orga-ecre:before             { content: "\f807"; }
.icon-orga-european-commission:before             { content: "\f808"; }
.icon-orga-fao:before             { content: "\f809"; }
.icon-orga-gichd:before             { content: "\f810"; }
.icon-orga-human-rights-watch:before             { content: "\f811"; }
.icon-orga-icrc:before             { content: "\f812"; }
.icon-orga-icva:before             { content: "\f813"; }
.icon-orga-ifrc:before             { content: "\f814"; }
.icon-orga-iftdh:before             { content: "\f815"; }
.icon-orga-ilo:before             { content: "\f816"; }
.icon-orga-interaction:before             { content: "\f817"; }
.icon-orga-iom:before             { content: "\f818"; }
.icon-orga-irc:before             { content: "\f819"; }
.icon-orga-itu:before             { content: "\f820"; }
.icon-orga-iucn:before             { content: "\f821"; }
.icon-orga-loas:before             { content: "\f822"; }
.icon-orga-lwf:before             { content: "\f823"; }
.icon-orga-mdm:before             { content: "\f824"; }
.icon-orga-nato:before             { content: "\f825"; }
.icon-orga-ocha:before             { content: "\f826"; }
.icon-orga-ohchr:before             { content: "\f827"; }
.icon-orga-oic:before             { content: "\f828"; }
.icon-orga-osagi:before             { content: "\f829"; }
.icon-orga-osce:before             { content: "\f830"; }
.icon-orga-oxfam:before             { content: "\f831"; }
.icon-orga-international-save-the-child-alliance:before             { content: "\f832"; }
.icon-orga-union-of-africa:before             { content: "\f833"; }
.icon-orga-unaids:before             { content: "\f834"; }
.icon-orga-undp:before             { content: "\f834"; }
.icon-orga-unece:before             { content: "\f835"; }
.icon-orga-unep:before             { content: "\f836"; }
.icon-orga-unesco:before             { content: "\f837"; }
.icon-orga-unfpa:before             { content: "\f838"; }
.icon-orga-unhcr:before             { content: "\f839"; }
.icon-orga-unhsp:before             { content: "\f840"; }
.icon-orga-unicef:before             { content: "\f841"; }
.icon-orga-unitair:before             { content: "\f842"; }
.icon-orga-undoc:before             { content: "\f843"; }
.icon-orga-unops:before             { content: "\f844"; }
.icon-orga-unrwa:before             { content: "\f84a"; }
.icon-orga-unv:before             { content: "\f845"; }
.icon-orga-voice:before             { content: "\f846"; }
.icon-orga-wcc:before             { content: "\f847"; }
.icon-orga-wfp:before             { content: "\f848"; }
.icon-orga-who:before             { content: "\f849"; }
.icon-orga-wipo:before             { content: "\f850"; }
.icon-orga-wmo:before             { content: "\f851"; }
.icon-orga-word-bank:before             { content: "\f852"; }
.icon-orga-msf:before             { content: "\f853"; }
.icon-orga-unog:before             { content: "\f854"; }
.icon-orga-cartong:before             { content: "\f855"; }
