<template>
  <div class="container-fluid">
    <div class="row">
      <!-- Display this section only on large screens -->
      <div class="col-md-12 col-lg-8 d-none d-lg-block bg-image">
        <div align="center" class="textover">
          This system was built for the Malawi government to support the
          department of disaster management affairs through UNDP under the DRM4R
          project All rights reserved © {{ year }}
        </div>
      </div>
      <!-- Display this section on all screen sizes -->
      <div class="col-md-12 col-lg-4 bg-colour ">
        <router-view></router-view>
      </div>
    </div>
  </div>
</template>

<script>
import { BaseNav } from "@/components";
import { ZoomCenterTransition } from "vue2-transitions";
import HorizontalCollapseItem from "./HorizontalCollapseItem";

export default {
  data() {
    return {
      year: null,
    };
  },
  components: {
    BaseNav,
    ZoomCenterTransition,
    HorizontalCollapseItem,
  },
  mounted() {
    const d = new Date();
    this.year = d.getFullYear();
  },
};
</script>

<style lang="scss" scoped>
.bg-image {
  background-image: url("/Disaster 1.jpg") !important;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  overflow: hidden;
  height: 100vh;
}
.textover {
  background-color: rgba(255, 255, 255, 0.6);
  width: 70%; /* Adjust width as needed */
  padding: 20px;
  position: absolute;
  top: 50%; /* Center vertically */
  left: 50%; /* Center horizontally */
  transform: translate(-50%, -50%);
  border-radius: 15px;
  text-align: center;
  font-family: "Calibri", sans-serif;
  font-size: 1rem; /* Adjust font size as needed */
}
.bg-colour{
  background-color: #eaedf1
}

.col-md-8,
.col-md-4 {
  padding: 0; /* Remove padding */
}

.rounded-left {
  border-radius: 30px 0 0 30px !important;
}
</style>
