<template>
  <div class="content">
    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">All</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/districtmanager/dashboard">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">Accounts</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">All</li>
            </ol>
          </nav>
        </div>
      </div>
      <div class="row"></div>
    </base-header>
    <div class="container-fluid mt--6">
      <div>
        <card
          class="no-border-card"
          body-classes="px-0 pb-1"
          footer-classes="pb-2"
        >
          <div>
            <div
              class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
            >
              <el-select
                class="select-primary pagination-select"
                v-model="pagination.perPage"
                placeholder="Per page"
              >
                <el-option
                  class="select-primary"
                  v-for="item in pagination.perPageOptions"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>

              <div>
                <base-input
                  v-model="searchQuery"
                  prepend-icon="fas fa-search"
                  placeholder="Search..."
                ></base-input>
              </div>
            </div>
            <el-table
              :data="queriedData"
              row-key="id"
              header-row-class-name="thead-light"
              @sort-change="sortChange"
              @selection-change="selectionChange"
            >
              <el-table-column
                v-for="column in tableColumns"
                :key="column.label"
                v-bind="column"
              ></el-table-column>
              <el-table-column min-width="150px" label="Role">
                <div slot-scope="{ $index, row }">
                  <span
                    v-if="
                      row.roleName == 'officer' ||
                        row.roleName == 'district manager'
                    "
                    >{{ row.roleName }} (
                    {{ row.district.admin2_name_en }} )</span
                  >
                  <span v-else>{{ row.roleName }}</span>
                </div>
              </el-table-column>
              <!--   <el-table-column min-width="180px" align="right" label="Actions">
                <div slot-scope="{$index, row}" class="d-flex">
                  <base-button
                    @click.native="handleEdit($index, row)"
                    class="edit"
                    type="primary"
                    size="sm"
                    icon
                  >
                    <i class="text-white ni ni-scissors"></i>
                  </base-button>
                  <base-button
                    @click.native="handleDelete($index, row)"
                    class="remove btn-link"
                    type="danger"
                    size="sm"
                    icon
                  >
                    <i class="text-white ni ni-fat-remove"></i>
                  </base-button>
                </div>
              </el-table-column>-->
            </el-table>
          </div>
          <div
            slot="footer"
            class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
          >
            <div class>
              <p class="card-category">
                Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
                <span v-if="selectedRows.length"
                  >&nbsp; &nbsp; {{ selectedRows.length }} rows selected</span
                >
              </p>
            </div>
            <base-pagination
              class="pagination-no-border"
              v-model="pagination.currentPage"
              :per-page="pagination.perPage"
              :total="total"
            ></base-pagination>
          </div>
        </card>
      </div>
    </div>
  </div>
</template>
<script>
import { Table, TableColumn, Select, Option } from "element-ui";
import RouteBreadCrumb from "@/components/Breadcrumb/RouteBreadcrumb";
import { BasePagination } from "@/components";
import clientPaginationMixin from "@/components/PaginatedTables/clientPaginationMixin";
import swal from "sweetalert2";
import { accounts } from "../../api/accounts/accounts";

export default {
  mixins: [clientPaginationMixin],
  components: {
    BasePagination,
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn
  },
  data() {
    return {
      propsToSearch: ["name", "email", "age"],
      tableColumns: [
        {
          prop: "firstName",
          label: "Last Name",
          minWidth: 120,
          sortable: true
        },
        {
          prop: "lastName",
          label: "First Name",
          minWidth: 120,
          sortable: true
        },
        {
          prop: "phone",
          label: "Phone",
          minWidth: 120,
          sortable: true
        },
        {
          prop: "email",
          label: "Email",
          minWidth: 200,
          sortable: true
        }
      ],
      tableData: [],
      selectedRows: []
    };
  },
  methods: {
    handleRegister() {
      this.$router.push({
        name: "districtManagedAccountsRegister",
        params: {}
      });
    },
    handleEdit(index, row) {
      this.$router.push({
        name: "districtManagedAccountsEdit",
        params: { id: row.id }
      });
    },
    handleDelete(index, row) {
      swal({
        title: "Are you sure?",
        text: `You won't be able to revert this!`,
        type: "warning",
        showCancelButton: true,
        confirmButtonClass: "btn btn-success btn-fill",
        cancelButtonClass: "btn btn-danger btn-fill",
        confirmButtonText: "Yes, delete it!",
        buttonsStyling: false
      }).then(result => {
        if (result.value) {
          accounts.remove(row.id, this.$session.get("jwt")).then(
            response => {
              swal({
                title: "Deleted!",
                text: `You deleted ${row.firstName + " " + row.lastName}`,
                type: "success",
                confirmButtonClass: "btn btn-success btn-fill",
                buttonsStyling: false
              });
              this.deleteRow(row);
            },
            reason => {
              this.loading = false;
              this.$swal({
                title: "Failed to delete account",
                text: "possible invalid account (" + reason + ")",
                type: "error",
                confirmButtonClass: "btn btn-success btn-fill",
                buttonsStyling: false
              });
            }
          );
        }
      });
    },
    deleteRow(row) {
      let indexToDelete = this.tableData.findIndex(
        tableRow => tableRow.id === row.id
      );
      if (indexToDelete >= 0) {
        this.tableData.splice(indexToDelete, 1);
      }
    },
    selectionChange(selectedRows) {
      this.selectedRows = selectedRows;
    }
  },
  mounted() {
    accounts.get(this.$session.get("jwt")).then(response => {
      this.tableData = response.data.filter(item => {
        return (
          item.roleName != "admin" &&
          item.roleName != "manager" &&
          this.$session.get("user").admin2_name_en ==
            item.district.admin2_name_en
        );
      });
    });
  }
};
</script>
<style>
.no-border-card .card-footer {
  border-top: 0;
}
</style>
