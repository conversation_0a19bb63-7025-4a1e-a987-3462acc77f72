<template>
  <div>
    <h2>LOGISTICS</h2>
    <h3>
      *
      <small>Hint &nbsp;</small>
      <b
        ><font color="primary"
          >(HH: Households, FHH : Female Headed Households, MHH : Male Headed
          Households)</font
        ></b
      >
    </h3>
    <hr class="mt-3 mb-3" />
    <h2>
      <b class="alert-suc">Impact on structures</b>
    </h2>

    <div
      class="row"
      v-for="(value, index) in impacted_structures"
      v-bind:key="index"
    >
      <div class="col-md">
        <base-input label="Please choose an option">
          <select
            class="form-control"
            v-model="value.name"
            data-toggle="tooltip"
            data-placement="top"
            title="choose an option"
          >
            <option
              v-for="structure_type in structure_types"
              :value="structure_type.name"
              >{{ structure_type.name }}</option
            >
          </select>
        </base-input>
      </div>
      <div class="col-md pt-5">
        <base-radio
          name="partly damaged"
          class="mb-3"
          v-bind:value="'partly damaged'"
          data-toggle="tooltip"
          data-placement="top"
          title="partly damaged"
          v-model="value.status"
          >Partly damaged</base-radio
        >
      </div>

      <div class="col-md pt-5">
        <base-radio
          name="damaged"
          class="mb-3"
          v-bind:value="'damaged'"
          data-toggle="tooltip"
          data-placement="top"
          title="amaged"
          v-model="value.status"
          >Damaged</base-radio
        >
      </div>
      <div class="col-md pr-5 pt-5">
        <base-button
          size="sm"
          type="warning"
          class="btn-icon-only rounded-circle"
          v-if="impacted_structures.length > 0"
          data-toggle="tooltip"
          data-placement="top"
          title="Remove impacted structure"
          @click="
            removeItemRow(
              'impacted_structures',
              impacted_structures,
              structure_types,
              index,
              'name'
            )
          "
          >X</base-button
        >
      </div>
    </div>

    <base-button
      size="md"
      type="info"
      class="btn-icon-only rounded-circle noprint"
      data-toggle="tooltip"
      data-placement="top"
      title="Add impacted structure"
      @click="
        addItemRow(
          'impacted_structures',
          impacted_structures,
          structure_types,
          'name'
        )
      "
    >
      <i class="ni ni-fat-add"></i>
    </base-button>
    <hr />
    <h2>
      <b class="alert-suc">Status of Other Impacted Structures</b>
    </h2>
    <form>
      <div
        class="row"
        v-for="(value, index) in other_impacted_structures"
        v-bind:key="index"
        data-toggle="tooltip"
        data-placement="top"
        title="Choose an option"
      >
        <div class="col-md">
          <base-input
            label="Structure name"
            data-toggle="tooltip"
            data-placement="top"
            title="Specify structure name"
            placeholder="Specify if any"
            v-model="value.name"
          />
        </div>

        <div class="col-md pt-5">
          <base-radio
            name="partly damaged"
            class="mb-3"
            v-model="value.status"
            v-bind:value="'partly damaged'"
            data-toggle="tooltip"
            data-placement="top"
            title="partly damaged"
            :disabled="value.name == null || value.name === ''"
            >Partly damaged</base-radio
          >
        </div>

        <div class="col-md pt-5">
          <base-radio
            name="damaged"
            class="mb-3"
            v-model="value.status"
            :disabled="value.name == null || value.name === ''"
            data-toggle="tooltip"
            data-placement="top"
            title="damaged"
            v-bind:value="'damaged'"
            >Damaged</base-radio
          >
        </div>

        <div class="col-md pr-5 pt-5">
          <base-button
            size="sm"
            type="warning"
            @click="
              removeItemRow(
                'other_impacted_structures',
                other_impacted_structures,
                [],
                index,
                'name'
              )
            "
            class="btn-icon-only rounded-circle"
            >X</base-button
          >
        </div>
      </div>
    </form>
    <base-button
      size="md"
      type="info"
      data-toggle="tooltip"
      data-placement="top"
      title="Add other"
      class="btn-icon-only rounded-circle noprint"
      @click="
        addItemRow(
          'other_impacted_structures',
          other_impacted_structures,
          [],
          'name'
        )
      "
    >
      <i class="ni ni-fat-add"></i>
    </base-button>
    <hr />

    <h2>
      <b class="alert-suc">Impact on Telecommunications</b>
    </h2>

    <div
      class="row"
      v-for="(value, index) in impacted_telecoms"
      v-bind:key="index"
    >
      <div class="col-md">
        <base-input
          label="Please choose an option"
          data-toggle="tooltip"
          data-placement="top"
          title="Choose an option"
        >
          <select class="form-control" v-model="value.name">
            <option v-for="telecom in telecoms" :value="telecom.name">{{
              telecom.name
            }}</option>
          </select>
        </base-input>
      </div>
      <div class="col-md pt-5">
        <base-radio
          name="functioning"
          class="mb-3"
          v-bind:value="'functioning'"
          data-toggle="tooltip"
          data-placement="top"
          title="functioning"
          v-model="value.status"
          >Functioning</base-radio
        >
      </div>

      <div class="col-md pt-5">
        <base-radio
          name="partly functioning"
          class="mb-3"
          v-bind:value="'partly functioning'"
          data-toggle="tooltip"
          data-placement="top"
          title="partly functioning"
          v-model="value.status"
          >Partly functioning</base-radio
        >
      </div>
      <div class="col-md pt-5">
        <base-radio
          name="not functioning"
          class="mb-3"
          v-bind:value="'not functioning'"
          data-toggle="tooltip"
          data-placement="top"
          title="not functioning"
          v-model="value.status"
          >Not functioning</base-radio
        >
      </div>

      <div class="col-md pr-5 pt-5">
        <base-button
          size="sm"
          type="warning"
          class="btn-icon-only rounded-circle"
          data-toggle="tooltip"
          data-placement="top"
          title="Remove impact on telecoms"
          v-if="impacted_telecoms.length > 0"
          @click="
            removeItemRow(
              'impacted_telecoms',
              impacted_telecoms,
              telecoms,
              index,
              'name'
            )
          "
          >X</base-button
        >
      </div>
    </div>

    <base-button
      size="md"
      type="info"
      class="btn-icon-only rounded-circle noprint"
      data-toggle="tooltip"
      data-placement="top"
      title="Add impact on telecoms"
      @click="
        addItemRow('impacted_telecoms', impacted_telecoms, telecoms, 'name')
      "
    >
      <i class="ni ni-fat-add"></i>
    </base-button>
    <hr />

    <h2>
      <b class="alert-suc">Access of the following from Affected Areas</b>
    </h2>

    <div
      class="row"
      v-for="(value, index) in access_of_structures"
      v-bind:key="index"
    >
      <div class="col-md">
        <base-input
          label="Main Roads to/from District Center"
          placeholder="Specify name of road"
          v-model="value.road_name"
        />
      </div>

      <div class="col-md pt-5">
        <base-radio
          name="accessible"
          class="mb-3"
          v-bind:value="'accessible'"
          v-model="value.accessibility"
          data-toggle="tooltip"
          data-placement="top"
          title="partly accessible"
          >Partly accessible</base-radio
        >
      </div>

      <div class="col-md pt-5">
        <base-radio
          name="partially accessible"
          class="mb-3"
          v-bind:value="'partially accessible'"
          data-toggle="tooltip"
          data-placement="top"
          title="accessible"
          v-model="value.accessibility"
          >Accessible</base-radio
        >
      </div>
      <div class="col-md pt-5">
        <base-radio
          name="not accessible"
          class="mb-3"
          v-bind:value="'not accessible'"
          data-toggle="tooltip"
          data-placement="top"
          title="not accessible"
          v-model="value.accessibility"
          >Not accessible</base-radio
        >
      </div>

      <div
        class="col-md-12"
        v-if="
          value.accessibility === 'partially accessible' ||
            value.accessibility === 'not accessible'
        "
      >
        <base-input label="Description">
          <textarea
            class="form-control"
            id="exampleFormControlTextarea3"
            v-model="value.accessibility_description"
            data-toggle="tooltip"
            data-placement="top"
            title="Provide description"
            placeholder="Type description if any"
            rows="3"
          ></textarea>
        </base-input>
      </div>
    </div>

    <hr />

    <h2>
      <b class="alert-suc">Status of Other means of transport</b>
    </h2>
    <div
      class="row"
      v-for="(value, index) in other_access_to_structures"
      v-bind:key="index"
    >
      <div class="col-md">
        <base-input
          label="Specify name"
          placeholder="Specify if any"
          v-model="value.name"
        />
      </div>
      <div class="col-md pt-5">
        <base-radio
          name="partially accessible"
          class="mb-3"
          v-bind:value="'partially accessible'"
          data-toggle="tooltip"
          data-placement="top"
          title="partially accessible"
          :disabled="value.name == null || value.name === ''"
          v-model="value.status"
          >Partly accessible</base-radio
        >
      </div>

      <div class="col-md pt-5">
        <base-radio
          name="accessible"
          class="mb-3"
          v-bind:value="'accessible'"
          data-toggle="tooltip"
          data-placement="top"
          title="accessible"
          :disabled="value.name == null || value.name === ''"
          v-model="value.status"
          >Accessible</base-radio
        >
      </div>
      <div class="col-md pt-5">
        <base-radio
          name="not accessible"
          data-toggle="tooltip"
          data-placement="top"
          title="not accessible"
          class="mb-3"
          v-bind:value="'not accessible'"
          :disabled="value.name == null || value.name === ''"
          v-model="value.status"
          >Not accessible</base-radio
        >
      </div>
      <div
        class="col-md-12"
        v-if="
          value.status === 'partially accessible' ||
            value.status === 'not accessible'
        "
      >
        <base-input label="Description">
          <textarea
            class="form-control"
            id="exampleFormControlTextarea3"
            data-toggle="tooltip"
            data-placement="top"
            title="Provide description"
            v-model="value.description"
            placeholder="Type description if any"
            rows="3"
          ></textarea>
        </base-input>
      </div>
      <div class="col-md pr-5 pt-5 noprint">
        <base-button
          size="sm"
          type="warning"
          class="btn-icon-only rounded-circle noprint"
          data-toggle="tooltip"
          data-placement="top"
          title="Remove other access"
          @click="
            removeItemRow(
              'other_access_to_structures',
              other_access_to_structures,
              [],
              index,
              'name'
            )
          "
          >X</base-button
        >
      </div>
    </div>

    <base-button
      size="md"
      type="info"
      data-toggle="tooltip"
      data-placement="top"
      title="Add other structures damaged"
      class="btn-icon-only rounded-circle noprint"
      @click="
        addItemRow(
          'other_access_to_structures',
          other_access_to_structures,
          [],
          'name'
        )
      "
    >
      <i class="ni ni-fat-add"></i>
    </base-button>
    <hr />
    <b>Response Needed for the Logistics Cluster</b>
    <hr class="mt-3 mb-3" />
    <base-input label="General Response needed for Logistics cluster">
      <textarea
        class="form-control"
        id="exampleFormControlTextarea3"
        data-toggle="tooltip"
        data-placement="top"
        title="Type the general response needed"
        placeholder="Type the response needed for the Logistics cluster"
        v-model="response_needed"
        rows="3"
      ></textarea>
    </base-input>
    <base-input label="Urgent Response needed for Logistics cluster">
      <textarea
        class="form-control"
        id="exampleFormControlTextarea3"
        data-toggle="tooltip"
        data-placement="top"
        title="Type the urgent response needed"
        placeholder="Type the urgent response needed for the Logistics cluster"
        v-model="urgent_response_needed"
        rows="3"
      ></textarea>
    </base-input>
    <base-button
      size="lg"
      type="primary"
      data-toggle="tooltip"
      data-placement="top"
      title="Save and goto next section"
      @click.stop="save"
      class="noprint"
      >Save & Continue</base-button
    >
  </div>
</template>
<script src="./index.js"/>
