<template>
  <div>
    <h2>NUTRITION</h2>
    <h3>
      *
      <small>Hint &nbsp;</small>
      <b
        ><font color="primary"
          >(HH: Households, FHH : Female Headed Households, MHH : Male Headed
          Households)</font
        ></b
      >
    </h3>
    <hr class="mt-3 mb-3" />
    <h2>
      <b class="alert-suc">Affected Population</b>
    </h2>
    <form>
      <div
        class="row row-example"
        v-for="(value, index) in affected_population"
        v-bind:key="index"
      >
        <div class="col-md">
          <base-input label="Please choose a population type">
            <select
              class="form-control"
              id="exampleFormControlSelect1"
              data-toggle="tooltip"
              data-placement="top"
              title="Choose a population type"
              v-model="value.name"
              @change="
                TestPopulationType(
                  value.name,
                  affected_population,
                  'affected_males'
                )
              "
            >
              <option v-for="types in population_type" :value="types.name">{{
                types.name
              }}</option>
            </select>
          </base-input>
        </div>
        <div class="col-md">
          <base-input
            label="Males"
            placeholder="# of males affected"
            type="number"
            v-model.number="value.affected_males"
            oninput="validity.valid||(value='');"
            data-toggle="tooltip"
            data-placement="top"
            title="Enter number of males affected"
            min="0"
            :disabled="
              value.name === 'Lactating Women' ||
                value.name === 'Pregnant Women' ||
                value.name === '' ||
                value.name == null
            "
            @input="
              init_totals(
                'affected_males',
                affected_population,
                'affected_males'
              )
            "
            v-bind:max="maxValue"
          />
        </div>
        <div class="col-md">
          <base-input
            label="Female "
            type="number"
            placeholder="# of females affected"
            v-model.number="value.affected_females"
            data-toggle="tooltip"
            data-placement="top"
            title="Enter number of females affected"
            oninput="validity.valid||(value='');"
            min="0"
            :disabled="value.name === '' || value.name == null"
            :rules="[v => !!v || 'value is required']"
            @input="
              init_totals(
                'affected_females',
                affected_population,
                'affected_females'
              )
            "
            v-bind:max="maxValue"
          />
        </div>
        <div class="col-md pt-5">
          <base-button
            size="sm"
            type="warning"
            class="btn-icon-only rounded-circle"
            data-toggle="tooltip"
            data-placement="top"
            title="Remove affected population"
            v-if="affected_population.length > 0"
            @click="
              removeItemRow(
                'affected_population',
                affected_population,
                population_type,
                index,
                'name',
                ['affected_females', 'affected_males']
              )
            "
            >X</base-button
          >
        </div>
      </div>
    </form>
    <base-button
      size="md"
      type="info"
      data-toggle="tooltip"
      data-placement="top"
      title="Add affected population"
      class="btn-icon-only rounded-circle noprint"
      @click="
        addItemRow(
          'affected_population',
          affected_population,
          population_type,
          'name'
        )
      "
    >
      <i class="ni ni-fat-add"></i>
    </base-button>

    <hr />
    <div class="row row-example">
      <div class="col-sm-6">
        <h5 slot="header" class="mb-0">
          Total population affected: {{ tt_affected_population }}
        </h5>
      </div>
    </div>

    <hr />
    <h2>
      <b class="alert-suc">Impact on Nutrition Programme</b>
    </h2>
    <form>
      <div
        class="row row-example"
        v-for="(value, index) in impact_on_nutrition_programmes"
        v-bind:key="index"
      >
        <div class="col-md">
          <base-input
            label="Please choose a source"
            data-toggle="tooltip"
            data-placement="top"
            title="Choose a nutrition programme"
          >
            <select
              class="form-control"
              id="exampleFormControlSelect1"
              v-model="value.name"
            >
              <option
                v-for="types in nutrition_programmes"
                :value="types.name"
                >{{ types.name }}</option
              >
            </select>
          </base-input>
        </div>
        <div class="row pt-5">
          <div class="col-md">
            <base-radio
              name="Not disturbed"
              data-toggle="tooltip"
              data-placement="top"
              title="Enter number of programmes not disturbed"
              class="mb-3"
              v-model="value.status"
              v-bind:value="'Not Disturbed'"
              >Not disturbed</base-radio
            >
          </div>

          <div class="col-md">
            <base-radio
              name="Partly Disturbed"
              data-toggle="tooltip"
              data-placement="top"
              title="Enter number of programmes partly disturbed"
              class="mb-3"
              v-model="value.status"
              v-bind:value="'Partly Disturbed'"
              >Partly disturbed</base-radio
            >
          </div>
          <div class="col-md">
            <base-radio
              name="Completely Closed"
              data-toggle="tooltip"
              data-placement="top"
              title="Enter number of programmes completely close"
              class="mb-3"
              v-model="value.status"
              v-bind:value="'Completely Closed'"
              >Completely closed</base-radio
            >
          </div>
        </div>

        <div class="col-md pr-5 pt-5">
          <base-button
            size="sm"
            type="warning"
            class="btn-icon-only rounded-circle noprint"
            v-if="impact_on_nutrition_programmes.length > 0"
            data-toggle="tooltip"
            data-placement="top"
            title="Remove impact on nutrition programmes"
            @click="
              removeItemRow(
                'impact_on_nutrition_programmes',
                impact_on_nutrition_programmes,
                nutrition_programmes,
                index,
                'name'
              )
            "
            >X</base-button
          >
        </div>
      </div>
    </form>
    <base-button
      size="md"
      type="info"
      data-toggle="tooltip"
      data-placement="top"
      title="Add impact on nutrition programs"
      class="btn-icon-only rounded-circle noprint"
      @click="
        addItemRow(
          'impact_on_nutrition_programmes',
          impact_on_nutrition_programmes,
          nutrition_programmes,
          'name'
        )
      "
    >
      <i class="ni ni-fat-add"></i>
    </base-button>
    <hr />
    <b>Response Needed for the Nutrition Cluster</b>
    <hr class="mt-3 mb-3" />
    <base-input label="General Response needed for Nutrition cluster">
      <textarea
        class="form-control"
        id="exampleFormControlTextarea3"
        placeholder="Type the response needed for the Nutrition cluster"
        data-toggle="tooltip"
        data-placement="top"
        title="Type the general response neeed"
        v-model="response_needed"
        rows="3"
      ></textarea>
    </base-input>
    <base-input label="Urgent Response needed for Nutrition cluster">
      <textarea
        class="form-control"
        id="exampleFormControlTextarea3"
        data-toggle="tooltip"
        data-placement="top"
        title="Type the urgent response needed"
        placeholder="Type the urgent response needed for the Nutrition cluster"
        v-model="urgent_response_needed"
        rows="3"
      ></textarea>
    </base-input>
    <base-button
      size="lg"
      type="primary"
      data-toggle="tooltip"
      data-placement="top"
      title="Save and goto next section"
      @click.stop="save"
      class="noprint"
    >
      Save & Continue
    </base-button>
  </div>
</template>

<script src="./index.js"/>
