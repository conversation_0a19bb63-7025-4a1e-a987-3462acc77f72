<template>
  <div>
    <div class="vld-parent">
      <loading
        :active.sync="isLoading"
        :can-cancel="false"
        :is-full-page="fullPage"
      ></loading>
    </div>

    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Edit Signature</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/districtmanager/dashboard">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item active" aria-current="page">
                Signatures
              </li>
            </ol>
          </nav>
        </div>
        <div class="col-lg-6 col-5 text-right"></div>
      </div>
    </base-header>
    <div class="container-fluid mt--6">
      <div class="row justify-content-center">
        <div class="col-lg-7 card-wrapper">
          <!-- Grid system -->
          <card class="card">
            <!-- Card body -->
            <div class="row">
              <div class="col-lg-12">
                <p class="mb-0">Signatory details</p>
              </div>
            </div>
            <hr />
            <form enctype="multipart/form-data">
              <div class="form-row">
                <div class="col-md-8">
                  <div class="fields">
                    <label>Upload Signature Image</label><br />
                    <input
                      type="file"
                      ref="file"
                      @change="onFileChange"
                      accept="image/x-png,image/gif,image/jpeg"
                    /><br />
                    <div id="preview">
                      <img v-if="url" :src="url" />
                    </div>
                  </div>
                </div>
              </div>

              <hr />
              <div class="form-row">
                <div class="col-md-6">
                  <base-input label="Role">
                    <select class="form-control" v-model="model.role">
                      <option
                        v-for="item in rolesData"
                        :key="item.name"
                        :value="item.name"
                        >{{ item.name }}</option
                      >
                    </select>
                  </base-input>
                </div>
              </div>
              <base-button type="primary" native-type="submit" @click="Submit"
                >Update Signature</base-button
              >
            </form>
          </card>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { signatures } from "../../api/accounts/signatures";

import { accounts } from "../../api/accounts/accounts";
import { roles } from "../../api/accounts/roles";
import { admin2s } from "../../api/location/admin2s";
import swal from "sweetalert2";
import { Select, Option } from "element-ui";
// Import component
import Loading from "vue-loading-overlay";
// Import stylesheet
import "vue-loading-overlay/dist/vue-loading.css";
export default {
  components: { Loading, [Select.name]: Select, [Option.name]: Option },
  data() {
    return {
      url: null,
      model: {
        _id: "",
        district: "",
        signature: "",
        signatory_fname: this.$session.get("userObj").firstName,
        signatory_lname: this.$session.get("userObj").lastName,
        signatory_email: this.$session.get("userObj").email,
        role: "",
        userId: "",
        status: "Active"
      },
      StatusData: [{ name: "Active" }, { name: "inactive" }],
      rolesData: [
        { name: "ADRM Officer" },
        { name: "DRM Officer" }
      ],
      selectOptions: [
        {
          label: "DINR Forms",
          value: "DINR Forms"
        },
        {
          label: "5WS Forms",
          value: "5WS Forms"
        }
      ],
      admin2sData: [],
      //password: "",
      //passwordvalidation: "",
      isLoading: false,
      fullPage: true
    };
  },
  methods: {
    Submit() {
      {
        swal({
          title: "Are you sure you want to update signature?",
          text: `You won't be able to revert this!`,
          type: "warning",
          showCancelButton: true,
          confirmButtonClass: "btn btn-success btn-fill",
          cancelButtonClass: "btn btn-danger btn-fill",
          confirmButtonText: "Yes, update it!",
          buttonsStyling: false
        }).then(result => {
          if (result.value) {
            if (this.$refs.file.files[0] === undefined) {
              this.updateSignature(this.model);
            } else {
              let fd = new FormData();
              fd.append("file", this.$refs.file.files[0]);

              signatures.upload(fd).then(response => {
                if (response.status === 201) {
                  this.model.district = this.$session.get(
                    "user"
                  ).admin2_name_en;
                  this.model.userId = this.$session.get("user")._id;
                  this.model._id = this.$route.params.id;
                  this.model.signature = response.data.path;
                  this.updateSignature(this.model);
                }
              });
            }
          }
        });
      }
    },

    getImgUrl(pic) {
      return process.env.VUE_APP_ENGINE_URL + "/" + pic;
    },

    onFileChange(e) {
      const file = e.target.files[0];
      this.url = URL.createObjectURL(file);
    },
    updateSignature(data) {
      signatures
        .update(data)
        .then(
          response => {
            swal({
              title: "Signature Updated!",
              type: "success",
              confirmButtonClass: "btn btn-success btn-fill",
              buttonsStyling: false
            });
          },
          reason => {
            swal({
              title: "Failed to update signature",
              text: "possible network issue (" + reason + ")",
              type: "error",
              confirmButtonClass: "btn btn-success btn-fill",
              buttonsStyling: false
            });
          }
        )
        .finally(() => {});
    }
  },
  mounted() {


    signatures.getOne(this.$route.params.id).then(response => {
      this.url = this.getImgUrl(response.signature);
      this.model = response;
    });
  }
};
</script>
<style>
#preview {
  display: flex;
  padding-top: 30px;
  justify-content: left;
  align-items: left;
}

#preview img {
  max-width: 30%;
  max-height: 100px;
}
</style>
