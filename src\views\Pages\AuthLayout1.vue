<template>
  <div class="container-fluid border border-primary">
    <div class="row bg-primary">
      <div
        class="col-8"
        style="
          background-image: url('/Disaster 1.jpg') !important;
          background-repeat: no-repeat;
          background-size: cover;
        "
      >
        <div align="center" class="textover">
          This system was built for the Malawi government to support the
          department of disaster management affairs through UNDP under the DRM4R
          project All rights reserved © 2022
        </div>
      </div>
      <div
        class="col-4 bg-white"
        style="border-top-left-radius: 25px; border-bottom-left-radius: 25px"
      >
        <div class="main-content">
              <router-view></router-view>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { BaseNav } from "@/components";
import { ZoomCenterTransition } from "vue2-transitions";
import HorizontalCollapseItem from "./HorizontalCollapseItem";

export default {
  data() {
    return {
      collapsed: false,
    };
  },
  components: {
    BaseNav,
    ZoomCenterTransition,
    HorizontalCollapseItem,
  },
  props: {
    backgroundColor: {
      type: String,
      default: "black",
    },
  },
  data() {
    return {
      showMenu: false,
      menuTransitionDuration: 250,
      pageTransitionDuration: 200,
      year: new Date().getFullYear(),
      pageClass: "login-page",
      dodmaURL: process.env.VUE_APP_DODMA_URL,
      undpURL: process.env.VUE_APP_UNDP_URL,
      drmisURL: process.env.VUE_APP_DRMIS_URL,
    };
  },
  computed: {
    title() {
      return `${this.$route.name} Page`;
    },
  },
  methods: {
    toggleNavbar() {
      document.body.classList.toggle("nav-open");
      this.showMenu = !this.showMenu;
    },
    closeMenu() {
      document.body.classList.remove("nav-open");
      this.showMenu = false;
    },
    setBackgroundColor() {
      document.body.classList.add("bg-default");
    },
    removeBackgroundColor() {
      document.body.classList.remove("bg-default");
    },
    updateBackground() {
      if (!this.$route.meta.noBodyBackground) {
        this.setBackgroundColor();
      } else {
        this.removeBackgroundColor();
      }
    },
  },
  beforeDestroy() {
    this.removeBackgroundColor();
  },
  beforeRouteUpdate(to, from, next) {
    // Close the mobile menu first then transition to next page
    if (this.showMenu) {
      this.closeMenu();
      setTimeout(() => {
        next();
      }, this.menuTransitionDuration);
    } else {
      next();
    }
  },
  watch: {
    $route: {
      immediate: true,
      handler: function () {
        this.updateBackground();
      },
    },
  },
};
</script>
<style lang="scss">
$scaleSize: 0.8;
@keyframes zoomIn8 {
  from {
    opacity: 0;
    transform: scale3d($scaleSize, $scaleSize, $scaleSize);
  }
  100% {
    opacity: 1;
  }
}

.main-content .zoomIn {
  animation-name: zoomIn8;
}

@keyframes zoomOut8 {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    transform: scale3d($scaleSize, $scaleSize, $scaleSize);
  }
}

.main-content .zoomOut {
  animation-name: zoomOut8;
}

.bg-image {
  background-image: url("/Disaster 1.jpg") !important;
  background-size: cover;
}

.textover {
  background-color: #fff;
  width: 50%;
  height: 22%;
  color: #080808;
  opacity: 0.6;
  padding: 20px;
  position: relative;
  top: 40%;
  left: 25%;
  border-radius: 15px;
  // text-align: center;
  font-family: "Calibri", sans-serif;
  font-size: 19px;
}
.col-md-1 {
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
  // background: #2ec3a3;
  // padding: 0px;
  // padding-right: 0px;
  // padding-left: 0px;
  padding: 0px 0px 0px 0px;
}
.col-md-4 {
  padding-right: 0px;
}
.col-md-11 {
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
  margin-left: 0px;
  padding: 0px 0px 0px 0px;
}

$transitionDurationMs: 0.5s;

.horizontal-collapse {
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: row;
  justify-content: right;
  margin-bottom: 0rem;
  color: #fff;

  *,
  *:before,
  *:after {
    box-sizing: border-box;
  }

  &__inner {
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: row;
  }
  &__item {
    min-height: 42rem;
    list-style: none;
    padding: 0;
    overflow: hidden;
    min-width: 10rem;
    max-width: 40rem;
    position: relative;
    transition: width $transitionDurationMs;
    border-top-left-radius: 20px;
    border-bottom-left-radius: 20px;

    &--1 {
      background-color: #2ec3a3;
    }

    &.is-active {
      .horizontal-collapse__inactive-content {
        opacity: 0;
      }

      .horizontal-collapse__active-content {
        opacity: 1;
      }
    }
  }

  &__item-inner {
    padding: 2rem 0rem 0rem 1rem;
  }

  &__inactive-content {
    opacity: 1;
    transition: opacity $transitionDurationMs;
    // position: absolute;
    bottom: 2rem;
    left: 1rem;

    // .horizontal-collapse__heading {

    //   cursor: default;
    //   font-size: 1rem;

    // }
  }

  &__active-content {
    opacity: 0;
    transition: opacity $transitionDurationMs;
    cursor: default;
  }

  &__heading {
    margin-top: 0;
    margin-bottom: 0rem;
  }

  &__body {
    margin-bottom: 0rem;
  }

  &__link {
    display: inline-block;
    background-color: #fff;
    color: #333;
    text-decoration: none;
    line-height: 1;
    padding: 1rem 2rem;
    border-radius: 2rem;
    font-size: 1.7rem;
    font-weight: bold;
  }
}



</style>
