<template>
  <div>
    <div class="vld-parent">
      <loading :active.sync="isLoading" :can-cancel="false" :is-full-page="fullPage"></loading>
    </div>

    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Register</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">Accounts</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">
                Register
              </li>
            </ol>
          </nav>
        </div>
        <div class="col-lg-6 col-5 text-right"></div>
      </div>
    </base-header>
    <div class="container-fluid mt--6">
      <div class="row justify-content-center">
        <div class="col-lg-7 card-wrapper">
          <!-- Grid system -->
          <card class="card">
            <!-- Card body -->
            <div class="row">
              <div class="col-lg-12">
                <p class="mb-0">Personal details</p>
              </div>
            </div>
            <hr />
            <form class="needs-validation" @submit.prevent="validate">
              <div class="form-row">
                <div class="col-md-8">
                  <base-input label="First name" name="First name" placeholder="First name"
                    :error="getError('First name')" :valid="isValid('First name')" v-validate="'required'"
                    v-model="model.firstName"></base-input>
                </div>
              </div>
              <div class="form-row">
                <div class="col-md-8">
                  <base-input label="Last name" name="Last name" placeholder="Last name" :error="getError('Last name')"
                    :valid="isValid('Last name')" v-validate="'required'" v-model="model.lastName"></base-input>
                </div>
              </div>
              <div class="row">
                <div class="col-lg-12">
                  <p class="mb-0">Contact details</p>
                </div>
              </div>
              <hr />

              <div class="form-row">
                <div class="col-md-8">
                  <base-input label="Email" name="Email" placeholder="Email" :error="getError('Email')"
                    :valid="isValid('Email')" v-validate="'required|email'" v-model="model.email"></base-input>
                </div>
              </div>
              <div class="form-row">
                <div class="col-md-8">
                  <b-form-group label="Email Status" v-slot="{ ariaDescribedby }">
                    <b-form-radio-group id="radio-slots" v-model="emailStatus" :aria-describedby="ariaDescribedby"
                      name="radio-options-slots">
                      <!-- Radios in this slot will appear first -->
                      <template #first>
                        <b-form-radio value="active">Active</b-form-radio>
                      </template>

                      <!-- Radios in the default slot will appear after any option generated radios -->
                      <b-form-radio value="inActive">Inactive</b-form-radio>
                    </b-form-radio-group>
                  </b-form-group>
                </div>
              </div>
              <div class="form-row">
                <div class="col-md-8">
                  <b-form-group label="Send Email Acknowledgment">
                    <b-form-checkbox v-model="checked">Send Acknowledgment</b-form-checkbox>
                  </b-form-group>
                </div>
              </div>
              <div class="form-row">
                <div class="col-md-6">
                  <base-input label="Phone" type="number" name="Phone" placeholder="Phone" :error="getError('Phone')"
                    :valid="isValid('Phone')" v-validate="'required'" v-model="model.phone"></base-input>
                </div>
              </div>
              <div class="row">
                <div class="col-lg-12">
                  <p class="mb-0">Administrative details</p>
                </div>
              </div>
              <hr />
              <div class="form-row">
                <div class="col-md-6">
                  <base-input label="Password" name="Password" placeholder="Password" type="password"
                    :error="getError('Password')" :valid="isValid('Password')" v-validate="'required|min:6'"
                    v-model="model.password"></base-input>
                </div>

                <!--
                <div class="col-md-6">
                  <base-input
                    label="Password Validation"
                    name="Validation"
                    placeholder="Password Validation"
                    :error="getError('Validation')"
                    :valid="isValid('Validation')"
                    v-validate="'required|min:6|confirmed:password'"
                    v-model="passwordvalidation"
                    data-vv-as="password"
                  ></base-input>
                </div>-->
              </div>
              <div class="form-row">
                <div class="col-md-6">
                  <base-input label="Role">
                    <select class="form-control" v-model="model.roleName">
                      <option v-for="item in rolesData" :key="item.name" :value="item.name">
                        {{ item.name }}
                      </option>
                    </select>
                  </base-input>
                </div>
              </div>

              <div class="form-row" v-show="model.roleName === 'cluster user'">
                <div class="col-md-6">
                  <base-input label="User Type">
                    <select class="form-control" v-model="model.cluster_user_type">
                      <option v-for="item in clusterUserTypes" :key="item" :value="item">{{ item }}
                      </option>
                    </select>
                  </base-input>
                </div>

                <div class="col-md-6" v-if="model.cluster_user_type !== 'Cluster Lead' || 'Roads Authority'">
                  <base-input label="Cluster">
                    <el-select v-model="model.cluster" multiple filterable placeholder="Clusters">
                      <el-option v-for="option in clusters" :key="option" :label="option" :value="option"></el-option>
                    </el-select>
                  </base-input>
                </div>

                <div class="col-md-6" v-if="model.cluster_user_type == 'Cluster Lead'">
                  <base-input label="Cluster">
                    <select class="form-control" v-model="model.cluster">
                      <option v-for="item in clusters" :key="item" :value="item">{{ item }}
                      </option>
                    </select>
                  </base-input>
                </div>
              </div>

              <div class="form-row" v-show="model.roleName === 'officer' ||
                model.roleName === 'district manager' ||
                model.roleName === 'district commissioner' ||
                model.roleName === 'acpc' ||
                model.roleName === 'dcpc' ||
                model.roleName === 'cluster user' ||
                model.roleName === 'leanseason'
                ">
                <div class="col-md-4" v-if="model.cluster_user_type != 'Cluster Lead' || 'Roads Authority'">
                  <base-input label="District Office">
                    <select class="form-control" id="selecteddistrict" v-model="model.district"
                      @change="onSelectDistrict()">
                      <option v-for="item in admin2sData" :key="item.admin2_pcode" :value="{
                        admin2_pcode: item.admin2_pcode,
                        admin2_name_en: item.admin2_name_en
                      }">{{
  item.admin2_name_en,

}}
                      </option>
                    </select>
                  </base-input>
                </div>

                <div class="col-md-4" v-show="model.roleName === 'officer'">
                  <base-input label="Cluster">
                    <el-select v-model="model.usercluster" multiple filterable placeholder="Clusters">
                      <el-option v-for="option in clusters" :key="option" :label="option" :value="option"></el-option>
                    </el-select>
                  </base-input>
                </div>
                 <div class="col-md-4" v-show="model.roleName === 'acpc'">

                  <base-input label="Traditional Authority">
                    <select class="form-control" v-model="model.ta">
                      <option
                        v-for="item in filteredTAs"
                        :key="item.admin3_pcode"
                        :value="item"
                        >{{ item.admin3_name_en }}
                      </option>
                    </select>
                  </base-input>

                </div>
              </div>
              <div class="form-row">
                <div class="col-md-6">
                  <base-input label="Organisation">
                    <el-select v-model="model.organisation" filterable collapse-tags placeholder="Select">
                      <el-option v-for="item in organisations" :key="item.value" :label="item.label" :value="item.value">
                      </el-option>
                    </el-select>
                  </base-input>
                </div>
              </div>
              <hr />
              <base-button type="primary" native-type="submit">Register</base-button>
            </form>
          </card>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { accounts } from "../../api/accounts/accounts";
import { verifier } from "../../api/accounts/email-verify";
import { roles } from "../../api/accounts/roles";
import { admin2s } from "../../api/location/admin2s";
import { ta_s } from "../../api/location/ta_s";
import swal from "sweetalert2";
import { Select, Option } from "element-ui";

//import verifier from "email-verify";
// Import component
import Loading from "vue-loading-overlay";
// Import stylesheet
import "vue-loading-overlay/dist/vue-loading.css";
export default {
  components: { Loading, [Select.name]: Select, [Option.name]: Option },
  data() {
    return {
      organisations: [
        {
          value: "DODMA",
          label: "DODMA"
        },
        {
          value: "UNDP",
          label: "UNDP"
        },
        {
          value: "UNFPA",
          label: " UNFPA"
        },
        {
          value: "WFP",
          label: " WFP"
        },
        {
          value: "UNESCO",
          label: " UNESCO"
        },
        {
          value: "UNHCR",
          label: " UNHCR"
        },
        {
          value: "WHO",
          label: " WHO"
        },
        {
          value: "GOVERNMENT",
          label: " GOVERNMENT"
        }
      ],
      organisation: [],
      clusters: [
        "shelter",
        "agriculture",
        "logistics",
        "wash",
        "food",
        "health",
        "protection",
        "environment",
        "education",
        "nutrition",
        "camps"
      ],
      clusterUserTypes: [
        "Cluster Lead",
        "Cluster Data Collector",
        "Cluster District coordinator",

        "Roads Authority"
      ],
      checked: false,
      validated: false,
      emailExists: "",
      model: {
        firstName: "",
        lastName: "",
        email: "",
        roleName: "",
        nrbNumber: "N/A",
        employeeNumber: "N/A",
        statusId: 1,
        phone: "",
        password: "",
        forms: ["DINR Forms"],
        organisation: ""
      },

      selectOptions: [
        {
          label: "DINR Forms",
          value: "DINR Forms"
        },
        {
          label: "5WS Forms",
          value: "5WS Forms"
        }
      ],
      rolesData: [],
      admin2sData: [],
      admin3sData: [],
      admin2: null,
      filteredTAs: [],
      //password: "",
      //passwordvalidation: "",
      isLoading: false,
      fullPage: true,
      emailStatus: "active"
    };
  },
  methods: {
    onSelectDistrict() {
      this.filteredTAs = this.admin3sData
        .filter(d => d.admin2_pcode === this.model.district.admin2_pcode)
        .sort();
    },

    validate() {
      this.$validator.validateAll().then(response => {
        if (response === true) {
          this.validated = true;
          this.submit();
        } else response === false;
        {
          this.validated = false;
        }
      });
    },
    async submit() {
      if (this.validated === true) {
        // this.isLoading = true;
        // let emailStatus = await verifier.verify({ email: this.model.email });
        let emailStatus = {
          success: true
        };
        //let errStatus = await verifier.verify({ email: this.model.email });
        let errStatus = {
          code: "ENOTFOUND"
        };
        this.model.emailStatus = this.emailStatus;
        if (this.checked) {
          this.model.ackEmail = "1";
        } else {
          this.model.ackEmail = "0";
        }

        try {
          this.model.district.clusters = this.model.usercluster;
        } catch (error) {
          // Handle the error here
          console.error('An error occurred:', error);
        }

        if (emailStatus.success) {
          accounts.create(this.$session.get("jwt"), this.model).then(
            response => {
              this.isLoading = false;
              swal({
                title: "Succesfully Registered Account",
                text:
                  "Please check your email and click on the verification link and account details",
                type: "success",
                confirmButtonClass: "btn btn-success btn-fill",
                buttonsStyling: false
              }).then(result => {
                this.isLoading = false;
                this.$router.push({
                  name: "AdminAllAccounts",
                  params: {}
                });
              });
            },
            reason => {
              this.isLoading = false;
              swal({
                title: "Failed to create account",
                text:
                  "possible duplication of  email and username (" +
                  reason +
                  ")",
                type: "error",
                confirmButtonClass: "btn btn-success btn-fill",
                buttonsStyling: false
              });
            }
          );
        } else if (errStatus.code == "ENOTFOUND") {
          this.isLoading = false;
          swal({
            title: "Failed to create account",
            text: "Email Address Does Not Exist",
            type: "error",
            confirmButtonClass: "btn btn-success btn-fill",
            buttonsStyling: false
          });
        } else {
          this.isLoading = false;
          swal({
            title: "Failed to create account",
            text: "Email Address Does Not Exist",
            type: "error",
            confirmButtonClass: "btn btn-success btn-fill",
            buttonsStyling: false
          });
        }
      }
    },
    getError(name) {
      return this.errors.first(name);
    },
    isValid(name) {
      return this.validated && !this.errors.has(name);
    },
    clear() {
      this.model = {
        firstName: "",
        lastName: "",
        email: "",
        roleName: "",
        nrbNumber: "N/A",
        employeeNumber: "N/A",
        statusId: 1,
        phone: "",
        password: "",
        organisation: ""
      };
      this.validated = true;
    }
  },
  mounted() {
    roles.get(this.$session.get("jwt")).then(response => {
      this.rolesData = response.data;
    });

    admin2s.get().then(response => {
      this.admin2sData = response.data;
    });

    ta_s.get().then(response => {
      this.admin3sData = response.data;
    });
  }
};
</script>
<style></style>
