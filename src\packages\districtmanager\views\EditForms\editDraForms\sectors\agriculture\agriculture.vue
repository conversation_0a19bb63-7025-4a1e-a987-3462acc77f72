<template>
  <div>
    <h2>AGRICULTURE</h2>
    <h3>
      *
      <small>Hint &nbsp;</small>
      <b
        ><font color="primary"
          >(HH: Households, FHH : Female Headed Households, MHH : Male Headed
          Households)</font
        ></b
      >
    </h3>
    <hr class="mt-3 mb-3" />
    <h2>
      <b class="alert-suc">Impact on crops</b>
    </h2>

    <div
      class="row row-example"
      v-for="(value, index) in impact_on_crops"
      v-bind:key="index"
    >
      <div class="col-md">
        <base-input label="Please choose a crop type">
          <select class="form-control" v-model="value.name" data-toggle="tooltip"
                    data-placement="top"
                    title="Choose a crop type">
            <option v-for="types in crop_types" :value="types.name">{{
              types.name
            }}</option>
          </select>
        </base-input>
      </div>
      <div class="col-md">
        <base-input
          label="#HH affected "
          placeholder="# of HH affected"
          v-model.number="value.hh_affected"
          data-toggle="tooltip"
                    data-placement="top"
                    title="Enter the numer of HH affected"
          oninput="validity.valid||(value='');"
          type="number"
          min="0"
          :rules="[v => !!v || 'value is required']"
          @input="init_totals('hh_affected', impact_on_crops, 'hh_affected')"
          v-bind:max="maxValue"
        />
      </div>
      <div class="col-md">
        <base-input
          label="Hectares damaged "
          v-model.number="value.hectares_damaged"
          data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of hectares damaged"
          oninput="validity.valid||(value='');"
          :rules="[v => !!v || 'value is required']"
          type="number"
          min="0"
          @input="
            init_totals('hectares_damaged', impact_on_crops, 'hectares_damaged')
          "
          v-bind:max="maxValue"
          placeholder="# of Hectares damaged"
        />
      </div>
      <div class="col-md pt-5">
        <base-button
          size="sm"
          type="warning"
          data-toggle="tooltip"
                    data-placement="top"
                    title="Remove impact on crops"
          v-if="impact_on_crops.length > 0"
          @click="
            removeItemRow(
              'impact_on_crops',
              impact_on_crops,
              crop_types,
              index,
              'name',
              ['hh_affected', 'hectares_damaged']
            )
          "
          class="btn-icon-only rounded-circle noprint"
          >X</base-button
        >
      </div>
    </div>

    <base-button
      size="md"
      type="info"
      class="btn-icon-only rounded-circle noprint"
      data-toggle="tooltip"
                    data-placement="top"
                    title="Add impact on crops"
      @click="
        addItemRow('impact_on_crops', impact_on_crops, crop_types, 'name')
      "
    >
      <i class="ni ni-fat-add"></i>
    </base-button>

    <hr />

    <div class="row">
      <div class="col-md-6">
        Total HH affected:
        {{ hh_affected_crops }}
      </div>
      <div class="col-md-6">
        Total hectares damaged:
        {{ hectares_damaged_total }}
      </div>
    </div>
    <hr />

    <h2>
      <b class="alert-suc">Crop Damaged/Loss by Hectares</b>
    </h2>
    <form>
      <div
        class="row row-example"
        v-for="(value, index) in crops_damaged"
        v-bind:key="index"
      >
        <div class="col-md">
          <base-input label="Please choose a crop">
            <select class="form-control" v-model="value.name" data-toggle="tooltip"
                    data-placement="top"
                    title="Select a crop type">
              <option v-for="item in crops" :value="item.name">{{
                item.name
              }}</option>
            </select>
          </base-input>
        </div>
        <div class="col-md">
          <base-input
            oninput="validity.valid||(value='');"
            :name="value.hectares_submerged"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of hectares submerged"
            type="number"
            :rules="[v => !!v || 'value is required']"
            min="0"
            placeholder="0"
            v-bind:max="maxValue"
            v-model.number="value.hectares_submerged"
            @input="
              init_totals(
                'hectares_submerged',
                crops_damaged,
                'hectares_submerged'
              )
            "
            label="Hectares Submerged"
          />
        </div>
        <div class="col-md">
          <base-input
            oninput="validity.valid||(value='');"
            type="number"
            :name="value.hectares_washed_away"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of hectares washed away"
            min="0"
            :rules="[v => !!v || 'value is required']"
            placeholder="0"
            v-bind:max="maxValue"
            @input="
              init_totals(
                'hectares_washed_away',
                crops_damaged,
                'hectares_washed_away'
              )
            "
            v-model.number="value.hectares_washed_away"
            label="Hectares Washed Away"
          />
        </div>
        <div class="col-md pt-5">
          <base-button
            size="sm"
            type="warning"
            class="btn-icon-only rounded-circle"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Remove crop damage"
            v-if="crops_damaged.length > 0"
            @click="
              removeItemRow(
                'crops_damaged',
                crops_damaged,
                crops,
                index,
                'name',
                ['hectares_submerged', 'hectares_washed_away']
              )
            "
          >
            <i class="ni ni-fat-delete"></i
          ></base-button>
        </div>
      </div>
    </form>
    <base-button
      size="md"
      type="info"
      class="btn-icon-only rounded-circle noprint"
      data-toggle="tooltip"
                    data-placement="top"
                    title="Add crop damaged"
      @click="addItemRow('crops_damaged', crops_damaged, crops, 'name')"
    >
      <i class="ni ni-fat-add"></i>
    </base-button>
    <hr />
    <div class="row row-example">
      <div class="col-sm-6">
        <h5 slot="header" class="mb-0">
          Total Hectares submerged: {{ tt_hectares_submerged }}
        </h5>
      </div>
      <div class="col-sm-6">
        <h5 slot="header" class="mb-0">
          Total Hectares washed away: {{ tt_hectares_washed_away }}
        </h5>
      </div>
    </div>

    <hr />

    <h2>
      <b class="alert-suc">Impact on livestock</b>
    </h2>
    <form>
      <div
        class="row row-example"
        v-for="(value, index) in impact_on_livestock"
        v-bind:key="index"
      >
        <div class="col-md">
          <base-input label="Please choose a livestock">
            <select class="form-control" v-model="value.name" data-toggle="tooltip"
                    data-placement="top"
                    title="Choose a livestock type">
              <option v-for="item in livestock_list" :value="item.name">{{
                item.name
              }}</option>
            </select>
          </base-input>
        </div>
        <div class="col-md">
          <base-input
            oninput="validity.valid||(value='');"
            type="number"
            :rules="[v => !!v || 'value is required']"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of households whose livestock were affected"
            min="0"
            placeholder="0"
            v-bind:max="maxValue"
            v-model.number="value.hh_affected_l"
            @input="
              init_totals('hh_affected_l', impact_on_livestock, 'hh_affected_l')
            "
            label="# HH affected"
          />
        </div>
        <div class="col-md">
          <base-input
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            :rules="[v => !!v || 'value is required']"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of livestock impacted"
            placeholder="0"
            v-bind:max="maxValue"
            @input="
              init_totals(
                'livestock_affected',
                impact_on_livestock,
                'livestock_affected'
              )
            "
            v-model.number="value.livestock_affected"
            label="# Livestock affected"
          />
        </div>
        <div class="col-md pt-5">
          <base-button
            size="sm"
            type="warning"
            class="btn-icon-only rounded-circle noprint"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Remove impact on livestock"
            v-if="impact_on_livestock.length > 0"
            @click="
              removeItemRow(
                'impact_on_livestock',
                impact_on_livestock,
                livestock_list,
                index,
                'name',
                ['hh_affected_l', 'livestock_affected']
              )
            "
          >
            <i class="ni ni-fat-delete"></i
          ></base-button>
        </div>
      </div>
    </form>
    <base-button
      size="md"
      type="info"
      data-toggle="tooltip"
                    data-placement="top"
                    title="Add impact on livestock"
      class="btn-icon-only rounded-circle noprint"
      @click="
        addItemRow(
          'impact_on_livestock',
          impact_on_livestock,
          livestock_list,
          'name'
        )
      "
    >
      <i class="ni ni-fat-add"></i>
    </base-button>
    <hr />
    <div class="row row-example">
      <div class="col-sm-6">
        <h5 slot="header" class="mb-0">
          Total HH affected:
          {{ hh_affected_livestock }}
        </h5>
      </div>
      <div class="col-sm-6">
        <h5 slot="header" class="mb-0">
          Total livestock affected:
          {{ tt_livestock_affected }}
        </h5>
      </div>
    </div>

    <hr />

    <h2>
      <b class="alert-suc">Food items damaged</b>
    </h2>
    <form>
      <div
        class="row row-example"
        v-for="(value, index) in food_item_damage"
        v-bind:key="index"
      >
        <div class="col-md-4">
          <base-input
            label="Food item name "
            placeholder="name of food item"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Enter the name of the food item"
            v-model="value.food_item_name"
            :rules="[v => !!v || 'food name is required']"
          />
        </div>
        <div class="col-md-4">
          <base-input
            label="# of KGs "
            placeholder="# of KGs affected"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of kilos damaged"
            type="number"
            oninput="validity.valid||(value='');"
            :name="value.number_of_kilos"
            min="0"
            :rules="[v => !!v || 'value is required']"
            :disabled="
              value.food_item_name == null || value.food_item_name === ''
            "
            v-bind:max="maxValue"
            @input="
              init_totals(
                'number_of_kilos',
                food_item_damage,
                'number_of_kilos'
              )
            "
            v-model.number="value.number_of_kilos"
          />
        </div>

        <div class="col-md-4 pt-5">
          <base-button
            size="sm"
            type="warning"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Delete food item damage"
            class="btn-icon-only rounded-circle"
            v-if="food_item_damage.length > 0"
            @click="
              removeItemRow(
                'food_item_damage',
                food_item_damage,
                dummy_list,
                index,
                'food_item_name',
                ['number_of_kilos']
              )
            "
            >X</base-button
          >
        </div>
      </div>
    </form>
    <base-button
      size="md"
      type="info"
      data-toggle="tooltip"
                    data-placement="top"
                    title="Add food item damage"
      icon
      fab
      dark
      color="primary"
      class="btn-icon-only rounded-circle noprint"
      @click="addItemRow('food_item_damage', food_item_damage)"
    >
      <i class="ni ni-fat-add"></i>
    </base-button>

    <hr />
    <div class="row" v-if="food_item_damage.length > 0">
      <div class="col-md">
        Total #KGs damaged:
        {{ tt_kgs_damaged }}
      </div>
    </div>
    <hr />
    <b>Response Needed for the Agriculture Cluster</b>
    <hr class="mt-3 mb-3">
    <base-input label="General Response needed for Agriculture cluster">
      <textarea
        class="form-control"
        data-toggle="tooltip"
                    data-placement="top"
                    title="Type the general response that is needed"
        id="exampleFormControlTextarea3"
        placeholder="Type the response needed for the Agriculture cluster"
        v-model="response_needed"
        rows="3"
      ></textarea>
    </base-input>
    <base-input label="Urgent Response needed for Agriculture cluster">
      <textarea
        class="form-control"
        id="exampleFormControlTextarea3"
        data-toggle="tooltip"
                    data-placement="top"
                    title="Type the urgent response that is needed"
        placeholder="Type the urgent response needed for the Agriculture cluster"
        v-model="urgent_response_needed"
        rows="3"
      ></textarea>
    </base-input>

    <base-button size="lg" type="primary"  data-toggle="tooltip"
                    data-placement="top"
                    title="Save and goto next section" @click.stop="save" class="noprint">
      Save & Continue
    </base-button>
  </div>
</template>
<script src="./index.js"/>

<style scoped>
@media print {
  .noprint {
    visibility: hidden;
  }
}
</style>
