let malawiAdmin3GeoJson = require("../../../../data/malawi.tas");
let malawiLakeGeoJson = require("../../../../data/malawi-lake.features");
var malawDistrictsLatLng = require("../../../../data/latlondistricts");

let buildMap = (
    id,
    dc,
    d3,
    taDimension,
    taGroup,
    groupname,
    getColor,
    numberFormat,
    data,
    width,
    district
) => {
   let selectedAdmin3sFeatures = malawiAdmin3GeoJson.features.filter(
        d => d.properties.DIST_NAME == district
    )
    let malawiGeoJsonWithFilteredAdmin3 = {...malawiAdmin3GeoJson}
    malawiGeoJsonWithFilteredAdmin3.features = [...selectedAdmin3sFeatures]
    //console.log(malawiAdmin3GeoJson.features);
    var projection = d3.geoMercator().fitSize([width, 900], malawiGeoJsonWithFilteredAdmin3);

    let choroplethMap = dc.geoChoroplethChart(id, groupname);
     choroplethMap.removeGeoJson("district")
     choroplethMap.removeGeoJson("data")

    choroplethMap
        .width(1000)
        .height(960)
        .transitionDuration(1000)
        .dimension(taDimension)
        .group(taGroup)
        .projection(projection)
        .colorDomain([1, 80])
        .colorCalculator(function(d) {
            let color = getColor(d);
            return d && color ? color : "#dbddde";
        })
        //.removeGeoJson("district")
        .keyAccessor(d => d.key.trim())
        .valueAccessor(d => d.value.exceptionCount)
        .overlayGeoJson(malawiGeoJsonWithFilteredAdmin3.features, "district", function(d) {
            return d.properties.TA_NAME.trim();
        })

        .overlayGeoJson(malawiLakeGeoJson.features, "water", function(d) {
            return d.properties.ADMIN3;
        })
        .title(function(d) {
         
            var prop = d.key;
            let datas = [];
       data.forEach((x) => {
            var Obj = {
                ta: x.ta,
                hh: x.total_without_shelter_hh 
            }
          datas.push(Obj);
      });
    
      const sum = datas.filter(x => x.ta === prop)
                    .map(x => x.hh)
                    .reduce((ac, ba) => ac + ba, 0);
 // Property name stored in JS variable
     
     var allStats = `T/A: ${d.key}\nTotal Disasters: ${numberFormat(d.value ? d.value : 0)} \nTotal HH affected: ${numberFormat(sum ? sum : 0)}`
          return allStats;  
    
        })
        .on("renderlet", function(chart) {
            d3.selectAll("g.water.malawi path")
                .attr("stroke", "white")
                .style("fill", function(d) {
                    return "#409ffb";
                });
 d3
                .select("#labelG")
                .remove()
            var labelG = d3
                .select("#mapChartId svg")
                .append("svg:g")
                .attr("id", "labelG")
                .attr("class", "Title");

   



            labelG
                .selectAll("text")
                .data(malawDistrictsLatLng.filter(x => x.admin == district))
                .enter()
                .append("svg:text")
                .text(function(d) {
                    
                    //if (data.some(item => item.district == d.admin)) {
                    return d.admin;
                    //}
                })
                .attr("x", function(d) {
                    return projection([d.lng, d.lat])[0];
                })
                .attr("y", function(d) {
                    return projection([d.lng, d.lat])[1];
                })
                .attr("dx", "-1em")
                .attr("stroke", "black")
                .style("font-size", "30px");

            labelG
                .selectAll("text")
                .data(malawiGeoJsonWithFilteredAdmin3.features)
                .enter()
                .append("svg:text")
                .text(function(d) {
                    //console.log(d);
                    //if (data.some(item => item.district == d.admin)) {
                    return d.properties.TA_NAME;
                    //}
                })
                .attr("x", function(d) {
                    return projection(d3.geoCentroid(d.geometry))[0];
                })
                .attr("y", function(d) {
                    return projection(d3.geoCentroid(d.geometry))[1];
                })
                .attr("dx", "-1em");

            var center = d3.geoCentroid(malawiLakeGeoJson);
            //console.log(center);

            d3.selectAll("#mapChartId g.district path")
                .attr("stroke", "white")
                .style("cursor", "pointer");
        });

    choroplethMap.legendables = function() {
        let categories = [0, 1, 5, 10, 20, 40, 60, 80];
        return categories.map(function(d, i) {
            var legendable = {
                name: categories[i] === 0 ?
                    categories[i] :
                    categories[i + 1] ?
                    categories[i] + " - " + categories[i + 1] :
                    "> " + categories[i],
                chart: choroplethMap
            };

            legendable.color = getColor(categories[i]);
            return legendable;
        });
    };

    choroplethMap.legend(
        dc
        .legend()
        .x(width+20)
        .y(6)
        .itemHeight(500 / 30)
        .gap(5)
    );

    return choroplethMap;
};

export { buildMap };
