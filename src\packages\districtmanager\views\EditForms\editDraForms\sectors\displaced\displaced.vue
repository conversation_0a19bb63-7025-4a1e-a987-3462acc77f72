<template>
  <div>
    <h2>DISPLACED</h2>
    <h3>
      *
      <small>Hint &nbsp;</small>
      <b>
        <font
          color="primary"
        >(HH: Households, FHH : Female Headed Households, MHH : Male Headed Households)</font>
      </b>
    </h3>
    <hr class="mt-3 mb-3" />
    <h2>
      <b class="alert-suc">Hummanitarian Assistance</b>
    </h2>
    <form ref="form">
      <div class="row" v-for="(value, index) in hummanitarian_assistance" v-bind:key="index">
        <div class="col-md">
          <label>Are there people that need immediate assistance?</label>
        </div>

        <div class="col-md">
          <base-radio name="yes" class="mb-3" v-bind:value="'yes'" v-model="value.assistance" data-toggle="tooltip"
                    data-placement="top"
                    title="Yes">Yes</base-radio>
        </div>

        <div class="col-md">
          <base-radio name="no" class="mb-3" v-bind:value="'no'" v-model="value.assistance" data-toggle="tooltip"
                    data-placement="top"
                    title="No">No</base-radio>
        </div>

        <div v-if="value.assistance==='yes'" class="col-md-12">
          <div class="col-md">
            <label>
              <b>What help is needed?</b>
            </label>
            <br />
          </div>
          <base-checkbox
            class="mb-3"
            unchecked
            v-bind:value="'true'"
            v-model="value.education_needed"
          >Education</base-checkbox>
          <base-checkbox
            class="mb-3"
            unchecked
            v-bind:value="'true'"
            v-model="value.food_needed"
          >Food</base-checkbox>
          <base-checkbox
            class="mb-3"
            unchecked
            v-bind:value="'true'"
            v-model="value.health_needed"
          >Health</base-checkbox>
          <base-checkbox
            class="mb-3"
            unchecked
            v-bind:value="'true'"
            v-model="value.logistics_needed"
          >Infrastructure and Logistics</base-checkbox>
          <base-checkbox
            class="mb-3"
            unchecked
            v-bind:value="'true'"
            v-model="value.agriculture_needed"
          >Livelihoods and Agriculture</base-checkbox>
          <base-checkbox
            class="mb-3"
            unchecked
            v-bind:value="'true'"
            v-model="value.nutrition_needed"
          >Nutrition</base-checkbox>
          <base-checkbox
            class="mb-3"
            unchecked
            v-bind:value="'true'"
            v-model="value.protection_needed"
          >Protection</base-checkbox>
          <base-checkbox
            class="mb-3"
            unchecked
            v-bind:value="'true'"
            v-model="value.shelter_needed"
          >Shelter</base-checkbox>
          <base-checkbox
            class="mb-3"
            unchecked
            v-bind:value="'true'"
            v-model="value.wash_needed"
          >WASH</base-checkbox>
        </div>
      </div>
    </form>

    <hr />

    <h2>
      <b class="alert-suc">Households displaced</b>
    </h2>
    <br />
    <h4>
      <b class="alert-suc">Displaced households in</b>
    </h4>
    <form row wrap v-for="row in PeopleAffectedrows" v-bind:key="row.name">
      <div class="row">
        <h3 class="col-md">(GVH : {{ row.name }})</h3>
      </div>
      <div class="row row-example">
        <div class="col-md">
          <base-input
            label="Damaged FHH "
            oninput="validity.valid||(value='');"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of female-headed househols"
            type="number"
            min="0"
            placeholder="# of FHH"
            v-bind:max="maxValue"
            @input="init_totals('number_displaced_by_gender_fhh', PeopleAffectedrows, 'total_displaced_fhh')"
            v-model="row.number_displaced_by_gender_fhh"
          ></base-input>
        </div>
        <div class="col-md">
          <base-input
            label="Damaged MHH "
            type="number"
            oninput="validity.valid||(value='');"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of male-headed households"
            min="0"
            placeholder="# of MHH"
            v-bind:max="maxValue"
            @input="init_totals('number_displaced_by_gender_mhh', PeopleAffectedrows, 'total_displaced_mhh')"
            v-model="row.number_displaced_by_gender_mhh"
          ></base-input>
        </div>
      </div>
    </form>

    <hr />
    <div class="row">
      <div class="col-md">
        <h5 slot="header" class="mb-0">Total HH affected ({{TA}})</h5>
      </div>
      <div class="col-md">
        <h5 slot="header" class="mb-0">Total FHH: {{total_displaced_fhh}}</h5>
      </div>
      <div class="col-md">
        <h5 slot="header" class="mb-0">Total MHH: {{ total_displaced_mhh }}</h5>
      </div>
      <div class="col-md">
        <h5 slot="header" class="mb-0">Total HH: {{total_displaced_hh}}</h5>
      </div>
    </div>
    <hr />

    <h2>
      <b class="alert-suc">Displaced households accommodated in structures</b>
    </h2>
    <form>
      <div
        class="row"
        row
        wrap
        v-for="(value, index) in displaced_households_accommodated"
        v-bind:key="index"
      >
        <div class="col-md">
          <base-input label="Structures">
            <select class="form-control" v-model="value.name" data-toggle="tooltip"
                    data-placement="top"
                    title="Choose a structure">
              <option v-for="structure in structures" :value="structure.name">{{structure.name}}</option>
            </select>
          </base-input>
        </div>
        <div class="col-md">
          <base-input
            label="Male HH"
            type="number"
            @input="init_totals('accomodated_males_hh', displaced_households_accommodated, 'accomodated_males_hh')"
            v-model.number="value.accomodated_males_hh"
            oninput="validity.valid||(value='');"
            min="0"
               data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of male households"
            @focus="testIfgreater('accomodated_males_hh')"

            @change="testIfgreater('accomodated_males_hh')"
            @mousedown="testIfgreater('accomodated_males_hh')"
            @keyup="testIfgreater('accomodated_males_hh')"
            placeholder="# of male HH"
            v-bind:max="maxValue"
            value="0"
          />
        </div>
        <div class="col-md">
          <base-input
            label="Female HH"
            @input="init_totals('accomodated_females_hh', displaced_households_accommodated, 'accomodated_females_hh')"
            v-model.number="value.accomodated_females_hh"
            oninput="validity.valid||(value='');"
            @focus="testIfgreater('accomodated_females_hh')"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of female households"
            @change="testIfgreater('accomodated_females_hh')"
            @mousedown="testIfgreater('accomodated_females_hh')"
            @keyup="testIfgreater('accomodated_females_hh')"
            type="number"
            min="0"
            placeholder="# of female HH"
            v-bind:max="maxValue"
          />
        </div>
        <div class="col-md pt-5">
          <base-button
            size="md"
            type="warning"
            class="noprint btn-icon-only rounded-circle"
            depressed
            icon
            fab
            dark
            color="red"
            v-if="displaced_households_accommodated.length > 0"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Remove displaced households accommodated"
            @click="removeItemRow('displaced_households_accommodated',displaced_households_accommodated,  structures, index, 'name', ['accomodated_males_hh', 'accomodated_females_hh'] )"
          >X</base-button>
        </div>
      </div>
    </form>
    <base-button
      size="md"
      type="info"
      class="btn-icon-only rounded-circle noprint"
      depressed
      icon
      fab
      data-toggle="tooltip"
                    data-placement="top"
                    title="Add displaced households accommodated"
      dark
      color="primary"
      @click="addItemRow('displaced_households_accommodated', displaced_households_accommodated, structures, 'name')"
    >
      <i class="ni ni-fat-add"></i>
    </base-button>
    <p v-if="errorsDisplaced === true">
      <b>
        <br />
        <font
          color="red"
        >Number of accommodated households cannot exceed total displaced households (NB: Please resolve these discrepancies)</font>
      </b>
    </p>
    <hr />
    <div class="row" row wrap v-if="displaced_households_accommodated.length > 0">
      <div class="col-md">
        <h5 slot="header" class="mb-0">Total Males HH : {{ total_accomodated_males_hh }}</h5>
      </div>
      <div class="col-md">
        <h5 slot="header" class="mb-0">Total Females HH : {{ total_accomodated_females_hh }}</h5>
      </div>
      <div class="col-md">
        <h5 slot="header" class="mb-0">Total accommodated HH : {{ total_accomodated_hh }}</h5>
      </div>
      <hr />
    </div>

    <h2>
      <b class="alert-suc">Displaced persons disaggregated by vulnerable grouping</b>
    </h2>
    <form>
      <div
        class="row"
        row
        wrap
        v-for="(value, index) in displaced_disaggregated"
        v-bind:key="index"
      >
        <div class="col-md">
          <base-input label="Category">
            <select class="form-control" v-model="value.name" data-toggle="tooltip"
                    data-placement="top"
                    title="Choose a population type">
              <option
                v-for="people_type in peoples_disagregated"
                :value="people_type.name"
              >{{people_type.name}}</option>
            </select>
          </base-input>
        </div>
        <div class="col-md">
          <base-input
            label="Males"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of disaggregated males"
            @input="init_totals('displaced_males', displaced_disaggregated, 'displaced_males')"
            v-model.number="value.displaced_males"
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            placeholder="# of males"
             @focus="testIfgreater2('displaced_males')"

            @change="testIfgreater2('displaced_males')"
            @mousedown="testIfgreater2('displaced_males')"
            @keyup="testIfgreater2('displaced_males')"
            v-bind:max="maxValue"
          />
        </div>
        <div class="col-md">
          <base-input
            label="Females"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of disaggregated females"
            type="number"
            @input="init_totals('displaced_females', displaced_disaggregated, 'displaced_females')"
            v-model.number="value.displaced_females"
            oninput="validity.valid||(value='');"
            min="0"
            placeholder="# of females"
               @focus="testIfgreater2('displaced_males')"

            @change="testIfgreater2('displaced_males')"
            @mousedown="testIfgreater2('displaced_males')"
            @keyup="testIfgreater2('displaced_males')"
            v-bind:max="maxValue"
          />
        </div>
        <div class="col-md pt-5">
          <base-button
            size="sm"
            type="warning"
            depressed
            icon
            fab
            dark
            color="red"
            small
            data-toggle="tooltip"
                    data-placement="top"
                    title="Remove a displaced disaggregated grouping"
            v-if="displaced_disaggregated.length > 0"
            @click="removeItemRow('displaced_disaggregated',displaced_disaggregated,  peoples_disagregated, index, 'name', ['displaced_males', 'displaced_females'])"
            class="btn-icon-only rounded-circle noprint"
          >X</base-button>
        </div>
      </div>
    </form>
    <base-button
      size="md"
      type="info"
      depressed
      icon
      fab
      dark
      color="primary"
      small
      class="btn-icon-only rounded-circle noprint"
      data-toggle="tooltip"
                    data-placement="top"
                    title="Add displaced disaggregated grouping"
      @click="addItemRow('displaced_disaggregated', displaced_disaggregated, peoples_disagregated, 'name')"
    >
      <i class="ni ni-fat-add"></i>
    </base-button>
    <hr />
    <div class="row">
      <div class="col-md">
        <h5 slot="header" class="mb-0">Total displaced Males : {{ total_displaced_males }}</h5>
      </div>
      <div class="col-md">
        <h5 slot="header" class="mb-0">Total displaced Females: {{ total_displaced_females }}</h5>
      </div>
      <div class="col-md">
        <h5 slot="header" class="mb-0">Total displaced: {{ total_displaced}}</h5>
      </div>
    </div>

    <hr />

    <h2>
      <b class="alert-suc">Displaced vulnerable persons accomodated in structures</b>
    </h2>
    <form>
      <div
        class="row"
        row
        wrap
        v-for="(value, index) in displaced_individuals_accommodated"
        v-bind:key="index"
      >
        <div class="col-md">
          <base-input label="Category">
            <select class="form-control" v-model="value.name" data-toggle="tooltip"
                    data-placement="top"
                    title="Choose a population type">
              <option
                v-for="people_type in peoples_vulnerable"
                :value="people_type.name"
              >{{people_type.name}}</option>
            </select>
          </base-input>
        </div>
        <div class="col-md">
          <base-input
            label="Males"
            @input="init_totals('accomodated_males', displaced_individuals_accommodated, 'accomodated_males')"
            v-model.number="value.accomodated_males"
            oninput="validity.valid||(value='');"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of male individuals accommodated"
            type="number"
            min="0"
             @focus="testIfgreater2('accomodated_males')"

            @change="testIfgreater2('accomodated_males')"
            @mousedown="testIfgreater2('accomodated_males')"
            @keyup="testIfgreater2('accomodated_males')"
            placeholder="# of males"
            v-bind:max="maxValue"
          />
        </div>
        <div class="col-md">
          <base-input
            @input="init_totals('accomodated_females', displaced_individuals_accommodated, 'accomodated_females')"
            v-model.number="value.accomodated_females"
            oninput="validity.valid||(value='');"
            type="number"
            min="0"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Enter number of female indiviual accomodated"
            v-bind:max="maxValue"
            label="Females"
                @focus="testIfgreater2('accomodated_females')"

            @change="testIfgreater2('accomodated_females')"
            @mousedown="testIfgreater2('accomodated_females')"
            @keyup="testIfgreater2('accomodated_females')"
            placeholder="# of females"
          />
        </div>
        <div class="col-md pt-5">
          <base-button
            size="sm"
            type="warning"
            depressed
            icon
            fab
            dark
            color="red"
            data-toggle="tooltip"
                    data-placement="top"
                    title="Remove displaced individuals acommodated"
            small
            v-if="displaced_individuals_accommodated.length > 0"
            @click="removeItemRow('displaced_individuals_accommodated',displaced_individuals_accommodated,  peoples_vulnerable, index, 'name', ['accomodated_females', 'accomodated_males'] )"
            class="btn-icon-only rounded-circle noprint"
          >X</base-button>
        </div>
      </div>
    </form>
    <base-button
      size="md"
      type="info"
      small
      class="btn-icon-only rounded-circle noprint"
      depressed
      icon
      fab
      dark
      data-toggle="tooltip"
                    data-placement="top"
                    title="Add displaced individual accommodated"
      color="primary"
      @click="addItemRow('displaced_individuals_accommodated', displaced_individuals_accommodated, peoples_vulnerable, 'name')"
    >
      <i class="ni ni-fat-add"></i>
    </base-button>
    <hr />
    <div class="row">
      <div class="col-md">
        <h5 slot="header" class="mb-0">Total Males : {{ total_accomodated_males }}</h5>
      </div>
      <div class="col-md">
        <h5 slot="header" class="mb-0">Total Females : {{ total_accomodated_females }}</h5>
      </div>
      <div class="col-md">
        <h5 slot="header" class="mb-0">Total accommodated persons : {{ total_accomodated }}</h5>
      </div>
    </div>

        <p v-if="errorsDisplaced2 === true">
      <b>
        <br />
        <font
          color="red"
        >* The total number of Households cannot exceed the total number of people displaced or acommodated please ensure you provide correct figures <br/> * Number of accommodated people cannot exceed total displaced people (NB: Please resolve these discrepancies)</font>
      </b>
    </p>

    <hr />
    <b>Response Needed for the Displaced Cluster</b>
    <hr class="mt-3 mb-3">
    <base-input label="General Response needed for displaced cluster">
      <textarea
        class="form-control"
        id="exampleFormControlTextarea3"
        data-toggle="tooltip"
                    data-placement="top"
                    title="Type the general response needed"
        placeholder="Type the response needed for the displaced cluster"
        v-model="response_needed"
        rows="3"
      ></textarea>
    </base-input>
    <base-input label="Urgent Response needed for displaced cluster">
      <textarea
        class="form-control"
        id="exampleFormControlTextarea3"
        data-toggle="tooltip"
                    data-placement="top"
                    title="Type the urgent response needed"
        placeholder="Type the urgent response needed for the displaced cluster"
        v-model="urgent_response_needed"
        rows="3"
      ></textarea>
    </base-input>

    <base-button size="lg" type="primary"  data-toggle="tooltip"
                    data-placement="top"
                    title="Save and goto next section" @click.stop="save" class="noprint">Save & Continue</base-button>
  </div>
</template>
<script src="./index.js"/>

<style scoped>
.alert-success {
  color: #000;
}
.alert-suc {
  color: #000;
}
.alert-danger {
  color: red;
}
@media print {
  .page-break {
    overflow-y: visible;
    display: block;
  }

  .not-active {
    pointer-events: none;
    cursor: default;
    text-decoration: none;
    color: black;
  }
  .noprint {
    visibility: hidden;
  }
}
</style>
