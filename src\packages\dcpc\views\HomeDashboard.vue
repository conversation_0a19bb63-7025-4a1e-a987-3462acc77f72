<template>
  <div>
    <base-header class="pb-6" type>
      <div class="row align-items-center pb-4">
        <div class="col-lg-12">
          <tabs
            v-if="isLoaded"
            fill
            tabNavClasses="nav-fill flex-column flex-sm-row nav-wrapper"
            tabContentClasses="card shadow"
          >
            <card shadow>
              <tabPane
                id="1"
                active="true"
                icon="ni ni-cloud-upload-96"
                class="active"
                title="Overview"
              >
                <span slot="title">
                  <center style>
                    <span
                    id="2"
                      style="font-size: 150%"
                      data-toggle="tooltip"
                      data-placement="top"
                      title="Overview"
                      class="humanitarianicons-House"
                    ></span>
                    <p style="margin-bottom: 0px; font-weight: bold">
                      Overview
                    </p>
                  </center>
                </span>
                <p class="description">
                  <overview-dashboard
                    :dinrs="dinrs"
                    :dras="dras"
                    :flatData="flatData"
                  ></overview-dashboard>
                </p>
              </tabPane>
              <tabPane id="2" icon="ni ni-bell-55" title="Map">
                <span slot="title">
                  <center>
                    <span
                    id="3"
                      style="font-size: 150%"
                      data-toggle="tooltip"
                      data-placement="top"
                      title="Map"
                      class="humanitarianicons-Location"
                    ></span>
                    <p style="margin-bottom: 0px; font-weight: bold">Map</p>
                  </center>
                </span>
                <p class="description">
                  <map-dashboard
                    :dinrs="dinrs"
                    :dras="dras"
                    :flatData="flatData"
                  ></map-dashboard>
                </p>
              </tabPane>

              <!-- <tabPane id="3" icon="ni ni-calendar-grid-58" title="Charts">
                <span slot="title">
                  <center>
                    <span
                      style="font-size: 150%"
                      data-toggle="tooltip"
                      data-placement="top"
                      title="Charts"
                      class="humanitarianicons-Chart"
                    ></span>
                    <p style="margin-bottom: 0px; font-weight: bold">Charts</p>
                  </center>
                </span>
                <p class="description">
                  <chart-dashboard
                    :flatData="flatData"
                  ></chart-dashboard>
                </p>
              </tabPane> -->
              <tabPane id="4" icon="ni ni-calendar-grid-58" title="Reports">
                <span slot="title">
                  <center>
                    <span
                    id="4"
                      style="font-size: 150%"
                      data-toggle="tooltip"
                      data-placement="top"
                      title="Reports"
                      class="humanitarianicons-Report"
                    ></span>
                    <p style="margin-bottom: 0px; font-weight: bold">Reports</p>
                  </center>
                </span>
                <p class="description">
                  <report-dashboard
                    :dinrs="dinrsData"
                    :dras="dras"
                    :flatData="flatData"
                  ></report-dashboard>
                </p>
              </tabPane>
              <tabPane id="5" icon="ni ni-calendar-grid-58" title="Dataset">
                <span slot="title">
                  <center>
                    <span
                    id="5"
                      style="font-size: 150%"
                      data-toggle="tooltip"
                      data-placement="top"
                      title="Dataset"
                      class="humanitarianicons-Table"
                    ></span>
                    <p style="margin-bottom: 0px; font-weight: bold">Dataset</p>
                  </center>
                </span>
                <p class="description">
                  <dataset-dashboard></dataset-dashboard>
                </p>
              </tabPane>
              <!-- <tabPane id="6" icon="ni ni-calendar-grid-58" title="Downloads">
                <span slot="title">
                  <center>
                    <span
                    id="6"
                      style="font-size: 150%"
                      data-toggle="tooltip"
                      data-placement="top"
                      title="Downloads"
                      class="humanitarianicons-Download"
                    ></span>
                    <p style="margin-bottom: 0px; font-weight: bold">
                      Downloads
                    </p>
                  </center>
                </span>
                <p class="description">
                  <download-dashboard
                  ></download-dashboard>
                </p>
              </tabPane> -->
            </card>
          </tabs>
          <p class="description col-12">
            <center>
              <grid-loader
                :loading="loading"
                v-if="!isLoaded"
                :color="'#bf6c00'"
                :width="'100%'"
                size="40px"
                style="margin-top: 20%"
              ></grid-loader>
            </center>
          </p>
        </div>
      </div>
    </base-header>
  </div>
</template>

<script>
// Charts

import tabPane from "../../../components/Tabs/Tab";
import tabs from "../../../components/Tabs/Tabs";
const ChartDashboard =() => import("../../dashboards/charts/index");
const OverviewDashboard =() => import("../../dashboards/overview/index");
const ReportDashboard =() => import("../../dashboards/reports/index");
const MapDashboard =() => import("../../dashboards/maps/index");
const DatasetDashboard =() => import("../../dashboards/dataset/index");
const DownloadDashboard =() => import("../../dashboards/downloads/index");
import gridLoader from "vue-spinner/src/GridLoader";
import { mapActions } from "vuex";
var _ = require("lodash");
export default {
  name: "DMDashboard",
  components: {
    gridLoader,
    tabs,
    tabPane,
    ChartDashboard,
    OverviewDashboard,
    ReportDashboard,
    DatasetDashboard,
    DownloadDashboard,
    MapDashboard,
  },
  data() {
    return { isLoaded: false, loading: true, model: "1" };
  },
  computed: {
    dinrs: function () {
      return this.dinrsData;
    },
    dras: function () {
      return this.drasData;
    },
    flatData: function () {
      return this.flatRecords;
    },
    aggregatedData: function () {
      return this.aggregatedRecords;
    },
  },

  methods: {
    ...mapActions("dinrs", {
      loadDinrs: "get",
    }),
    ...mapActions("dras", {
      loadDras: "get",
    }),

    ...mapActions("flatdras", {
      loadFlatdras: "loadFlatdras",
    }),

    getPath(object) {
      let self = this;
      return Object.entries(object).reduce((r, [k, v], i) => {
        if (v && typeof v === "object") {
          r.push(
            ...self
              .getPath(v)
              .map(([left, right]) => [
                (Array.isArray(object)
                  ? "F" + (i + 1).toString().padStart(2, 0)
                  : k) +
                  "_" +
                  left,
                right,
              ])
          );
        } else {
          r.push([k, v]);
        }
        return r;
      }, []);
    },
    compressData(data) {
      let self = this;
      return data.map((o) =>
        Object.fromEntries(
          Object.entries(o).reduce((r, [k, v]) => {
            if (k === "station") {
              r.push([k, v.name]);
            } else if (v && typeof v === "object") {
              if (k.endsWith("Values")) k = k.slice(0, -6);
              r.push(
                ...self
                  .getPath(v)
                  .map(([left, right]) => [k + "_" + left, right])
              );
            } else {
              r.push([k, v]);
            }
            return r;
          }, [])
        )
      );
    },
    normilizeObjectProperties(newdata, properties) {
      let data = [...newdata];
      for (var i in data) {
        for (var j in properties) {
          data[i][properties[j]] = data[i][properties[j]] || "";
        }
      }
      return data;
    },
  },
  async mounted() {
    this.$nextTick(async function () {
      this.dinrsData = await this.loadDinrs();
     
      this.dinrsData = [...this.dinrsData].filter(
        (data) =>
          data.district.admin2_name_en ==
          this.$session.get("user").admin2_name_en
      );
      this.drasData = await this.loadDras();

      this.flatRecords = (await this.loadFlatdras()).filter(
        (data) => data.district == this.$session.get("user").admin2_name_en
      );;
 
      this.isLoaded = true;
      this.loading = false;
    });

    /*   this.drasData.forEach((dra, index) => {
      //console.log(index);
      this.drasData[index].dinr = this.dinrsData.find(
        (dinr) => dinr._id == dra.dinrFormId
      );
    }); */
    /* let dra = this.compressData([...this.drasData]);

    let arrayofKeys = [];
    let newShapeArray = [];
    //cleaning the output
    dra.forEach(function (obj, index) {
      let newObject = {};
      Object.keys(obj).forEach(function (key) {
        //console.log(obj[key]);
        if (
          key.indexOf("code") != -1 ||
          key.indexOf("id") != -1 ||
          key.indexOf("dinr_approvalMetadata") != -1
        ) {
        } else {
          if (typeof key == "string") {
            arrayofKeys.push(key);
            newObject[key] = obj[key];
          }
        }
      });

      newShapeArray.push(newObject);
    });

    this.detailedFlatData = newShapeArray;

    this.columnsData = [...new Set(arrayofKeys)];

    //normalize
    this.detailedFlatData = await this.normilizeObjectProperties(
      this.detailedFlatData,
      this.columnsData
    );

    let columnsBuffer = [];
    columnsBuffer[0] = {
      type: "selection",
    };

    this.columnsData.forEach((colName, index) => {
      columnsBuffer.push({
        prop: colName,
        label: colName.replace(/_/g, " "),
        minWidth: 200,
        sortable: true,
      });
    });

    this.columnsData = columnsBuffer; */
  },
};
</script>
