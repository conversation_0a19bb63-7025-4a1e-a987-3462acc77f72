import { draforms } from '../../../../../api/forms/draforms'

export default {
  $_veeValidate: {
    validator: 'new'
  },
  props: ['shelter', 'max', 'value'],
  data () {
    return {
      PeopleAffectedrows: [],
      dataset: {},
      response_needed: '',
      gvhdata: [],
      PeopleDeadrows: [],
      PeopleInjuredrows: [],
      valid: false,
      maxValue: this.max,
      labelValue: '',
      extent: 0,
      hectares_total: 0,
      damaged_mhh: 0,
      damaged_fhh: 0,
      total_houses_damaged: 0,
      females: 0,
      males: 0,
      damaged_total: 0,
      people_injured_females: 0,
      people_injured_males: 0,
      females_dead: 0,
      males_dead: 0,
      TA: '',
      hide: true,
      errorUW: false,
      errorPD: false,
      errorAll: false,
      errorPDI: false,
      errorCD: false,
      defaultValue: this.value,
      checked_houses: false,
      checked_police: false,
      checked_cc: false,
      checked_msq: false,
      checked_go: false,
      checked_church: false,
      step: 1,
      e13: 1,
      malecounter: 0,
      femalecounter: 0,
      other_structures_damaged: [],
      people_without_shelter: [],
      structures: [
        { name: 'CHURCHES' },
        { name: 'MOSQUES' },
        { name: 'COMMUNITY CENTRES' },
        { name: 'GOVERNMENT OFFICES' },
        { name: 'POLICE STATIONS' }
      ],
      people_types: [
        { name: 'Children (0 - 18 Years)' },
        { name: 'Adults (18 and over)' },
        { name: 'Elderly (65 Years and over)' },
        { name: 'Pregnant Women' },
        { name: 'Person with Disability' }
      ]
    }
  },

  beforeMount () {
    typeof this.shelter !== 'undefined' ? this.shelter : {}

    setInterval(this.autosave, 1200)

    this.TA = this.$session.get('TA')

    this.labelValue = 'Total HH damaged ( TA :' + this.TA + ' )'

    draforms.get(this.$route.params.id).then(response => {
      this.gvhdata = response.data.gvhs

      try {
        this.dataset = JSON.parse(localStorage.getItem('shelter').data)
      } catch (error) {
        this.dataset = this.shelter
      }

      var shelterCopy = this.dataset

      if (
        shelterCopy.PeopleAffectedrows === undefined ||
        shelterCopy.PeopleAffectedrows.length == 0
      ) {
        for (var data in this.gvhdata) {
          this.PeopleAffectedrows.push({
            name: this.gvhdata[data].name,
            damaged_fhh: '',
            damaged_mhh: '',
            partly_blown_roof: '',
            wall_damaged: '',
            fully_blown_roof: ''
          })
        }
      } else {
        this.PeopleAffectedrows = shelterCopy.PeopleAffectedrows
      }

      if (
        shelterCopy.PeopleInjuredrows === undefined ||
        shelterCopy.PeopleInjuredrows.length == 0
      ) {
        for (var data in this.gvhdata) {
          this.PeopleInjuredrows.push({
            name: this.gvhdata[data].name,
            people_injured_females: '',
            people_injured_males: ''
          })
        }
      } else {
        // alert(shelterCopy.PeopleInjuredrows);
        this.PeopleInjuredrows = shelterCopy.PeopleInjuredrows
      }

      if (
        shelterCopy.PeopleDeadrows === undefined ||
        shelterCopy.PeopleDeadrows.length == 0
      ) {
        for (var data in this.gvhdata) {
          this.PeopleDeadrows.push({
            name: this.gvhdata[data].name,
            females_dead: '',
            males_dead: ''
          })
        }
      } else {
        this.PeopleDeadrows = shelterCopy.PeopleDeadrows
      }

      this.other_structures_damaged =
        typeof shelterCopy.other_structures_damaged === 'undefined'
          ? this.other_structures_damaged
          : shelterCopy.other_structures_damaged
      this.PeopleDeadrows =
        typeof shelterCopy.PeopleDeadrows === 'undefined'
          ? this.PeopleDeadrows
          : shelterCopy.PeopleDeadrows
      this.people_without_shelter =
        typeof shelterCopy.people_without_shelter === 'undefined'
          ? this.people_without_shelter
          : shelterCopy.people_without_shelter

      this.response_needed =
        typeof this.dataset.response_needed === 'undefined'
          ? this.response_needed
          : this.dataset.response_needed

      this.urgent_response_needed =
        typeof this.dataset.urgent_response_needed === 'undefined'
          ? this.urgent_response_needed
          : this.dataset.urgent_response_needed

      this.PeopleAffectedrows.length > 0
        ? this.init_totals(
            'damaged_fhh',
            this.PeopleAffectedrows,
            'damaged_fhh'
          )
        : ''
      this.PeopleAffectedrows.length > 0
        ? this.init_totals(
            'damaged_mhh',
            this.PeopleAffectedrows,
            'damaged_mhh'
          )
        : ''

      this.people_without_shelter.length > 0
        ? this.init_totals('males', this.people_without_shelter, 'males')
        : ''

      this.people_without_shelter.length > 0
        ? this.init_totals('females', this.people_without_shelter, 'females')
        : ''

      this.PeopleInjuredrows.length > 0
        ? this.init_totals(
            'people_injured_males',
            this.PeopleInjuredrows,
            'total_people_injured_males'
          )
        : ''

      this.PeopleInjuredrows.length > 0
        ? this.init_totals(
            'people_injured_females',
            this.PeopleInjuredrows,
            'total_people_injured_females'
          )
        : ''

      this.PeopleDeadrows.length > 0
        ? this.init_totals(
            'females_dead',
            this.PeopleDeadrows,
            'total_people_dead_females'
          )
        : ''

      this.PeopleDeadrows.length > 0
        ? this.init_totals(
            'males_dead',
            this.PeopleDeadrows,
            'total_people_dead_males'
          )
        : ''
    })
  },

  computed: {
    total_people_dead_males: {
      get () {
        return this.males_dead
      },
      set (new_total) {
        this.males_dead = new_total
      }
    },
    total_people_dead_females: {
      get () {
        return this.females_dead
      },
      set (new_total) {
        this.females_dead = new_total
      }
    },
    total_people_dead () {
      return this.total_people_dead_females + this.total_people_dead_males
    },

    total_people_injured_males: {
      get () {
        return this.people_injured_males
      },
      set (new_total) {
        this.people_injured_males = new_total
      }
    },
    total_people_injured_females: {
      get () {
        return this.people_injured_females
      },
      set (new_total) {
        this.people_injured_females = new_total
      }
    },
    total_people_injured () {
      return this.total_people_injured_females + this.total_people_injured_males
    },
    males_without_shelter: {
      get () {
        return this.males
      },
      set (new_total) {
        this.males = new_total
      }
    },
    females_without_shelter: {
      get () {
        return this.females
      },
      set (new_total) {
        this.females = new_total
      }
    },
    total_people_without_shelter () {
      return this.females_without_shelter + this.males_without_shelter
    },
    total_houses_damaged_mhh: {
      get () {
        return this.damaged_mhh
      },
      set (new_total) {
        this.damaged_mhh = new_total
      }
    },

    total_houses_damaged_fhh: {
      get () {
        return this.damaged_fhh
      },
      set (new_total) {
        this.damaged_fhh = new_total
      }
    },

    total_damaged () {
      let sum = this.total_houses_damaged_fhh + this.total_houses_damaged_mhh
      this.extent = sum
      return sum
    },
    TestValidity () {
      var sum =
        parseInt(
          isNaN(this.shelter.partly_damaged) ||
            this.shelter.partly_damaged.length === 0
            ? 0
            : parseInt(this.shelter.partly_damaged)
        ) +
        parseInt(
          isNaN(this.shelter.under_water) ||
            this.shelter.under_water.length === 0
            ? 0
            : parseInt(this.shelter.under_water)
        ) +
        parseInt(
          isNaN(this.shelter.completely_damaged) ||
            this.shelter.completely_damaged.length === 0
            ? 0
            : parseInt(this.shelter.completely_damaged)
        )

      if (sum > this.extent) {
        this.errorAll = true
      } else {
        this.errorAll = false
      }

      this.shelter.total_extent_of_damage = sum

      return sum
    },
    checkAndCreateArray (array, value) {
      this.shelter[array]
        ? this.shelter[array].find(x => x.name === value)
          ? this.shelter[array]
          : this.shelter[array].push({ name: value })
        : (this.shelter[array] = [{ name: value }])
    }
  },
  methods: {
    TestPopulationType (population_type, incomingArray, name) {
      for (let i = 0; i < incomingArray.length; i++) {
        if (population_type === 'Pregnant Women') {
          incomingArray
            .filter(item => item.name === 'Pregnant Women')
            .map(function (item) {
              return (item[name] = 0)

              this.init_totals('males', this.affected_population, 'males')
            })
        }
      }
    },
    addItemRow (member, array_name, static_data = [], key_value) {
      this.$emit(
        'addItemRow',
        'shelter',
        member,
        array_name,
        static_data,
        key_value
      )
    },
    removeItemRow (
      member,
      array_name,
      static_data = [],
      index,
      key_value,
      arrayTotals
    ) {
      this.$emit(
        'removeItemRow',
        'shelter',
        member,
        array_name,
        static_data,
        index,
        key_value
      )
      let arrayCopy = []
      for (let i = 0; i < arrayTotals.length; i++) {
        if (array_name == '') {
          let dynamic_element = arrayTotals[i]
          //alert(dynamic_element+"mia")
          arrayCopy.push({ dynamic_element: 0 })
          this.init_totals(arrayTotals[i], arrayCopy, arrayTotals[i])
        } else {
          this.init_totals(arrayTotals[i], array_name, arrayTotals[i])
        }
      }
    },

    init_totals (key, array_name, member) {
      this[member] = array_name
        .map(function (item) {
          return parseInt(item[key]) ? parseInt(item[key]) : 0
        })
        .reduce((sum, value) => sum + value, 0)
    },

    autosave () {
      //save and remove empty objects

      this.shelter.response_needed = this.response_needed
      this.shelter.urgent_response_needed = this.urgent_response_needed
      this.shelter.PeopleAffectedrows = this.PeopleAffectedrows.filter(
        value => Object.keys(value).length !== 0
      )
      this.shelter.PeopleInjuredrows = this.PeopleInjuredrows.filter(
        value => Object.keys(value).length !== 0
      )
      this.shelter.PeopleDeadrows = this.PeopleDeadrows.filter(
        value => Object.keys(value).length !== 0
      )
      this.shelter.other_structures_damaged = this.other_structures_damaged.filter(
        value => Object.keys(value).length !== 0
      )
      this.shelter.people_without_shelter = this.people_without_shelter.filter(
        value => Object.keys(value).length !== 0
      )

      this.$emit('autosave', this.shelter, 'shelter')
    },

    save () {
      //save and remove empty objects

      this.shelter.response_needed = this.response_needed
      this.shelter.PeopleAffectedrows = this.PeopleAffectedrows.filter(
        value => Object.keys(value).length !== 0
      )
      this.shelter.PeopleInjuredrows = this.PeopleInjuredrows.filter(
        value => Object.keys(value).length !== 0
      )
      this.shelter.PeopleDeadrows = this.PeopleDeadrows.filter(
        value => Object.keys(value).length !== 0
      )
      this.shelter.other_structures_damaged = this.other_structures_damaged.filter(
        value => Object.keys(value).length !== 0
      )
      this.shelter.people_without_shelter = this.people_without_shelter.filter(
        value => Object.keys(value).length !== 0
      )

      //  alert(JSON.stringify(this.shelter));
      this.$emit('save', this.shelter, 'shelter')
    },

    checkIfValueExceeds () {
      const sum = this.total_people_dead + this.total_people_injured

      if (sum > this.total_damaged) {
        this.errorPDI = true
      } else if (sum <= this.total_damaged) {
        this.errorPDI = false
      }
    },

    testIfUWgreater () {
      var uw = parseInt(this.shelter.under_water)

      if (isNaN(uw) || uw.length == 0) {
        uw = 0

        this.errorUW = false
        this.shelter.errorUW = false
      } else if (!isNaN(uw) && uw !== 0) {
        if (uw > this.extent) {
          this.errorUW = true
          this.shelter.errorUW = true
        } else if (uw <= this.extent) {
          this.errorUW = false
          this.shelter.errorUW = false
        } else if (this.TestValidity > this.extent) {
          this.errorAll = true
          this.shelter.errorAll = true
        } else {
          this.errorAll = false
          this.shelter.errorAll = false
        }
      }
    },

    testIfPDgreater () {
      var pd = parseInt(this.shelter.partly_damaged)

      if (isNaN(pd) || pd.length == 0) {
        pd = 0

        this.errorPD = false
        this.shelter.errorPD = false
      } else if (!isNaN(pd) && pd !== 0) {
        if (pd > this.extent) {
          this.errorPD = true
          this.shelter.errorPD = true
        } else if (pd <= this.extent) {
          this.errorPD = false
          this.shelter.errorPD = false
        } else if (this.TestValidity > this.extent) {
          this.errorAll = true
          this.shelter.errorAll = true
        } else {
          this.errorAll = false
          this.shelter.errorAll = false
        }
      }
    },

    testIfCDgreater () {
      var summation
      var cd = parseInt(this.shelter.completely_damaged)

      if (isNaN(cd) || cd.length == 0) {
        cd = 0

        this.errorCD = false
        this.shelter.errorCD = false
      } else if (!isNaN(cd) && cd !== 0) {
        if (cd > this.extent) {
          this.errorCD = true
          this.shelter.errorCD = true
        } else if (cd <= this.extent) {
          this.errorCD = false
          this.shelter.errorCD = false
        } else if (this.TestValidity > this.extent) {
          this.errorAll = true
          this.shelter.errorAll = true
        } else {
          this.errorAll = false
          this.shelter.errorAll = false
        }
      }
    }
  }
}
