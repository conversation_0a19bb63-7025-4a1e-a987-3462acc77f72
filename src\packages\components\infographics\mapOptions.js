import markerimage from "leaflet/dist/images/marker-icon.png";
import * as L from "leaflet";
import { data } from "jquery";

export const mapOptions = (map, data, zoom, latlng) => {

  
  map
    .center(latlng)
    .zoom(zoom)
    //.mapOptions({..})  
    .valueAccessor(function(kv) {
      return kv.value.count;
    })
    .locationAccessor(function(kv) {
      return kv.value.geo;
    })
    //
    .popup(function(kv) {
      var prop = kv.key;
      // console.log({prop});
      let datas = [];
      data.forEach(x => {
        var Obj = {
          district: x.district,
          disaster: x.disaster,
          hh: x.total_without_shelter_hh
        };
        //console.log(kv, "HHHHHHHHHHHHHHHH")
        datas.push(Obj);
      });

      const sum = datas
        .filter(x => x.district === kv.value.data.location.city)
        .reduce((ac, ba) => ac + ba.hh, 0);

      return `<h4 style="color:teal">${
        kv.value.data.location.city
      } :  ${sum} HH affected
         </h4>`;
    })

    .fitOnRender(true)
    .fitOnRedraw(true)
    .clusterOptions({ singleMarkerMode: false })
    .title(function(kv) {
      var prop = kv.key;
      let datas = [];
      data.forEach(x => {
        var Obj = {
          district: x.district,
          disaster: x.disaster,
          hh: x.total_without_shelter_hh
        };

        datas.push(Obj);
      });
      // console.log({datas});

      datas.forEach(el => {
        if (el.disaster === prop) {
          //  console.log(el, prop, "my hh mmmmmmmmmmmmmmmmm");
        }
      });
      const sum = datas
        .filter(x => x.district === kv.value.data.location.city)
        .reduce((ac, ba) => ac + ba.hh, 0);
      // Property name stored in JS variable
      return (
        kv.value.data.location.city + ": " + sum + " HH affected"
      ); /* + " : " + kv.value.items; */
    })
    .icon(function(kv) {
      return L.icon({
        iconSize: [20, 30],
        iconUrl: markerimage
      });
    })
    .renderPopup(true)
    .cluster(false)
    .brushOn(true);
  return map;
};
