import axios from 'axios'

const endpoint = process.env.VUE_APP_ENGINE_URL + '/formsFinal/dinr'

const endpoint2 = process.env.VUE_APP_ENGINE_URL + '/forms/dinr'

export class dinrforms {
  static update (data) {
    return axios.patch(endpoint + '/' + data._id, data).then(response => {
      return response
    })
  }

  static updateUnapproved (data) {
    return axios.patch(endpoint2 + '/' + data._id, data).then(response => {
      return response
    })
  }
}
