
export default {
  $_veeValidate: {
    validator: 'new'
  },
  components: {},
  props: ['nutrition', 'max'],
  data () {
    return {
      dateset: {},

      maxValue: this.max,
      malnourished_max: 100,
      response_needed: '',
      errorMax: false,
      valid: false,
      radio: {
        radio1: 'radio1',
        radio2: 'radio3'
      },

      impact_on_nutrition_programmes: [],
      affected_males: 0,
      affected_females: 0,
      affected_population: [],
      population_type: [
        { name: 'Under 2' },
        { name: 'Under 5' },
        { name: 'Lactating Women' },
        { name: 'Pregnant Women' },
        { name: 'Malnourished' },
        { name: 'Chronically ill' },
        { name: 'People Living with HIV/AIDS' },
        { name: 'Elderly' },
        { name: 'Persons Living with Disability' }
      ],
      nutrition_programmes: [
        { name: 'School Feeding Programme' },
        { name: 'Antenatal Clinics' },
        { name: 'Under-5 Clinics' },
        { name: 'OutPatient Therepeutic and Supplementary feeding programme' }
      ]
    }
  },
  computed: {
    tt_affected_population () {
      return (
        parseInt(
          this.affected_females === undefined ||
            this.affected_females.length === 0
            ? 0
            : this.affected_females
        ) +
        parseInt(
          this.affected_males === undefined || this.affected_males.length === 0
            ? 0
            : this.affected_males
        )
      )
    }
  },

  methods: {
    TestPopulationType (population_type, incomingArray, name) {
      for (let i = 0; i < incomingArray.length; i++) {
        if (
          population_type === 'Pregnant Women' ||
          population_type === 'Lactating Women'
        ) {
          incomingArray[i][name] = 0
          this.init_totals(
            'affected_males',
            this.affected_population,
            'affected_males'
          )
        }
      }
    },
    addItemRow (member, array_name, static_data = [], key_value) {
      this.$emit(
        'addItemRow',
        'nutrition',
        member,
        array_name,
        static_data,
        key_value
      )
    },
    removeItemRow (
      member,
      array_name,
      static_data = [],
      index,
      key_value,
      arrayTotals
    ) {
      this.$emit(
        'removeItemRow',
        'nutrition',
        member,
        array_name,
        static_data,
        index,
        key_value
      )
      let arrayCopy = []
      for (let i = 0; i < arrayTotals.length; i++) {
        if (array_name == '') {
          let dynamic_element = arrayTotals[i]
          arrayCopy.push({ dynamic_element: 0 })
          this.init_totals(arrayTotals[i], arrayCopy, arrayTotals[i])
        } else {
          this.init_totals(arrayTotals[i], array_name, arrayTotals[i])
        }
      }
    },
    init_totals (key, array_name, member) {
      this[member] = array_name
        .map(function (item) {
          return parseInt(item[key]) ? parseInt(item[key]) : 0
        })
        .reduce((sum, value) => sum + value, 0)
    },
    checkValidity (value) {
      if (value > this.tt_affected_population) {
        this.errorMax = true
      } else {
        this.errorMax = false
      }
    },
    save () {
      this.nutrition.response_needed = this.response_needed
      this.nutrition.urgent_response_needed = this.urgent_response_needed
      this.nutrition.impact_on_nutrition_programmes = this.impact_on_nutrition_programmes.filter(
        value => Object.keys(value).length !== 0
      )
      this.nutrition.affected_population = this.affected_population.filter(
        value => Object.keys(value).length !== 0
      )

      this.$emit('save', this.nutrition, 'nutrition')
    },
    autosave () {
      this.nutrition.response_needed = this.response_needed
      this.nutrition.urgent_response_needed = this.urgent_response_needed
      this.nutrition.impact_on_nutrition_programmes = this.impact_on_nutrition_programmes.filter(
        value => Object.keys(value).length !== 0
      )
      this.nutrition.affected_population = this.affected_population.filter(
        value => Object.keys(value).length !== 0
      )

      this.$emit('autosave', this.nutrition, 'nutrition')
    }
  },

  beforeMount () {
    this.nutrition = typeof this.nutrition !== 'undefined' ? this.nutrition : {}

    setInterval(this.autosave, 1200)

    let dataset = JSON.parse(localStorage.getItem('nutrition').data)

    this.impact_on_nutrition_programmes =
      typeof dataset.impact_on_nutrition_programmes === 'undefined'
        ? this.impact_on_nutrition_programmes
        : dataset.impact_on_nutrition_programmes

    this.affected_population =
      typeof dataset.affected_population === 'undefined'
        ? this.affected_population
        : dataset.affected_population
    this.response_needed =
      typeof dataset.response_needed === 'undefined'
        ? this.response_needed
        : dataset.response_needed

    this.urgent_response_needed =
      typeof dataset.urgent_response_needed === 'undefined'
        ? this.urgent_response_needed
        : dataset.urgent_response_needed

    this.init_totals(
      'affected_males',
      this.affected_population,
      'affected_males'
    )
    this.init_totals(
      'affected_females',
      this.affected_population,
      'affected_females'
    )
  }
}
