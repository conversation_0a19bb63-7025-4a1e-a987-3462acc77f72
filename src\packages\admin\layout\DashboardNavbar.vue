<template>
  <base-nav
    container-classes="container-fluid"
    class="navbar-top border-bottom navbar-expand"
    :class="{ 'bg-success navbar-dark': type === 'default' }"
  >
    <!-- Navbar links -->
    <ul class="navbar-nav align-items-center ml-md-auto">
      <li class="nav-item d-xl-none">
        <!-- Sidenav toggler -->
        <div
          class="pr-3 sidenav-toggler"
          :class="{
            active: $sidebar.showSidebar,
            'sidenav-toggler-dark': type === 'default',
            'sidenav-toggler-light': type === 'light'
          }"
          @click="toggleSidebar"
        >
          <div class="sidenav-toggler-inner text-primary">
            <i class="sidenav-toggler-line text-primary"></i>
            <i class="sidenav-toggler-line text-primary"></i>
            <i class="sidenav-toggler-line text-primary"></i>
          </div>
        </div>
      </li>
    </ul>
    <base-dropdown
      class="navbar-nav ml-auto ml-md-0"
      data-toggle="dropdown"
      title-classes="btn btn-sm btn-neutral mr-0"
      menu-on-right
      :has-toggle="false"
    >
      <template slot="title" style="text-transform:capitalize">
        <div class="media align-items-center">
          <span class="avatar avatar-sm rounded-circle">
            <i class="ni ni-circle-08"></i>
          </span>
          <div class="media-body ml-2 d-none d-lg-block">
            <span
              class="mb-0 text-sm font-weight-bold"
              style="text-transform:capitalize;"
              >{{ this.$session.get("jwtuser") }}</span
            >
          </div>
        </div>
      </template>


      <a class="dropdown-item"  @click="handleProfile()">Profile</a>
      <button class="dropdown-item" @click="handleSignOut()">Logout</button>
    </base-dropdown>
  </base-nav>
</template>
<script>
import { CollapseTransition } from "vue2-transitions";
import { BaseNav, Modal } from "@/components";
import { user } from "@/api/user";

export default {
  components: {
    CollapseTransition,
    BaseNav,
    Modal
  },
  props: {
    type: {
      type: String,
      default: "light", // default|light
      description:
        "Look of the dashboard navbar. Default (Green) or light (gray)"
    }
  },
  computed: {
    routeName() {
      const { name } = this.$route;
      return this.capitalizeFirstLetter(name);
    }
  },
  data() {
    return {
      activeNotifications: false,
      showMenu: false,
      searchModalVisible: false,
      searchQuery: ""
    };
  },
  methods: {
    capitalizeFirstLetter(string) {
      return string.charAt(0).toUpperCase() + string.slice(1);
    },
    toggleNotificationDropDown() {
      this.activeNotifications = !this.activeNotifications;
    },
    closeDropDown() {
      this.activeNotifications = false;
    },
    toggleSidebar() {
      this.$sidebar.displaySidebar(!this.$sidebar.showSidebar);
    },
    hideSidebar() {
      this.$sidebar.displaySidebar(false);
    },
    handleSignOut() {
      this.$session.destroy();
      this.$router.push({ name: "Login", params: { session: "lost" } });
    },
    handleProfile(){

      this.$router.push({ name: "AdminEditProfile", params: { id: this.$session.get("jwtuid")} });
    }
  }
};
</script>
