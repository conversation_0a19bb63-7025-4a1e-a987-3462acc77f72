import { Table, TableColumn, Select, Option } from 'element-ui'
import RouteBreadCrumb from '@/components/Breadcrumb/RouteBreadcrumb'
import { BasePagination } from '@/components'
import clientPaginationMixin from '@/components/PaginatedTables/clientPaginationMixin'
import dateformat from 'dateformat'
import { mapGetters, mapActions } from 'vuex'
import { clusterreports } from '../../../../../../api/cluster/index.js'

import swal from 'sweetalert2'
export default {
  mixins: [clientPaginationMixin],
  components: {
    BasePagination,
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn
  },
  data() {
    return {
      report: [],

      updatedData: [],
      structrures: [
        'Road Embankment',
        'Drainage structure',
        'Railway',
        'Telecommunication infrastructure',
        'Other'
      ],

      vehicle_size: [
        'Light 4WD vehicle only',
        "Small truck (like lorry yaying'ono 7 mt)",
        'Bycycle; by foot; motor bike',
        '15 - 20mt truck',
        'No limitations; all trucks',
        'None; not passable'
      ],

      condition_of: [
        'Normal/Operational',
        'Slightly damaged',
        'Significantly damaged',
        'Under repair'
      ],

      surface_condition: [
        'Dry, normal',
        'Muddy/wet',
        'Flooded (not passable)',
        'Other',
      ],

      infrastructure: [
        'Telephone line',
        'VHF/HF radio',
        'Cellphone',
        'National radio station',
        'Community radio station',
        'Television'
      ],

      function_of: ['Functioning', 'Partially functioning', 'Not functioning']
    }
  },
  methods: {
    ...mapActions('clusterreports', {
      getclusterreportsAction: 'get'
    }),

    formatedDate(data) {
      let finalDate = dateformat(data, 'dd-mm-yyyy hh:mm:ss')
      return finalDate
    },

    updateData(dataObject, editedData) {

      editedData[0].status = '4';


      console.log(editedData[0], "dddddddddddddddddddddddddddddddddddddd")

      let editedDataObject = Object.assign({}, editedData[0])
      let dateOfupdate = new Date().toLocaleString()
      let editedby = this.$session.get('jwtuser')
      dataObject.editcounter = dataObject.editcounter + 1
      this.updatedData.push({
        editedby: editedby,
        editedon: dateOfupdate,
        updateddata: editedDataObject
      })

      dataObject.editedhistory = [...this.updatedData]
      this.confirmSubmission(dataObject)
    },

    confirmSubmission(data) {
      clusterreports.update(data).then(
        response => {
          swal({
            title: 'Succesfully updated report details',
            type: 'success',
            confirmButtonClass: 'btn btn-success btn-fill',
            buttonsStyling: false
          })
        },
        reason => {
          swal({
            title: 'Failed to update client',
            text: 'possible error in details (' + reason + ')',
            type: 'error',
            confirmButtonClass: 'btn btn-success btn-fill',
            buttonsStyling: false
          })
        }
      )
    }

  },

  async mounted() {
    let clusterdata = [...(await this.getclusterreportsAction())]
    this.report = clusterdata.filter(
      report => report._id == this.$route.params.id
    )
  }

}


