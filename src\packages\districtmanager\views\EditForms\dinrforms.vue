<template>
  <div>
    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Edit</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link :to="{ name: 'Dashboard', params: {} }">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                Forms
              </li>
              <li class="breadcrumb-item">
                <router-link :to="{ name: 'DINR Forms', params: {} }"
                  >DINR</router-link
                >
              </li>
              <li class="breadcrumb-item active" aria-current="page">Edit</li>
            </ol>
          </nav>
        </div>
      </div>
    </base-header>
    <div class="container-fluid mt--6">
      <div class="row justify-content-center">
        <div class="col-lg-8 card-wrapper">
          <!-- Grid system -->
          <card class="card">
            <!-- Card header -->
            <h3 slot="header" class="mb-0">DINR FORM DRAFT</h3>

            <!-- Card body -->
            <div class="row justify-content-center">
              <div class="col-lg-8">
                <div class="component-example__container">
                  <dl class="headings">
                    <dd
                      class="text-center text-muted mb-4"
                      style="font-size:22px"
                    >
                      <br />
                      <p>
                        <img src="../../../../assets/logo.png" height="70" />
                      </p>
                      <p>
                        <strong class="text-center text-muted mb-4"
                          >Government of Malawi</strong
                        >
                        <br />
                        <span
                          >Department of Disaster Management Affairs
                          (DoDMA)</span
                        >
                        <br />
                        <span style="background:orange;width:200vh;color:black"
                          >Draft Disaster Impact and Needs Report (DINR) Form
                        </span>
                      </p>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
            <div>
              <div>
                <p style="text-align:center;font-size:110%">
                  <b>ACRONYMS</b>
                </p>
                <table
                style="margin:0 auto;padding:0px !important; border:none !important;width:92%;"
              >
                <tr
                  style="margin:0px !important;padding:0px !important; border:none;"
                >
                  <td
                    style="margin:0px !important;padding:0px !important; border:none;"
                  >
                    <table style="margin:0 auto;border:1px solid;padding:0px">
                      <tr>
                        <td style="width:20%">
                          <b>ADRMC </b>
                        </td>
                        <td>Area Disaster Risk Management Committee</td>
                      </tr>
                      <tr>
                        <td>
                          <b>DDRMC</b>
                        </td>
                        <td>District Disaster Risk Management Committee</td>
                      </tr>
                      <tr>
                        <td>
                          <b>GVH</b>
                        </td>
                        <td>Group Village Headman</td>
                      </tr>
                      <tr>
                        <td>
                          <b>HH</b>
                        </td>
                        <td>Household</td>
                      </tr>
                      <tr>
                        <td>
                          <b>NR</b>
                        </td>
                        <td>Not reported</td>
                      </tr>
                    </table>
                  </td>
                  <td
                    style="margin:4px !important;padding:4px !important; border:none;"
                  ></td>
                  <td
                    style="margin:0px !important;padding:0px !important; border:none;"
                  >
                    <table style="margin:0 auto;border:1px solid;padding:0px">
                      <tr>
                        <td>
                          <b>N/A</b>
                        </td>
                        <td>Not Applicable</td>
                      </tr>

                      <tr>
                        <td>
                          <b>TA</b>
                        </td>
                        <td>Tradition Authority</td>
                      </tr>
                      <tr>
                        <td>
                          <b>T</b>
                        </td>
                        <td>Total</td>
                      </tr>
                      <tr>
                        <td>
                          <b>VDRMC</b>
                        </td>
                        <td>Village Disaster  Risk Management Committee</td>
                      </tr>
                      <tr>
                        <td>
                          <b>VH</b>
                        </td>
                        <td>Village Headman</td>
                      </tr>
                    </table>
                  </td>
                </tr>
              </table>
                <br />
              </div>
            </div>
            <br />
            <div>
              <hr class="mt-2 mb-2" />
              <form>
                <h3>Date of Disaster and Assessment</h3>
                <div class="row">
                  <div class="col-md-6">
                    <base-input label="Date of Disaster (DoD) From">
                      <flat-picker
                        slot-scope="{ focus, blur }"
                        data-toggle="tooltip"
                        data-placement="top"
                        title="When did the disaster start"
                        @on-open="focus"
                        @on-close="blur"
                        @input="controlDateValue()"
                        :config="{ allowInput: true, maxDate: maxDate() }"
                        class="form-control datepicker"
                        v-model="formModel.dodFrom"
                      ></flat-picker>
                    </base-input>
                  </div>

                  <div class="col-md-6">
                    <base-input label="Date of Disaster (DoD) To">
                      <flat-picker
                        slot-scope="{ focus, blur }"
                        @on-open="focus"
                        @on-close="blur"
                        @input="controlDateValue()"
                        data-toggle="tooltip"
                        data-placement="top"
                        title="When did the disaster end"
                        :config="{ allowInput: true, maxDate: maxDate() }"
                        class="form-control datepicker"
                        v-model="formModel.dodTo"
                      ></flat-picker>
                    </base-input>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-6">
                    <base-input label="Date of Assessment (DoA) ACPC">
                      <flat-picker
                        slot-scope="{ focus, blur }"
                        @on-open="focus"
                        @on-close="blur"
                        :config="{ allowInput: true, maxDate: maxDate() }"
                        data-toggle="tooltip"
                        data-placement="top"
                        title="When was the assessment done by the ACPC"
                        class="form-control datepicker"
                        v-bind:max="maxDate()"
                        @input="controlACPCDateValue()"
                        v-model="formModel.doaAcpc"
                      ></flat-picker>
                    </base-input>
                  </div>

                  <div class="col-md-6">
                    <base-input label="Date of Assessment (DoA) DCPC">
                      <flat-picker
                        slot-scope="{ focus, blur }"
                        @on-open="focus"
                        @on-close="blur"
                        :config="{ allowInput: true, maxDate: maxDate() }"
                        data-toggle="tooltip"
                        data-placement="top"
                        title="When was the assessment done by the DCPC"
                        class="form-control datepicker"
                        @input="controlDCPCDateValue()"
                        v-bind:max="maxDate()"
                        v-model="formModel.doaDcpc"
                      ></flat-picker>
                    </base-input>
                  </div>
                </div>
                <h4>Disaster Type (Choose a disaster type below)</h4>
                <div class="row">
                  <div class="col-md-6">
                    <base-input label="Disaster type">
                      <select
                        class="form-control"
                        v-model="disastername"
                        data-toggle="tooltip"
                        data-placement="top"
                        title="Choose disaster type"
                      >
                        <option v-for="item in disasters" :key="item">{{
                          item.name
                        }}</option>
                      </select>
                    </base-input>
                  </div>
                </div>
                <base-button
                  size="sm"
                  type="primary"
                  @click.native="createDisasterProfile"
                  data-toggle="tooltip"
                  data-placement="top"
                  title="Save entered disaster details"
                  >Save Details</base-button
                >

                <hr class="mt-1 mb-2" />
                <h4>
                  Please press Edit in the section below on the form you would
                  like to start filling form.
                </h4>
                <hr class="mt-1 mb-1" />
                <div>
                  <card
                    class="no-border-card"
                    body-classes="px-0 pb-0"
                    footer-classes="pb-0"
                  >
                    <div>
                      <el-table
                        :data="draForms"
                        header-row-class-name="thead-light"
                        @sort-change="sortChange"
                        @selection-change="selectionChange"
                      >
                        <el-table-column
                          label="TA"
                          prop="ta"
                          min-width="140px"
                          sortable
                        >
                          <template v-slot="{ row }">
                            <span>{{ row.admin3.admin3Name_en }}</span>
                          </template>
                        </el-table-column>

                        <el-table-column
                          label="GVHs"
                          min-width="170px"
                          sortable
                        >
                          <template v-slot="{ row }">
                            <span v-for="(item, i) in row.gvhs" :key="i">
                              <!-- if not wrapped in a span will leave a space before the comma -->
                              <span>{{ i > 0 ? ", " : "" }}</span>
                              <span class="text-nowrap">{{ item.name }}</span>
                            </span>
                          </template>
                        </el-table-column>

                        <el-table-column
                          label="Villages"
                          min-width="170px"
                          sortable
                        >
                          <template v-slot="{ row }">
                            <span v-for="(item, i) in row.villages" :key="i">
                              <!-- if not wrapped in a span will leave a space before the comma -->
                              <span>{{ i > 0 ? ", " : "" }}</span>
                              <span class="text-nowrap">{{ item.name }}</span>
                            </span>
                          </template>
                        </el-table-column>

                        <el-table-column
                          label="Camps"
                          min-width="170px"
                          sortable
                        >
                          <template v-slot="{ row }">
                            <span v-for="(item, i) in row.camps" :key="i">
                              <!-- if not wrapped in a span will leave a space before the comma -->
                              <span>{{ i > 0 ? ", " : "" }}</span>
                              <span class="text-nowrap">{{ item.name }}</span>
                            </span>
                          </template>
                        </el-table-column>

                        <el-table-column
                          min-width="300px"
                          align="right"
                          label="Actions"
                        >
                          <div slot-scope="{ $index, row }" class="d-flex">
                            <base-button
                              @click.native="handleEdit($index, row)"
                              data-toggle="tooltip"
                              data-placement="top"
                              title="Begin filling TA-Based form"
                              class="edit"
                              type="primary"
                              size="sm"
                              icon
                            >
                              <i class="text-white ni ni-scissors"></i> Edit
                            </base-button>
                          </div>
                        </el-table-column>
                      </el-table>
                    </div>
                    <div
                      slot="footer"
                      class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
                    >
                      <div class>
                        <p class="card-category">
                          Showing {{ from + 1 }} to {{ to }} of
                          {{ total }} entries
                          <span v-if="selectedRows.length"
                            >&nbsp; &nbsp; {{ selectedRows.length }} rows
                            selected</span
                          >
                        </p>
                      </div>
                      <base-pagination
                        class="pagination-no-border"
                        v-model="pagination.currentPage"
                        :per-page="pagination.perPage"
                        :total="total"
                      ></base-pagination>
                    </div>
                  </card>
                </div>
              </form>
            </div>
          </card>
        </div>
      </div>
    </div>
  </div>
</template>
<script src="./index.js" />
