<template>
  <div class="content">
    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Disaster</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">REPORTS</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">All</li>
            </ol>
          </nav>
        </div>
        <div class="col-lg-6 col-6 text-right pr-3">
          <router-link to="/manager/reports/excel" class="mr-2">
            <base-button size="sm" type="neutral">
              <i class="text-primary ni ni-archive-2"></i> ARCHIVES
            </base-button>
          </router-link>

          <base-dropdown
            title-classes="btn btn-sm btn-warning mr-0"
            menu-on-right
            :has-toggle="false"
          >
            <a slot="title" class="dropdown-toggle" type="neutral" size="sm" >
              <i class="text-black ni ni-cloud-download-95"></i> EXPORT TO EXCEL
            </a>
            <a class="dropdown-item" @click="generateColorCodeSheet('TA')">All TA-Based data</a>
            <a class="dropdown-item" @click="generateColorCodeSheet('GVH')">All GVH-based data</a>
            <a class="dropdown-item" @click="generateColorCodeSheet('EXCEL_data')">All Disaster Summary</a>
          </base-dropdown>


          <base-dropdown
            style="padding-left:5px"
            title-classes="btn btn-sm btn-primary mr-0"
            menu-on-right
            :has-toggle="false"
          >
            <a slot="title" class="dropdown-toggle" type="neutral" size="sm">
              <i class="text-black ni ni-cloud-download-95"></i> EXPORT TO CSV
            </a>
            <a class="dropdown-item" @click="generateDisasterSummarySheet('CSV_data')">All Disaster Summary</a>
          </base-dropdown>
        </div>
        <div class="col-lg-6 col-5 text-right"></div>
      </div>

      <!-- Card stats -->
    </base-header>
    <div class="container-fluid mt--6">
      <div>
        <card class="no-border-card" body-classes="px-0 pb-1" footer-classes="pb-2">
          <template slot="header">
            <h3 class="mb-0">Disaster Reports</h3>
          </template>
          <div>
            <div class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap">
              <el-select
                class="select-primary pagination-select"
                v-model="pagination.perPage"
                placeholder="Per page"
              >
                <el-option
                  class="select-primary"
                  v-for="item in pagination.perPageOptions"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>

              <div>
                <base-input
                  v-model="searchQuery"
                  prepend-icon="fas fa-search"
                  placeholder="Search..."
                ></base-input>
              </div>
            </div>
            <el-table
              :data="queriedData"
              row-key="id"
              header-row-class-name="thead-light"
              @sort-change="sortChange"
            >
              <el-table-column
                v-for="column in tableColumns"
                :key="column.label"
                v-bind="column"
                id="reportForm"
              ></el-table-column>
             <el-table-column min-width="340px" align="right" label="Actions">
                <div slot-scope="{ $index, row }" class="d-flex">
                  <!--  <base-button
                    @click.native="handleInfographics($index, row)"
                    class="like btn-link"
                    type="info"
                    size="sm"
                    icon
                  >
                    <i class="text-white ni ni-chart-bar-32"></i>
                  </base-button>-->
                  <base-button
                    @click.native="review(row)"
                    class="edit"
                    type="primary"
                    size="sm"
                    icon
                  >
                    <i class="text-white ni ni-single-copy-04"></i>Detailed
                  </base-button>
                  <base-button
                    @click.native="reviewSummary(row)"
                    style="color: white; background: #ab6f00; border:#ab6f00;"
                    class="edit"
                    type="primary"
                    size="sm"
                    icon
                  >
                    <i class="text-white  ni ni-badge"></i>Summary
                  </base-button>

                  <!-- <base-button
                    @click.native="handleInfographics(row)"
                    style="color: white; background: #454545; border:#454545;"
                    class="edit"
                    type="dark"
                    size="sm"
                    icon
                  >
                    <i class="text-white ni ni-chart-bar-32"></i>Infographics
                  </base-button> -->

                  <base-button
                     
                    @click="downloadDpictures(row)"
                     style="color: white; background: #077757 ; border:#077757 ;"
                    class="edit"
                    type="dark background"
                    size="sm"
                    icon
                  >
                    <i class="text-white ni ni-cloud-download-95"></i>Photos
                  </base-button>
                  
                </div>
              </el-table-column>
            </el-table>
          </div>
          <div
            slot="footer"
            class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
          >
            <div class>
              <p class="card-category">
                Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
                <span
                  v-if="selectedRows.length"
                >&nbsp; &nbsp; {{ selectedRows.length }} rows selected</span>
              </p>
            </div>
            <base-pagination
              class="pagination-no-border"
              v-model="pagination.currentPage"
              :per-page="pagination.perPage"
              :total="total"
            ></base-pagination>
          </div>
        </card>
      </div>
    </div>
  </div>
</template>
<script>
import { Table, TableColumn, Select, Option } from "element-ui";
import RouteBreadCrumb from "@/components/Breadcrumb/RouteBreadcrumb";
import { BasePagination } from "@/components";
import clientPaginationMixin from "@/components/PaginatedTables/clientPaginationMixin";
import { admin2s } from "../api/location/admin2s";

import { generateExcel, generateTAGVHExcel, generateDisasterSummaryExcel } from "../../../util/generateExcel";

import { MongoReports } from "../api/MongoReports";
import downloadexcel from "vue-json-excel";
import JSZip from 'jszip';
import FileSaver from 'file-saver';
import moment from "moment";
import dateformat from "dateformat";
import { mapGetters, mapActions } from "vuex";
import { dinrforms } from '../../districtmanager/api/forms/dinrforms.js'

export default {
  mixins: [clientPaginationMixin],
  components: {
    BasePagination,
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    [Table.name]: Table,
    downloadexcel,
    [TableColumn.name]: TableColumn,
  },
  data() {
    return {
      propsToSearch: ["name", "email", "age"],
      dinrFormsData: {},
      villagesArray: [],
      campsArray: [],
      bufferDraFormsData: [],
      finalExcelData: [],
      TAarray: [],
      draFormsData: [],
      download: [],
      gvhDATA: [],
      downloadData: [],
      testarray: [],
      TaData: [],
      admin2sData: [],
      AllDisasterSummaryExcel:[],

      gridOptions: {},
      tableColumns: [
        {
          prop: "id",
          label: "ID",
          minWidth: 50,
          sortable: true,
        },
        {
          prop: "disaster",
          label: "Disaster",
          minWidth: 150,
          sortable: true,
        },
        {
          prop: "district",
          label: "District",
          minWidth: 100,
          sortable: true,
        },
        {
          prop: "officer",
          label: "Officer",
          minWidth: 120,
          sortable: true,
        },
        {
          prop: "date",
          label: "Submmited on",
          minWidth: 135,
          sortable: true,
        },
      ],
      tableData: [],
      selectedRows: [],
    };
  },
   computed: {
    ...mapGetters({
      getDinrs: "dinrs/get",
      getDraById: "dras/getById",
    })},
  methods: {
     hasPictures(row) {
       
    if (Object.keys(row).includes('disasterImages')) {
      return row.disasterImages.length > 0
    }

    return false
   },
   
    downloadDpictures(form) {
       const resource = process.env.VUE_APP_ENGINE_URL + '/forms/dinr'
      const url = `${resource}/${form.uuid}/archives`
      axios.get(url, {
        responseType: 'blob',
        onDownloadProgress: e => {
          console.log(e)
        }
      }).then(response => {
        const blob = new Blob([response.data])
        const a = document.createElement('a')
        a.href = window.URL.createObjectURL(blob)
        a.download = `${form.disaster.split(' ').join('_')}_${moment(form.dodFrom).format('DDMMYYYY')}.zip`

        a.click()
      })
    },

      ...mapActions("dinrs", {
      getDinrsAction: "get",
    }),
    ...mapActions("dras", {
      getDrasAction: "get",
    }),
    generateColorCodeSheet(type) {
      if (type === "TA") {
        for (let i in this.download) {
          try {
            this.download[i].all.sectors.wash === undefined
              ? (this.download[i].all.sectors.wash = {})
              : this.download[i].all.sectors.wash;
            this.download[i].all.sectors.shelter === undefined
              ? (this.download[i].all.sectors.wash = {})
              : this.download[i].all.sectors.shelter;
            this.download[i].all.sectors.displaced === undefined
              ? (this.download[i].all.sectors.wash = {})
              : this.download[i].all.sectors.displaced;
            this.download[i].all.sectors.agriculture === undefined
              ? (this.download[i].all.sectors.agriculture = {})
              : this.download[i].all.sectors.agriculture;
            this.download[i].all.sectors.education === undefined
              ? (this.download[i].all.sectors.education = {})
              : this.download[i].all.sectors.education;
            this.download[i].all.sectors.nutrition === undefined
              ? (this.download[i].all.sectors.nutrition = {})
              : this.download[i].all.sectors.nutrition;
            this.download[i].all.sectors.protection === undefined
              ? (this.download[i].all.sectors.protection = {})
              : this.download[i].all.sectors.protection;
            this.download[i].all.sectors.logistics === undefined
              ? (this.download[i].all.sectors.logistics = {})
              : this.download[i].all.sectors.logistics;
            this.download[i].all.sectors.health === undefined
              ? (this.download[i].all.sectors.health = {})
              : this.download[i].all.sectors.health;
            this.download[i].all.sectors.environment === undefined
              ? (this.download[i].all.sectors.environment = {})
              : this.download[i].all.sectors.environment;
            this.download[i].all.sectors.food === undefined
              ? (this.download[i].all.sectors.food = {})
              : this.download[i].all.sectors.food;
            this.download[i].all.sectors.livelihoods === undefined
              ? (this.download[i].all.sectors.livelihoods = {})
              : this.download[i].all.sectors.livelihoods;

            this.TaData.push({
              DISASTERID: this.download[i].all.dinrFormId,
              Disaster: this.download[i].all.dinrform.disaster,
              District: this.download[i].all.dinrform.district,
              "TA PCODE": this.download[i].all.admin3.admin3Pcode,
              TA: this.download[i].all.admin3.admin3Name_en,
              "GVHS affected": this.download[i].all.gvhsAffected,

              // "GVH affected": "gvhs.name",

              "Disaster Start Date": this.download[i].all.dinrform.dodFrom,
              "Disaster End Date": this.download[i].all.dinrform.dodTo,
              "ACPC Assessment Date": this.download[i].all.dinrform.doaAcpc,
              "DCPC Assessement Date": this.download[i].all.dinrform.doaDcpc,
              "Submission Date": this.download[i].all.dinrform.date,

              //shelter

              "Houses partly damaged (shelter)": this.catchError(
                this.download[i].all.sectors.shelter.partly_damaged
              ),
              "Houses underwater (shelter)": this.catchError(
                this.download[i].all.sectors.shelter.under_water
              ),
              "Houses completely (shelter)": this.catchError(
                this.download[i].all.sectors.shelter.completely_damaged
              ),

              "number of males without shelter(shelter)": this.catchError(
                this.download[i].all.shelter_without_shelter_male
              ),
              "number of females without shelter (shelter)": this.catchError(
                this.download[i].all.shelter_without_shelter_female
              ),

              "number of female HH affected (shelter)": this.catchError(
                this.download[i].all.shelter_fhh_affected
              ),
              "number of male HH affected(shelter)": this.catchError(
                this.download[i].all.shelter_mhh_affected
              ),

              "Total Households affected (shelter)": this.catchError(
                this.download[i].all.shelter_affected_hh
              ),

              "number of injured females(shelter)": this.catchError(
                this.download[i].all.shelter_people_injured_female
              ),
              "number of injured males in category (shelter)": this.catchError(
                this.download[i].all.shelter_people_injured_female
              ),

              "Total people injured (shelter)": this.catchError(
                this.download[i].all.shelter_injured
              ),

              "number of dead females (shelter)": this.catchError(
                this.download[i].all.shelter_people_dead_female
              ),
              "number of dead males (shelter)": this.catchError(
                this.download[i].all.shelter_people_dead_male
              ),

              "Total people dead (shelter)": this.catchError(
                this.download[i].all.shelter_dead
              ),

              "Urgent response needed (shelter)": this.catchError(
                this.download[i].all.sectors.shelter.urgent_response_needed
              ),
              "General response needed (shelter)": this.catchError(
                this.download[i].all.sectors.shelter.response_needed
              ),

              //displaced

              "number of displaced male Households (displaced)": this.catchError(
                this.download[i].all.PeopleAffectedrows_male
              ),
              "number of displaced females Households (displaced)": this.catchError(
                this.download[i].all.PeopleAffectedrows_female
              ),

              "Total displaced Households (displaced)": this.catchError(
                this.download[i].all.displaced_hh
              ),

              "number of male HH accomodated (displaced)": this.catchError(
                this.download[i].all.displaced_households_accommodated_male
              ),
              "number of female HH accomodated (displaced)": this.catchError(
                this.download[i].all.displaced_households_accommodated_female
              ),

              "number of males disaggregated (displaced)": this.catchError(
                this.download[i].all.displaced_disagregated_male
              ),
              "number of females disaggregated (displaced)": this.catchError(
                this.download[i].all.displaced_disagregated_female
              ),

              "number of males accomodated (displaced)": this.catchError(
                this.download[i].all.displaced_individuals_accommodated_male
              ),
              "number of females accomodated (displaced)": this.catchError(
                this.download[i].all.displaced_individuals_accommodated_female
              ),

              "Urgent response needed (displaced)": this.catchError(
                this.download[i].all.sectors.displaced.urgent_response_needed
              ),
              "general response needed (displaced)": this.catchError(
                this.download[i].all.sectors.displaced.response_needed
              ),

              //wash

              "FHH with safe water (wash)": this.catchError(
                this.download[i].all.sectors.wash.with_safe_water_fhh
              ),

              "MHH with safe water (wash)": this.catchError(
                this.download[i].all.sectors.wash.with_safe_water_mhh
              ),
              "FHH with toilet access (wash)": this.catchError(
                this.download[i].all.sectors.wash.access_to_toilets_fhh
              ),
              "MHH with toilet access (wash)": this.catchError(
                this.download[i].all.sectors.wash.access_to_toilets_mhh
              ),
              "FHH risking contamination (wash)": this.catchError(
                this.download[i].all.sectors.wash.risk_contamination_fhh
              ),
              "MHH risking contamination  (wash)": this.catchError(
                this.download[i].all.sectors.wash.risk_contamination_mhh
              ),

              "Urgent response needed (wash)": this.catchError(
                this.download[i].all.sectors.wash.urgent_response_needed
              ),

              "general response needed (wash)": this.catchError(
                this.download[i].all.sectors.wash.response_needed
              ),

              //health

              "number of facilities partially functioning (health)": this.catchError(
                this.download[i].all.health_partially_functioning
              ),
              "number of  facilities on verge of closing (health)": this.catchError(
                this.download[i].all.health_verge_of_closing
              ),

              "number  facilities closed (health)": this.catchError(
                this.download[i].all.health_closed
              ),

              "state of medical supply availability (health)": this.catchError(
                this.download[i].all.medical_supply_availability
              ),

              "state of health personnel availability (health)": this.catchError(
                this.download[i].all.health_personel_availability
              ),
              "Urgent response needed (health)": this.catchError(
                this.download[i].all.sectors.health.urgent_response_needed
              ),

              "general response needed (health)": this.catchError(
                this.download[i].all.sectors.health.response_needed
              ),

              //food

              "is food available? (food)": this.catchError(
                this.download[i].all.is_food_available_food
              ),

              "No. of FHH with 1-2 month Food Availability (food)": this.catchError(
                this.download[i].all.food_1_2_months
              ),
              "No. of FHH with less than 1 month Food Availability (food)": this.catchError(
                this.download[i].all.food_less_1_month
              ),

              "No. of FHH with more than 2 months Food Availability (food)": this.catchError(
                this.download[i].all.food_2_months
              ),

              "No. of FHH who lost Food Stock (food)": this.catchError(
                this.download[i].all.food_stock_lost
              ),

              "No. of MHH with 1-2 month Food Availability (food)": this.catchError(
                this.download[i].all.food_1_2_months_male
              ),
              "No. of MHH with less than 1 month Food Availability (food)": this.catchError(
                this.download[i].all.food_less_1_month_male
              ),

              "No. of MHH with more than 2 months Food Availability (food)": this.catchError(
                this.download[i].all.food_2_months_male
              ),

              "No. of MHH who lost Food Stock (food)": this.catchError(
                this.download[i].all.food_stock_lost_male
              ),

              "Urgent response needed (food)": this.catchError(
                this.download[i].all.sectors.food.urgent_response_needed
              ),
              "general response needed (food)": this.catchError(
                this.download[i].all.sectors.food.response_needed
              ),

              //logistics

              "state of access to main roads (logistics)": this.catchError(
                this.download[i].all.road_access
              ),

              "Urgent response needed (logistics)": this.catchError(
                this.download[i].all.sectors.logistics.urgent_response_needed
              ),

              "general response needed (logistics)": this.catchError(
                this.download[i].all.sectors.logistics.response_needed
              ),

              //agriculture
              "food items damaged (KGs) (agriculture)": this.catchError(
                this.download[i].all.food_item_damage
              ),
              "number of crop hectares submerged  (agriculture)": this.catchError(
                this.download[i].all.hectares_submerged
              ),
              "number crop hectares washed off  (agriculture)": this.catchError(
                this.download[i].all.hectares_washed_away
              ),
              "number of households whose crops are impacted (agriculture)": this.catchError(
                this.download[i].all.impact_on_crops_hh_affected
              ),
              "number of crop hectares damaged in affected households  (agriculture)": this.catchError(
                this.download[i].all.impact_on_crops_hectares_damaged
              ),

              "hh affected per impacted livestock  (agriculture)": this.catchError(
                this.download[i].all.impact_on_livestock_hh
              ),
              "number of impacted livestock  (agriculture)": this.catchError(
                this.download[i].all.impact_on_livestock_la
              ),

              "Urgent response needed (agriculture)": this.catchError(
                this.download[i].all.sectors.agriculture.response_needed
              ),

              "general response needed (agriculture)": this.catchError(
                this.download[i].all.sectors.agriculture.response_needed
              ),

              //protection

              "impacted females (protection)": this.catchError(
                this.download[i].all.impact_on_vulnerable_persons_females
              ),

              "impacted males (protection)": this.catchError(
                this.download[i].all.impact_on_vulnerable_persons_males
              ),

              "Urgent response needed (protection)": this.catchError(
                this.download[i].all.sectors.protection.urgent_response_needed
              ),

              "general response needed (protection)": this.catchError(
                this.download[i].all.sectors.protection.response_needed
              ),

              //nutrition

              "number of affected males (nutrition)": this.catchError(
                this.download[i].all.nutrition_affected_pop_male
              ),
              "number of affected females (nutrition)": this.catchError(
                this.download[i].all.nutrition_affected_pop_female
              ),

              "general response needed (nutrition)": this.catchError(
                this.download[i].all.sectors.nutrition.response_needed
              ),

              //education

              "number of school buildings functioning (education)": this.catchError(
                this.download[i].all.education_building_functioning
              ),
              "number of school buildings underwater (education)": this.catchError(
                this.download[i].all.education_underwater
              ),
              "number of school buildings completely damaged (education)": this.catchError(
                this.download[i].all.education_completely_damaged
              ),

              "number of school buildings partially functioning (education)": this.catchError(
                this.download[i].all.education_building_partly_functioning
              ),

              "number of school buildings closed  (education)": this.catchError(
                this.download[i].all.education_closed_buildings
              ),

              "males of out school (education)": this.catchError(
                this.download[i].all.education_males_out_of_school
              ),
              "females of out school (education)": this.catchError(
                this.download[i].all.education_females_out_of_school
              ),

              //livelihoods

              "number severely affected (livelihoods)": this.catchError(
                this.download[i].all.livelihoods_severely_affected
              ),

              "number slightly affected (livelihoods)": this.catchError(
                this.download[i].all.livelihoods_slightly_affected
              ),

              "Urgent response needed (livelihoods)": this.catchError(
                this.download[i].all.sectors.livelihoods.response_needed
              ),

              "general response needed (livelihoods)": this.catchError(
                this.download[i].all.sectors.livelihoods.response_needed
              ),

              "Urgent response needed (environment)": this.catchError(
                this.download[i].all.sectors.environment.urgent_response_needed
              ),

              "general response needed (environment)": this.catchError(
                this.download[i].all.sectors.environment.response_needed
              ),
              sheet: "TA-BASED DATA",
            });
          } catch (error) {
            console.log(error);
          }
        }
        //console.log(this.TaData);
        generateTAGVHExcel(this.TaData);
      } 

      else if (type === "EXCEL_data") {
        for (let i in this.download) {
          try {
            this.download[i].all.sectors.wash === undefined
              ? (this.download[i].all.sectors.wash = {})
              : this.download[i].all.sectors.wash;
            this.download[i].all.sectors.shelter === undefined
              ? (this.download[i].all.sectors.wash = {})
              : this.download[i].all.sectors.shelter;
            this.download[i].all.sectors.displaced === undefined
              ? (this.download[i].all.sectors.wash = {})
              : this.download[i].all.sectors.displaced;
            this.download[i].all.sectors.agriculture === undefined
              ? (this.download[i].all.sectors.agriculture = {})
              : this.download[i].all.sectors.agriculture;
            this.download[i].all.sectors.education === undefined
              ? (this.download[i].all.sectors.education = {})
              : this.download[i].all.sectors.education;
            this.download[i].all.sectors.nutrition === undefined
              ? (this.download[i].all.sectors.nutrition = {})
              : this.download[i].all.sectors.nutrition;
            this.download[i].all.sectors.protection === undefined
              ? (this.download[i].all.sectors.protection = {})
              : this.download[i].all.sectors.protection;
            this.download[i].all.sectors.logistics === undefined
              ? (this.download[i].all.sectors.logistics = {})
              : this.download[i].all.sectors.logistics;
            this.download[i].all.sectors.health === undefined
              ? (this.download[i].all.sectors.health = {})
              : this.download[i].all.sectors.health;
            this.download[i].all.sectors.environment === undefined
              ? (this.download[i].all.sectors.environment = {})
              : this.download[i].all.sectors.environment;
            this.download[i].all.sectors.food === undefined
              ? (this.download[i].all.sectors.food = {})
              : this.download[i].all.sectors.food;
            this.download[i].all.sectors.livelihoods === undefined
              ? (this.download[i].all.sectors.livelihoods = {})
              : this.download[i].all.sectors.livelihoods;

            this.AllDisasterSummaryExcel.push({
              District: this.download[i].all.district,
              Floods:"",
              "Strong winds": "",

              "Hail storms": "",
             
              "Lightening":"",

              // "GVH affected": "gvhs.name",

              "Stormy rains":"",
              "Heavy rains": "",
              "Landslide": "",
              "Road accidents": "",
              "Animal-human conflict": "",

              //shelter

              "Fire": "",
              "Total No. of Injuries": "",
              "Total No. of Deaths": "",

              "Displaced HHs": "",
             
              sheet: "All District Disaster Summary",
            });
          } catch (error) {
            console.log(error);
          }
        }
        
       
        generateTAGVHExcel(this.AllDisasterSummaryExcel);
      } 

      else {
        generateTAGVHExcel(this.gvhDATA);
      }
    },


    review(row) {
      this.$router.push({ path: "/manager/detailsreport/" + row.uuid });
    },
    reviewSummary(row) {
      this.$router.push({ path: "/manager/summaryreport/" + row.uuid });
    },
    handleInfographics(row) {
      this.$router.push({ path: "/manager/dinrreports/" + row.uuid });
    },
    formatedDate(data) {
      const cur_date = this.addDays(data, 1);
      const finalDate = dateformat(cur_date, "dd-mm-yyyy");
      return finalDate;
    },

    addDays(date, days) {
      var result = new Date(date);
      result.setDate(result.getDate() + days);
      return result;
    },
    sumArrayValues(item, sector, array, key) {
      if (typeof item["sectors"][sector][array] !== "undefined") {
        return item["sectors"][sector][array]
          .map(function (item) {
            return parseInt(item[key]) ? parseInt(item[key]) : 0;
          })
          .reduce((sum, value) => sum + value, 0);
      } else if (typeof item[sector][array] === "undefined") {
        return 0;
      }
    },

    returnFieldvalue(item, sector, array, key) {
      if (typeof item["sectors"][sector][array] !== "undefined") {
        return item["sectors"][sector][array][0][key];
      } else if (typeof item["sectors"][sector][array] === "undefined") {
        return "";
      }
    },

    returnFieldItemvalue(item, sector, array, key) {
      if (typeof item["sectors"][sector][array] !== "undefined") {
        for (let i in item["sectors"][sector][array]) {
          if (item["sectors"][sector][array][i].name === key) {
            return item["sectors"][sector][array][i].status;
          } else if (
            typeof item["sectors"][sector][array][i].key === "undefined"
          ) {
            return "";
          }
        }
      }
    },

    sumArrayValuesNoAggregate(item, sector, array, key, filterBy, filterValue) {
      if (typeof item["sectors"][sector][array] !== "undefined") {
        return item["sectors"][sector][array]
          .filter((item) => item[filterBy] === filterValue)
          .map(function (item) {
            return parseInt(item[key]) ? parseInt(item[key]) : 0;
          })
          .reduce((sum, value) => sum + value, 0);
      } else if (typeof item[sector][array] === "undefined") {
        return 0;
      }
    },

    processExcel(item, dinformDataset) {
      this.downloadData.all = item;

      this.draFormsData.push(item);

      for (let i = 0; i < this.draFormsData.length; i++) {
        for (let a = 0; a < this.draFormsData[i].villages.length; a++) {
          this.villagesArray.push(this.draFormsData[i].villages[a].name);
        }

        for (let a = 0; a < this.draFormsData[i].camps.length; a++) {
          this.campsArray.push(this.draFormsData[i].camps[a].name);
        }
      }

      this.numberOfTAs++;

      let Gvharray = [];

      this.downloadData.dinrform = {
        ...this.downloadData.dinrform,
        ...dinformDataset,
      };

      for (let i = 0; i < item.gvhs.length; i++) {
        let GVHname = item.gvhs[i].name;

        Gvharray.push(GVHname);
      }

      this.downloadData.all.dinrform = this.downloadData.dinrform;

      try {
        this.downloadData.all.dinrform.doaAcpc = this.formatedDate(
          this.downloadData.dinrform.doaAcpc
        );
      } catch (error) {
        this.downloadData.all.dinrform.doaAcpc = "";
      }

      try {
        this.downloadData.all.dinrform.doaDcpc = this.formatedDate(
          this.downloadData.dinrform.doaDcpc
        );
      } catch (error) {
        this.downloadData.all.dinrform.doaDcpc = "";
      }

      try {
        this.downloadData.all.dinrform.dodFrom = this.formatedDate(
          this.downloadData.dinrform.dodFrom
        );
      } catch (error) {
        this.downloadData.all.dinrform.dodFrom = "";
      }

      try {
        this.downloadData.all.dinrform.dodTo = this.formatedDate(
          this.downloadData.dinrform.dodTo
        );
      } catch (error) {
        this.downloadData.all.dinrform.dodTo = "";
      }

      try {
        this.downloadData.all.gvhsAffected = Gvharray.join();
      } catch (error) {
        this.downloadData.all.gvhsAffected = "";
      }

      try {
        this.downloadData.all.is_food_available_food = this.returnFieldvalue(
          item,
          "food",
          "food_availability",
          "foodavailable"
        );
      } catch (error) {
        this.downloadData.all.is_food_available_food = "";
      }

      try {
        this.downloadData.all.medical_supply_availability = this.returnFieldItemvalue(
          item,
          "health",
          "available_health_medical",
          "Medical supply"
        );
      } catch (error) {
        this.downloadData.all.medical_supply_availability = "";
      }

      try {
        this.downloadData.all.health_personel_availability = this.returnFieldItemvalue(
          item,
          "health",
          "available_health_medical",
          "Health personel"
        );
      } catch (error) {
        this.downloadData.all.health_personel_availability = "";
      }

      try {
        this.downloadData.all.road_access =
          item.sectors.logistics.access_of_structures[0].accessibility;
      } catch (error) {
        this.downloadData.all.road_access = "";
      }

      try {
        this.downloadData.all.food_1_2_months = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "female_HH",
          "category",
          "No. of HH with 1-2 month Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_1_2_months = 0;
      }

      try {
        this.downloadData.all.food_2_months = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "female_HH",
          "category",
          "No. of HH with more than 2 months Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_2_months = 0;
      }

      try {
        this.downloadData.all.food_stock_lost = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "female_HH",
          "category",
          "No. of HH who lost Food Stock"
        );
      } catch (error) {
        this.downloadData.all.food_stock_lost = 0;
      }

      try {
        this.downloadData.all.food_less_1_month = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "female_HH",
          "category",
          "No. of HH with less than 1 month Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_less_1_month = 0;
      }

      try {
        this.downloadData.all.food_less_1_month_male = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "male_HH",
          "category",
          "No. of HH with less than 1 month Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_less_1_month_male = 0;
      }

      try {
        this.downloadData.all.food_1_2_months_male = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "male_HH",
          "category",
          "No. of HH with 1-2 month Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_1_2_months_male = 0;
      }

      try {
        this.downloadData.all.food_2_months_male = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "male_HH",
          "category",
          "No. of HH with more than 2 months Food Availability"
        );
      } catch (error) {
        this.downloadData.all.food_2_months_male = 0;
      }

      try {
        this.downloadData.all.food_stock_lost_male = this.sumArrayValuesNoAggregate(
          item,
          "food",
          "food_stocks_avaliability",
          "male_HH",
          "category",
          "No. of HH who lost Food Stock"
        );
      } catch (error) {
        this.downloadData.all.food_stock_lost_male = 0;
      }

      try {
        this.downloadData.all.food_item_damage = this.sumArrayValues(
          item,
          "agriculture",
          "food_item_damage",
          "number_of_kilos"
        );
      } catch (error) {
        this.downloadData.all.food_item_damage = 0;
      }
      try {
        this.downloadData.all.PeopleAffectedrows_female = this.sumArrayValues(
          item,
          "displaced",
          "PeopleAffectedrows",
          "number_displaced_by_gender_fhh"
        );
      } catch (error) {
        this.downloadData.all.PeopleAffectedrows_female = 0;
      }

      try {
        this.downloadData.all.PeopleAffectedrows_male = this.sumArrayValues(
          item,
          "displaced",
          "PeopleAffectedrows",
          "number_displaced_by_gender_mhh"
        );
      } catch (error) {
        this.downloadData.all.PeopleAffectedrows_male = 0;
      }

      try {
        this.downloadData.all.displaced_hh =
          parseInt(this.downloadData.all.PeopleAffectedrows_female) +
          parseInt(this.downloadData.all.PeopleAffectedrows_male);
      } catch (error) {
        this.downloadData.all.displaced_hh = 0;
      }
      try {
        this.downloadData.all.hectares_submerged = this.sumArrayValues(
          item,
          "agriculture",
          "crops_damaged",
          "hectares_submerged"
        );
      } catch (error) {
        this.downloadData.all.hectares_submerged = 0;
      }

      try {
        this.downloadData.all.hectares_washed_away = this.sumArrayValues(
          item,
          "agriculture",
          "crops_damaged",
          "hectares_washed_away"
        );
      } catch (error) {
        this.downloadData.all.hectares_washed_away = 0;
      }

      try {
        this.downloadData.all.impact_on_crops_hh_affected = this.sumArrayValues(
          item,
          "agriculture",
          "impact_on_crops",
          "hh_affected"
        );
      } catch (error) {
        this.downloadData.all.impact_on_crops_hh_affected = 0;
      }

      try {
        this.downloadData.all.impact_on_crops_hectares_damaged = this.sumArrayValues(
          item,
          "agriculture",
          "impact_on_crops",
          "hectares_damaged"
        );
      } catch (error) {
        this.downloadData.all.impact_on_crops_hectares_damaged = 0;
      }

      try {
        this.downloadData.all.impact_on_livestock_hh = this.sumArrayValues(
          item,
          "agriculture",
          "impact_on_livestock",
          "hh_affected_l"
        );
      } catch (error) {
        this.downloadData.all.impact_on_livestock_hh = 0;
      }

      try {
        this.downloadData.all.impact_on_livestock_la = this.sumArrayValues(
          item,
          "agriculture",
          "impact_on_livestock",
          "livestock_affected"
        );
      } catch (error) {
        this.downloadData.all.impact_on_livestock_la = 0;
      }

      try {
        this.downloadData.all.displaced_households_accommodated_male = this.sumArrayValues(
          item,
          "displaced",
          "displaced_households_accommodated",
          "accomodated_males_hh"
        );
      } catch (error) {
        this.downloadData.all.displaced_households_accommodated_male = 0;
      }
      try {
        this.downloadData.all.displaced_households_accommodated_female = this.sumArrayValues(
          item,
          "displaced",
          "displaced_households_accommodated",
          "accomodated_females_hh"
        );
      } catch (error) {
        this.downloadData.all.displaced_households_accommodated_female = 0;
      }

      try {
        this.downloadData.all.displaced_disagregated_male = this.sumArrayValues(
          item,
          "displaced",
          "displaced_disaggregated",
          "displaced_males"
        );
      } catch (error) {
        this.downloadData.all.displaced_disagregated_male = 0;
      }

      try {
        this.downloadData.all.displaced_disagregated_female = this.sumArrayValues(
          item,
          "displaced",
          "displaced_disaggregated",
          "displaced_females"
        );
      } catch (error) {
        this.downloadData.all.displaced_disagregated_female = 0;
      }

      try {
        this.downloadData.all.displaced_individuals_accommodated_male = this.sumArrayValues(
          item,
          "displaced",
          "displaced_individuals_accommodated",
          "accomodated_males"
        );
      } catch (error) {
        this.downloadData.all.displaced_individuals_accommodated_male = 0;
      }

      try {
        this.downloadData.all.displaced_individuals_accommodated_female = this.sumArrayValues(
          item,
          "displaced",
          "displaced_individuals_accommodated",
          "accomodated_females"
        );
      } catch (error) {
        this.downloadData.all.displaced_individuals_accommodated_female = 0;
      }

      try {
        this.downloadData.all.education_building_functioning = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "functioning_buildings"
        );
      } catch (error) {
        this.downloadData.all.education_building_functioning = 0;
      }

      try {
        this.downloadData.all.education_building_partly_functioning = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "partially_functioning_buildings"
        );
      } catch (error) {
        this.downloadData.all.education_building_partly_functioning = 0;
      }

      try {
        this.downloadData.all.education_closed_buildings = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "closed_buildings"
        );
      } catch (error) {
        this.downloadData.all.education_closed_buildings = 0;
      }

      try {
        this.downloadData.all.education_females_out_of_school = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "females_out_of_school"
        );
      } catch (error) {
        this.downloadData.all.education_females_out_of_school = 0;
      }

      try {
        this.downloadData.all.education_males_out_of_school = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "males_out_of_school"
        );
      } catch (error) {
        this.downloadData.all.education_males_out_of_school = 0;
      }

      try {
        this.downloadData.all.education_underwater = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "underwater"
        );
      } catch (error) {
        this.downloadData.all.education_underwater = 0;
      }

      try {
        this.downloadData.all.education_completely_damaged = this.sumArrayValues(
          item,
          "education",
          "impact_on_schools",
          "completely_damaged"
        );
      } catch (error) {
        this.downloadData.all.education_completely_damaged = 0;
      }

      try {
        this.downloadData.all.health_partially_functioning =
          this.sumArrayValues(
            item,
            "health",
            "available_health_facilities",
            "partially_functioning"
          ) +
          this.sumArrayValues(
            item,
            "health",
            "other_health_facilities",
            "partialy_functioning"
          );
      } catch (error) {
        this.downloadData.all.health_partially_functioning = 0;
      }

      try {
        this.downloadData.all.health_verge_of_closing =
          this.sumArrayValues(
            item,
            "health",
            "available_health_facilities",
            "verge_of_closing"
          ) +
          this.sumArrayValues(
            item,
            "health",
            "other_health_facilities",
            "verge_of_closing"
          );
      } catch (error) {
        this.downloadData.all.health_verge_of_closing = 0;
      }

      try {
        this.downloadData.all.health_closed =
          this.sumArrayValues(
            item,
            "health",
            "available_health_facilities",
            "closed"
          ) +
          this.sumArrayValues(
            item,
            "health",
            "other_health_facilities",
            "closed"
          );
      } catch (error) {
        this.downloadData.all.health_closed = 0;
      }

      try {
        this.downloadData.all.livelihoods_slightly_affected = this.sumArrayValues(
          item,
          "livelihoods",
          "livelihoods_affected",
          "severely_affected"
        );
      } catch (error) {
        this.downloadData.all.livelihoods_slightly_affected = 0;
      }
      try {
        this.downloadData.all.livelihoods_severely_affected = this.sumArrayValues(
          item,
          "livelihoods",
          "livelihoods_affected",
          "slightly_affected"
        );
      } catch (error) {
        this.downloadData.all.livelihoods_severely_affected = 0;
      }

      try {
        this.downloadData.all.impact_on_vulnerable_persons_males = this.sumArrayValues(
          item,
          "protection",
          "impact_on_vulnerable_persons",
          "impacted_males"
        );
      } catch (error) {
        this.downloadData.all.impact_on_vulnerable_persons_males = 0;
      }

      try {
        this.downloadData.all.impact_on_vulnerable_persons_females = this.sumArrayValues(
          item,
          "protection",
          "impact_on_vulnerable_persons",
          "impacted_females"
        );
      } catch (error) {
        this.downloadData.all.impact_on_vulnerable_persons_females = 0;
      }

      try {
        this.downloadData.all.nutrition_affected_pop_male = this.sumArrayValues(
          item,
          "nutrition",
          "affected_population",
          "affected_males"
        );
      } catch (error) {
        this.downloadData.all.nutrition_affected_pop_male = 0;
      }

      try {
        this.downloadData.all.nutrition_affected_pop_female = this.sumArrayValues(
          item,
          "nutrition",
          "affected_population",
          "affected_females"
        );
      } catch (error) {
        this.downloadData.all.nutrition_affected_pop_female = 0;
      }

      try {
        this.downloadData.all.shelter_without_shelter_male = this.sumArrayValues(
          item,
          "shelter",
          "people_without_shelter",
          "males"
        );
      } catch (error) {
        this.downloadData.all.shelter_without_shelter_male = 0;
      }

      try {
        this.downloadData.all.shelter_without_shelter_female = this.sumArrayValues(
          item,
          "shelter",
          "people_without_shelter",
          "females"
        );
      } catch (error) {
        this.downloadData.all.shelter_without_shelter_female = 0;
      }

      try {
        this.downloadData.all.shelter_people_dead_female = this.sumArrayValues(
          item,
          "shelter",
          "PeopleDeadrows",
          "females_dead"
        );
      } catch (error) {
        this.downloadData.all.shelter_people_dead_female = 0;
      }

      try {
        this.downloadData.all.shelter_people_dead_male = this.sumArrayValues(
          item,
          "shelter",
          "PeopleDeadrows",
          "males_dead"
        );
      } catch (error) {
        this.downloadData.all.shelter_people_dead_male = 0;
      }

      try {
        this.downloadData.all.shelter_dead =
          parseInt(this.downloadData.all.shelter_people_dead_male) +
          parseInt(this.downloadData.all.shelter_people_dead_female);
      } catch (error) {
        this.downloadData.all.shelter_dead = 0;
      }

      try {
        this.downloadData.all.shelter_people_injured_female = this.sumArrayValues(
          item,
          "shelter",
          "PeopleInjuredrows",
          "people_injured_females"
        );
      } catch (error) {
        this.downloadData.all.shelter_people_injured_female = 0;
      }

      try {
        this.downloadData.all.shelter_people_injured_male = this.sumArrayValues(
          item,
          "shelter",
          "PeopleInjuredrows",
          "people_injured_males"
        );
      } catch (error) {
        this.downloadData.all.shelter_people_injured_male = 0;
      }

      try {
        this.downloadData.all.shelter_injured =
          parseInt(this.downloadData.all.shelter_people_injured_female) +
          parseInt(this.downloadData.all.shelter_people_injured_male);
      } catch (error) {
        this.downloadData.all.shelter_injured = 0;
      }

      try {
        this.downloadData.all.shelter_fhh_affected = this.sumArrayValues(
          item,
          "shelter",
          "PeopleAffectedrows",
          "damaged_fhh"
        );
      } catch (error) {
        this.downloadData.all.shelter_fhh_affected = 0;
      }

      try {
        this.downloadData.all.shelter_mhh_affected = this.sumArrayValues(
          item,
          "shelter",
          "PeopleAffectedrows",
          "damaged_mhh"
        );
      } catch (error) {
        this.downloadData.all.shelter_mhh_affected = 0;
      }

      try {
        this.downloadData.all.shelter_affected_hh =
          parseInt(this.downloadData.all.shelter_mhh_affected) +
          parseInt(this.downloadData.all.shelter_fhh_affected);
      } catch (error) {
        this.downloadData.all.shelter_affected_hh = "";
      }

      this.download.push({ all: this.downloadData.all });
    },

    processGVHExcel() {
      let gvhdata = [];
      for (let k in this.download) {
        try {
          this.download[k].all.sectors.shelter =
            this.download[k].all.sectors.shelter === undefined
              ? (this.download[k].all.sectors.shelter = [
                  {
                    damaged_fhh: "",
                    damaged_mhh: "",
                    fully_blown_roof: "",
                    partly_blown_roof: "",
                    burnt: "",
                  },
                ])
              : this.download[k].all.sectors.shelter;

          this.download[k].all.sectors.displaced =
            this.download[k].all.sectors.displaced === undefined
              ? (this.download[k].all.sectors.displaced = [
                  {
                    number_displaced_by_gender_fhh: "",
                    number_displaced_by_gender_mhh: "",
                  },
                ])
              : this.download[k].all.sectors.displaced;

          if (
            typeof this.download[k].all.sectors.shelter.PeopleInjuredrows !==
              "undefined" ||
            typeof this.download[k].all.sectors.shelter.PeopleAffectedrows !==
              "undefined" ||
            typeof this.download[k].all.sectors.shelter.PeopleDeadrows !==
              "undefined" ||
            typeof this.download[k].all.sectors.displaced.PeopleAffectedrows !==
              "undefined"
          ) {
            let districtdata = {
              DistasterID: this.download[k].all.dinrform.uuid,
              DisasterName: this.download[k].all.dinrform.disaster,
              country: this.download[k].all.dinrform.country,
              country_pcode: this.download[k].all.dinrform.country_pcode,

              district: this.download[k].all.dinrform.district,
              TAname: this.download[k].all.admin3.admin3Name_en,
              TApcode: this.download[k].all.admin3.admin3Pcode,
              dstart: this.download[k].all.dinrform.dodFrom,
              dend: this.download[k].all.dinrform.dodTo,
              dacpc: this.download[k].all.dinrform.doaAcpc,
              ddcpc: this.download[k].all.dinrform.doaDcpc,
              submited: this.download[k].all.dinrform.date,
              gvhs: this.download[k].all.gvhs,
              shelter_peopleaffected:
                this.download[k].all.sectors.shelter.PeopleAffectedrows ===
                undefined
                  ? (this.download[
                      k
                    ].all.sectors.shelter.PeopleAffectedrows = [])
                  : this.download[k].all.sectors.shelter.PeopleAffectedrows,

              shelter_peopleinjured: this.download[k].all.sectors.shelter
                .PeopleInjuredrows,
              shelter_peopledead: this.download[k].all.sectors.shelter
                .PeopleDeadrows,

              shelter_urgent_response: this.download[k].all.sectors.shelter
                .urgent_response_needed,
              shelter_longterm_response: this.download[k].all.sectors.shelter
                .response_needed,

              displaced_peopleaffected:
                this.download[k].all.sectors.displaced.PeopleAffectedrows ===
                undefined
                  ? (this.download[
                      k
                    ].all.sectors.displaced.PeopleAffectedrows = [])
                  : this.download[k].all.sectors.displaced.PeopleAffectedrows,

              displaced_urgent_response:
                this.download[k].all.sectors.displaced.urgent_response_needed ==
                undefined
                  ? ""
                  : this.download[k].all.sectors.displaced
                      .urgent_response_needed,
              displaced_longterm_response:
                this.download[k].all.sectors.displaced.response_needed ==
                undefined
                  ? ""
                  : this.download[k].all.sectors.displaced.response_needed,
            };

            for (let i in districtdata.gvhs) {
              let gvhname = districtdata.gvhs[i].name;

              let femalesInjured = 0;
              let malesInjured = 0;

              let femalesDead = 0;
              let malesDead = 0;

              let diplaced_fhh = "";
              let diplaced_mhh = "";

              let total_injured = 0;

              try {
                if (
                  typeof districtdata.shelter_peopleinjured == "undefined" ||
                  typeof districtdata.shelter_peopledead == "undefined"
                ) {
                  femalesInjured = 0;
                  malesInjured = 0;
                  femalesDead = 0;
                  malesDead = 0;

                  diplaced_fhh = "";
                  diplaced_mhh = "";

                  total_injured = 0;
                } else {
                  femalesInjured = districtdata.shelter_peopleinjured.find(
                    (item) => item.name == gvhname
                  ).people_injured_females;

                  malesInjured = districtdata.shelter_peopleinjured.find(
                    (item) => item.name == gvhname
                  ).people_injured_males;

                  total_injured =
                    parseInt(femalesInjured || 0) + parseInt(malesInjured || 0);
                  femalesDead = districtdata.shelter_peopledead.find(
                    (item) => item.name == gvhname
                  ).females_dead;

                  malesDead = districtdata.shelter_peopledead.find(
                    (item) => item.name == gvhname
                  ).males_dead;

                  diplaced_fhh = districtdata.displaced_peopleaffected.find(
                    (item) => item.name == gvhname
                  ).number_displaced_by_gender_fhh;

                  diplaced_mhh = districtdata.displaced_peopleaffected.find(
                    (item) => item.name == gvhname
                  ).number_displaced_by_gender_mhh;
                }
                gvhdata.push({
                  sheet: "GVH-BASED DATA",

                  DistasterID: districtdata.DistasterID,
                  Disaster: districtdata.DisasterName,
                  TA: districtdata.TAname,
                  "TA PCODE": districtdata.TApcode,
                  District: districtdata.district,
                  GVH: gvhname,

                  "Disaster Start Date": districtdata.dstart,
                  "Disaster End Date": districtdata.dend,
                  "ACPC Assessment Date": districtdata.dacpc,
                  "DCPC Assessement Date": districtdata.ddcpc,
                  "Submission Date": districtdata.submited,

                  "Female Households affected (shelter)": districtdata.shelter_peopleaffected.find(
                    (item) => item.name == gvhname
                  ).damaged_fhh,

                  "Male Households affected (shelter)": districtdata.shelter_peopleaffected.find(
                    (item) => item.name == gvhname
                  ).damaged_mhh,

                  "Total Households affected (shelter)":
                    parseInt(
                      districtdata.shelter_peopleaffected.find(
                        (item) => item.name == gvhname
                      ).damaged_mhh || 0
                    ) +
                    parseInt(
                      districtdata.shelter_peopleaffected.find(
                        (item) => item.name == gvhname
                      ).damaged_mhh || 0
                    ),

                  "Houses With fully blown roofs (shelter)": districtdata.shelter_peopleaffected.find(
                    (item) => item.name == gvhname
                  ).fully_blown_roof,

                  "Houses with partly blown roofs (shelter)": districtdata.shelter_peopleaffected.find(
                    (item) => item.name == gvhname
                  ).partly_blown_roof,

                  "Houses with walls damaged (shelter)": districtdata.shelter_peopleaffected.find(
                    (item) => item.name == gvhname
                  ).wall_damaged,

                  "Houses burnt (shelter)": districtdata.shelter_peopleaffected.find(
                    (item) => item.name == gvhname
                  ).burnt,

                  "Number of females injured (shelter)": femalesInjured,
                  "Number of males injured (shelter)": malesInjured,

                  "Total people injured (shelter)": total_injured,

                  "Number of females dead (shelter)": femalesDead,

                  "Number of males dead (shelter)": malesDead,

                  "Total people dead (shelter)":
                    parseInt(malesDead || 0) + parseInt(femalesDead || 0),

                  "Urgent needed (shelter)":
                    districtdata.shelter_urgent_response,
                  "General response needed (shelter)":
                    districtdata.shelter_longterm_response,

                  "Female Households affected (displaced)": diplaced_fhh,

                  "Male Households affected (displaced)": diplaced_mhh,

                  "Total Households affected (displaced)":
                    parseInt(diplaced_fhh || 0) + parseInt(diplaced_mhh || 0),

                  "Urgent response needed (displaced)":
                    districtdata.displaced_urgent_response,
                  "Long-term response needed (displaced)":
                    districtdata.displaced_longterm_response,
                });
              } catch (error) {
                // console.log(error);
              }
            }
          } else {
            this.download[k].all.sectors.shelter.PeopleAffectedrows = [];
            this.download[k].all.sectors.shelter.PeopleDeadrows = [];
            this.download[k].all.sectors.shelter.PeopleMissingrows = [];
            this.download[k].all.sectors.shelter.PeopleInjuredrows = [];

            this.download[k].all.sectors.shelter.urgent_response_needed = "";
            this.download[k].all.sectors.shelter.response_needed = "";
            this.download[k].all.sectors.displaced.urgent_response_needed = "";
            this.download[k].all.sectors.displaced.response_needed = "";
          }

          this.gvhDATA = gvhdata;
        } catch (error) {
          // console.log(error);
        }
      }
    },
    catchError(data = "") {
      if (typeof data === undefined) {
        return "";
      }

      return data;6
    },
  },
  async mounted() {
   
    let dinrs =[... await this.getDinrsAction()];
    let dras=[...await this.getDrasAction()];

    let DINRData = await dinrs;

    DINRData.forEach((dinr, index) => {
      DINRData.sort(function (a, b) {
        return new Date(b.createdon) - new Date(a.createdon);
      });

      let row = DINRData[index]._id;
      this.tableData.push({
        id: index + 1,
        disaster: DINRData[index].disaster,
        district: DINRData[index].district.admin2_name_en,
        date: this.formatedDate(DINRData[index].createdon),
        officer:
          DINRData[index].account.firstName +
          " " +
          DINRData[index].account.lastName,
        uuid: DINRData[index]._id,
      });

      let dinformDataset = {
        disaster: DINRData[index].disaster,
        district: DINRData[index].district.admin2_name_en,
        date: this.formatedDate(DINRData[index].createdon),
        doaAcpc: DINRData[index].doaAcpc,
        doaDcpc: DINRData[index].doaDcpc,
        dodFrom: DINRData[index].dodFrom,
        dodTo: DINRData[index].dodTo,
        officer:
          DINRData[index].account.firstName +
          " " +
          DINRData[index].account.lastName,
        uuid: DINRData[index]._id,
        country: "MALAWI",
        country_pcode: "MWI",
      };

      let dra = dras.filter((dra) => dra.dinrFormId == row);

      this.draFormsData = [];

      this.downloadData.dinrform = {};

      dra.forEach((item, index, array) => {
        this.processExcel(item, dinformDataset);
      });
    });

    this.processGVHExcel();

      admin2s.get().then(response => {
      //console.log(response.data);
      this.admin2sData = response.data;
    })
  },


};
</script>
<style>
.no-border-card .card-footer {
  border-top: 0;
}
</style>
