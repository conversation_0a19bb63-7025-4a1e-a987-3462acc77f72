<template>
  <div class="content">
    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Disaster</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">Trends</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">All</li>
            </ol>
          </nav>
        </div>
        <div class="col-lg-6 col-5 text-right"></div>
      </div>
      <!-- Card stats -->
    </base-header>
    <div class="container-fluid mt--6">
      <div>
        <card
          class="no-border-card"
          body-classes="px-0 pb-1"
          footer-classes="pb-2"
        >
          <template slot="header">
            <h3 class="mb-0">TRENDS OF DISASTER REPORTS</h3>
          </template>
          <div>
            <div
              class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
            >
              <div class="row date-filters row mt-4">
                <div class="col-4">
                  <span>TA DISASTER DATE</span>
                  <span id="identifier-c"></span>
                </div>
                <div class="col-1"></div>
              </div>
            </div>
            <div style="width:100%;text-algin:center;" id="my_dataviz"></div>
          </div>
          <div
            slot="footer"
            class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
          ></div>
        </card>
      </div>
    </div>
  </div>
</template>
<script>
import { Table, TableColumn, Select, Option } from "element-ui";
import RouteBreadCrumb from "@/components/Breadcrumb/RouteBreadcrumb";
import * as d3 from "d3";
import * as dc from "dc";
import crossfilter from "crossfilter2";
var globalData = [];
import { MongoReports } from "../../districtmanager/api/MongoReports";
var moment = require("moment");

export default {
  components: {
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn
  },
  data() {
    return {
      unsigned: {
        lessThan24Hours: [],
        lessThan7Days: [],
        lessThan30Days: [],
        lessThan90Days: [],
        lessThan12Months: [],
        lifeTime: []
      },
      unApproved: {
        lessThan24Hours: [],
        lessThan7Days: [],
        lessThan30Days: [],
        lessThan90Days: [],
        lessThan12Months: [],
        lifeTime: []
      },
      approved: {
        lessThan24Hours: [],
        lessThan7Days: [],
        lessThan30Days: [],
        lessThan90Days: [],
        lessThan12Months: [],
        lifeTime: []
      },
      submitted: {
        lessThan24Hours: [],
        lessThan7Days: [],
        lessThan30Days: [],
        lessThan90Days: [],
        lessThan12Months: [],
        lifeTime: []
      },
      all: [],
      data: [],
      data: [
        {
          group: "last 24 Hours",
          Unsigned: [],
          Signed: [],
          Submitted: []
        },
        {
          group: "last 7 day",
          Unsigned: [],
          Signed: [],
          Submitted: []
        },
        {
          group: "last 30 day",
          Unsigned: [],
          Signed: [],
          Submitted: []
        },
        {
          group: "last 90 days",
          Unsigned: [],
          Signed: [],
          Submitted: []
        },
        {
          group: "last 12 Months",
          Unsigned: [],
          Signed:[],
          Submitted: []
        },
        {
          group: "Lifetime",
          Unsigned:[],
          Signed: [],
          Submitted: []
        }
      ]
    };
  },
  computed: {
    drawGraph1() {
      ///tooltip
   
    },
    getData() {
      return this.data;
    }
  },
  async mounted() {
    await this.loadData().then(response => {
      this.drawGraph();
    });
  },
  methods: {
    async loadData() {
      var response = await MongoReports.getUnapprovedDinrs().then(response => {
        var formated = response.data.map(r => {
          var data = {};
          data.admin2_name_en = r.district.admin2_name_en;
          data.hours = moment().diff(moment(r.createdon), "hours");
          data.approvalMetadata = r.approvalMetadata;
          data.isApproved = r.isApproved ? r.isApproved : false;
          data.isRejected = r.isRejected ? r.isRejected : false;
          return data;
        });

        this.updateLast24hours(formated.filter(d => d.hours <= 24));
        this.upateLast7Days(formated.filter(d => d.hours <= 168));

        this.upateLast30Days(formated.filter(d => d.hours <= 720));
        this.upateLast90Days(formated.filter(d => d.hours <= 2160));
        this.upateLast12Months(formated.filter(d => d.hours <= 8760));
        this.upateLifetime(formated);

        return {};
      });
      return response;
    },
    updateLast24hours(data) {
      var trends = this.getTrends(data);
      this.data.push({
        group: "last 24 Hours",
        Unsigned: trends.unsigned,
        Signed: trends.unApproved,
        Submitted: trends.approved
      });

      return;
    },
    upateLast7Days(data) {
      var trends = this.getTrends(data);
      this.data.push({
        group: "last 7 days",
        Unsigned: trends.unsigned,
        Signed: trends.unApproved,
        Submitted: trends.approved
      });
      return;
    },
    upateLast30Days(data) {
      var trends = this.getTrends(data);
      this.data.push({
        group: "last 30 days",
        Unsigned: trends.unsigned,
        Signed: trends.unApproved,
        Submitted: trends.approved
      });
      return;
    },
    upateLast90Days(data) {
      var trends = this.getTrends(data);
      this.data.push({
        group: "last 90 days",
        Unsigned: trends.unsigned,
        Signed: trends.unApproved,
        Submitted: trends.approved
      });
      return;
    },
    upateLast12Months(data) {
      var trends = this.getTrends(data);
      this.data.push({
        group: "last 12 Months",
        Unsigned: trends.unsigned,
        Signed: trends.unApproved,
        Submitted: trends.approved
      });
      return;
    },
    upateLifetime(data) {
      var trends = this.getTrends(data);
      this.data.push({
        group: "Lifetime",
        Unsigned: trends.unsigned,
        Signed: trends.unApproved,
        Submitted: trends.approved
      });
      return;
    },
    getTrends(data) {
      let UnsignedReports = data.filter(
        item =>
          item.isApproved === false &&
          item.isRejected === false &&
          item.approvalMetadata.signature &&
          item.approvalMetadata.signature.length === 0
      );

      let unapprovedReports = data.filter(
        item =>
          item.isApproved === false &&
          item.isRejected === false &&
          item.approvalMetadata.signature &&
          item.approvalMetadata.signature.length > 0
      );

      let approvedReports = data.filter(item => item.isApproved == true);

      return {
        unsigned: UnsignedReports.length,
        unApproved: unapprovedReports.length,
        approved: approvedReports.length
      };
    },
    redrawTrend(message) {
      
    },
    drawGraph(filter) {

         const tooltip = d3
        .select("#my_dataviz")
        .append("div")
        .style("opacity", 0)
        .attr("class", "tooltip")
        .style("background-color", "white")
        .style("border", "solid")
        .style("border-width", "1px")
        .style("border-radius", "5px")
        .style("padding", "10px");
      var containerWidth =
        document.querySelector("#my_dataviz").clientWidth - 2;
      var margin = { top: 10, right: 30, bottom: 20, left: 50 },
        width = 960 - margin.left - margin.right,
        height = 400 - margin.top - margin.bottom;

      var containerWidth =
        document.querySelector("#my_dataviz").clientWidth -
        margin.left -
        margin.right;

      // append the svg object to the body of the page
      var svg = d3
        .select("#my_dataviz")
        .append("svg")
        .attr("width", containerWidth)
        .attr("height", 400)
        .append("g")
        .attr("transform", "translate(" + margin.left + "," + margin.top + ")");
      globalData = this.getData;
      var data = this.getData;

      var subgroups = Object.keys(data[0]).slice(1);

      var groups = d3.map(data, function(d) {
        return d.group;
      });

      // Add X axis
      var x = d3
        .scaleBand()
        .domain(groups)
        .range([0, width])
        .padding([0.2]);

      svg
        .append("g")
        .attr("transform", "translate(0," + height + ")")
        .call(d3.axisBottom(x).tickSize(0));

      // Add Y axis
      var y = d3
        .scaleLinear()
        .domain([0, 40])
        .range([height, 0]);
      svg.append("g").call(d3.axisLeft(y));

      // Another scale for subgroup position?
      var xSubgroup = d3
        .scaleBand()
        .domain(subgroups)
        .range([0, x.bandwidth()])
        .padding([0.05]);

      // color palette = one color per subgroup
      var color = d3
        .scaleOrdinal()
        .domain(subgroups)
        .range(["#a82a2a", "#bf6c00", "#ff9000"]);

      // Show the bars
      svg
        .append("g")
        .selectAll("g")
        // Enter in data = loop group per group
        .data(data)
        .enter()
        .append("g")
        .attr("transform", function(d) {
          return "translate(" + x(d.group) + ",0)";
        })
        .selectAll("rect")
        .data(function(d) {
          return subgroups.map(function(key) {
            return { key: key, value: d[key] };
          });
        })
        .enter()
        .append("rect")
        .attr("x", function(d) {
          return xSubgroup(d.key);
        })
        .attr("y", function(d) {
          return y(d.value);
        })
        .attr("width", xSubgroup.bandwidth())
        .attr("height", function(d) {
          return height - y(d.value);
        })
        .attr("fill", function(d) {
          return color(d.key);
        })
        .on("mouseover", function(event, d) {
          const subgroupName = d.key;
          const subgroupValue = d.value;

         
          tooltip
            .html(
              "subgroup: " + subgroupName + "<br>" + "Value: " + subgroupValue
            )
            .style("opacity", 1);
        });

      var legend = svg
        .selectAll(".legend")
        .data(
          subgroups.map(function(d) {
            return d;
          })
        )
        .enter()
        .append("g")
        .attr("class", "legend")
        .attr("transform", function(d, i) {
          return "translate(0," + i * 20 + ")";
        })
        .style("opacity", "0");

      legend
        .append("rect")
        .attr("x", width - 18)
        .attr("width", 18)
        .attr("height", 18)
        .style("fill", function(d) {
          return color(d);
        });

      legend
        .append("text")
        .attr("x", width - 24)
        .attr("y", 9)
        .attr("dy", ".35em")
        .style("text-anchor", "end")
        .text(function(d) {
          return d;
        });

      legend
        .transition()
        .duration(500)
        .delay(function(d, i) {
          return 1300 + 100 * i;
        })
        .style("opacity", "1");
    
      var xf = crossfilter(data);
      var identifierDimension = xf.dimension(function(d) {
        return d.group;
      });
      

      new dc.SelectMenu("#identifier-c")
        .dimension(identifierDimension)
        .group(identifierDimension.group())

        .promptText("SELECT")
        .on("postRender", function() {
          d3.select(".dc-select-menu").attr(
            "class",
            "btn btn-outline-secondary border p-2 mr-4 text-left"
          );
        })
        .on("filtered", function(chart, filter) {
          
          document.querySelector("#my_dataviz").innerHTML = "";
          //##############################################################
          var containerWidth =
            document.querySelector("#my_dataviz").clientWidth - 2;
          var margin = { top: 10, right: 30, bottom: 20, left: 50 },
            width = 960 - margin.left - margin.right,
            height = 400 - margin.top - margin.bottom;

          var containerWidth =
            document.querySelector("#my_dataviz").clientWidth -
            margin.left -
            margin.right;

          // append the svg object to the body of the page
          var svg = d3
            .select("#my_dataviz")
            .append("svg")
            .attr("width", containerWidth)
            .attr("height", 400)
            .append("g")
            .attr(
              "transform",
              "translate(" + margin.left + "," + margin.top + ")"
            );
          
          var data = globalData.filter(d => d.group === filter);

          var subgroups = Object.keys(data[0]).slice(1);

          var groups = d3.map(data, function(d) {
            return d.group;
          });

          // Add X axis
          var x = d3
            .scaleBand()
            .domain(groups)
            .range([0, width])
            .padding([0.2]);

          svg
            .append("g")
            .attr("transform", "translate(0," + height + ")")
            .call(d3.axisBottom(x).tickSize(0));

          // Add Y axis
          var y = d3
            .scaleLinear()
            .domain([0, 40])
            .range([height, 0]);
          svg.append("g").call(d3.axisLeft(y));

          // Another scale for subgroup position?
          var xSubgroup = d3
            .scaleBand()
            .domain(subgroups)
            .range([0, x.bandwidth()])
            .padding([0.05]);

          // color palette = one color per subgroup
          var color = d3
            .scaleOrdinal()
            .domain(subgroups)
            .range(["#a82a2a", "#bf6c00", "#ff9000"]);

          // Show the bars
          svg
            .append("g")
            .selectAll("g")
            // Enter in data = loop group per group
            .data(data)
            .enter()
            .append("g")
            .attr("transform", function(d) {
              return "translate(" + x(d.group) + ",0)";
            })
            .selectAll("rect")
            .data(function(d) {
              return subgroups.map(function(key) {
                return { key: key, value: d[key] };
              });
            })
            .enter()
            .append("rect")
            .attr("x", function(d) {
              return xSubgroup(d.key);
            })
            .attr("y", function(d) {
              return y(d.value);
            })
            .attr("width", xSubgroup.bandwidth())
            .attr("height", function(d) {
              return height - y(d.value);
            })
            .attr("fill", function(d) {
              return color(d.key);
            });

          var legend = svg
            .selectAll(".legend")
            .data(
              subgroups.map(function(d) {
                return d;
              })
            )
            .enter()
            .append("g")
            .attr("class", "legend")
            .attr("transform", function(d, i) {
              return "translate(0," + i * 20 + ")";
            })
            .style("opacity", "0");

          legend
            .append("rect")
            .attr("x", width - 18)
            .attr("width", 18)
            .attr("height", 18)
            .style("fill", function(d) {
              return color(d);
            });

          legend
            .append("text")
            .attr("x", width - 24)
            .attr("y", 9)
            .attr("dy", ".35em")
            .style("text-anchor", "end")
            .text(function(d) {
              return d;
            });

          legend
            .transition()
            .duration(500)
            .delay(function(d, i) {
              return 1300 + 100 * i;
            })
            .style("opacity", "1");
          //######################################
        });

      dc.renderAll();
    }
  }
};
</script>

<style>
#trends {
  font-family: Arial, Helvetica, sans-serif;
  border-collapse: collapse;
  width: 90%;
  margin-left: 5%;
}

#trends td,
#trends th {
  border: 1px solid #ddd;
  padding: 8px;
}

#trends tr:nth-child(even) {
  background-color: #f2f2f2;
}

#trends tr:hover {
  background-color: #ddd;
}

#trends th {
  padding-top: 12px;
  padding-bottom: 12px;
  text-align: left;
  background-color: #04aa6d;
  color: white;
}
#identifier-c select {
  padding-top: 12px !important;
  padding-bottom: 12px !important;
}
</style>
