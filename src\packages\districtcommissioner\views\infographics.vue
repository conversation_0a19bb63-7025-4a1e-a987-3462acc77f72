<template>
  <div>
    <base-header class="pb-1" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0"></h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/districtcommissioner/dashboard">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">Reports</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">
                Info-graphics
              </li>
            </ol>
          </nav>
        </div>

        <div class="col-lg-6 col-6 text-right pr-5">
          <base-button size="sm" type="neutral">
            <span class="btn-inner--icon">
              <!--  <i class="ni ni-fat-add"></i> -->
            </span>
            <span class="btn-inner--text" @click="report()">Print</span>
          </base-button>
        </div>
      </div>
    </base-header>

    <div class="col-md-11" id="section-to-print" style="margin:auto">
      <tabs
        tabNavClasses="nav-fill flex-column flex-sm-row nav-wrapper"
        tabContentClasses="card shadow"
        :value="model"
        v-model="model"
      >
        <tabpane :id="1" title="1" :key="1">
          <span slot="title">
            <center>
              <span
                style="font-size:300%;"
                data-toggle="tooltip"
                data-placement="top"
                title="SHELTER"
                class="humanitarianicons-Shelter"
              ></span>
            </center>
          </span>

          <shelter
            :data="data"
            v-on:Dinr="getDinr"
            v-on:Dra="getDra"
            v-if="childDataLoaded"
          ></shelter>
        </tabpane>

        <tabpane :id="2" title="2" :key="2">
          <span slot="title">
            <center>
              <span
                style="font-size:300%;"
                data-toggle="tooltip"
                data-placement="top"
                title="DISPLACED"
                class="humanitarianicons-Internally-displaced"
              ></span>
            </center>
          </span>
          <displaced
            :data="data"
            v-on:Dinr="getDinr"
            v-on:Dra="getDra"
            v-if="childDataLoaded"
          ></displaced>
        </tabpane>

        <tabpane :id="3" title="3" :key="3">
          <span slot="title">
            <center>
              <span
                style="font-size:300%;"
                data-toggle="tooltip"
                data-placement="top"
                title="AGRICULTURE"
                class="humanitarianicons-Agriculture"
              ></span>
            </center>
          </span>
          <agriculture
            :data="data"
            v-on:Dinr="getDinr"
            v-on:Dra="getDra"
            v-if="childDataLoaded"
          ></agriculture>
        </tabpane>
        <tabpane :id="4" title="4" :key="4">
          <span slot="title">
            <center>
              <span
                style="font-size:300%;"
                data-toggle="tooltip"
                data-placement="top"
                title="WASH"
                class="humanitarianicons-Water-Sanitation-and-Hygiene"
              ></span>
            </center>
          </span>
          <wash
            :data="data"
            v-on:Dinr="getDinr"
            v-on:Dra="getDra"
            v-if="childDataLoaded"
          ></wash>
        </tabpane>
        <tabpane :id="5" title="5" :key="5">
          <span slot="title">
            <center>
              <span
                style="font-size:300%;"
                data-toggle="tooltip"
                data-placement="top"
                title="HEALTH"
                class="humanitarianicons-Health"
              ></span>
            </center>
          </span>
          <health
            :data="data"
            v-on:Dinr="getDinr"
            v-on:Dra="getDra"
            v-if="childDataLoaded"
          ></health>
        </tabpane>
        <tabpane :id="6" title="6" :key="6">
          <span slot="title">
            <center>
              <span
                style="font-size:300%;"
                data-toggle="tooltip"
                data-placement="top"
                title="FOOD"
                class="humanitarianicons-Food-Security"
              ></span>
            </center>
          </span>
          <food
            :data="data"
            v-on:Dinr="getDinr"
            v-on:Dra="getDra"
            v-if="childDataLoaded"
          ></food>
        </tabpane>

        <tabpane :id="8" title="8" :key="8">
          <span slot="title">
            <center>
              <span
                style="font-size:300%;"
                data-toggle="tooltip"
                data-placement="top"
                title="PROTECTION"
                class="humanitarianicons-Protection"
              ></span>
            </center>
          </span>
          <protection
            :data="data"
            v-on:Dinr="getDinr"
            v-on:Dra="getDra"
            v-if="childDataLoaded"
          ></protection>
        </tabpane>
        <tabpane :id="9" title="9" :key="9">
          <span slot="title">
            <center>
              <span
                style="font-size:300%;"
                data-toggle="tooltip"
                data-placement="top"
                title="NUTRITION"
                class="humanitarianicons-Nutrition"
              ></span>
            </center>
          </span>
          <nutrition
            :data="data"
            v-on:Dinr="getDinr"
            v-on:Dra="getDra"
            v-if="childDataLoaded"
          ></nutrition>
        </tabpane>
        <tabpane :id="10" title="10" :key="10">
          <span slot="title">
            <center>
              <span
                style="font-size:300%;"
                data-toggle="tooltip"
                data-placement="top"
                title="EDUCATION"
                class="humanitarianicons-Education"
              ></span>
            </center>
          </span>
          <education
            :data="data"
            v-on:Dinr="getDinr"
            v-on:Dra="getDra"
            v-if="childDataLoaded"
          ></education>
        </tabpane>
        <tabpane :id="11" title="11" :key="11">
          <span slot="title">
            <center>
              <span
                style="font-size:300%;"
                data-toggle="tooltip"
                data-placement="top"
                title="LIVELIHOODS"
                class="humanitarianicons-Livelihood"
              ></span>
            </center>
          </span>
          <livehoods
            :data="data"
            v-on:Dinr="getDinr"
            v-on:Dra="getDra"
            v-if="childDataLoaded"
          ></livehoods>
        </tabpane>
      </tabs>
    </div>
  </div>
</template>
<script>
import tabs from "../../../components/Tabs/Tabs";
import tabpane from "../../../components/Tabs/Tab";
import shelter from "../views/infographics/shelter";
import displaced from "../views/infographics/displaced";
import agriculture from "../views/infographics/agriculture";
import food from "../views/infographics/food";
import wash from "../views/infographics/wash";
import health from "../views/infographics/health";
import protection from "../views/infographics/protection";
import nutrition from "../views/infographics/nutrition";
import education from "../views/infographics/education";
import livehoods from "../views/infographics/livelihood";

import { Reports } from "../api/reports";

import Swal from "sweetalert2";

import $ from "jquery";

import html2canvas from "html2canvas";

export default {
  components: {
    tabs,
    tabpane,
    shelter,
    displaced,
    agriculture,
    food,
    wash,
    health,
    education,
    protection,
    livehoods,
    nutrition
  },

  data() {
    return {
      childDataLoaded: false,
      data: {},
      TAsData: [],
      uuid: null,
      tabIndex: 1,
      district: "",
      dialog: false,
      notifications: false,
      isVisible: true,
      sound: true,
      widgets: false,
      i: 0, // set i to track the tab index
      model: "1",
      Ta: "",
      step: 1,
      e13: 1,
      max: 50000,
      value: 0,
      accessToken: null,
      malecounter: 0,
      femalecounter: 0,
      sector: {}
    };
  },

  computed: {},

  methods: {
    async makeScreenshot(selector = "body") {
      return new Promise((resolve, reject) => {
        let node = document.querySelector(selector);

        html2canvas(node, {
          onrendered: canvas => {
            let pngUrl = canvas.toDataURL();
            resolve(pngUrl);
          }
        });
      });
    },

    async report() {
      let screenshot = await this.makeScreenshot("#section-to-print"); // png dataUrl
      let img = document.querySelector("#section-to-print");
      img.src = screenshot;
     // console.log(img);
      // ... show screenshot to user, allow to draw region
      // and send screnshot with region coordinates to server
    },
    getDinr(dinrId) {
      this.childDataLoaded = false;
      Reports.getDinrReport(dinrId).then(response => {
        this.data = null;
        this.data = response.data.data;
        var TAs = this.data.TA.split(",");
        var IDs = this.data.oid.split(",");
        this.data.TAList = [];
        this.data.TAname = this.data.TA;
        this.data.TAList.push({ label: "All", value: this.data.dinr });
        for (let i = 0; i < TAs.length; i++) {
          this.data.TAList.push({ label: TAs[i], value: IDs[i] });
        }
        this.childDataLoaded = true;
      });
    },
    getDra(draId) {
      //this.data = null;
      this.childDataLoaded = false;
      Reports.getOneReport(draId).then(response => {
        this.data = response.data;
        this.data.TAList = this.TAsData;
        this.data.TAname = this.data.TA;
        this.childDataLoaded = true;
        //console.log(this.data);
      });
    },
    printdiv(printpage) {
      var headstr = "<html><head><title></title></head><body>";
      var footstr = "</body>";
      var newstr = document.all.item(printpage).innerHTML;
      var oldstr = document.body.innerHTML;
      document.body.innerHTML = headstr + newstr + footstr;
      window.print();
      document.body.innerHTML = oldstr;
      location.reload();
      return false;
    },
    downloadImage(printpage) {
      var self = this;
      htmlToImage
        .toJpeg(window.document.getElementById(printpage), {
          backgroundColor: "white"
        })
        .then(function(dataUrl) {
          // console.log(self.loan.loan.type);
          var link = document.createElement("a");
          link.download = "dmis_dashboard" + this.$route.params.uuid;
          link.target = "_blank";

          // Construct the URI
          link.href = dataUrl;
          document.body.appendChild(link);
          link.click();

          // Cleanup the DOM
          document.body.removeChild(link);
          //this.download(dataUrl, "my-node.png");
        });
    }
  },
  created() {
    //console.log(this.$route.params.uuid);
    Reports.getDinrReport(this.$route.params.uuid).then(response => {
      // console.log(response.data.data);
      this.data = response.data.data;
      var TAs = this.data.TA.split(",");
      var IDs = this.data.oid.split(",");
      this.data.TAname = "All";
      this.data.TAList = [];
      this.data.TAList.push({ label: "All", value: this.data.dinr });
      for (let i = 0; i < TAs.length; i++) {
        this.data.TAList.push({ label: TAs[i], value: IDs[i] });
      }
      this.TAsData = this.data.TAList;
      this.childDataLoaded = true;
      //console.log(this.data);
    });

    $(function() {});
  }
};
</script>

<style scoped>
.alert-suc {
  color: #006666;
}
@media print {
  .section-not-to-print {
    visibility: hidden;
  }

  #section-to-print {
    visibility: visible;
  }

  .tab-pane {
    display: block;
  }
}

@import "../../../assets/style.css";
@import "../../../assets/ie7/ie7.css";
</style>
