<template>
  <div
    ref="printMap"
    style="background: white; padding-left: 1rem; padding-right: 1rem"
    class="container-fluid"
  >
    <div class="row">
      <div class="col-12 p-1">
        <li class="list-group-item">
          <div class="row">
            <div class="col-8 row text-left">
              <div class="col-4 mx-0 mt-0 datarange">
                <span> START DATE </span>
                <span>
                  <base-input addon-left-icon="ni ni-calendar-grid-58">
                    <flat-picker
                      class="form-control datepicker bg-white"
                      :config="{ dateFormat: 'd-m-Y' }"
                      v-model="dates.start"
                    >
                    </flat-picker>
                  </base-input>
                </span>
              </div>
              <div class="col-4 mx-0 mt-0 datarange">
                <span> END DATE </span>
                <span>
                  <base-input addon-left-icon="ni ni-calendar-grid-58">
                    <flat-picker
                      class="form-control datepicker bg-white"
                      :config="{ dateFormat: 'd-m-Y' }"
                      v-model="dates.end"
                    >
                    </flat-picker>
                  </base-input>
                </span>
              </div>
              <div class="col-4 mx-0 mt-2 my-auto">
                <base-button
                  @click="filterByDate"
                  style="background: #a8a8a8; border: #dbddde"
                >
                  FILTER
                </base-button>
              </div>
            </div>
            <div class="text-right col-4 mx-0 px-0 mt-4 exports">
              <base-button
                type="warning"
                class="reset mx-1 py-2"
                title-classes="btn btn-sm btn-primary mX-0 py-2"
                id="resetall"
              >
                Reset all
              </base-button>

              <base-dropdown
                menu-on-right
                type="primary"
                title-classes="btn btn-xl btn-primary pr-2 pl-2 mr-0 py-2"
                :has-toggle="false"
              >
                <a
                  slot="title"
                  class="dropdown-toggle"
                  type="neutral"
                  size="md"
                >
                  <i class="text-white ni ni-cloud-download-95"></i> EXPORTS
                </a>

                <a class="dropdown-item" @click="printImage()">Screenshot</a>
                <a class="dropdown-item" @click="exportToPDF">PDF</a>
              </base-dropdown>
            </div>
          </div>

          <div class="row mt-3 mb-0 pb-0" style="border-top: 1px solid #e2ded0">
            <div class="col-3 mx-0 mt-4 pull-left exports">
              <span>DISTRICT DISASTER DATE</span>
              <span id="identifier" style="width: 100px !important"></span>
            </div>
            <!-- <div class="col-12"> -->
            <!-- <span id="identifier"></span> -->

            <!-- <select
              class="btn btn-outline-secondary border p-2 text-left date-filter-buttons-container"
            >
              <option selected>DATE CATEGORIES [Created on]</option>
            </select>
            <span
              style="
                color: black !important;
                font-size: 18pt;
                font-weight: bold;
              "
              class="mx-1"
            >
              <span id="start"></span> -
              <span id="end"></span>
            </span>-->
            <!-- </div> -->
          </div>
        </li>
      </div>
      <div class="row">
        <div class="col-2">
          <ul class="list-group">
            <li
              class="
                list-group-item
                d-flex
                justify-content-center
                align-items-center
                text-center
                m-1
              "
              style="border: 1px solid #bf6c00 !important; height: 40px"
            >
              <span
                style="color: black !important"
                class="text-uppercase font-weight-bold col-title"
                >DISASTERS</span
              >
            </li>
            <div
              :style="{ display: disaster.hasValues ? 'block' : 'none' }"
              class="disasters"
              v-for="disaster in diastersData"
              :id="disaster.id + '$'"
              @click="filterPage(disaster.id + '$', disaster.name)"
              :key="disaster.id"
            >
              <li
                class="
                  list-group-item
                  disaster-impact-stats
                  d-flex
                  justify-content-between
                  align-items-center
                  m-1
                  border-top
                "
              >
                <summary class="row text-center font-weight-bold">
                  <div class="col-3">
                    <span>
                      <i
                        style="font-size: 26pt !important"
                        :class="disaster.icon"
                      ></i>
                    </span>
                  </div>
                  <div class="col-9">
                    <span>
                      <h5 class="card-title text-white text-uppercase">
                        {{ disaster.name }}
                      </h5>
                    </span>
                  </div>
                  <div class="col-12">
                    <h1
                      class="display-4"
                      style="font-size: 30pt !important"
                      :id="disaster.id"
                    ></h1>
                  </div>
                </summary>
              </li>
            </div>
          </ul>
        </div>
        <div class="col-8">
          <ul class="list-group">
            <li
              style="border: 1px solid #ffd5ad !important; height: 40px"
              class="
                list-group-item
                d-flex
                justify-content-between
                align-items-center
                m-1
              "
            >
              <span
                style="color: black !important"
                class="font-weight-bold text-uppercase col-title"
                id="header-map-date"
              >
               Disasters per T/A  (
                <span id="map-start-year"></span> -
                <span id="map-end-year"></span> )
              </span>
              <span>Filter by clicking each region</span>
            </li>
            <li
              class="
                list-group-item
                d-flex
                justify-content-between
                align-items-center
                m-1
              "
            >
              <div style="display: block; margin: auto" id="mapChartId"></div>
         
            </li>
          </ul>
        </div>
        <div class="col-2">
          <ul class="list-group text-white">
            <li
              class="
                list-group-item
                d-flex
                justify-content-center
                align-items-center
                m-1
              "
              style="border: 1px solid #ffd5ad !important; height: 40px"
            >
              <span
                style="color: black !important"
                class="text-uppercase font-weight-bold text-center"
              >
                <b style="color: #37c2a3">{{
                  filterValue == "DISASTERS" ? "Impact" : filterValue
                }}</b>
              </span>
            </li>
            <li
              class="
                list-group-item
                impact-stats
                d-flex
                justify-content-between
                align-items-center
                m-1
                border-top
              "
            >
              <div class="row text-center font-weight-bold">
                <div class="col-12">
                  <span>
                    <h5 class="card-title text-white text-uppercase col-title">
                      HH affected [Shelter]
                    </h5>
                  </span>
                </div>
                <div class="col-12">
                  <span>
                    <i
                      class="stats-icon huma huma-house-affected m-0 p-0"
                      style="font-size: 25pt !important"
                    ></i>
                  </span>
                </div>
                <div class="col-12">
                  <span>
                    <h1
                      class="display-4 mb-0 pb-0"
                      style="margin-bottom: -2% !important"
                      id="shelter_hh"
                    ></h1>
                  </span>
                </div>
                <div class="col-6 border-right">
                  <span>
                    <i
                      class="stats-icon huma huma-person-1"
                      style="font-size: 22pt !important"
                    ></i>
                  </span>
                </div>
                <div class="col-6 border-left">
                  <span>
                    <i
                      class="stats-icon huma huma-person-2"
                      style="font-size: 22pt !important"
                    ></i>
                  </span>
                </div>
                <div class="col-6 border-right font-weight-bold">
                  <span id="shelter_fhh"></span>
                </div>
                <div class="col-6 border-left font-weight-bold">
                  <span id="shelter_mhh"></span>
                </div>
              </div>
            </li>
            <li
              class="
                list-group-item
                impact-stats
                d-flex
                justify-content-between
                align-items-center
                m-1
                border-top
              "
            >
              <div class="row text-center font-weight-bold">
                <div class="col-12">
                  <span>
                    <h5 class="card-title text-white text-uppercase">
                      HH affected [Displaced]
                    </h5>
                  </span>
                </div>
                <div class="col-12">
                  <span>
                    <i
                      class="stats-icon huma huma-house-destroyed"
                      style="font-size: 25pt !important"
                    ></i>
                  </span>
                </div>
                <div class="col-12">
                  <span>
                    <h1
                      class="display-4"
                      style="margin-bottom: -2% !important"
                      id="displaced_hh"
                    ></h1>
                  </span>
                </div>
                <div class="col-6 border-right">
                  <span>
                    <i
                      class="stats-icon huma huma-person-1"
                      style="font-size: 22pt !important"
                    ></i>
                  </span>
                </div>
                <div class="col-6 border-left">
                  <span>
                    <i
                      class="stats-icon huma huma-person-2"
                      style="font-size: 22pt !important"
                    ></i>
                  </span>
                </div>
                <div class="col-6 border-right font-weight-bold">
                  <span id="displaced_fhh"></span>
                </div>
                <div class="col-6 border-left font-weight-bold">
                  <span id="displaced_mhh"></span>
                </div>
              </div>
            </li>
            <!--    <li
          class="list-group-item impact-stats d-flex justify-content-between align-items-center m-1 border-top"
        >
          <summary class="row text-center font-weight-bold">
            <div class="col-12">
              <span>
                <h5 class="card-title text-white text-uppercase">Missing</h5>
              </span>
            </div>
            <div class="col-12">
              <span>
                <i class="fas stats-icon fa-home fa-lg"></i>
              </span>
            </div>
            <div class="col-12">
              <span>
                <h1 class="display-4">50,000</h1>
              </span>
            </div>
            <div class="col-6 border-right">
              <span>
                <i class="fas stats-icon fa-home fa-sm"></i>
              </span>
            </div>
            <div class="col-6 border-left">
              <span>
                <i class="fas stats-icon fa-home fa-sm"></i>
              </span>
            </div>
            <div class="col-6 border-right font-weight-bold">
              <span>70</span>
            </div>
            <div class="col-6 border-left font-weight-bold">
              <span>40</span>
            </div>
          </summary>
          </li>-->
            <li
              class="
                list-group-item
                impact-stats
                d-flex
                justify-content-between
                align-items-center
                m-1
                border-top
              "
            >
              <div class="row text-center font-weight-bold">
                <div class="col-12">
                  <span>
                    <h5 class="card-title text-white text-uppercase">
                      Injuries
                    </h5>
                  </span>
                </div>
                <div class="col-12">
                  <span>
                    <i
                      class="stats-icon huma huma-injured"
                      style="font-size: 26pt !important"
                    ></i>
                  </span>
                </div>
                <div class="col-12">
                  <span>
                    <h1
                      class="display-4"
                      style="margin-bottom: -2% !important"
                      id="injured"
                    ></h1>
                  </span>
                </div>
                <div class="col-6 border-right">
                  <span>
                    <i
                      class="stats-icon huma huma-person-1"
                      style="font-size: 22pt !important"
                    ></i>
                  </span>
                </div>
                <div class="col-6 border-left">
                  <span>
                    <i
                      class="stats-icon huma huma-person-2"
                      style="font-size: 22pt !important"
                    ></i>
                  </span>
                </div>
                <div class="col-6 border-right font-weight-bold">
                  <span id="injured_females"></span>
                </div>
                <div class="col-6 border-left font-weight-bold">
                  <span id="injured_males"></span>
                </div>
              </div>
            </li>
            <li
              class="
                list-group-item
                impact-stats
                d-flex
                justify-content-between
                align-items-center
                m-1
                border-top
              "
            >
              <div class="row text-center font-weight-bold">
                <div class="col-12">
                  <span>
                    <h5 class="card-title text-white text-uppercase">Deaths</h5>
                  </span>
                </div>
                <div class="col-12">
                  <span>
                    <i
                      class="stats-icon huma huma-dead"
                      style="font-size: 30pt !important"
                    ></i>
                  </span>
                </div>
                <div class="col-12">
                  <span>
                    <h1
                      class="display-4"
                      style="margin-bottom: -2% !important"
                      id="dead"
                    ></h1>
                  </span>
                </div>
                <div class="col-6 border-right">
                  <span>
                    <i
                      class="stats-icon huma huma-person-1"
                      style="font-size: 22pt !important"
                    ></i>
                  </span>
                </div>
                <div class="col-6 border-left">
                  <span>
                    <i
                      class="stats-icon huma huma-person-2"
                      style="font-size: 22pt !important"
                    ></i>
                  </span>
                </div>
                <div class="col-6 border-right font-weight-bold">
                  <span id="dead_females"></span>
                </div>
                <div class="col-6 border-left font-weight-bold">
                  <span id="dead_males"></span>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </div>
      <div class="col-12">
        <div class="col-8">
          <h4 style="color:font-weight:bold">
            <br />
            <h3 class="font-weight-light text-justify m-0">
              Please note that boundaries and names shown and the designations
              used on this map do not imply official endorsement or acceptance
              by the United Nations.
            </h3>
            <br />

            <h3>
              <b>KEY</b>
            </h3>

            <b>HH = Household</b>
            <br />
            <i class="huma huma-person-2" style="font-size: 20pt !important"></i
            >= <b>Male or Male headed household</b> depending on where its
            placed
            <br />
            <i
              class="huma huma-person-1"
              style="font-size: 20pt !important"
            ></i>
            =
            <b>Female or Female headed</b> household depending where its placed
            <br />
            <br />
            <b class="text-warning">
              For more details of the icons used on this page please go to
              <a href="https://www.unocha.org/story/iconography-part-un%E2%80%99s-humanitarian-efforts-ocha-releases-new-humanitarian-icons" target="_blank">UN OCHA Website</a>
            </b>
          </h4>
        </div>
        <div
          class="col-4 text-right"
          style="float: right !important; margin-top: -6%"
        >
          <img
            src="../../../assets/logo.png"
            style="width: 80px"
            alt="Malawi govt Logo"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as d3 from "d3";
import * as dc from "dc";
import crossfilter from "crossfilter2";
import moment from "moment";
var $ = require("jquery");
import flatPicker from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import { buildMap } from "./helpers/map.js";
import utils from "../../../util/dashboard";
import reductio from "reductio";
import jspdf from "jspdf";
import domtoimage from "dom-to-image";
//const malawiAdmin3GeoJson = require("./../../../data/malawi.features");

//for filtering trick
var dcFilterHandler = {};
var dcDateFilterHandler = {};

export default {
  components: { flatPicker },
  props: ["flatData"],
  data() {
    return {
      latlng: [-13.3, 34.8],
      fullrange: "",
      dates: { start: "", end: "" },
      zoom: 7,
      disasters: [
        {
          name: "Floods",
          id: "floods",
          hasValues: true,
          flag: 1,
          icon: "huma huma-flash-flood",
        },
        {
          name: "Heavy rains",
          id: "heavy",
          hasValues: true,
          flag: 1,
          icon: "huma huma-heavy-rain",
        },
        {
          name: "Stormy rains",
          id: "stormy",
          hasValues: true,
          flag: 1,
          icon: "huma huma-storm",
        },
        {
          name: "Strong winds (with no rains)",
          id: "winds",
          hasValues: true,
          icon: "huma huma-violent-wind",
          flag: 1,
        },
        {
          name: "Hailstorm",
          id: "hailstorm",
          icon: "huma huma-storm-surge",
          hasValues: true,
          flag: 1,
        },
        {
          name: "Water pollution",
          icon: "huma huma-spring-water",
          id: "water",
          hasValues: true,
          flag: 1,
        },
        {
          name: "Droughts",
          id: "drought",
          hasValues: true,
          flag: 1,
          icon: "huma huma-drought",
        },
        {
          name: "Earthquake",
          id: "earthquake",
          icon: "huma huma-earthquake",
          hasValues: true,
          flag: 1,
        },
        {
          name: "Disease and Pest outbreak",
          id: "disease",
          icon: "huma huma-insect-infestation",
          hasValues: true,
          flag: 1,
        },
        {
          name: "Lightening",
          id: "lightning",
          icon: "huma huma-storm",
          hasValues: true,
          flag: 1,
        },
        {
          name: "Extreme temperatures",
          id: "temperatures",
          hasValues: true,
          flag: 1,
          icon: "huma huma-cold-wave",
          value: 0,
        },
        {
          name: "Severe storms",
          icon: "huma huma-cyclone",
          id: "severestorm",
          hasValues: true,
          flag: 1,
        },
        {
          name: "Landslides",
          icon: "huma huma-landslide-mudslide",
          id: "land",
          hasValues: true,
          flag: 1,
        },
        {
          name: "Fire",
          icon: "huma huma-fire",
          id: "fire",
          hasValues: true,
          flag: 1,
        },
        {
          name: "Epidemic",
          icon: "huma huma-epidemic",
          id: "epidemic",
          hasValues: true,
          flag: 1,
        },
        {
          name: "Explosion",
          icon: "huma huma-volcano",
          id: "explosion",
          hasValues: true,
          flag: 1,
        },
        {
          name: "Human-animal conflicts",
          id: "human",
          hasValues: true,
          icon: "huma huma-livestock",
          flag: 1,
          value: 0,
        },
        {
          name: "Accident",
          icon: "huma huma-injured",
          id: "accident",
          hasValues: true,
          flag: 1,
        },
      ],
      dateRangeMethods: {
        weeks: this.weeksRange,
        months: this.monthsRange,
        years: this.yearsRange,
      },
      dateCategories: {
        "This Week": {
          type: "weeks",
          number: 0,
        },
        "Last Week": {
          type: "weeks",
          number: 6,
        },
        "This Month": {
          type: "months",
          number: 0,
        },
        "Last Month": {
          type: "months",
          number: 1,
        },
        "Last 2 Months": {
          type: "months",
          number: 2,
        },
        "Last 3 Months": {
          type: "months",
          number: 3,
        },
        "Last 6 Months": {
          type: "months",
          number: 6,
        },
        "This Year": {
          type: "years",
          number: 0,
        },
        "Last Year": {
          type: "years",
          number: 1,
        },
        "Last 2 Years": {
          type: "years",
          number: 2,
        },
        "Last 3 Years": {
          type: "years",
          number: 3,
        },
        "Last 5 Years": {
          type: "years",
          number: 5,
        },
        "Last 10 Years": {
          type: "years",
          number: 10,
        },
      },
      checker: "",
      counter: 0,
      checker2: 0,
      groupname: "choropleth",
      filterValue: "DISASTERS",
    };
  },
  computed: {
    diastersData: function () {
      return this.disasters.sort(
        (a, b) => parseFloat(b.value | 0) - parseFloat(a.value | 0)
      );
    },
  },
  methods: {
    filterByDate() {
      let date_end = new Date(
        moment(this.dates.end, "DD-MM-YYYY").format("YYYY-MM-DD")
      );
      let date_start = new Date(
        moment(this.dates.start, "DD-MM-YYYY").format("YYYY-MM-DD")
      );
      if (
        this.dates.start &&
        this.dates.end &&
        this.dates.start < this.dates.end
      ) {
        dcDateFilterHandler.filter(null);
        dcDateFilterHandler.filter([date_start, date_end]);
        dc.redrawAll(this.groupname);
      } else {
        alert("Date range should be filled in valid range");
      }
    },
    resetAll() {
      //chart.filterAll();
      dcDateFilterHandler.filter(null);
      checkboxDimesions.filter(null);
      this.dates.range = this.fullrange;
      //chart.filterAll(group);
      dc.redrawAll(this.groupname);
    },
    getScreenSize() {
      let width = window.screen.width;
      //let width = window.screen.width;
      let height = window.screen.height;
      return [width, height];
    },
    ChangedDateRange() {
      dcDateFilterHandler.filter(null);
      dcDateFilterHandler.filter(null);

      var ranges = this.$refs.overviewDateRange
        .getElementsByClassName("datepicker")[0]
        .value.split("to");

      // var dateFrom =
      //   refs.datefrom.value === "" ? bottomDate : refs.datefrom.value;
      dcDateFilterHandler.filter([new Date(ranges[0]), new Date(ranges[1])]);
      dc.redrawAll(this.groupname);
    },
    getChartWidth(percentage, screenWidth) {
      return screenWidth * percentage;
    },

    calculateChartWidth(percentage) {
      let screenSize = this.getScreenSize()[0];
      return this.getChartWidth(percentage, screenSize) / 2;
    },
    async printImage() {
      let exportsButtons = document.getElementsByClassName("exports");
      let buttons = document.getElementsByTagName("button");
      let resetButtons = document.querySelectorAll('[type="reset"]');
      let dateRange = document.getElementsByClassName("datarange");
      let x = this.dates.start;
      let y = this.dates.end;
      if (x == "" || y == "") {
        dateRange = document.getElementsByClassName("datarange");

        await utils.Visibility.hide([
          ...exportsButtons,
          ...buttons,
          ...resetButtons,
          ...dateRange,
        ]);
      } else {
        await utils.Visibility.hide([
          ...exportsButtons,
          ...buttons,
          ...resetButtons,
        ]);
      }

      let dataUrl = await utils.Screenshot.take(this.$refs.printMap);
      utils.download(dataUrl);
      utils.Visibility.show([
        ...dateRange,
        ...exportsButtons,
        ...buttons,
        ...resetButtons,
      ]);
    },

    async exportToPDF() {
      let exportsButtons = document.getElementsByClassName("exports");
      let buttons = document.getElementsByTagName("button");
      let resetButtons = document.querySelectorAll('[type="reset"]');
      let dateRange = document.getElementsByClassName("datarange");
      let x = this.dates.start;
      let y = this.dates.end;
      if (x == "" || y == "") {
        dateRange = document.getElementsByClassName("datarange");

        await utils.Visibility.hide([
          ...exportsButtons,
          ...buttons,
          ...resetButtons,
          ...dateRange,
        ]);
      } else {
        await utils.Visibility.hide([
          ...exportsButtons,
          ...buttons,
          ...resetButtons,
        ]);
      }

      domtoimage.toPng(await this.$refs.printMap).then(function (dataUrl) {
        var img = new Image();

        img.src = dataUrl;
        const doc = new jspdf({
          orientation: "portrait",
        });

        var width = doc.internal.pageSize.getWidth();
        var height = doc.internal.pageSize.getHeight();

        doc.addImage(img, "JPEG", 0, 0, width, height);
        const date = new Date();
        const filename =
          "DRMIS_dashboard" +
          date.getFullYear() +
          ("0" + (date.getMonth() + 1)).slice(-2) +
          ("0" + date.getDate()).slice(-2) +
          ("0" + date.getHours()).slice(-2) +
          ("0" + date.getMinutes()).slice(-2) +
          ("0" + date.getSeconds()).slice(-2) +
          ".pdf";
        doc.save(filename);

        utils.Visibility.show([
          ...dateRange,
          ...exportsButtons,
          ...buttons,
          ...resetButtons,
        ]);
      });
    },

    fake_groupAll(dim, field, method) {
      return {
        value: function () {
          //get year
          return dim[method](1)[0]
            ? dim[method](1)[0][field].getFullYear()
            : "";
        },
      };
    },
    getColor(d) {
      return d > 80
        ? "#bf6c00"
        : d > 60
        ? "#cb7e2d"
        : d > 40
        ? "#d68f4c"
        : d > 20
        ? "#e0a169"
        : d > 10
        ? "#e9b386"
        : d > 5
        ? "#f0c6a3"
        : d > 0
        ? "#f7d9c1"
        : "#f2efe9";
    },
    weeksRange(number) {
      var now = moment(this.fixed_now());
      var mon = now.startOf("week").add(1, "days");
      if (number != 0) mon = now.startOf("week").subtract(6, "days");
      var startMoment = mon.format("YYYY-MM-DD");
      var endMoment = mon.add(6, "days").format("YYYY-MM-DD");
      this.displayDates(startMoment, endMoment);
    },

    monthsRange(number) {
      var firstDate = moment(this.fixed_now()).startOf("month");
      var endDate = moment(this.fixed_now()).endOf("month");
      if (number > 0) {
        firstDate = moment(this.fixed_now())
          .startOf("month")
          .subtract("months", number);
        endDate = moment(this.fixed_now()).endOf("month").subtract(1, "months");
      }
      var startMoment = firstDate.format("YYYY-MM-DD");
      var endMoment = endDate.format("YYYY-MM-DD");
      this.displayDates(startMoment, endMoment);
    },

    yearsRange(number) {
      var firstDate = moment(this.fixed_now()).startOf("year");
      var endDate = moment(this.fixed_now()).endOf("year");
      if (number > 0) {
        firstDate = moment(this.fixed_now())
          .startOf("year")
          .subtract("years", number);
        endDate = moment(this.fixed_now()).endOf("year").subtract(1, "years");
      }
      var startMoment = firstDate.format("YYYY-MM-DD");
      var endMoment = endDate.format("YYYY-MM-DD");
      this.displayDates(startMoment, endMoment);
    },
    fixed_now() {
      return new Date();
    },
    displayDates(start, end) {
      $("#start").text(moment(start).format("DD/MM/YYYY"));
      $("#end").text(moment(end).format("DD/MM/YYYY"));
    },
    filterCharts(filterValue, id) {
      if (this.checker == id && this.checker2 == 1) {
        this.filterValue = "DISASTERS";
        dcFilterHandler.filterAll(this.groupname);
        this.checker2 = 0;
      } else {
        dcFilterHandler.filterFunction(this.multivalue_filter([filterValue]));
        this.checker2 = 1;
        if (this.checker != id) this.checker2 = 0;
        this.filterValue = filterValue;
      }
      dc.redrawAll(this.groupname);
    },
    setResetChart(id, chart, groupname) {
      d3.select(`#${id}`).on("click", function () {
        chart.filterAll();
        if (id == "resetall") {
          chart.filterAll(groupname);
        }
        dcDateFilterHandler.filter(null);
        dcFilterHandler.filter(null);
        dc.redrawAll(groupname);
      });
    },
    multivalue_filter(values) {
      return function (v) {
        return values.indexOf(v) !== -1;
      };
    },
    toggleBGColor(id) {
      if (this.checker2 == 0 || this.checker != id) {
        this.disasters
          .filter((i) => i.hasValues)
          .forEach((item) => {
            document.getElementById(item.id + "@").style.backgroundColor =
              "white";
          });
      }

      let el = document.getElementById(id);
      el.style.backgroundColor =
        el.style.backgroundColor == "white" ? "#37c2a3" : "white";
      this.checker = id;
      this.counter++;
    },
    async dcCharts(data) {
      let self = this;

      var xf = crossfilter(data);

      var numberFormat = d3.format(",");

      //for filtering trick
      dcFilterHandler = xf.dimension(function (d) {
        return d.disaster;
      });

       //map
      var taDimension = xf.dimension(function (d) {
        
        return d.ta;
      });

      var taGroup = taDimension.group();

    

      var reducer = reductio()
        .exception(function (d) {
          
          return d.disasterId;
        })
        .exceptionCount(true);
        

      var numberHHDimension = xf.dimension(function (d) {
        return d;
      });
      var shelterHHGroup = numberHHDimension.group().reduceSum((item) => {
        return (
          (+item.without_shelter_fhh || 0) + (+item.without_shelter_mhh || 0)
        );
      });

      var identifierDimension = xf.dimension(function (d) {
        return d.identifier;
      });

      new dc.SelectMenu("#identifier", this.groupname)
        .dimension(identifierDimension)
        .group(identifierDimension.group())
        .promptText("SELECT")
        .on("postRender", function () {
          d3.select(".dc-select-menu").attr(
            "class",
            "btn btn-outline-secondary border p-2 mr-4 text-left"
          );
        });

      var dropdowns = d3
        .select(".date-filter-buttons-container")
        .selectAll("option")
        .data([
          "This Week",
          "Last Week",
          "This Month",
          "Last Month",
          "Last 2 Months",
          "Last 3 Months",
          "Last 6 Months",
          "This Year",
          "Last Year",
          "Last 2 Years",
          "Last 3 Years",
          "Last 5 Years",
          "Last 10 Years",
        ]);

      dropdowns = dropdowns
        .enter()
        .append("option")
        .attr("class", "dc-select-option");

      // fill the buttons with the year from the data assigned to them
      dropdowns.each(function (d) {
        this.innerHTML = d;
      });

      dropdowns.on("click", dropdownsHandler);

      dcDateFilterHandler = xf.dimension(function (d) {
        return d.created_on;
      });

      function dropdownsHandler() {
        // our year will this.innerText

        let dateCategoryProperties = self.dateCategories[this.innerText];
        dateRangeHandler(
          dateCategoryProperties.type,
          dateCategoryProperties.number
        );

        function dateRangeHandler(type, number) {
          self.dateRangeMethods[type](number);
        }

        let startDateArray = $("#start").text().split("/");
        let endDateArray = $("#end").text().split("/");
        var start =
          startDateArray[2] + "/" + startDateArray[1] + "/" + startDateArray[0];
        var end =
          endDateArray[2] + "/" + endDateArray[1] + "/" + endDateArray[0];

        dcDateFilterHandler.filter(null);
        dcDateFilterHandler.filter([new Date(start), new Date(end)]);
        dcFilterHandler.filterAll(this.groupname);
        dc.redrawAll(self.groupname);
      }

      var shelterFHHGroup = numberHHDimension
        .group()
        .reduceSum((item) => +item.without_shelter_fhh, 0);

      var shelterMHHGroup = numberHHDimension
        .group()
        .reduceSum((item) => +item.without_shelter_mhh, 0);

      var displacedHHGroup = numberHHDimension
        .group()
        .reduceSum(
          (item) => (+item.displaced_fhh || 0) + (+item.displaced_mhh || 0),
          0
        );

      var displacedFHHGroup = numberHHDimension
        .group()
        .reduceSum((item) => +item.displaced_fhh, 0);

      var displacedMHHGroup = numberHHDimension
        .group()
        .reduceSum((item) => +item.displaced_mhh, 0);

      var injuredGroup = numberHHDimension
        .group()
        .reduceSum(
          (item) => (item.injured_females || 0) + (item.injured_males || 0),
          0
        );

      var injuredFemalesGroup = numberHHDimension
        .group()
        .reduceSum((item) => +item.injured_females, 0);

      var injuredMalesGroup = numberHHDimension
        .group()
        .reduceSum((item) => +item.injured_males, 0);

      var deadGroup = numberHHDimension
        .group()
        .reduceSum(
          (item) => (item.dead_females || 0) + (item.dead_males || 0),
          0
        );

      var deadFemalesGroup = numberHHDimension
        .group()
        .reduceSum((item) => +item.dead_females, 0);

      var deadMalesGroup = numberHHDimension
        .group()
        .reduceSum((item) => +item.dead_males, 0);

      dc.numberDisplay("#shelter_hh", self.groupname)
        .group(shelterHHGroup)
        .formatNumber(numberFormat);

      dc.numberDisplay("#shelter_mhh", self.groupname)
        .group(shelterMHHGroup)
        .formatNumber(numberFormat);

      dc.numberDisplay("#shelter_fhh", self.groupname)
        .group(shelterFHHGroup)
        .formatNumber(numberFormat);

      dc.numberDisplay("#displaced_hh", self.groupname)
        .group(displacedHHGroup)
        .formatNumber(numberFormat);

      dc.numberDisplay("#displaced_mhh", self.groupname)
        .group(displacedMHHGroup)
        .formatNumber(numberFormat);

      dc.numberDisplay("#displaced_fhh", self.groupname)
        .group(displacedFHHGroup)
        .formatNumber(numberFormat);

      dc.numberDisplay("#injured", self.groupname)
        .group(injuredGroup)
        .formatNumber(numberFormat);

      dc.numberDisplay("#injured_males", self.groupname)
        .group(injuredMalesGroup)
        .formatNumber(numberFormat);

      dc.numberDisplay("#injured_females", self.groupname)
        .group(injuredFemalesGroup)
        .formatNumber(numberFormat);

      dc.numberDisplay("#dead", self.groupname)
        .group(deadGroup)
        .formatNumber(numberFormat);

      dc.numberDisplay("#dead_males", self.groupname)
        .group(deadMalesGroup)
        .formatNumber(numberFormat);

      dc.numberDisplay("#dead_females", self.groupname)
        .group(deadFemalesGroup)
        .formatNumber(numberFormat);

      let disasterDim = xf.dimension((d) => [d.disaster, d.disasterId]);
      function unique_disaster_counter(group, key) {
        return {
          value: function () {
            return group.all().filter(function (kv) {
              return kv.value > 0 && (key ? kv.key[0] == key : true);
            }).length;
          },
        };
      }

      // let disastersArray=data.map(item => { return{name:item.disaster,id:item.disasterId}}).filter((v,i,a)=>a.findIndex(t=>(t.id === v.id))===i)
      
      self.disasters.forEach((item, index) => {
        dc.numberDisplay("#" + item.id, self.groupname)
          .group(unique_disaster_counter(disasterDim.group(), item.name))
          .formatNumber(numberFormat)
          .valueAccessor((i) => {
            let isGreater = i > 0;
            if (self.disasters[index].flag) {
              self.disasters[index].hasValues = isGreater;
              self.disasters[index].flag = 0;
            }
            self.disasters[index].value = i;
            return i;
          });
      });
      // console.log(this.$session.get("user").admin2_name_en);
      let mapChart = buildMap(
        "#mapChartId",
        dc,
        d3,
        taDimension,
        reducer(taGroup),
        self.groupname,
        self.getColor,
        numberFormat,
        data,
        this.calculateChartWidth(0.79),
        this.$session.get("user").admin2_name_en
      );

      var dateData = xf.dimension(dc.pluck("created_on"));

      dc.numberDisplay("#start-year", self.groupname)
        .group(this.fake_groupAll(dateData, "created_on", "bottom"))
        .formatNumber(d3.format(""))
        .valueAccessor((x) => x)
        .render();

      dc.numberDisplay("#end-year", self.groupname)
        .group(this.fake_groupAll(dateData, "created_on", "top"))
        .formatNumber(d3.format(""))
        .valueAccessor((x) => x)
        .render();
      dc.numberDisplay("#map-start-year", self.groupname)
        .group(this.fake_groupAll(dateData, "created_on", "bottom"))
        .formatNumber(d3.format(""))
        .valueAccessor((x) => x)
        .render();

      dc.numberDisplay("#map-end-year", self.groupname)
        .group(this.fake_groupAll(dateData, "created_on", "top"))
        .formatNumber(d3.format(""))
        .valueAccessor((x) => x)
        .render();

      dc.renderAll(self.groupname);

      this.setResetChart("resetall", dc, self.groupname);
      this.setResetChart("resetmapchart", mapChart, self.groupname);
    },
  },
  mounted() {
    let data = this.flatData;
    //console.log(data)
    this.dcCharts(data);
    var xf = crossfilter(data);
    var minDate = d3.min(xf.all(), function (d) {
      return d.created_on;
    });
    var maxDate = d3.max(xf.all(), function (d) {
      return d.created_on;
    });
    this.dates.range =
      new Date(minDate).toISOString().slice(0, 10) +
      " to " +
      new Date(maxDate).toISOString().slice(0, 10);
    this.fullrange = this.dates.range;
  },
};
</script>

<style>
.impact-stats {
  font-weight: bolder !important;
  background-color: #37c2a3 !important;
}

.impact-stats:hover {
  background-color: #bf6c00 !important;
}

.disaster-impact-stats {
  background-color: #bf6c00 !important;
  color: white;
}

.disaster-impact-stats:hover {
  background-color: #37c2a3 !important;
}

.display-4 {
  color: white;
  font-weight: bolder !important;
}

.stats-icon {
  color: white;
}

.nopadding {
  padding: 0 !important;
  margin: 0 !important;
}

.number-display {
  font-weight: bold !important;

  font-family: "Calibri" !important;
}

g.water.malawi path {
  fill: "#409ffb" !important;
}

g:hover {
  cursor: pointer !important;
}
#identifier select {
  padding-top: 12px !important;
  padding-bottom: 12px !important;
  width: 300px;
}
#header-map-date .number-display {
  color: black !important;
  font-size: large;
}

.col-title {
  font-weight: bolder !important;
}

@import "../../../assets/css/nucleo/dc.css";
</style>
<style lang="sccs"></style>
