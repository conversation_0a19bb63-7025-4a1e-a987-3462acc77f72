<template>
  <div class="content">
    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Camps</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/manager/dashboard">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">REPORTS</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">All</li>
            </ol>
          </nav>
        </div>
        <div class="col-lg-6 col-6 text-right pr-3">
          <base-dropdown
            title-classes="btn btn-sm btn-primary mr-0"
            menu-on-right
            :has-toggle="false"
          >
            <a slot="title" class="dropdown-toggle" type="neutral" size="sm">
              <i class="text-black ni ni-cloud-download-95"></i> EXPORT TO EXCEL
            </a>
            <a class="dropdown-item" @click="downloadExcel()">Download</a>
          </base-dropdown>
          <!-- <base-button title-classes="btn btn-sm btn-primary mr-0" size="sm" type="primary" @click="downloadExcel()">
              <i class="text-black ni ni-cloud-download-95"></i>
              <span class="btn-inner--text">EXPORT TO EXCEL</span>
              <span class="btn-inner--text"></span>
            </base-button> -->
        </div>
        <div class="col-lg-6 col-5 text-right"></div>
      </div>

      <!-- Card stats -->
    </base-header>
    <div class="container-fluid mt--6">
      <div>
        <card
          class="no-border-card"
          body-classes="px-0 pb-1"
          footer-classes="pb-2"
        >
          <template slot="header">
            <h3 class="mb-0">Camp Reports</h3>
          </template>

          <div>
            <div
              class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
            >
              <el-select
                class="select-primary pagination-select"
                v-model="pagination.perPage"
                placeholder="Per page"
              >
                <el-option
                  class="select-primary"
                  v-for="item in pagination.perPageOptions"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>

              <div>
                <base-input
                  v-model="searchQuery"
                  prepend-icon="fas fa-search"
                  placeholder="Search..."
                ></base-input>
              </div>
            </div>
            <div>
              <b-table
                responsive
                sticky-header
                bordered="true"
                outlined="true"
                sort-icon="true"
                fixed="true"
                :items="queriedData"
                :fields="tableColumns"
              >
              </b-table>
            </div>
          </div>
          <div
            slot="footer"
            class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
          >
            <div class>
              <p class="card-category">
                Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
                <span v-if="selectedRows.length"
                  >&nbsp; &nbsp; {{ selectedRows.length }} rows selected</span
                >
              </p>
            </div>
            <base-pagination
              class="pagination-no-border"
              v-model="pagination.currentPage"
              :per-page="pagination.perPage"
              :total="total"
            ></base-pagination>
          </div>
        </card>
      </div>
    </div>
  </div>
</template>
<script>
import { Table, TableColumn, Select, Option } from 'element-ui'
import RouteBreadCrumb from '@/components/Breadcrumb/RouteBreadcrumb'
import { BasePagination } from '@/components'
import clientPaginationMixin from '@/components/PaginatedTables/clientPaginationMixin'
import { admin2s } from '../api/location/admin2s'
import { flatten } from '../../../store/flatdinrs/flatten'

import {
  generateExcel,
  generateTAGVHExcel,
  generateDisasterSummaryExcel
} from '../../../util/generateExcel'
import { generateCSV } from '../../../util/generateCSV'
import utils from '../../../util/dashboard'
import { MongoReports } from '../api/MongoReports'
import downloadexcel from 'vue-json-excel'
import JSZip from 'jszip'
import FileSaver from 'file-saver'
import moment from 'moment'
import dateformat from 'dateformat'
import { mapGetters, mapActions } from 'vuex'
import { campsreports } from '../../districtmanager/api/forms/campsreports.js'
import { download } from '../../../util/download'

export default {
  mixins: [clientPaginationMixin],
  components: {
    BasePagination,
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    [Table.name]: Table,
    downloadexcel,
    [TableColumn.name]: TableColumn
  },

  data () {
    return {
      propsToSearch: ['disaster', 'district', 'date'],
      filter: '',
      dateranges: ['24hrs', '48hrs', '1 Week', '2 Weeks', 'Lifetime'],
      gridOptions: {},

      tableColumns: [
        {
          key: 'district',
          label: 'District',
          sortable: true
        },
        {
          key: 'TA',
          label: 'TA',
          sortable: true
        },

        {
          key: 'camp_name',
          label: 'Camp',
          sortable: true
        },

        {
          key: 'camp_long',
          label: 'Long',
          sortable: true
        },

        {
          key: 'camp_lat',
          label: 'Lat',
          sortable: true
        },

        {
          key: 'camp_assessed',
          label: 'Camp assessed?',
          sortable: true
        },

        {
          key: 'has_wash_needs',
          label: 'Has wash needs?',
          sortable: true
        },

        {
          key: 'has_Food_security_needs',
          label: 'Has Food security needs?',
          sortable: true
        },

        {
          key: 'has_pgi',
          label: 'Has pgi?',
          sortable: true
        },
        {
          key: 'camp_assisted',
          label: 'Camp assisted?',
          sortable: true
        },

        {
          key: 'type_of_assistance',
          label: 'Type of assistance?',
          sortable: true,
          hidden: true
        },

        {
          key: 'camp_hh',
          label: '# of HH',
          sortable: true
        },

        {
          key: 'camp_pop',
          label: 'Population',
          sortable: true
        },

        {
          key: 'males',
          label: 'Male',
          sortable: true
        },
        {
          key: 'females',
          label: 'Females',
          sortable: true
        },
        {
          key: 'u5',
          label: 'U5',
          sortable: true
        },
        {
          key: 'sixto11',
          label: '6-11 yrs',
          sortable: true
        },

        {
          key: 'twelveto17',
          label: '12-17 yrs',
          sortable: true
        },

        {
          key: 'eighteento29',
          label: '18-20 yrs',
          sortable: true
        },

        {
          key: 'thirtyto49',
          label: '30-49 yrs',
          sortable: true
        },

        {
          key: 'plus50',
          label: '>50 yrs',
          sortable: true
        },

        {
          key: 'contactname',
          label: 'Contact',
          sortable: true
        },

        {
          key: 'contact_email',
          label: 'Contact Email',
          sortable: true
        },

        {
          key: 'contactno',
          label: 'Contact Phone',
          sortable: true
        },

        {
          key: 'date',
          label: 'Submmited at ',
          sortable: true
        }
      ],
      tableData: [],
      selectedRows: [],
      TAarray: [],
      premreports: [],
      totalinjured: 0,
      totaldeaths: 0,
      totalmissing: 0
    }
  },

  computed: {
    filteredItems () {
      let counter = 0

      if (this.filter == '' || 'Lifetime') {
        counter = 240000000000
      }
      if (this.filter == '24hrs') {
        counter = 24
      }
      if (this.filter == '48hrs') {
        counter = 48
      }
      if (this.filter == '1 Week') {
        counter = 168
      }
      if (this.filter == '2 Weeks') {
        counter = 336
      }

      return this.tableData.filter(
        item => moment().diff(moment(item.dateof), 'hours') <= counter
      )
    }
  },

  created () {
    this.loadData()
  },
  methods: {
    async loadData() {
  const response = await campsreports.getReports();
  console.log(response);

  const camps = response.map(item => {
    const row = {
      ...item,
      TA: item.TA,
      id: item._id,
      camp_name: item.campname,
      camp_hh: item.camphh,
      camp_pop: item.camppop,
      camp_assessed: item.assessed,
      has_wash_needs: item.wash,
      has_Food_security_needs: item.food,
      has_pgi: item.PGI,
      camp_assisted: item.assisted,
      type_of_assistance: item.assistance,
      males: item.males,
      females: item.females,
      U5: item.u5,
      sixto11: item.sixto11,
      sixto11f: item.sixto11f,
      sixto11m: item.sixto11m,
      twelveto17: item.twelveto17,
      twelveto17m: item.twelveto17m,
      twelveto17f: item.twelveto17f,
      eighteento29: item.eighteento29,
      eighteento29f: item.eighteento29f,
      eighteento29m: item.eighteento29m,
      thirtyto49: item.thirtyto49,
      thirtyto49f: item.thirtyto49f,
      thirtyto49m: item.thirtyto49m,
      plus50: item.plus50,
      plus50f: item.plus50f,
      plus50m: item.plus50m,
      lactactingmothers: item.lactactingmothers,
      pregnantwomen: item.pregnantwomen,
      disabled: item.disabled,
      chronicallyill: item.chronicallyill,
      plhiv: item.plhiv,
      injured: item.injured,
      dead: item.dead,
      missing: item.missing,
      needs: item.needs,
      comments: item.comments,
      camp_lat: item.gpslat,
      camp_long: item.gpslong,
      district: item.district,
      date: moment(new Date(item.createdon)).format('DD-MM-YYYY')
    };

    try {
      row.contactname = item.user.firstName + ' ' + item.user.lastName;
      row.contact_email = item.user.email;
      row.contactno = item.user.phone;
    } catch (error) {}

    return row;
  });

  camps.sort((a, b) => new Date(b.createdon) - new Date(a.createdon));

  const rows = [...camps];
  this.tableData = rows;
}
,

    downloadExcel(type) {
  // generate disaggregated excel sheet
  var data = this.tableData.map(item => {
    return {
      District: item.district,
      TA: item.TA,
      Camp: item.camp_name,
      'Is camp assessed?': item.assessed,
      'Does it have WASH needs?': item.wash,
      'Does it have Food security needs?': item.food,
      PGI: item.PGI,
      'Is camp assisted': item.assisted,
      'Type of assistance?': item.assistance,
      'No. of Households': item.camp_hh,
      Population: item.camp_pop,
      Males: item.males,
      Females: item.females,
      U5: item.u5,
      'People (6 - 11)': item.sixto11,
      'People (6 - 11 Female)': item.sixto11f,
      'People (6 - 11 Male)': item.sixto11m,
      'People (12 - 17)': item.twelveto17,
      'People (12 - 17 Female)': item.twelveto17f,
      'People (12 - 17 Male)': item.twelveto17m,
      'People (18 - 29)': item.eighteento29,
      'People (18 - 29 Female)': item.eighteento29f,
      'People (18 - 29 Male)': item.eighteento29m,
      'People (30 - 49)': item.thirtyto49,
      'People (30 - 49 Female)': item.thirtyto49f,
      'People (30 - 49 Male)': item.thirtyto49m,
      'People (50 >)': item.plus50,
      'People (50 > Female)': item.plus50f,
      'People (50 > Male)': item.plus50m,
      'Lactating Mothers': item.lactactingmothers,
      'Pregnant Women': item.pregnantwomen,
      'Disabled': item.disabled,
      'Chronically Ill': item.chronicallyill,
      'PLHIV': item.plhiv,
      'Injured': item.injured,
      'Dead': item.dead,
      'Missing': item.missing,
      'Needs': item.needs,
      'Comments': item.comments,
      'Last updated': item.date,
      Lat: item.gpslat,
      Long: item.gpslong,
      'Contact Name': item.contactname,
      'Contact Email': item.contact_email,
      'Contact #': item.contactno
    }
  })
  generateTAGVHExcel(data)
}


  }
}
</script>

<style>
.no-border-card .card-footer {
  border-top: 0;
}
</style>
