<template>
  <div>
    <div style="background:#F4F4F4">
      <div class="row pt-4">
        <div class="col">
          <div class="card">
            <div class="card-header">
              <span style="color:teal;font-size:150%">
                <b>
                  FOOD SECURITY [
                  <span style="color:red"
                    >{{ data.Disaster }} - {{ data.District }}</span
                  >
                  ]
                </b>
                <b class="pull-right"
                  >ASSESSMENT PERIOD :
                  {{
                    dateFormate(
                      new Date(data.Date_of_Disaster_from).toLocaleDateString(
                        "en-US"
                      )
                    )
                  }}
                  -
                  {{
                    dateFormate(
                      new Date(data.Date_of_Disaster_to).toLocaleDateString(
                        "en-US"
                      )
                    )
                  }}</b
                >
              </span>
              <hr />
              <div class="row">
                <base-input label="TA" class="col-xl-3">
                  <el-select
                    v-model="selected"
                    @change="getInfoGraphicsData(selected)"
                    placeholder="select"
                  >
                    <el-option
                      v-for="option in data.TAList"
                      :key="option.label"
                      :label="option.label"
                      :value="option.value + ',' + option.label"
                    ></el-option>
                  </el-select>
                </base-input>
                <base-input
                  label="GVHs"
                  class="col-xl-9"
                  :value="data.GVHS_affected"
                  readonly
                ></base-input>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-xl-4 col-md-6">
          <stats-card
            title="HH with Food lost"
            type="gradient-green"
            :sub-title="
              (
                parseInt(data.No_of_FHH_who_lost_Food_Stock_food) +
                  parseInt(data.No_of_MHH_who_lost_Food_Stock_food) || 0
              ).toString()
            "
            icon="icon-event-foodshortage"
          >
            <template slot="footer">
              <span>
                <i class="icon-ocha-affected-man"></i>
                {{ data.No_of_MHH_who_lost_Food_Stock_food }}
              </span>
              <span class="pull-right">
                <i class="icon-ocha-affected-woman"></i>
                {{ data.No_of_FHH_who_lost_Food_Stock_food }}
              </span>
            </template>
          </stats-card>
        </div>

        <div class="col-xl-4 col-md-6">
          <stats-card
            title="HH < 1 month food"
            type="gradient-green"
            :sub-title="
              (
                parseInt(
                  data.No_of_MHH_with_less_than_1_month_Food_Availability_food
                ) +
                  parseInt(
                    data.No_of_MHH_with_less_than_1_month_Food_Availability_food
                  ) || 0
              ).toString()
            "
            icon="icon-event-foodshortage"
          >
            <template slot="footer">
              <span>
                <i class="icon-ocha-affected-man"></i>
                {{
                  data.No_of_MHH_with_less_than_1_month_Food_Availability_food
                }}
              </span>
              <span class="pull-right">
                <i class="icon-ocha-affected-woman"></i>
                {{
                  data.No_of_MHH_with_less_than_1_month_Food_Availability_food
                }}
              </span>
            </template>
          </stats-card>
        </div>
        <div class="col-xl-4 col-md-6">
          <stats-card
            title="HH with 1-2 months"
            type="gradient-green"
            :sub-title="
              (
                parseInt(data.No_of_FHH_with_1_2_month_Food_Availability_food) +
                  parseInt(
                    data.No_of_MHH_with_1_2_month_Food_Availability_food
                  ) || 0
              ).toString()
            "
            icon="icon-event-foodshortage"
          >
            <template slot="footer">
              <span>
                <i class="icon-ocha-affected-man"></i>
                {{ data.No_of_FHH_with_1_2_month_Food_Availability_food }}
              </span>
              <span class="pull-right">
                <i class="icon-ocha-affected-woman"></i>
                {{ data.No_of_MHH_with_1_2_month_Food_Availability_food }}
              </span>
            </template>
          </stats-card>
        </div>
      </div>
      <div class="row">
        <div class="col-xl-8">
          <div class="row">
            <div class="col-xl-6">
              <card>
                <template slot="header">
                  <!-- Subtitle -->
                  <h6 class="surtitle">Food availability</h6>
                  <!-- Title -->
                </template>
                <div>
                  <doughtNutChart
                    :data="doughnutChartData"
                    :options="doughnutChartOptions"
                  ></doughtNutChart>
                </div>
              </card>
            </div>
            <div class="col-xl-6">
              <card>
                <template slot="header">
                  <!-- Subtitle -->
                  <h6 class="surtitle">Food lost</h6>
                </template>
                <div>
                  <barChart
                    :data="barChartData"
                    :options="barChartOptions"
                  ></barChart>
                </div>
              </card>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import flatPicker from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";

import RouteBreadCrumb from "@/components/Breadcrumb/RouteBreadcrumb";
import BaseHeader from "@/components/BaseHeader";
import StatsCard from "@/components/Cards/StatsCard";
import { Select, Option } from "element-ui";
import malawiMap from "./components/map-core";
import doughtNutChart from "./components/doughtNutChart";
import barChart from "./components/barChart";
import TagsInput from "@/components/Inputs/TagsInput";
import moment from "moment";

window.jQuery = require("jquery");

function randomScalingFactor() {
  return Math.round(Math.random() * 100);
}

export default {
  components: {
    StatsCard,
    BaseHeader,
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    flatPicker,
    malawiMap,
    doughtNutChart,
    barChart,
    TagsInput
  },
  props: ["data"],
  data() {
    return {
      TAList: [],
      selected: {},
      tags: ["Floods", "2019-01-01 to 2019-12-31"],
      doughnutChartOptions: {
        hoverBorderWidth: 20
      },
      doughnutChartData: {
        hoverBackgroundColor: "red",
        hoverBorderWidth: 10,
        labels: ["lost", "< 1 month", "1 - 2 months", ">2 months"],
        datasets: [
          {
            label: "HH",
            backgroundColor: ["#00000", "teal", "#a97142", "teal"],
            data: [
              parseInt(this.data.No_of_FHH_who_lost_Food_Stock_food) +
                parseInt(this.data.No_of_MHH_who_lost_Food_Stock_food),
              parseInt(
                this.data
                  .No_of_FHH_with_less_than_1_month_Food_Availability_food
              ) +
                parseInt(
                  this.data
                    .No_of_MHH_with_less_than_1_month_Food_Availability_food
                ),
              parseInt(
                this.data.No_of_MHH_with_1_2_month_Food_Availability_food
              ) +
                parseInt(this.No_of_FHH_with_1_2_month_Food_Availability_food),
              parseInt(
                this.data
                  .No_of_MHH_with_more_than_2_months_Food_Availability_food
              ) +
                parseInt(
                  this.data
                    .No_of_FHH_with_more_than_2_months_Food_Availability_food
                )
            ]
          }
        ]
      },
      barChartOptions: {
        hoverBorderWidth: 20
      },
      barChartData: {
        hoverBackgroundColor: "red",
        hoverBorderWidth: 10,
        labels: ["Male", "Female"],
        datasets: [
          {
            label: "HH",
            backgroundColor: "teal",
            data: [
              this.data.No_of_MHH_who_lost_Food_Stock_food,
              this.data.No_of_FHH_who_lost_Food_Stock_food
            ]
          }
        ]
      },

      filter: {
        disasters: "Floods",
        range: "2019-01-01 to 2019-12-31"
      },
      disasters: [
        {
          name: "Heavy Rains"
        },
        {
          name: "Earth quake"
        },
        {
          name: "Floods"
        }
      ],
      districts: [
        {
          label: "Chikwawa",
          value: "Chikwawa",
          region: "South"
        },
        {
          label: "Mangochi",
          value: "Mangochi",
          region: "South"
        },
        {
          label: "Balaka",
          value: "Balaka",
          region: "North"
        },
        {
          label: "Phalombe",
          value: "Phalombe",
          region: "Central"
        }
      ],
      regions: [
        {
          region: "South"
        },
        {
          region: "Central"
        },
        {
          region: "North"
        },
        {
          region: "East"
        }
      ],
      houses: [
        {
          id: 1,
          name: "Completely damaged",
          number: 67,
          males: 78,
          females: 59
        },
        {
          id: 2,
          name: "Partly damaged",
          number: 400,
          males: 76,
          females: 79
        },
        {
          id: 3,
          name: "Underwater",
          number: 79,
          males: 68,
          females: 99
        }
      ]
    };
  },
  methods: {
    dateFormate(dateData) {
      const cur_date = dateData;
      const formattedDate = moment(cur_date).format("DD/MM/YYYY");

      return formattedDate;
    },
    getInfoGraphicsData(selectedTA) {
      var data = selectedTA.split(",");
  
      var TA = data[1];
      var ID = data[0];
      if (TA === "All") {
        this.$emit("Dinr", ID);
      } else {
        this.$emit("Dra", ID);
      }
    }
  },
  mounted() {
    this.selected = this.data.TAname;
  }
};
</script>
<style>
#malawiMap {
}
@import "../../../../assets/fonts/font-humanitarian.css";
</style>
