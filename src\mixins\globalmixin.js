import JSZip from 'jszip'
import FileSaver from 'file-saver'

import { auth } from './../api/auth'
import { mail } from './../api/mail'

export default {
  data: () => ({
    token: '',
    mailparticipants: [],
    gvhs_affected: [],
    emails: []
  }),

  methods: {
    
    convertBase64ToFile (base64String, fileName) {
      let arr = base64String.split(',')
      let mime = arr[0].match(/:(.*?);/)[1]
      let bstr = atob(arr[1])
      let n = bstr.length
      let uint8Array = new Uint8Array(n)
      while (n--) {
        uint8Array[n] = bstr.charCodeAt(n)
      }
      let file = new File([uint8Array], fileName, { type: mime })
      return file
    },

    createArchive (images, name) {
      var zip = new JSZip()
      var img = zip.folder('images')

      for (var i = 0; i < images.length; i++) {
        img.file(name + '-' + i + '.jpg', images[i], { base64: true })
      }
      zip.generateAsync({ type: 'blob' }).then(function (file) {
        saveAs(file, name + '-' + 'images' + '.zip')
      })
    },

    downloadBase64Data (base64Strings) {
      let baseImages = base64Strings.images
      let archiveName = ''
      for (let i in baseImages) {
        archiveName = base64Strings.ta + '-' + Date.now()
        let imgFile = this.convertBase64ToFile(baseImages[i], archiveName)
        this.images.push(imgFile)
      }
      this.createArchive(this.images, archiveName)
    },

    getAuthToken (data) {
      let response = auth.login({
        email: '<EMAIL>',
        password: 'admin429'
      })
      response.then(response => {
        this.token = response.id

        this.generateMailingList(this.token, data)
      })
    },

    generateMailingList (token, dataset) {
      auth.allAccounts(token).then(response => {
        this.emails = response
          .filter(data => data.roleName == 'manager')
          .filter(
            result => result.organisation == 'DODMA2' && result.statusId == 1
          )

        for (let mail in this.emails) {
          if (this.emails.indexOf(this.emails[mail].email) === -1) {
            this.mailparticipants.push(this.emails[mail].email)
          }
        }

        this.sendMail(dataset)
      })
    },

    sendMail (data) {
      for (let i in data.impact) {
        this.gvhs_affected.push(data.impact[i].gvh.name)
      }

      this.gvhs_affected = String(this.gvhs_affected.join())

      let totalHH = data.impact.reduce((accumulator, object) => {
        return accumulator + parseInt(object.fhh) + parseInt(object.mhh)
      }, 0)

      let model = {
        from: 'DRMIS PRELIMINARY DISASTER REPORTING',
        to: this.mailparticipants.join(),
        subject: 'NEW PRELIMINARY DISASTER REPORT',
        text:
          'Please take note and approve the attached preliminary disaster report. Below are the summary details and the actions you should take',
        html: {
          disaster: data.disaster,
          ta: data.TA,
          district: data.user.district.admin2_name_en,
          disasterdate: data.disasterdate,
          gvhs: this.gvhs_affected,
          hh: String(parseInt(totalHH)),
          message:
            '<b>NB: Data has been verified and Approved by RRO and DCPC members</b></br>'
        }
      }

      // mail.create(model).then(() => {})
    }
  }
}
