import Vue from 'vue'
import DashboardPlugin from './plugins/dashboard-plugin'
import App from './App.vue'
import axios from 'axios'
import VueAxios from 'vue-axios'
import Vuex from 'vuex'
import VueSession from 'vue-session'
import VueHtml2Canvas from 'vue-html2canvas'
import modules from './store/index'
import '@platyplus/humanitarian-icons/dist/icons.css'
import * as L from 'leaflet'
import { BootstrapVue, BootstrapVueIcons, IconsPlugin } from 'bootstrap-vue'

Vue.use(BootstrapVue)
Vue.use(BootstrapVueIcons)
Vue.use(IconsPlugin)
Vue.use(VueHtml2Canvas)
Vue.use(VueSession)
Vue.use(Vuex)

// router setup
import router from './routes/router'
import svgJs from './plugins/vueSvgPlugin'

import Notifications from 'vue-notification'

Vue.use(svgJs)
Vue.use(Notifications)
// plugin setup
Vue.use(DashboardPlugin)

//axios accessible globally
window.axios = axios

const store = new Vuex.Store({
  modules,
  strict: process.env.DEV
})

/* eslint-disable no-new */
new Vue({
  el: '#app',
  render: h => h(App),
  router,
  store
})
