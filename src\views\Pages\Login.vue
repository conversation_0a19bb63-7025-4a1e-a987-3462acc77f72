<template>
  <div>
    <div class="row-col">
      <!-- Header -->
      <div style="position: center;">
        <div class="header mt-3">
          <div class="container">
            <div class="header-body text-center">
              <div class="row justify-content-center">
                <div class="col-12 text-center">
                  <p align="center" class="mt-1">
                    <img width="80" src="../../../static/logo.png" />
                  </p>
                </div>
                <div class="col-12">
                  <h1 class="text-lead text-primary display-4">
                    <b>DRMIS : WEB APP</b>
                  </h1>
                  <p class="text-lead text-primary">
                    (Disaster Risk Management Information System)
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
  <div class="container-fluid bg-image-container">
    <div class="row justify-content-center align-items-center">
      <div class="col-lg-10 col-md-12 col-sm-12 col-12">
        <div class="card bg-secondary border-0 mb-0 my-0 py-0">
          <div class="card-body px-3 pb-1">
            <form role="form" @submit.prevent="handleSignIn" class="my-0 py-1" >
              <div class="text-center text-muted mb-4 my-0 py-0">
                <small><b class="text-primary">Sign in with valid credentials</b></small>
              </div>
              <base-input
                alternative
                class="round"
                type="text"
                prepend-icon="ni ni-email-83"
                placeholder="Email"
                v-model="model.email"
              ></base-input>

              <base-input
                class="round"
                alternative
                prepend-icon="ni ni-lock-circle-open"
                type="password"
                id="password"
                placeholder="Password"
                v-model="model.password"
              ></base-input>


                <input
                          type="checkbox"
                          class="pt-2 ml-3 mb-3"
                          @change="visibility()"
                        /><span class="pl-0 ml-2">Show Password</span>



              <div
                class="round"
                alternative
              >
              <base-button type="primary" native-type="submit" class="button">Sign in</base-button></div>




              <div
                class="round"
                alternative
              >
              <p class="text-left text-muted mb-0">
                <a @click.prevent="$router.push({ name: 'ForgotPassword' })" class="text-left text-primary mb-0">
                  Forgot password?
                </a>
                  </p></div>
            </form>
          </div>

        </div>
      </div>
    </div>
  </div>
 <!-- Modal -->
 <div class="pa-2 ma-2">
          <b-modal
            scrollable
            body-class="p-0"
            centered
            hide-backdrop
            size="xs"
            class="modal-xs"
            v-model="modalShow"
            hide-header="true"
            hide-footer="true"
          >
            <b-card
              no-body
              class="p-2 m-2 look"
              header-tag="header"
              footer-tag="footer"
            >
              <template #header> </template>
              <a @click="$router.push({ name: 'ForgotPassword' })" class="text-left text-primary mb-0">
                Forgot password?
              </a>
            </b-card>
          </b-modal>
        </div>
        <div class="pb-4"></div>
        <!-- Footer -->
        <footer style="position: center;">
          <div class="container-fluid">
            <div class="row pb-0 mb-0">
              <div class="col-8">
                <ul class="nav nav-footer">
                  <li class="nav-item"></li>
                  <li class="nav-item">
                    <a
                      v-bind:href="drmisURL"
                      class="nav-link font-weight-bold text-primary"
                      target="_blank"
                      >© {{ year }} DRMIS</a
                    >
                  </li>
                </ul>
              </div>
              <div class="col-4">
                <div class="d-flex align-items-end justify-content-end">
                  <div class="border-right pr-2 mr-0">
                    <img width="80" src="../../../static/donor_logo.png" />
                  </div>
                  <div>
                    <img width="23" class="ml-0" src="../../../static/undp.png" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
  </div>
</template>

<script>
import { auth } from '@/api/auth'
import { generatePassword } from '../../api/pass'
import userPass from '../../api/pass'
import swal from 'sweetalert2'
import { Relay } from '../../packages/districtmanager/api/relay'
import { accounts } from '../../packages/admin/api/accounts/accounts'
import { relay } from '../../packages/districtmanager/api/relay'
var $ = require('jquery')
export default {
  data () {
    return {
      model: {
        email: '',
        password: ''
      },
      year: 2019,
      dodmaURL: process.env.VUE_APP_DODMA_URL,
      undpURL: process.env.VUE_APP_UNDP_URL,
      drmisURL: process.env.VUE_APP_DRMIS_URL,
      usersArr: null,
      modalShow: false,
      session: false
    }
  },

  methods: {
    async sendRequest () {
      let token = null
      let user = userPass.userPass
      await auth.login(user).then(response => {
        token = response.id
      })
      await accounts.get(token).then(response => {
        this.usersArr = response.data.filter(u => u.email === this.model.email)
        //console.log(this.usersArr, "mmmmmmmmmmmmmmmmmmmmmmm");
      })
      if (this.usersArr.length > 0) {
        Relay.sendMail({
          dodFrom: new Date(),
          email: this.model.email,
          status: 'passwordReset'
        }).then(() => {
          this.modalShow = !this.modalShow

          swal({
            text:
              'The email has been sent. go to your email account to proceed',
            type: 'success',
            toast: true,
            position: 'middle',
            confirmButtonClass: 'btn btn-success btn-fill',
            buttonsStyling: false
          })
        })
      } else {
        swal({
          text: 'The user is not valid, re-enter the email',
          type: 'error',
          toast: true,
          position: 'middle',
          confirmButtonClass: 'btn btn-success btn-fill',
          buttonsStyling: false
        })
      }
    },
    async openStat () {
      this.modalShow = !this.modalShow
    },
    visibility () {
      const passwordField = document.querySelector('#password')
      if (passwordField.getAttribute('type') === 'password')
        passwordField.setAttribute('type', 'text')
      else {
        passwordField.setAttribute('type', 'password')
      }
    },
    handleSignIn () {
      if (this.model.email !== null && this.model.email !== null) {
        var response = auth.login(this.model)
        response.then(
          response => {
            this.$session.start()
            this.$session.set('jwt', response.id)
            this.$session.set('jwtuid', response.userId)
            this.$session.set('jwturole', response.Role.name)
            this.$session.set('user', response.user.district)
            this.$session.set('userObj', response.user)
            //isNewPassword
            //alert(JSON.stringify(response.user))
            ;``
            this.$session.set(
              'jwtuser',
              response.user.firstName + ' ' + response.user.lastName
            )
            if (response.user.isNewPassword) {
              this.$router.push({
                path: '/changePassword',
                query: { email: response.user.email }
              })
            } else if (
              response.user.cluster_user_type == 'Cluster Data Collector'
            ) {
              swal({
                text:
                  'Unauthorized User (You do not have access to this service) ',
                type: 'error',
                toast: true,
                timer: 4000,
                position: 'top-end',
                confirmButtonClass: 'btn btn-success btn-fill',
                buttonsStyling: false
              })
            } else {
              swal({
                title: '',
                text: 'Signed In Succesfully .',
                type: 'success',
                toast: true,
                timer: 2500,
                confirmButtonClass: 'btn btn-success btn-fill',
                buttonsStyling: false,
                position: 'top-end'
              })

              if (response.Role.name == 'acpc') {
                //to be deleted

                this.$router.push({
                  path: '/districtmanager',
                  params: {}
                })
              } else {
                this.$router.push({
                  path: '/' + response.Role.name.replace(/\s+/g, ''),
                  params: {}
                })
              }
            }
          },
          reason => {
            swal({
              text:
                'Failed To Sign In ( possible invalid email and / or password : ' +
                reason +
                ' )',
              type: 'error',
              toast: true,
              position: 'top-end',
              confirmButtonClass: 'btn btn-success btn-fill',
              buttonsStyling: false
            })
          }
        )
      } else {
        swal({
          text:
            'Failed To Sign In ( possible invalid email and / or password )',
          type: 'error',
          toast: true,
          position: 'top-end',
          confirmButtonClass: 'btn btn-success btn-fill',
          buttonsStyling: false
        })
      }
    }
  },
  async mounted () {
    $('#popover-button-variant').click(function () {
      $('.ni-bold-left').toggleClass('ni-bold-right')
    })

    const d = new Date()
    let year = d.getFullYear()
    this.year = year

    let pass = await generatePassword()
    let token = null
    let user = userPass.userPass
    await auth.login(user).then(response => {
      token = response.id
    })
    await accounts.get(token).then(response => {
      this.usersArr = response.data
    })
    if (typeof this.$route.query.session !== 'undefined') {
      swal({
        text:
          "Sorry you don't have an active session. Please sign in to access system",
        type: 'error',
        toast: true,
        position: 'top-end',
        confirmButtonClass: 'btn btn-success btn-fill',
        buttonsStyling: false
      })
    }
  }
}
</script>
<style scoped>
/* Center the form on the page */
.form-page {
  height: 100vh; /* Full viewport height */
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8f9fa; /* Light background for contrast */
}

/* Form container styles */
.form-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Subtle shadow */
}

/* Input field styles */
.round {
  border-radius: 15px;
  background: #fff;
  padding: 0 10px;
}

/* Button styles */
.btn {
  width: 100%;
  border-radius: 5px;
}

/* Adjustments for smaller screens */
@media (max-width: 768px) {
  .form-container {
    max-width: 90%;
  }
}
</style>
