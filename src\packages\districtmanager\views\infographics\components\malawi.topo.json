{"type": "Topology", "arcs": [[[6555, 19943], [-15, -2], [-1447, -5], [-43, 6], [-74, 21], [-30, 1], [-32, -7], [-42, -13], [-22, -1], [-72, 40], [-474, 357]], [[4304, 20340], [-765, 1071], [-38, 70], [-25, 30], [-24, 7], [-121, -10], [-76, -1], [-32, 11], [-42, 22], [-114, 86], [-27, 15], [-13, 2], [-45, 5], [-36, 6], [-37, 18], [-24, 22], [-27, 36], [-17, 18], [-22, 15], [-25, 12], [-40, 25], [-16, 22], [-6, 25], [2, 38], [6, 11], [14, 9], [85, 24], [162, 91], [17, 14], [20, 26], [12, 11], [43, 7], [27, 9], [45, 35], [22, 25], [22, 31], [30, 60], [12, 15], [36, 25], [42, 39], [22, 17], [20, 11], [25, 7], [57, 10], [13, 18], [2, 33], [-22, 52], [-8, 30], [-4, 58], [-14, 28], [-23, 35], [-10, 28], [-2, 22], [10, 52], [2, 35], [-14, 26], [-14, 14], [-64, 107]], [[3305, 22900], [1, 0], [85, 10], [89, 45], [107, 27], [92, -52], [86, -77], [87, -52], [78, -29], [95, -51], [81, -61], [36, -60], [39, -26], [55, 54], [0, 21], [50, 29], [27, 31], [12, 40], [2, 55], [24, 92], [6, 53], [-62, 136], [50, 77], [79, 59], [40, 41], [55, 44], [116, -38], [104, -61], [4, -3], [32, -34], [47, -23], [113, -124], [25, -13], [58, -57], [97, -40], [326, -310], [100, -33], [21, -40], [8, -54], [17, -58], [19, -24], [75, -69], [15, -8], [60, -52], [4, -13], [335, -372], [38, -63], [100, -128], [39, -104], [5, -33], [-7, -35], [-30, -59], [-7, -31], [26, -103], [111, -225], [32, -162], [28, -66], [7, -38], [-1, -32], [-68, -332], [15, -66], [65, -97], [14, -56], [-8, -29], [-31, -68], [-7, -39], [3, -17], [3, -24], [16, -42], [22, -40], [27, -34], [28, -12], [29, 1], [11, -3]], [[6555, 19943], [12, -3], [9, -35], [-2, -59], [9, -18], [72, -76], [31, -62], [3, -66], [-44, -64], [21, -28], [6, -14], [14, -17], [22, -14], [20, -22], [9, -39], [-41, -27], [-21, -26], [13, -80], [-43, -190], [0, -135], [-7, -32], [-62, -124], [-31, -35]], [[6545, 18777], [-1, 0], [-15, 0], [-1266, 0], [-135, -96], [-463, -243]], [[4665, 18438], [-46, 80], [-42, 53], [-47, 51], [-215, 154], [-24, 7], [-12, -16], [-8, -21], [-23, -132], [-28, -18], [-47, -5], [-316, 40], [-103, -11], [-122, -29], [-17, -10], [-7, -19], [7, -18], [10, -18], [9, -13], [6, -14], [-4, -18], [-10, -22], [-21, -21], [-23, -15], [-27, -9], [-27, -3], [-30, 3], [-31, 17], [-18, 22], [-27, 50], [-20, 13], [-29, 5], [-128, -17], [-36, -1], [-40, 7], [-46, 22], [-35, 24], [-35, 30], [-33, 22], [-34, 10], [-45, -9], [-24, -16], [-18, -20], [-42, -66], [-19, -22], [-50, -47], [-59, -45], [-68, -43], [-29, -8], [-27, 6], [-12, 31], [4, 25], [10, 25], [11, 23], [7, 29], [0, 30], [-9, 53], [-14, 53], [-18, 18], [-28, 12], [-113, 4], [-123, 18], [-154, 56], [-64, 23]], [[2102, 18748], [-11, 92], [-25, 96], [-48, 37], [-46, -6], [-42, 0], [-27, 17], [3, 44], [30, 53], [31, 10], [35, -9], [41, -1], [49, 28], [63, 85], [41, 32], [45, 14], [85, 14], [46, 17], [47, 7], [110, -19], [52, -3], [44, 18], [61, 55], [51, 8], [68, 38], [37, 53], [54, 122], [163, 164], [45, 27], [43, 17], [131, 85], [52, 43], [55, 74], [-1, 12]], [[3384, 19972], [95, 34], [825, 334]], [[6545, 18777], [-67, -77], [-16, -77], [14, -62], [45, -125], [10, -75], [25, -53], [159, -123], [18, -30], [10, -27], [16, -19], [61, -13], [27, -17], [20, -24], [9, -27], [10, -69], [30, -60], [44, -44], [53, -27], [0, -19], [-20, -10], [-29, -20], [-20, -10], [74, -124], [22, -23], [22, 2], [42, 34], [25, 7], [28, -3], [144, -43], [81, -39], [63, -50], [25, -63], [14, -23], [63, -46], [29, -83], [31, -32], [32, -24], [14, -22], [8, -19], [16, -27], [16, -36], [8, -44], [0, -158], [5, -26], [-1049, 11], [-116, -26], [-32, -39], [1, -55], [22, -132], [-7, -123], [-25, -120], [-136, -299], [-255, -345], [-360, -486], [-71, -154], [-16, -137], [5, -17]], [[5667, 15005], [-1, 0], [-1094, 1], [-245, 52], [-137, 53], [-45, 27], [-71, 57], [-33, 9], [-29, 1], [-63, -12]], [[3949, 15193], [-200, -2], [-27, 6], [-23, 12], [-18, 17], [-15, 23], [-16, 16], [-19, 11], [-57, 17], [-26, 12], [-20, 18], [-5, 25], [9, 29], [33, 49], [34, 34], [31, 21], [34, 10], [36, 4], [37, 7], [34, 15], [25, 23], [69, 83], [12, 29], [-8, 35], [-15, 30], [-13, 38], [-5, 42], [3, 62], [-7, 44], [-20, 72], [2, 28], [8, 27], [30, 42], [15, 62], [21, 54], [0, 52], [-13, 26], [-47, 35], [-14, 19], [-7, 20], [-5, 20], [-8, 14], [-10, 9], [-40, 30], [-20, 25], [-15, 30], [-7, 32], [-3, 32], [1, 35], [9, 64], [38, 167], [16, 34], [24, 33], [49, 50], [37, 26], [33, 18], [19, 8], [21, 12], [71, 62], [26, 14], [26, 6], [26, 0], [19, -2], [132, -33], [24, -3], [27, 1], [31, 10], [29, 14], [27, 32], [26, 43], [39, 83], [31, 36], [35, 22], [52, 3], [17, -3], [19, -4], [35, -15], [16, -4], [13, 4], [47, 21], [61, 14], [29, 13], [14, 26], [3, 51], [-41, 114], [-46, 46], [-14, 19], [-77, 275], [-18, 23], [-23, 15], [-49, 25], [-10, 36], [6, 54], [81, 243], [35, 218]], [[6801, 15628], [72, -4], [75, -24], [54, -32], [27, -62], [-10, -77], [-37, -75], [-53, -57], [-70, -36], [-76, -11], [-67, 20], [-42, 63], [-1, 64], [21, 71], [62, 132], [45, 28]], [[6357, 15694], [3, 2], [120, 23], [97, -51], [46, -93], [-31, -106], [-69, -61], [-51, 2], [-42, 40], [-43, 54], [-89, 90], [-8, 51], [67, 49]], [[3384, 19972], [-1, 11], [-3, 30], [-24, 55], [0, 80], [-91, 11], [-52, 53], [-66, 137], [-25, 26], [-94, 75], [-28, 35], [-88, 188], [-12, 73], [21, 170], [-8, 96], [-48, 50], [-66, 41], [-62, 74], [-87, 27], [-61, 45], [-55, 54], [-70, 54], [-273, 109], [-45, 56], [2, 78], [18, 77], [63, 144], [99, 139], [24, 64], [-22, 85], [-52, 120], [-22, 23], [-124, 41], [-41, 20], [-40, 32], [-29, 37], [-48, 82], [-37, 36], [-84, 47], [-32, 43], [-9, 45], [11, 28], [18, 25], [9, 32], [-5, 47], [-15, 40], [-48, 77], [-67, 42], [-181, 9], [-77, 33], [-20, -79], [-11, -90], [-29, -55], [-74, 24], [-18, 29], [-7, 37], [-14, 32], [-37, 16], [-45, -11], [-37, -23], [-37, -15], [-44, 14], [-41, 82], [59, 220], [-1, 83], [-30, 13], [-49, 40], [-22, 12], [-39, -1], [-22, -15], [-21, -6], [-32, 24], [-16, 41], [-8, 178], [52, 49], [64, 29], [65, 3], [59, -31], [167, -150], [206, -144], [31, -11], [90, -18], [98, -34], [13, -7], [30, 9], [46, 35], [22, 9], [47, 1], [70, -19], [40, -4], [28, 9], [53, 31], [30, 5], [60, -19], [188, -120], [62, -53], [58, -113], [40, -57], [52, -32], [64, -18], [140, -9], [104, 23], [79, 41], [81, 31], [114, -11], [102, -29], [88, -14]], [[3949, 15193], [-216, -162], [-58, -83], [-1, -26], [13, -87], [14, -36], [27, -24], [38, -14], [107, -8], [48, -25], [44, -40], [50, -92], [34, -46], [62, -63], [11, -46], [7, -67], [17, -45], [22, -36], [26, -32], [30, -29], [32, -15], [88, 3], [43, -7], [122, -55], [38, -12], [43, -9], [41, -4], [41, 4], [78, 18], [31, -1], [34, -15], [11, -13], [2, -11], [-5, -8], [-6, -7], [-50, -43], [-29, -29], [-24, -69], [-11, -23], [-17, -16], [-18, -8], [-63, -7], [-71, -15], [-30, -11], [-27, -14], [-53, -39], [-36, -20], [-111, -35], [-99, -14], [-27, -10], [-23, -14], [-19, -17], [-19, -19], [-90, -136], [-76, -91]], [[3924, 13470], [-28, -8], [-27, -14], [-13, -9], [-12, -8], [-16, 0], [-20, 13], [-73, 57], [-28, 14], [-25, 6], [-17, -3], [-39, -2], [-16, -5], [-13, -7], [-11, -10], [-28, -17], [-15, -7], [-34, -10], [-18, -4], [-21, -1], [-46, 3], [-44, 6], [-38, 10], [-16, 7], [-18, 10], [-17, 14], [-36, 37], [-59, 77], [-22, 22], [-15, 9], [-18, 6], [-21, 2], [-22, 2], [-40, -4], [-16, 15], [-12, 32], [3, 88], [10, 37], [13, 24], [13, 10], [11, 11], [7, 14], [-1, 21], [-8, 26], [-24, 38], [-43, 46], [-7, 23], [-6, 31], [-2, 165], [8, 37], [7, 16], [9, 14], [10, 13], [33, 33], [10, 13], [8, 14], [5, 18], [2, 19], [-18, 21], [-22, 17], [-150, 28], [-50, 10]], [[2848, 14490], [15, 8], [30, 49], [-6, 62], [-38, 13], [-56, -2], [-131, 41], [-247, -67], [-117, 37], [-65, 90], [-59, 212], [-42, 100], [-39, 41], [-94, 81], [-25, 49], [-5, 103], [12, 101], [132, 278], [31, 155], [19, 49], [12, 47], [-14, 44], [-37, 66], [-1, 49], [45, 115], [11, 111], [-30, 390], [-8, 105], [-20, 49], [-45, 46], [-55, 9], [-59, -7], [-55, 0], [-56, 19], [-12, 23], [50, 95], [21, 57], [4, 37], [-15, 260], [31, 43], [85, -63], [29, 41], [0, 39], [-6, 40], [5, 42], [16, 17], [51, 20], [18, 14], [5, 18], [-12, 35], [5, 19], [235, 325], [46, 85], [30, 91], [-108, 168], [-74, 67], [-62, 77], [-49, 84], [-32, 92], [-10, 89]], [[3924, 13470], [-45, -241], [-4, -95], [9, -30], [15, -33], [17, -17], [23, -8], [29, -1], [190, 20], [27, 0], [29, -4], [32, -9], [41, -22], [30, -34], [34, -52], [112, -115], [16, -40], [4, -16], [-5, -9], [-34, -15], [-132, -96], [-11, -11], [-14, -22], [-24, -55], [-9, -12], [-43, -37]], [[4211, 12516], [-243, -106], [-26, -20], [-32, -30], [-7, -23], [-17, -33], [-22, -16], [-31, -17], [-50, -18], [-45, -32], [-20, -22], [-43, -68], [-67, -70], [-37, -28], [-32, -16], [-25, -8], [-11, -16], [-8, -22], [-12, -13], [-17, -7], [-56, -10], [-56, -17], [-50, -25], [-53, -21], [-51, -5]], [[3200, 11873], [-52, -47], [-30, -66], [-13, -22], [-13, -15], [-30, -19], [-48, -23], [-15, -12], [-37, -61], [-27, -19], [-24, -32], [-22, -12], [-20, -6], [-21, 1], [-20, 2], [-16, 5], [-11, 4], [-11, 6], [-24, 18], [-11, 11], [-16, 10], [-19, 6], [-30, -1], [-16, -9], [-10, -14], [0, -16], [6, -15], [8, -13], [18, -26], [8, -14], [5, -14], [1, -17], [-3, -18], [-26, -68], [-12, -20], [-41, -59], [-24, -69], [-62, -106]], [[2542, 11123], [-28, -32], [-19, -7], [-31, -8], [-22, -13], [-29, -64], [-4, -23], [6, -16], [9, -12], [6, -21], [1, -28], [-6, -50], [-10, -26], [-13, -17], [-98, -73], [-60, -59]], [[2244, 10674], [-135, 135], [-93, 48], [-34, 28], [-46, 50], [-120, 100], [-40, 44], [-30, 50], [-36, 86], [-55, 103], [-16, 38], [-3, 37], [15, 63], [-1, 32], [-16, 45], [-21, 19], [-18, 9], [-738, -40], [-46, -5]], [[811, 11516], [47, 172], [61, 119], [84, 94], [100, 26], [18, 42], [-10, 36], [-54, 70], [-26, 51], [-14, 51], [-4, 53], [53, 337], [91, 186], [16, 84], [-29, 74], [-82, 68], [-90, 51], [-22, 33], [-20, 223], [12, 53], [45, 83], [121, 160], [103, 182], [35, 42], [47, 37], [28, 10], [23, -4], [18, -17], [14, -24], [24, -20], [29, 13], [30, 23], [27, 13], [53, 13], [25, 2], [32, -7], [23, -21], [40, -68], [14, -15], [35, 1], [16, 15], [11, 21], [20, 18], [17, 10], [15, 11], [20, 11], [34, 10], [47, 29], [55, 104], [39, 39], [27, 10], [89, 14], [42, -3], [32, -15], [28, -15], [27, -8], [74, 15], [76, 43], [69, 62], [52, 70], [39, 31], [101, 36], [30, 37], [2, 74], [16, 23], [51, 8], [24, 51], [87, 47]], [[2244, 10674], [-23, -44], [-21, -26], [-12, -11], [-20, -26], [-15, -30], [-11, -34], [-8, -15], [-45, -45], [-9, -13], [-7, -15], [0, -17], [8, -23], [2, -23], [-5, -35], [-10, -21], [-12, -16], [-20, -25], [-8, -14], [-6, -23], [-4, -77], [-8, -25], [-9, -19], [-22, -23], [-4, -20], [4, -27], [33, -59], [30, -34], [29, -26], [25, -18], [22, -22], [13, -22], [51, -114], [67, -90], [15, -32], [7, -37], [-7, -40], [-11, -24], [-4, -21], [1, -27], [22, -93], [-6, -49], [-26, -67], [-5, -37], [7, -42], [50, -125], [39, -152], [1, 0], [-9, -56]], [[2323, 8840], [-9, 16], [-24, 20], [-30, 11], [-48, 27], [-28, 28], [-84, 123], [-14, 54], [17, 152], [-37, 86], [-75, 52], [-185, 91], [-57, 61], [-104, 174], [-48, 35], [-33, -17], [-33, -77], [-30, -26], [-116, -30], [-47, -22], [-45, -46], [-105, -151], [-61, -12], [-78, 89], [-18, 46], [12, 154], [6, 17], [-1, 16], [-19, 26], [-27, 12], [-61, -3], [-27, 21], [-21, 47], [-7, 46], [-13, 46], [-167, 182], [-49, 38], [-103, 34], [-123, 22], [-97, 38], [-19, 82], [36, 34], [123, 52], [29, 33], [-19, 37], [-127, 159], [-89, 35], [-123, 1], [-106, 21], [-39, 97], [15, 63], [32, 24], [48, -5], [62, -18], [42, 6], [91, 68], [45, 26], [123, 16], [43, 27], [15, 69], [-2, 55], [8, 48], [28, 33], [61, 9], [95, 45], [60, 117], [45, 162]], [[5667, 15005], [83, -262], [197, -621], [185, -585], [44, -341], [55, -427], [46, -364], [39, -304], [-22, -257], [8, -250], [110, -221], [4, -6], [12, -10]], [[6428, 11357], [-1, 0], [-853, 0], [-290, -148], [-138, -52], [-82, -15], [-39, -4], [-101, -43]], [[4924, 11095], [-30, 403], [-68, 235], [3, 46], [14, 39], [13, 28], [4, 25], [-29, 159], [-9, 31], [-19, 24], [-37, 5], [-32, 1], [-28, 3], [-19, 15], [-14, 29], [0, 30], [6, 27], [8, 25], [5, 25], [-7, 16], [-17, 1], [-141, -39], [-13, -8], [-8, -12], [-23, -77], [-11, -24], [-16, -18], [-23, -17], [-27, -5], [-24, 7], [-32, 20], [-22, 19], [-18, 20], [4, 51], [-4, 29], [-37, 58], [-18, 24], [-17, 17], [-24, 60], [-3, 149]], [[6428, 11357], [57, -50], [39, -48], [68, -107], [15, -34], [4, -18], [10, -13], [36, -17], [54, -9], [429, 7], [114, -16], [112, -36], [3, -2]], [[7369, 11014], [-428, -1710]], [[6941, 9304], [-623, 16], [-24, -7], [-14, -7], [-18, -11], [-21, -16], [-70, -67], [-18, -12], [-15, -6], [-40, 22], [-54, 49], [-106, 140], [-112, 179], [-46, 49], [-207, 178], [-147, 185]], [[5426, 9996], [-34, 12], [-42, -1], [-26, -5], [-27, -7], [-116, -53], [-35, -11], [-29, 2], [-17, 14], [-5, 21], [2, 19], [14, 23], [22, 22], [45, 27], [93, 34], [37, 34], [7, 84]], [[5315, 10211], [14, 13], [3, 3], [7, 6], [13, 7], [21, 7], [33, 7], [49, 14], [16, 8], [17, 11], [9, 13], [6, 15], [4, 20], [-5, 11], [-13, 15], [-31, 15], [-19, 16], [-10, 26], [0, 18], [4, 30], [0, 33], [-10, 30], [-23, 15], [-19, 1], [-23, -7], [-119, -57], [-32, -5], [-29, 0], [-25, 6], [-28, 15], [-28, 20], [-151, 132], [-22, 15], [-41, 23], [-4, 10], [13, 21], [17, 24], [8, 48], [1, 35], [-51, 225]], [[4867, 11050], [57, 45]], [[7369, 11014], [102, -56], [342, -278], [293, -238], [292, -309], [360, -380], [306, -324], [287, -304], [225, -382], [290, -325], [351, -394], [121, -135]], [[10338, 7889], [0, 0], [-94, -67], [-75, -24], [-99, -3], [-82, -10], [-166, -35], [-72, -25], [-47, -26], [-51, -50], [-8, -16], [-1, -20], [16, -102], [-4, -42], [-15, -41], [-36, -41], [-33, -17], [-35, -11], [-51, -24], [-88, -81], [-48, -23], [-52, -9], [-49, 9], [-173, 52], [-36, 7], [-25, 9], [-20, 15], [-36, 53], [-36, 35], [-57, 45], [-20, 13], [-18, 7], [-15, 5], [-23, 2], [-10, -5], [-7, -12], [-5, -31], [-7, -14], [-17, -24], [-12, -28], [-2, -17], [1, -16], [3, -16], [16, -38], [1, -6], [-4, -25]], [[8746, 7242], [-921, 1], [-33, 0]], [[7792, 7243], [-112, 74], [-102, 40], [-44, 36], [-389, 460], [-93, 245], [-48, 213], [-22, 308]], [[6982, 8619], [0, 128], [-41, 557]], [[10338, 7889], [122, -136], [163, -146], [39, -44], [19, -52], [35, -637], [22, -27], [43, -6], [72, 2], [-126, -262], [-92, -193], [-162, -336], [-22, -76], [1, -71], [14, -50]], [[10466, 5855], [-1, 0], [-863, 13], [-10, -4], [-13, -10], [-7, -6], [-33, -25], [-35, -19], [-42, -17], [-71, -19], [-45, -6], [-37, 1], [-62, 16], [-171, 63], [-66, 13], [-39, -2], [-37, -11], [-22, -14], [-64, -58], [-33, -17], [-31, -2], [-42, 7], [-55, 17], [-56, 10], [-34, -5], [-27, -16], [-27, -26], [-41, -26], [-33, 1], [-24, 15], [-14, 20], [-28, 50]], [[8403, 5798], [27, 23], [26, 50], [11, 63], [49, 114], [20, 80], [60, 107], [7, 31], [4, 47], [10, 26], [21, 38], [102, 139], [50, 31], [35, 28], [22, 66], [33, 246], [-7, 143], [-38, 132], [-79, 75], [-10, 5]], [[3198, 7832], [59, 27], [30, 41], [33, 19], [30, 24], [75, 103], [10, 32], [10, 132], [10, 48], [17, 44], [24, 36], [30, 29], [43, 24], [232, 99], [39, 26], [40, 35], [71, 46], [40, 11], [60, -5], [32, 8], [81, 29], [31, 5], [32, 0], [27, 12], [27, 26], [17, 51], [16, 140], [15, 42], [26, 31], [48, 23], [128, 15], [35, 16], [40, 30], [42, 39], [38, 48], [51, 35], [52, 45], [34, 86], [20, 37], [28, 38], [50, 50], [43, 53], [48, 43], [30, 38], [92, 152], [13, 37], [16, 30], [17, 21], [40, 19], [85, 53], [62, 52], [59, 89]], [[6982, 8619], [-271, -146], [-252, -168], [-191, -70], [-52, -10], [-35, 3], [-39, 31], [-55, 23], [-24, 13], [-20, 13], [-23, 9], [-29, 7], [-51, 5], [-79, 13], [-18, 0], [-16, -4], [-35, -24], [-1, -1], [-41, -30]], [[5750, 8283], [-28, 36], [-94, 44], [-29, -4], [-25, -15], [-25, -17], [-25, -15], [-27, -5], [-53, 6], [-26, -3], [-331, -105], [-244, -6], [-107, -41], [-25, -31], [-39, -76], [-26, -16], [-352, 42], [-98, 0], [-92, -21], [-194, -105], [-96, -30], [-94, 8], [-180, 123], [-71, -20], [-51, -69], [-28, -88], [-1, -105], [-10, -54], [-33, -21], [-59, 16], [-40, 31], [-28, 41], [-21, 49]], [[3198, 7832], [-47, 115], [-29, 39], [-38, 26], [-90, 38], [-38, 26], [-29, 40], [-17, 38], [-22, 36], [-44, 34], [-142, 57], [-43, 31], [-61, 72], [-169, 304], [-59, 73], [-47, 79]], [[2542, 11123], [64, -38], [17, -14], [15, -21], [0, -23], [-21, -109], [0, -46], [56, -225], [5, -42], [13, -44], [33, -44], [156, -71], [198, -2], [529, 132], [30, 2], [18, -8], [8, -26], [3, -29], [7, -30], [22, -66], [12, -72], [15, -39], [39, -30], [60, -17], [118, 2], [161, 22], [45, 1], [53, -3], [81, -17], [98, -35], [68, -17], [22, -8], [41, -23], [27, -20], [10, -12], [18, -24], [29, -23], [50, -24], [173, 16], [70, 15], [60, 23], [40, 21], [39, 16], [43, 6], [121, -13], [34, 1], [93, 46]], [[7445, 1322], [24, 32], [0, 0], [72, 97], [56, 48], [61, 32], [41, 16], [18, 16], [2, 18], [-8, 25], [-3, 31], [18, 54], [21, 31], [181, 178], [64, 53], [25, 14], [11, 12], [9, 21], [110, 447]], [[8147, 2447], [185, -180], [27, -10], [33, -4], [13, 19], [57, 12], [156, -43], [1, 0]], [[8619, 2241], [-13, -41], [1, -121], [-18, -55], [-46, -42], [-234, -137], [-42, -26], [55, -135], [53, -75], [14, -32], [20, -25], [37, -11], [31, -5], [73, -25], [22, -10], [15, -14], [17, -32], [13, -16], [11, -6], [28, -11], [9, -6], [81, -77], [10, -16], [10, -202], [14, -60], [67, -119], [17, -75], [-84, -63], [-8, -64], [8, -115], [-11, -14], [-21, -14], [-16, -15], [2, -20], [15, -14], [108, -56], [20, -26], [-16, -40], [-36, -55], [-4, -50], [29, -114], [5, -62], [-9, -50], [-18, -42], [-26, -35], [0, 0], [-191, -18], [-338, 10], [-176, 24], [-51, 44], [10, 78], [-109, 147], [26, 95], [73, 52], [85, 37], [74, 46], [39, 83], [10, 98], [-2, 110], [-31, 95], [-75, 49], [-111, 3], [-100, -9], [-95, 14], [-99, 69], [-61, 72], [-29, 27], [-46, 30], [-23, 7], [-51, 8], [-23, 12], [-15, 23], [-11, 58], [-14, 26], [-23, 14]], [[7445, 1322], [-30, 20], [-59, 22], [-52, 27], [-32, 46], [-25, 66], [-27, 42], [-155, 115], [-34, 41], [-60, 102], [-43, 42], [-213, 99], [-25, 8], [-21, 14], [-41, 90], [-17, 28], [-23, 22], [-108, 72], [-42, 36], [-36, 47], [-94, 211], [-75, 68], [-148, 19], [-115, 4], [-66, 59], [-138, 266], [14, 103], [66, 197], [-15, 85], [-57, 70], [-279, 258], [-49, 26], [-111, 47], [-42, 32], [-36, 85], [-6, 108], [17, 106], [36, 77], [95, 90], [59, 41], [57, 24], [69, 18], [37, 19], [1, 2]], [[5622, 4276], [48, -17], [472, -179], [133, -37], [712, -16]], [[6987, 4027], [338, -28], [43, -7], [13, -12], [7, -12], [18, -42], [9, -14], [5, -17], [0, -19], [-13, -28], [-38, -59], [-9, -33], [5, -57], [-4, -26], [-51, -86], [-2, -34], [16, -14], [32, 0], [34, 3], [35, -8], [13, -16], [-2, -16], [-25, -29], [-4, -19], [1, -26], [14, -46], [9, -16], [10, -6], [21, 14]], [[7462, 3374], [192, -272], [447, -451], [5, -22], [1, -31], [-4, -22], [44, -129]], [[5622, 4276], [207, 254], [40, 97], [11, 121]], [[5880, 4748], [396, -37], [395, -37], [360, -34]], [[7031, 4640], [8, -189], [-7, -20], [-18, -25], [-21, -21], [-51, -25], [0, -38], [24, -68], [21, -227]], [[5880, 4748], [-31, 183], [1, 103], [41, 51], [61, 27], [65, 94], [68, 36], [22, 38], [17, 77], [15, 38], [38, 44], [41, 26], [44, 20], [45, 30], [62, 79], [13, 79], [-34, 217]], [[6348, 5890], [47, -4], [157, -27], [199, -55], [16, -1], [55, 8], [25, -1], [27, -6], [47, -15], [133, -22], [50, -14], [40, -16], [28, -21], [27, -25], [16, -43], [7, -34], [3, -30], [-1, -17], [-8, -32], [1, -14], [7, -11], [22, -5], [16, 1], [37, 38]], [[7299, 5544], [709, -33]], [[8008, 5511], [-146, -81]], [[7862, 5430], [-11, -4], [-60, -25], [-105, -12], [-34, -9], [-93, -72], [-32, -11], [-55, -72], [-122, -316], [-51, -71], [-57, -19], [-164, -93], [-39, -34], [-8, -52]], [[6348, 5890], [-36, 232], [8, 52], [31, 34], [-9, 34], [-21, 38], [-7, 43], [19, 43], [69, 84], [24, 45], [6, 51], [-24, 46], [-37, 46], [-30, 49], [-19, 60], [-8, 51], [-1, 225], [-19, 96], [-36, 89], [-56, 99], [-32, 104], [45, 181], [-4, 101], [-55, 97], [-174, 153], [-78, 85], [-109, 198], [-45, 57]], [[7792, 7243], [-17, -38], [7, -44], [14, -51], [2, -30], [-7, -39], [-56, -174], [-15, -28], [-171, -207], [-10, -21], [-3, -14], [10, -28], [2, -21], [0, -26], [-249, -978]], [[10466, 5855], [78, -284], [95, -342], [-9, -125], [0, -3]], [[10630, 5101], [-2, 0], [-382, -13], [-126, -35], [-82, -32], [-48, -47], [-79, -64], [-176, -102], [-40, -16], [-36, -23], [-28, -22], [-43, -95]], [[9588, 4652], [-81, -51], [-25, -20], [-99, -101], [-26, -8], [-42, -6], [-141, -5], [-42, -7], [-45, -20], [-29, -9], [-92, -6]], [[8966, 4419], [-152, 47], [-37, 20], [-40, 32], [-48, 61], [-23, 41], [-27, 68], [-22, 28], [-32, 20], [-53, 20], [-33, 21], [-24, 32], [-3, 30], [6, 43], [-6, 19], [-19, 6], [-38, -19], [-22, -14], [-22, -10], [-23, 7], [-46, -8]], [[8302, 4863], [-44, -1], [-19, 2], [-20, 9], [-15, 15], [-4, 23], [6, 19], [12, 18], [70, 73], [8, 17], [-5, 13], [-22, 4], [-58, 1], [-28, 4], [-32, 22], [-31, 32], [-112, 145], [-76, 58], [-70, 113]], [[8008, 5511], [135, 31], [42, 36], [66, 95], [152, 125]], [[10630, 5101], [-19, -241], [-30, -380], [-21, -270], [-34, -438], [-34, -113]], [[10492, 3659], [-1, -1], [-2, -1], [-3, 1], [-2, 1], [-5, 3], [-4, 1], [-8, 0], [-4, 1], [-5, 1], [-17, 9], [-6, 2], [-6, -1], [-4, -2], [-3, -1], [-9, 1], [-4, 0], [-6, -2], [-12, -4], [-7, -2], [-4, -2], [-3, -3], [-4, -3], [-16, -8], [-3, -1], [-6, -1], [-3, -1], [-3, -2], [-10, -2], [-4, -2], [-4, -3], [-4, -4], [-3, -3], [-5, -3], [-4, -2], [-4, 0], [-5, 0], [-15, 3], [-11, 4], [-6, 4], [-29, 20], [-22, 11], [-59, 41], [-51, 44], [-39, 25], [-14, 3], [-84, -25], [-133, -103], [-17, 18], [-97, 236], [-7, 12], [-4, 4], [-4, 1], [-3, 0], [-3, 0], [-5, 0], [-7, -3], [-2, 0], [-1, 0], [-5, 2], [-3, -1], [-3, 2], [-3, 2], [-3, 7], [-5, 6], [-2, 2], [-2, 4], [-4, 3], [-8, 9], [-3, 1], [-2, -1], [-2, -1], [-2, 0], [-3, 2], [-3, 3], [-3, 1], [-3, 2], [-3, 2], [-7, 5], [-5, 3], [-6, 5], [-5, 9], [-20, 48], [-12, 58], [-21, 280], [-3, 3], [-2, 2], [-3, 2], [-6, 4], [-4, 2], [-3, 2], [-11, 2], [-3, 1], [-2, 0], [-12, 1], [-2, -1], [-3, 0], [-3, 1], [-3, 4], [2, 15], [8, 18], [37, 58], [7, 9], [3, 2], [3, 3], [4, 7], [5, 6], [17, 29], [3, 2], [6, 2], [2, 1], [3, 3], [2, 3], [5, 8], [3, 2], [2, 6], [10, 31], [7, 14], [-1, 6], [-3, 6], [-25, 25]], [[10492, 3659], [-23, -78], [2, -48], [18, -93], [-3, -48], [-32, -65], [-51, -51], [-242, -148], [-35, -16], [-31, -2], [-64, 10], [-34, -1], [-320, -85], [-81, -7], [-16, -18], [-10, -47], [-22, -28], [-42, 1], [-45, 19], [-28, 24], [-25, 51], [-55, 32], [-69, 17], [-66, 3], [-76, -17], [-63, -38], [-233, -186], [-19, -20]], [[8827, 2820], [-1, 1], [-36, 34], [-12, 15], [-5, 27], [6, 33], [24, 53], [22, 30], [20, 21], [18, 13], [29, 27], [13, 87], [-41, 278]], [[8864, 3439], [45, 99], [16, 13], [18, 35], [3, 44], [-47, 505], [2, 112], [65, 172]], [[8827, 2820], [-74, -77], [-62, -114], [-23, -141], [8, -32], [40, -86], [-13, -30], [-79, -84], [-5, -15]], [[7462, 3374], [92, 22], [25, 16], [84, 67], [33, 36], [63, 100], [23, 28], [23, 19], [19, 9], [20, 4], [65, 3], [47, 9], [359, 192]], [[8315, 3879], [124, -2], [32, -8], [22, -13], [22, -30], [25, -25], [119, -98], [20, -28], [19, -43], [18, -65], [15, -28], [26, -30], [39, -35], [68, -35]], [[3200, 11873], [26, -107], [17, -14], [26, -16], [38, -19], [48, -33], [88, -78], [47, -61], [39, -62], [74, -169], [10, -41], [8, -136], [11, -53], [18, -23], [18, -12], [24, -3], [174, -2], [182, 56], [38, 6], [52, -1], [52, -9], [84, -28], [48, -9], [95, 7], [52, -2], [190, -63], [34, -5], [28, 0], [23, 5], [93, 34], [30, 15]], [[8315, 3879], [-75, 125], [-21, 54], [-5, 60], [7, 296], [-8, 88], [-13, 58], [-23, 22], [-4, 42], [3, 34], [126, 205]]], "transform": {"scale": [0.00029862626029669107, 0.00032857749654646495], "translate": [32.66330814700012, -17.135335387999902]}, "objects": {"mwi": {"type": "GeometryCollection", "geometries": [{"arcs": [[0, 1, 2]], "type": "Polygon", "properties": {"id": "CT", "featurecla": "Admin-1 scale rank", "scalerank": 8, "adm1_code": "MWI-1194", "diss_me": 1194, "iso_3166_2": "MW-CT", "wikipedia": "", "iso_a2": "MW", "adm0_sr": 1, "name": "<PERSON><PERSON><PERSON>", "name_alt": "", "name_local": "", "type_en": "District", "code_local": "", "note": "", "hasc_maybe": "KR", "postal": "CT", "sameascity": -99, "woe_name": "<PERSON><PERSON><PERSON>", "latitude": -10.0267, "longitude": 33.8876, "admin": "Malawi", "gn_a1_code": "08", "min_zoom": 8.7}}, {"arcs": [[3, 4, 5, 6, 7, -1]], "type": "Polygon", "properties": {"id": "RU", "featurecla": "Admin-1 scale rank", "scalerank": 8, "adm1_code": "MWI-1903", "diss_me": 1903, "iso_3166_2": "MW-RU", "wikipedia": "", "iso_a2": "MW", "adm0_sr": 1, "name": "<PERSON><PERSON><PERSON>", "name_alt": "<PERSON><PERSON><PERSON>", "name_local": "", "type_en": "District", "code_local": "", "note": "", "hasc_maybe": "", "postal": "RU", "sameascity": -99, "woe_name": "<PERSON><PERSON><PERSON>", "latitude": -10.7114, "longitude": 33.953, "admin": "Malawi", "gn_a1_code": "21", "min_zoom": 8.7}}, {"arcs": [[8, 9, 10, -5]], "type": "Polygon", "properties": {"id": "NA", "featurecla": "Admin-1 scale rank", "scalerank": 8, "adm1_code": "MWI-1901", "diss_me": 1901, "iso_3166_2": "MW-NB", "wikipedia": "", "iso_a2": "MW", "adm0_sr": 1, "name": "Nkhata Bay", "name_alt": "Chinteche", "name_local": "", "type_en": "District", "code_local": "", "note": "", "hasc_maybe": "", "postal": "NA", "sameascity": 7, "woe_name": "Nkhata Bay", "latitude": -11.6281, "longitude": 34.1604, "admin": "Malawi", "gn_a1_code": "17", "min_zoom": 8.7}}, {"arcs": [[[11]], [[12]]], "type": "MultiPolygon", "properties": {"id": "LK", "featurecla": "Admin-1 scale rank", "scalerank": 8, "adm1_code": "MWI-5509", "diss_me": 5509, "iso_3166_2": "MW-LK", "wikipedia": "", "iso_a2": "MW", "adm0_sr": 1, "name": "Likoma", "name_alt": "", "name_local": "", "type_en": "District", "code_local": "", "note": "", "hasc_maybe": "", "postal": "NA", "sameascity": 7, "woe_name": "Nkhata Bay", "latitude": -12.0727, "longitude": 34.706, "admin": "Malawi", "gn_a1_code": "27", "min_zoom": 8.7}}, {"arcs": [[-2, -8, 13]], "type": "Polygon", "properties": {"id": "CT", "featurecla": "Admin-1 scale rank", "scalerank": 8, "adm1_code": "MWI-1873", "diss_me": 1873, "iso_3166_2": "MW-CT", "wikipedia": "", "iso_a2": "MW", "adm0_sr": 1, "name": "<PERSON><PERSON><PERSON>", "name_alt": "", "name_local": "", "type_en": "District", "code_local": "", "note": "", "hasc_maybe": "", "postal": "CT", "sameascity": -99, "woe_name": "<PERSON><PERSON><PERSON>", "latitude": -9.88572, "longitude": 33.4345, "admin": "Malawi", "gn_a1_code": "04", "min_zoom": 8.7}}, {"arcs": [[-6, -11, 14, 15, 16]], "type": "Polygon", "properties": {"id": "MZ", "featurecla": "Admin-1 scale rank", "scalerank": 8, "adm1_code": "MWI-1900", "diss_me": 1900, "iso_3166_2": "MW-MZ", "wikipedia": "", "iso_a2": "MW", "adm0_sr": 1, "name": "Mzimba", "name_alt": "", "name_local": "", "type_en": "District", "code_local": "", "note": "", "hasc_maybe": "", "postal": "MZ", "sameascity": 7, "woe_name": "Mzimba", "latitude": -11.8792, "longitude": 33.6587, "admin": "Malawi", "gn_a1_code": "15", "min_zoom": 8.7}}, {"arcs": [[-16, 17, 18, 19, 20, 21, 22]], "type": "Polygon", "properties": {"id": "KS", "featurecla": "Admin-1 scale rank", "scalerank": 8, "adm1_code": "MWI-1899", "diss_me": 1899, "iso_3166_2": "MW-KS", "wikipedia": "", "iso_a2": "MW", "adm0_sr": 1, "name": "<PERSON><PERSON><PERSON>", "name_alt": "", "name_local": "", "type_en": "District", "code_local": "", "note": "", "hasc_maybe": "", "postal": "KS", "sameascity": -99, "woe_name": "<PERSON><PERSON><PERSON>", "latitude": -12.9319, "longitude": 33.4535, "admin": "Malawi", "gn_a1_code": "09", "min_zoom": 8.7}}, {"arcs": [[-22, 23, 24]], "type": "Polygon", "properties": {"id": "MC", "featurecla": "Admin-1 scale rank", "scalerank": 8, "adm1_code": "MWI-1911", "diss_me": 1911, "iso_3166_2": "MW-MC", "wikipedia": "", "iso_a2": "MW", "adm0_sr": 1, "name": "<PERSON><PERSON><PERSON>", "name_alt": "Fort Manning", "name_local": "", "type_en": "District", "code_local": "", "note": "", "hasc_maybe": "", "postal": "MC", "sameascity": 7, "woe_name": "<PERSON><PERSON><PERSON>", "latitude": -13.696, "longitude": 33.0113, "admin": "Malawi", "gn_a1_code": "13", "min_zoom": 8.7}}, {"arcs": [[25, 26, 27, -18, -15, -10]], "type": "Polygon", "properties": {"id": "NK", "featurecla": "Admin-1 scale rank", "scalerank": 8, "adm1_code": "MWI-1902", "diss_me": 1902, "iso_3166_2": "MW-NK", "wikipedia": "", "iso_a2": "MW", "adm0_sr": 1, "name": "Nkhotakota", "name_alt": "Kota Kota", "name_local": "", "type_en": "District", "code_local": "", "note": "", "hasc_maybe": "", "postal": "NK", "sameascity": 7, "woe_name": "Nkhotakota", "latitude": -12.8398, "longitude": 34.1715, "admin": "Malawi", "gn_a1_code": "18", "min_zoom": 8.7}}, {"arcs": [[28, 29, 30, 31, 32, 33, -27]], "type": "Polygon", "properties": {"id": "SA", "featurecla": "Admin-1 scale rank", "scalerank": 8, "adm1_code": "MWI-1914", "diss_me": 1914, "iso_3166_2": "MW-SA", "wikipedia": "", "iso_a2": "MW", "adm0_sr": 1, "name": "Salima", "name_alt": "", "name_local": "", "type_en": "District", "code_local": "", "note": "", "hasc_maybe": "", "postal": "SA", "sameascity": -99, "woe_name": "Salima", "latitude": -13.7572, "longitude": 34.4903, "admin": "Malawi", "gn_a1_code": "22", "min_zoom": 8.7}}, {"arcs": [[34, 35, 36, 37, 38, -30]], "type": "Polygon", "properties": {"id": "MG", "featurecla": "Admin-1 scale rank", "scalerank": 8, "adm1_code": "MWI-1910", "diss_me": 1910, "iso_3166_2": "MW-MG", "wikipedia": "", "iso_a2": "MW", "adm0_sr": 1, "name": "Mango<PERSON>", "name_alt": "Mangoche|Fort Johnston", "name_local": "", "type_en": "District", "code_local": "", "note": "", "hasc_maybe": "", "postal": "MG", "sameascity": 7, "woe_name": "Mango<PERSON>", "latitude": -14.374, "longitude": 35.3501, "admin": "Malawi", "gn_a1_code": "12", "min_zoom": 8.7}}, {"arcs": [[39, 40, 41, -36]], "type": "Polygon", "properties": {"id": "MA", "featurecla": "Admin-1 scale rank", "scalerank": 8, "adm1_code": "MWI-1904", "diss_me": 1904, "iso_3166_2": "MW-MH", "wikipedia": "", "iso_a2": "MW", "adm0_sr": 1, "name": "<PERSON><PERSON><PERSON>", "name_alt": "Kasupe|Kasupi|Liwonde", "name_local": "", "type_en": "District", "code_local": "", "note": "", "hasc_maybe": "", "postal": "MA", "sameascity": 7, "woe_name": "<PERSON><PERSON><PERSON>", "latitude": -14.9533, "longitude": 35.5384, "admin": "Malawi", "gn_a1_code": "28", "min_zoom": 8.7}}, {"arcs": [[42, -31, -39, 43, 44]], "type": "Polygon", "properties": {"id": "DE", "featurecla": "Admin-1 scale rank", "scalerank": 8, "adm1_code": "MWI-1907", "diss_me": 1907, "iso_3166_2": "MW-DE", "wikipedia": "", "iso_a2": "MW", "adm0_sr": 1, "name": "Dedza", "name_alt": "", "name_local": "", "type_en": "District", "code_local": "", "note": "", "hasc_maybe": "", "postal": "DE", "sameascity": 7, "woe_name": "Dedza", "latitude": -14.2064, "longitude": 34.1833, "admin": "Malawi", "gn_a1_code": "06", "min_zoom": 8.7}}, {"arcs": [[45, -24, -21, 46, -32, -43]], "type": "Polygon", "properties": {"id": "LI", "featurecla": "Admin-1 scale rank", "scalerank": 8, "adm1_code": "MWI-1909", "diss_me": 1909, "iso_3166_2": "MW-LI", "wikipedia": "", "iso_a2": "MW", "adm0_sr": 1, "name": "Lilongwe", "name_alt": "", "name_local": "", "type_en": "District", "code_local": "", "note": "", "hasc_maybe": "", "postal": "LI", "sameascity": 7, "woe_name": "Lilongwe", "latitude": -14.0415, "longitude": 33.7683, "admin": "Malawi", "gn_a1_code": "11", "min_zoom": 8.7}}, {"arcs": [[47, 48, 49]], "type": "Polygon", "properties": {"id": "NS", "featurecla": "Admin-1 scale rank", "scalerank": 8, "adm1_code": "MWI-1919", "diss_me": 1919, "iso_3166_2": "MW-NS", "wikipedia": "", "iso_a2": "MW", "adm0_sr": 1, "name": "<PERSON><PERSON><PERSON>", "name_alt": "Port Herald", "name_local": "", "type_en": "District", "code_local": "", "note": "", "hasc_maybe": "", "postal": "NS", "sameascity": 7, "woe_name": "<PERSON><PERSON><PERSON>", "latitude": -17.0479, "longitude": 35.1003, "admin": "Malawi", "gn_a1_code": "19", "min_zoom": 8.7}}, {"arcs": [[50, 51, 52, 53, -48]], "type": "Polygon", "properties": {"id": "CK", "featurecla": "Admin-1 scale rank", "scalerank": 8, "adm1_code": "MWI-1916", "diss_me": 1916, "iso_3166_2": "MW-CK", "wikipedia": "", "iso_a2": "MW", "adm0_sr": 1, "name": "Chikwawa", "name_alt": "", "name_local": "", "type_en": "District", "code_local": "", "note": "", "hasc_maybe": "", "postal": "CK", "sameascity": -99, "woe_name": "Chikwawa", "latitude": -16.1524, "longitude": 34.6636, "admin": "Malawi", "gn_a1_code": "02", "min_zoom": 8.7}}, {"arcs": [[54, 55, 56, -52]], "type": "Polygon", "properties": {"id": "MN", "featurecla": "Admin-1 scale rank", "scalerank": 8, "adm1_code": "MWI-1922", "diss_me": 1922, "iso_3166_2": "MW-MW", "wikipedia": "", "iso_a2": "MW", "adm0_sr": 1, "name": "<PERSON><PERSON><PERSON>", "name_alt": "", "name_local": "", "type_en": "District", "code_local": "", "note": "", "hasc_maybe": "", "postal": "MW", "sameascity": 7, "woe_name": "<PERSON><PERSON><PERSON>", "latitude": -15.6394, "longitude": 34.5126, "admin": "Malawi", "gn_a1_code": "25", "min_zoom": 8.7}}, {"arcs": [[57, 58, 59, 60, 61, -56]], "type": "Polygon", "properties": {"id": "NN", "featurecla": "Admin-1 scale rank", "scalerank": 8, "adm1_code": "MWI-5510", "diss_me": 5510, "iso_3166_2": "MW-NE", "wikipedia": "", "iso_a2": "MW", "adm0_sr": 1, "name": "<PERSON>eno", "name_alt": "", "name_local": "", "type_en": "District", "code_local": "", "note": "", "hasc_maybe": "", "postal": "MW", "sameascity": 7, "woe_name": "<PERSON><PERSON><PERSON>", "latitude": -15.4366, "longitude": 34.6946, "admin": "Malawi", "gn_a1_code": "31", "min_zoom": 8.7}}, {"arcs": [[62, -44, -38, 63, -59]], "type": "Polygon", "properties": {"id": "NU", "featurecla": "Admin-1 scale rank", "scalerank": 8, "adm1_code": "MWI-1912", "diss_me": 1912, "iso_3166_2": "MW-NU", "wikipedia": "", "iso_a2": "MW", "adm0_sr": 1, "name": "<PERSON><PERSON><PERSON>", "name_alt": "<PERSON><PERSON><PERSON>", "name_local": "", "type_en": "District", "code_local": "", "note": "", "hasc_maybe": "", "postal": "NU", "sameascity": 7, "woe_name": "<PERSON><PERSON><PERSON>", "latitude": -14.7843, "longitude": 34.6861, "admin": "Malawi", "gn_a1_code": "16", "min_zoom": 8.7}}, {"arcs": [[64, 65, 66, 67, 68, -61, 69, -41]], "type": "Polygon", "properties": {"id": "ZO", "featurecla": "Admin-1 scale rank", "scalerank": 8, "adm1_code": "MWI-1920", "diss_me": 1920, "iso_3166_2": "MW-ZO", "wikipedia": "", "iso_a2": "MW", "adm0_sr": 1, "name": "Zomba", "name_alt": "", "name_local": "", "type_en": "District", "code_local": "", "note": "", "hasc_maybe": "", "postal": "ZO", "sameascity": -99, "woe_name": "Zomba", "latitude": -15.4526, "longitude": 35.4256, "admin": "Malawi", "gn_a1_code": "23", "min_zoom": 8.7}}, {"arcs": [[70, 71, -66]], "type": "Polygon", "properties": {"id": "PH", "featurecla": "Admin-1 scale rank", "scalerank": 8, "adm1_code": "MWI-1924", "diss_me": 1924, "iso_3166_2": "MW-PH", "wikipedia": "", "iso_a2": "MW", "adm0_sr": 1, "name": "Phalombe", "name_alt": "", "name_local": "", "type_en": "District", "code_local": "", "note": "", "hasc_maybe": "", "postal": "PH", "sameascity": -99, "woe_name": "Phalombe", "latitude": -15.6883, "longitude": 35.667, "admin": "Malawi", "gn_a1_code": "30", "min_zoom": 8.7}}, {"arcs": [[72, 73, 74, -67, -72]], "type": "Polygon", "properties": {"id": "MJ", "featurecla": "Admin-1 scale rank", "scalerank": 8, "adm1_code": "MWI-1923", "diss_me": 1923, "iso_3166_2": "MW-MU", "wikipedia": "", "iso_a2": "MW", "adm0_sr": 1, "name": "<PERSON><PERSON><PERSON>", "name_alt": "Mlange|Mlanje", "name_local": "", "type_en": "District", "code_local": "", "note": "", "hasc_maybe": "", "postal": "MJ", "sameascity": 7, "woe_name": "<PERSON><PERSON><PERSON>", "latitude": -15.9868, "longitude": 35.5397, "admin": "Malawi", "gn_a1_code": "29", "min_zoom": 8.7}}, {"arcs": [[75, -49, -54, 76, 77, -74]], "type": "Polygon", "properties": {"id": "TH", "featurecla": "Admin-1 scale rank", "scalerank": 8, "adm1_code": "MWI-1918", "diss_me": 1918, "iso_3166_2": "MW-TH", "wikipedia": "", "iso_a2": "MW", "adm0_sr": 1, "name": "<PERSON><PERSON><PERSON>", "name_alt": "<PERSON><PERSON>", "name_local": "", "type_en": "District", "code_local": "", "note": "", "hasc_maybe": "", "postal": "TH", "sameascity": -99, "woe_name": "<PERSON><PERSON><PERSON>", "latitude": -16.1135, "longitude": 35.1071, "admin": "Malawi", "gn_a1_code": "05", "min_zoom": 8.7}}, {"arcs": [[-33, -47, -20, 78]], "type": "Polygon", "properties": {"id": "DO", "featurecla": "Admin-1 scale rank", "scalerank": 8, "adm1_code": "MWI-1908", "diss_me": 1908, "iso_3166_2": "MW-DO", "wikipedia": "", "iso_a2": "MW", "adm0_sr": 1, "name": "<PERSON><PERSON>", "name_alt": "", "name_local": "", "type_en": "District", "code_local": "", "note": "", "hasc_maybe": "", "postal": "DO", "sameascity": -99, "woe_name": "<PERSON><PERSON>", "latitude": -13.625, "longitude": 33.8651, "admin": "Malawi", "gn_a1_code": "07", "min_zoom": 8.7}}, {"arcs": [[-70, -60, -64, -37, -42]], "type": "Polygon", "properties": {"id": "BA", "featurecla": "Admin-1 scale rank", "scalerank": 8, "adm1_code": "MWI-1915", "diss_me": 1915, "iso_3166_2": "MW-BA", "wikipedia": "", "iso_a2": "MW", "adm0_sr": 1, "name": "<PERSON><PERSON><PERSON>", "name_alt": "", "name_local": "", "type_en": "District", "code_local": "", "note": "", "hasc_maybe": "", "postal": "BA", "sameascity": -99, "woe_name": "<PERSON><PERSON><PERSON>", "latitude": -15.037, "longitude": 35.0792, "admin": "Malawi", "gn_a1_code": "26", "min_zoom": 8.7}}, {"arcs": [[-75, -78, 79, -68]], "type": "Polygon", "properties": {"id": "CR", "featurecla": "Admin-1 scale rank", "scalerank": 8, "adm1_code": "MWI-1917", "diss_me": 1917, "iso_3166_2": "MW-CR", "wikipedia": "", "iso_a2": "MW", "adm0_sr": 1, "name": "Chiradzulu", "name_alt": "", "name_local": "", "type_en": "District", "code_local": "", "note": "", "hasc_maybe": "", "postal": "CR", "sameascity": 9, "woe_name": "Chiradzulu", "latitude": -15.7358, "longitude": 35.2224, "admin": "Malawi", "gn_a1_code": "03", "min_zoom": 8.7}}, {"arcs": [[-80, -77, -53, -57, -62, -69]], "type": "Polygon", "properties": {"id": "BL", "featurecla": "Admin-1 scale rank", "scalerank": 8, "adm1_code": "MWI-1921", "diss_me": 1921, "iso_3166_2": "MW-BL", "wikipedia": "", "iso_a2": "MW", "adm0_sr": 1, "name": "Blantyre", "name_alt": "", "name_local": "", "type_en": "District", "code_local": "", "note": "", "hasc_maybe": "", "postal": "BL", "sameascity": 7, "woe_name": "Blantyre", "latitude": -15.6875, "longitude": 34.9413, "admin": "Malawi", "gn_a1_code": "24", "min_zoom": 8.7}}, {"arcs": [[-28, -34, -79, -19]], "type": "Polygon", "properties": {"id": "NI", "featurecla": "Admin-1 scale rank", "scalerank": 8, "adm1_code": "MWI-1913", "diss_me": 1913, "iso_3166_2": "MW-NI", "wikipedia": "", "iso_a2": "MW", "adm0_sr": 1, "name": "<PERSON><PERSON><PERSON>", "name_alt": "Nchisi", "name_local": "", "type_en": "District", "code_local": "", "note": "", "hasc_maybe": "", "postal": "NI", "sameascity": -99, "woe_name": "<PERSON><PERSON><PERSON>", "latitude": -13.2659, "longitude": 33.8763, "admin": "Malawi", "gn_a1_code": "20", "min_zoom": 8.7}}]}}}