import {
  generateExcel,
  generateTAGVHExcel,
  generateDisasterSummaryExcel
} from "./generateExcel";
import utils from "./dashboard";

function catchError(data = "") {
  if (typeof data === undefined) {
    return "";
  }

  return data;
};
function fetchDisaster(district) {
  return 0;
};



export const exportAllDisasterData=(type,data=[],admin2sData=[],gvhDATA=[],allforms=[])=>{
  function formatedDate(data) {
    let finalDate = dateformat(data, "dd-mm-yyyy");
    return finalDate;
  }
  let TAData=[]
  let districtsCopy=[]
    if (type === "TA") {
      for (let i in data) {

        try {
          data[i].all.sectors.wash === undefined
            ? (data[i].all.sectors.wash = {})
            : data[i].all.sectors.wash;
          data[i].all.sectors.shelter === undefined
            ? (data[i].all.sectors.wash = {})
            : data[i].all.sectors.shelter;
          data[i].all.sectors.displaced === undefined
            ? (data[i].all.sectors.wash = {})
            : data[i].all.sectors.displaced;
          data[i].all.sectors.agriculture === undefined
            ? (data[i].all.sectors.agriculture = {})
            : data[i].all.sectors.agriculture;
          data[i].all.sectors.education === undefined
            ? (data[i].all.sectors.education = {})
            : data[i].all.sectors.education;
          data[i].all.sectors.nutrition === undefined
            ? (data[i].all.sectors.nutrition = {})
            : data[i].all.sectors.nutrition;
          data[i].all.sectors.protection === undefined
            ? (data[i].all.sectors.protection = {})
            : data[i].all.sectors.protection;
          data[i].all.sectors.logistics === undefined
            ? (data[i].all.sectors.logistics = {})
            : data[i].all.sectors.logistics;
          data[i].all.sectors.health === undefined
            ? (data[i].all.sectors.health = {})
            : data[i].all.sectors.health;
          data[i].all.sectors.environment === undefined
            ? (data[i].all.sectors.environment = {})
            : data[i].all.sectors.environment;
          data[i].all.sectors.food === undefined
            ? (data[i].all.sectors.food = {})
            : data[i].all.sectors.food;
          data[i].all.sectors.livelihoods === undefined
            ? (data[i].all.sectors.livelihoods = {})
            : data[i].all.sectors.livelihoods;


          //this.TaData

           TAData.push({
            // DISASTERID: data[i].all.dinrFormId,
            Disaster: data[i].all.dinrform.disaster,
            District: data[i].all.dinrform.district.admin2_name_en || data[i].all.dinrform.district,
            "TA PCODE": data[i].all.admin3.admin3Pcode,
            TA: data[i].all.admin3.admin3Name_en,
            "GVHS affected": data[i].all.gvhsAffected,
            "Villages Affected" : data[i].all.villages,
            "Camps": data[i].all.camps,


            // "GVH affected": "gvhs.name",

            "Disaster Start Date": data[i].all.dinrform.dodFrom,
            "Disaster End Date": data[i].all.dinrform.dodTo,
            "ACPC Assessment Date": data[i].all.dinrform.doaAcpc,
            "DCPC Assessement Date": data[i].all.dinrform.doaDcpc,
            "Submission Date": data[i].all.dinrform.date,
            "Reported to DEC": data[i].all.dinrform.date,

            //shelter
            "number of injured females(shelter)": catchError(
              data[i].all.shelter_people_injured_female
            ),
            "number of injured males (shelter)": catchError(
              data[i].all.shelter_people_injured_male
            ),

            "Total people injured (shelter)": catchError(
              data[i].all.shelter_injured
            ),

            "number of dead females (shelter)": catchError(
              data[i].all.shelter_people_dead_female
            ),
            "number of dead males (shelter)": catchError(
              data[i].all.shelter_people_dead_male
            ),

            "Total people dead (shelter)": catchError(
              data[i].all.shelter_dead
            ),


            "number of females missing (shelter)": catchError(
              data[i].all.shelter_female_missing
            ),
            "number of males missing (shelter)": catchError(
              data[i].all.shelter_male_missing
            ),

            "Total people missing (shelter)": catchError(
              data[i].all.shelter_total_people_missing
            ),



            "Houses partly damaged (shelter)": catchError(
              data[i].all.sectors.shelter.partly_damaged
            ),
            "Houses underwater (shelter)": catchError(
              data[i].all.sectors.shelter.under_water
            ),
            "Houses completely (shelter)": catchError(
              data[i].all.sectors.shelter.completely_damaged
            ),

            "number of males without shelter (shelter)": catchError(
              data[i].all.shelter_without_shelter_male
            ),
            "number of females without shelter (shelter)": catchError(
              data[i].all.shelter_without_shelter_female
            ),
            "Total people without shelter (shelter)": catchError(
              data[i].all.people_without_shelter
            ),
            "number of female HH affected (shelter)": catchError(
              data[i].all.shelter_fhh_affected
            ),
            "number of male HH affected(shelter)": catchError(
              data[i].all.shelter_mhh_affected
            ),

            "Total Households affected (shelter)": catchError(
              data[i].all.shelter_affected_hh
            ),

            "Houses fully brown roofs(shelter)": catchError(
              data[i].all.houses_fully_brown_roofs
            ),
            "Houses partly brown roofs(shelter)": catchError(
              data[i].all.houses_partly_brown_roofs
            ),

            "Houses Wall damaged(shelter)": catchError(
              data[i].all.wall_damaged || 0
            ),
            "Houses burnt(shelter)": catchError(
              data[i].all.burnt || 0
            ),

            "Structures damaged (shelter)": catchError(
              data[i].all.other_structures_damaged
            ),

            "Total structures damaged (shelter)": catchError(
              data[i].all.total_structures_damaged
            ),
            "Non-Food Items Needs (shelter)": catchError(
              data[i].all.response_needed_non_food
            ),

            "Urgent response needed (shelter)": catchError(
              data[i].all.sectors.shelter.urgent_response_needed
            ),
            "General response needed (shelter)": catchError(
              data[i].all.response_needed_emergency
            ),


            //displaced

            "number of displaced male Households (displaced)": catchError(
              data[i].all.PeopleAffectedrows_male
            ),
            "number of displaced females Households (displaced)": catchError(
              data[i].all.PeopleAffectedrows_female
            ),

            "Total displaced Households (displaced)": catchError(
              data[i].all.displaced_hh
            ),

            "number of male HH accomodated (displaced)": catchError(
              data[i].all.displaced_households_accommodated_male
            ),
            "number of female HH accomodated (displaced)": catchError(
              data[i].all.displaced_households_accommodated_female
            ),

            "number of males disaggregated (displaced)": catchError(
              data[i].all.displaced_disagregated_male
            ),
            "number of females disaggregated (displaced)": catchError(
              data[i].all.displaced_disagregated_female
            ),
            "total disaggregated (displaced)": catchError(
              data[i].all.total_disaggregated_diplaced
            ),

            // "number of males accomodated (displaced)": catchError(
            //   data[i].all.displaced_individuals_accommodated_male
            // ),
            // "number of females accomodated (displaced)": catchError(
            //   data[i].all.displaced_individuals_accommodated_female
            // ),
            "Non food items needs (displaced)": catchError(
              data[i].all.displaced_non_food_items_needs
            ),
            "Emergency needs (displaced)": catchError(
              data[i].all.displaced_response_needed_emergency
            ),
            "Urgent response needed (displaced)": catchError(
              data[i].all.sectors.displaced.urgent_response_needed
            ),
            "general response needed (displaced)": catchError(
              data[i].all.sectors.displaced.response_needed
            ),

            //agriculture
            "food items damaged (KGs) (agriculture)": catchError(
              data[i].all.food_item_damage
            ),
            "number of crop hectares submerged  (agriculture)": catchError(
              data[i].all.hectares_submerged
            ),
            "number crop hectares washed off  (agriculture)": catchError(
              data[i].all.hectares_washed_away
            ),
            "number of households whose crops are impacted (agriculture)": catchError(
              data[i].all.impact_on_crops_hh_affected
            ),
            "number of crop hectares damaged in affected households  (agriculture)": catchError(
              data[i].all.impact_on_crops_hectares_damaged
            ),

            "hh affected per impacted livestock  (agriculture)": catchError(
              data[i].all.impact_on_livestock_hh
            ),
            "number of impacted livestock  (agriculture)": catchError(
              data[i].all.impact_on_livestock_la
            ),

            "Urgent response needed (agriculture)": catchError(
              data[i].all.sectors.agriculture.response_needed
            ),

            "general response needed (agriculture)": catchError(
              data[i].all.sectors.agriculture.response_needed
            ),

          //livelihoods

          "number severely affected (livelihoods)": catchError(
            data[i].all.livelihoods_severely_affected
          ),

          "number slightly affected (livelihoods)": catchError(
            data[i].all.livelihoods_slightly_affected
          ),

          "Urgent response needed (livelihoods)": catchError(
            data[i].all.sectors.livelihoods.response_needed
          ),

          "general response needed (livelihoods)": catchError(
            data[i].all.sectors.livelihoods.response_needed
          ),

          "Urgent response needed (environment)": catchError(
            data[i].all.sectors.environment.urgent_response_needed
          ),

          "general response needed (environment)": catchError(
            data[i].all.sectors.environment.response_needed
          ),

            //wash

            "FHH with safe water (wash)": catchError(
              data[i].all.sectors.wash.with_safe_water_fhh
            ),

            "MHH with safe water (wash)": catchError(
              data[i].all.sectors.wash.with_safe_water_mhh
            ),
            "FHH with toilet access (wash)": catchError(
              data[i].all.sectors.wash.access_to_toilets_fhh
            ),
            "MHH with toilet access (wash)": catchError(
              data[i].all.sectors.wash.access_to_toilets_mhh
            ),
            "FHH risking contamination (wash)": catchError(
              data[i].all.sectors.wash.risk_contamination_fhh
            ),
            "MHH risking contamination  (wash)": catchError(
              data[i].all.sectors.wash.risk_contamination_mhh
            ),

            "Total risking contamination  (wash)": catchError(
              data[i].all.sectors.wash.risk_contamination_fhh +
              data[i].all.sectors.wash.risk_contamination_mhh
            ),

            "Wash needs (wash)": catchError(
              data[i].all.wash_response_needed
            ),

            "Urgent response needed (wash)": catchError(
              data[i].all.sectors.wash.urgent_response_needed
            ),

            "general response needed (wash)": catchError(
              data[i].all.sectors.wash.response_needed
            ),

            //health

            "number of facilities partially functioning (health)": catchError(
              data[i].all.health_partially_functioning
            ),
            "number of  facilities on verge of closing (health)": catchError(
              data[i].all.health_verge_of_closing
            ),

            "number  facilities closed (health)": catchError(
              data[i].all.health_closed
            ),

            "state of medical supply availability (health)": catchError(
              data[i].all.medical_supply_availability
            ),

            "state of health personnel availability (health)": catchError(
              data[i].all.health_personel_availability
            ),
            "Urgent response needed (health)": catchError(
              data[i].all.sectors.health.urgent_response_needed
            ),

            "general response needed (health)": catchError(
              data[i].all.sectors.health.response_needed
            ),

            //food

            "is food available? (food)": catchError(
              data[i].all.is_food_available_food
            ),

            "No. of FHH with 1-2 month Food Availability (food)": catchError(
              data[i].all.food_1_2_months
            ),
            "No. of FHH with less than 1 month Food Availability (food)": catchError(
              data[i].all.food_less_1_month
            ),

            "No. of FHH with more than 2 months Food Availability (food)": catchError(
              data[i].all.food_2_months
            ),

            "No. of FHH who lost Food Stock (food)": catchError(
              data[i].all.food_stock_lost
            ),

            "No. of MHH with 1-2 month Food Availability (food)": catchError(
              data[i].all.food_1_2_months_male
            ),
            "No. of MHH with less than 1 month Food Availability (food)": catchError(
              data[i].all.food_less_1_month_male
            ),

            "No. of MHH with more than 2 months Food Availability (food)": catchError(
              data[i].all.food_2_months_male
            ),

            "No. of MHH who lost Food Stock (food)": catchError(
              data[i].all.food_stock_lost_male
            ),

            "Urgent response needed (food)": catchError(
              data[i].all.sectors.food.urgent_response_needed
            ),
            "general response needed (food)": catchError(
              data[i].all.sectors.food.response_needed
            ),

            //logistics

            "state of access to main roads (logistics)": catchError(
              data[i].all.road_access
            ),

            "Urgent response needed (logistics)": catchError(
              data[i].all.sectors.logistics.urgent_response_needed
            ),

            "general response needed (logistics)": catchError(
              data[i].all.sectors.logistics.response_needed
            ),


            //protection

            "impacted females (protection)": catchError(
              data[i].all.impact_on_vulnerable_persons_females
            ),

            "impacted males (protection)": catchError(
              data[i].all.impact_on_vulnerable_persons_males
            ),

            "Urgent response needed (protection)": catchError(
              data[i].all.sectors.protection.urgent_response_needed
            ),

            "general response needed (protection)": catchError(
              data[i].all.sectors.protection.response_needed
            ),

            //nutrition

            "number of affected males (nutrition)": catchError(
              data[i].all.nutrition_affected_pop_male
            ),
            "number of affected females (nutrition)": catchError(
              data[i].all.nutrition_affected_pop_female
            ),

            "general response needed (nutrition)": catchError(
              data[i].all.sectors.nutrition.response_needed
            ),

            //education

            "number of school buildings functioning (education)": catchError(
              data[i].all.education_building_functioning
            ),
            "number of school buildings underwater (education)": catchError(
              data[i].all.education_underwater
            ),
            "number of school buildings completely damaged (education)": catchError(
              data[i].all.education_completely_damaged
            ),

            "number of school buildings partially functioning (education)": catchError(
              data[i].all.education_building_partly_functioning
            ),

            "number of school buildings closed  (education)": catchError(
              data[i].all.education_closed_buildings
            ),

            "males of out school (education)": catchError(
              data[i].all.education_males_out_of_school
            ),
            "females of out school (education)": catchError(
              data[i].all.education_females_out_of_school
            ),


            sheet: "TA-BASED DATA"
          });
        } catch (error) {

        }
      }

      generateTAGVHExcel(TAData);
    } else if (type === "EXCEL_data") {
      let mytotal = {};
      mytotal.admin2_name_en = "TOTAL";
      districtsCopy = [...admin2sData];
      districtsCopy.push(mytotal);

      var data = districtsCopy.map(item => {
        return {
          district: item.admin2_name_en,
          Floods: fetchDisaster(item.admin2_name_en),
          "Strong winds (with no rains)": fetchDisaster(
            item.admin2_name_en
          ),
          Epidemic: fetchDisaster(item.admin2_name_en),
          Accident: fetchDisaster(item.admin2_name_en),
          "Disease and Pest outbreak": fetchDisaster(
            item.admin2_name_en
          ),
          Explosion: fetchDisaster(item.admin2_name_en),
          "Severe storms": fetchDisaster(item.admin2_name_en),
          "water pollution": fetchDisaster(item.admin2_name_en),
          "Extreme temperatures": fetchDisaster(item.admin2_name_en),
          "Severe storms": fetchDisaster(item.admin2_name_en),
          Earthquake: fetchDisaster(item.admin2_name_en),
          Hailstorm: fetchDisaster(item.admin2_name_en),
          "Heavy rains": fetchDisaster(item.admin2_name_en),
          Lightening: fetchDisaster(item.admin2_name_en),
          "Stormy rains": fetchDisaster(item.admin2_name_en),
          "Heavy/Torrential rains": fetchDisaster(item.admin2_name_en),
          Landslides: fetchDisaster(item.admin2_name_en),
          "Human-animal conflicts": fetchDisaster(item.admin2_name_en),
          Fire: fetchDisaster(item.admin2_name_en),
          "Total No. of Injuries": fetchDisaster(item.admin2_name_en),
          "Total No. of Deaths": fetchDisaster(item.admin2_name_en),
          "Displaced Households": fetchDisaster(item.admin2_name_en),

          sheet: "Cummulative Number of Disasters"
        };
      });

      let totals = 0;

      for (let i in allforms) {
        try {
          data.forEach((element, index) => {
            if (
              element.district == allforms[i].district.admin2_name_en
            ) {
              //console.log("i have matched", allforms[i].disaster)

              if (
                element[allforms[i].disaster] == undefined ||
                element[allforms[i].disaster] == NaN
              ) {
                element[allforms[i].disaster] = 0;
                element[allforms[i].disaster] += 1;

                totals = +element[allforms[i].disaster];
              } else {
                element[allforms[i].disaster] += 1;

                totals = element[allforms[i].disaster];
              }

              for (let a in data) {
                if (data[a].all.dinrFormId == allforms[i]._id) {
                  if (data[a].all.shelter_injured > 0) {
                    element["Total No. of Injuries"] += data[
                      a
                    ].all.shelter_injured;
                  }
                  if (data[a].all.shelter_dead > 0) {
                    element["Total No. of Deaths"] += data[
                      a
                    ].all.shelter_dead;
                  }
                  if (data[a].all.displaced_hh > 0) {
                    element["Displaced Households"] += data[
                      a
                    ].all.displaced_hh;
                  }
                }
              }
            } else if (element.district == "TOTAL") {
              if (
                element[allforms[i].disaster] == undefined ||
                element[allforms[i].disaster] == NaN
              ) {
                element[allforms[i].disaster] = 0;
                element[allforms[i].disaster] += 1;
              } else {
                element[allforms[i].disaster] += 1;

                totals = element[allforms[i].disaster];

                //  element[allforms[i].disaster] = +this.totals;
              }

              for (let a in data) {
                if (data[a].all.dinrFormId == allforms[i]._id) {
                  if (data[a].all.shelter_injured > 0) {
                    element["Total No. of Injuries"] += data[
                      a
                    ].all.shelter_injured;
                  }
                  if (data[a].all.shelter_dead > 0) {
                    element["Total No. of Deaths"] += data[
                      a
                    ].all.shelter_dead;
                  }
                  if (data[a].all.displaced_hh > 0) {
                    element["Displaced Households"] += data[
                      a
                    ].all.displaced_hh;
                  }
                }
              }

              // console.log("i am ", element[allforms[i].disaster]);
            }
          });
        } catch (error) {
          console.log(error);
        }
      }



      generateTAGVHExcel(data);
    } else if (type === "CSV_data") {
      let mytotal = {};
      let totals=0
      mytotal.admin2_name_en = "TOTAL";
      districtsCopy = [...admin2sData];
      districtsCopy.push(mytotal);

      var csvData = districtsCopy.map(item => {
        return {
          district: item.admin2_name_en,
          Floods: fetchDisaster(item.admin2_name_en),
          "Strong winds (with no rains)": fetchDisaster(
            item.admin2_name_en
          ),
          Epidemic: fetchDisaster(item.admin2_name_en),
          Accident: fetchDisaster(item.admin2_name_en),
          "Disease and Pest outbreak": fetchDisaster(
            item.admin2_name_en
          ),
          Explosion: fetchDisaster(item.admin2_name_en),
          "Severe storms": fetchDisaster(item.admin2_name_en),
          "water pollution": fetchDisaster(item.admin2_name_en),
          "Extreme temperatures": fetchDisaster(item.admin2_name_en),
          "Severe storms": fetchDisaster(item.admin2_name_en),
          Earthquake: fetchDisaster(item.admin2_name_en),
          Hailstorm: fetchDisaster(item.admin2_name_en),
          "Heavy rains": fetchDisaster(item.admin2_name_en),
          Lightening: fetchDisaster(item.admin2_name_en),
          "Stormy rains": fetchDisaster(item.admin2_name_en),
          "Heavy/Torrential rains": fetchDisaster(item.admin2_name_en),
          Landslides: fetchDisaster(item.admin2_name_en),
          "Human-animal conflicts": fetchDisaster(item.admin2_name_en),
          Fire: fetchDisaster(item.admin2_name_en),
          "Total No. of Injuries": fetchDisaster(item.admin2_name_en),
          "Total No. of Deaths": fetchDisaster(item.admin2_name_en),
          "Displaced Households": fetchDisaster(item.admin2_name_en)

          // sheet: "All District Disaster Summary"
        };
      });

      for (let i in allforms) {
        try {
          csvData.forEach((element, index) => {
            if (
              element.district == allforms[i].district.admin2_name_en
            ) {
              //console.log("i have matched", allforms[i].disaster)

              if (
                element[allforms[i].disaster] == undefined ||
                element[allforms[i].disaster] == NaN
              ) {
                element[allforms[i].disaster] = 0;
                element[allforms[i].disaster] += 1;

               totals = +element[allforms[i].disaster];
              } else {
                element[allforms[i].disaster] += 1;

               totals = element[allforms[i].disaster];
              }

              for (let a in data) {
                if ( data[a].all.dinrFormId == allforms[i]._id) {
                  if ( data[a].all.shelter_injured > 0) {
                    element["Total No. of Injuries"] += data[
                      a
                    ].all.shelter_injured;
                  }
                  if ( data[a].all.shelter_dead > 0) {
                    element["Total No. of Deaths"] += data[
                      a
                    ].all.shelter_dead;
                  }
                  if ( data[a].all.displaced_hh > 0) {
                    element["Displaced Households"] += data[
                      a
                    ].all.displaced_hh;
                  }
                }
              }
            } else if (element.district == "TOTAL") {
              if (
                element[allforms[i].disaster] == undefined ||
                element[allforms[i].disaster] == NaN
              ) {
                element[allforms[i].disaster] = 0;
                element[allforms[i].disaster] += 1;
              } else {
                element[allforms[i].disaster] += 1;

               totals = element[allforms[i].disaster];

                //  element[allforms[i].disaster] = +this.totals;
              }

              for (let a in data) {
                if ( data[a].all.dinrFormId == allforms[i]._id) {
                  if ( data[a].all.shelter_injured > 0) {
                    element["Total No. of Injuries"] += data[
                      a
                    ].all.shelter_injured;
                  }
                  if ( data[a].all.shelter_dead > 0) {
                    element["Total No. of Deaths"] += data[
                      a
                    ].all.shelter_dead;
                  }
                  if ( data[a].all.displaced_hh > 0) {
                    element["Displaced Households"] += data[
                      a
                    ].all.displaced_hh;
                  }
                }
              }

              // console.log("i am ", element[allforms[i].disaster]);
            }
          });
        } catch (error) {
          console.log(error);
        }
      }



      utils.all_district_summaryCSV(utils.generateCSV(csvData), "csv");
    } else {

      generateTAGVHExcel(gvhDATA);
    }
  }

 export const processGVHExcel=(data,outputGvhdata)=>{
    let gvhdata = [];
    for (let k in data) {
      try {
        data[k].all.sectors.shelter =
          data[k].all.sectors.shelter === undefined
            ? (data[k].all.sectors.shelter = [
                {
                  damaged_fhh: "",
                  damaged_mhh: "",
                  fully_blown_roof: "",
                  partly_blown_roof: "",
                  burnt: ""
                }
              ])
            : data[k].all.sectors.shelter;

        data[k].all.sectors.displaced =
          data[k].all.sectors.displaced === undefined
            ? (data[k].all.sectors.displaced = [
                {
                  number_displaced_by_gender_fhh: "",
                  number_displaced_by_gender_mhh: ""
                }
              ])
            : data[k].all.sectors.displaced;

        if (
          typeof data[k].all.sectors.shelter.PeopleInjuredrows !==
            "undefined" ||
          typeof data[k].all.sectors.shelter.PeopleAffectedrows !==
            "undefined" ||
          typeof data[k].all.sectors.shelter.PeopleDeadrows !==
            "undefined" ||
          typeof data[k].all.sectors.displaced.PeopleAffectedrows !==
            "undefined"
        ) {


            let districtdata =
            {
            DistasterID: data[k].all.dinrform.uuid,
            DisasterName: data[k].all.dinrform.disaster,
            country: data[k].all.dinrform.country,
            country_pcode: data[k].all.dinrform.country_pcode,

            district: data[k].all.dinrform.district,
            TAname: data[k].all.admin3.admin3Name_en,
            TApcode: data[k].all.admin3.admin3Pcode,
            dstart: data[k].all.dinrform.dodFrom,
            dend: data[k].all.dinrform.dodTo,
            dacpc: data[k].all.dinrform.doaAcpc,
            ddcpc: data[k].all.dinrform.doaDcpc,
            submited: data[k].all.dinrform.date,
            gvhs: data[k].all.gvhs,
            shelter_peopleaffected:
              data[k].all.sectors.shelter.PeopleAffectedrows ===
              undefined
                ? (data[
                    k
                  ].all.sectors.shelter.PeopleAffectedrows = [])
                : data[k].all.sectors.shelter.PeopleAffectedrows,

            shelter_peopleinjured: data[k].all.sectors.shelter
              .PeopleInjuredrows,
            shelter_peopledead: data[k].all.sectors.shelter
              .PeopleDeadrows,

            shelter_urgent_response: data[k].all.sectors.shelter
              .urgent_response_needed,
            shelter_longterm_response: data[k].all.sectors.shelter
              .response_needed,

            displaced_peopleaffected:
              data[k].all.sectors.displaced.PeopleAffectedrows ===
              undefined
                ? (data[
                    k
                  ].all.sectors.displaced.PeopleAffectedrows = [])
                : data[k].all.sectors.displaced.PeopleAffectedrows,

            displaced_urgent_response:
              data[k].all.sectors.displaced.urgent_response_needed ==
              undefined
                ? ""
                : data[k].all.sectors.displaced
                    .urgent_response_needed,
            displaced_longterm_response:
              data[k].all.sectors.displaced.response_needed ==
              undefined
                ? ""
                : data[k].all.sectors.displaced.response_needed
          };

          for (let i in districtdata.gvhs) {
            let gvhname = districtdata.gvhs[i].name;

            let femalesInjured = 0;
            let malesInjured = 0;

            let femalesDead = 0;
            let malesDead = 0;

            let diplaced_fhh = "";
            let diplaced_mhh = "";

            let total_injured = 0;

            try {
              if (
                typeof districtdata.shelter_peopleinjured == "undefined" ||
                typeof districtdata.shelter_peopledead == "undefined"
              ) {
                femalesInjured = 0;
                malesInjured = 0;
                femalesDead = 0;
                malesDead = 0;

                diplaced_fhh = "";
                diplaced_mhh = "";

                total_injured = 0;
              } else {
                femalesInjured = districtdata.shelter_peopleinjured.find(
                  item => item.name == gvhname
                ).people_injured_females;

                malesInjured = districtdata.shelter_peopleinjured.find(
                  item => item.name == gvhname
                ).people_injured_males;

                total_injured =
                  parseInt(femalesInjured || 0) + parseInt(malesInjured || 0);
                femalesDead = districtdata.shelter_peopledead.find(
                  item => item.name == gvhname
                ).females_dead;

                malesDead = districtdata.shelter_peopledead.find(
                  item => item.name == gvhname
                ).males_dead;

                diplaced_fhh = districtdata.displaced_peopleaffected.find(
                  item => item.name == gvhname
                ).number_displaced_by_gender_fhh;

                diplaced_mhh = districtdata.displaced_peopleaffected.find(
                  item => item.name == gvhname
                ).number_displaced_by_gender_mhh;
              }
              outputGvhdata.push({
                sheet: "GVH-BASED DATA",

                DistasterID: districtdata.DistasterID,
                Disaster: districtdata.DisasterName,
                TA: districtdata.TAname,
                "TA PCODE": districtdata.TApcode,
                District: districtdata.district,
                GVH: gvhname,

                "Disaster Start Date": districtdata.dstart,
                "Disaster End Date": districtdata.dend,
                "ACPC Assessment Date": districtdata.dacpc,
                "DCPC Assessement Date": districtdata.ddcpc,
                "Submission Date": districtdata.submited,

                "Female Households affected (shelter)": districtdata.shelter_peopleaffected.find(
                  item => item.name == gvhname
                ).damaged_fhh,

                "Male Households affected (shelter)": districtdata.shelter_peopleaffected.find(
                  item => item.name == gvhname
                ).damaged_mhh,

                "Total Households affected (shelter)":
                  parseInt(
                    districtdata.shelter_peopleaffected.find(
                      item => item.name == gvhname
                    ).damaged_mhh || 0
                  ) +
                  parseInt(
                    districtdata.shelter_peopleaffected.find(
                      item => item.name == gvhname
                    ).damaged_mhh || 0
                  ),

                "Houses With fully blown roofs (shelter)": districtdata.shelter_peopleaffected.find(
                  item => item.name == gvhname
                ).fully_blown_roof,

                "Houses with partly blown roofs (shelter)": districtdata.shelter_peopleaffected.find(
                  item => item.name == gvhname
                ).partly_blown_roof,

                "Houses with walls damaged (shelter)": districtdata.shelter_peopleaffected.find(
                  item => item.name == gvhname
                ).wall_damaged,

                "Houses burnt (shelter)": districtdata.shelter_peopleaffected.find(
                  item => item.name == gvhname
                ).burnt,

                "Number of females injured (shelter)": femalesInjured,
                "Number of males injured (shelter)": malesInjured,

                "Total people injured (shelter)": total_injured,

                "Number of females dead (shelter)": femalesDead,

                "Number of males dead (shelter)": malesDead,

                "Total people dead (shelter)":
                  parseInt(malesDead || 0) + parseInt(femalesDead || 0),

                "Urgent needed (shelter)":
                  districtdata.shelter_urgent_response,
                "General response needed (shelter)":
                  districtdata.shelter_longterm_response,

                "Female Households affected (displaced)": diplaced_fhh,

                "Male Households affected (displaced)": diplaced_mhh,

                "Total Households affected (displaced)":
                  parseInt(diplaced_fhh || 0) + parseInt(diplaced_mhh || 0),

                "Urgent response needed (displaced)":
                  districtdata.displaced_urgent_response,
                "Long-term response needed (displaced)":
                  districtdata.displaced_longterm_response
              });


            } catch (error) {
              // console.log(error);
            }
          }
        } else {
          data[k].all.sectors.shelter.PeopleAffectedrows = [{}];
          data[k].all.sectors.shelter.PeopleDeadrows = [{}];
          data[k].all.sectors.shelter.PeopleMissingrows = [{}];
          data[k].all.sectors.shelter.PeopleInjuredrows = [{}];

          data[k].all.sectors.shelter.urgent_response_needed = "";
          data[k].all.sectors.shelter.response_needed = "";
          data[k].all.sectors.displaced.urgent_response_needed = "";
          data[k].all.sectors.displaced.response_needed = "";

          //outputGvhdata.push(data)
        }

        //this.gvhDATA = gvhdata;

        //outputGvhdata.push(gvhdata;


      } catch (error) {
        console.log({error});
      }
    }
  }

export const generateFlatSectorData=(item, dinformDataset,downloadData,villagesArray,campsArray,outputArray)=>{
    villagesArray=[...villagesArray]
    campsArray=[...campsArray]
    let downloadDataAll = item;
    downloadData=[...downloadData]

    let draFormsData=[]

    draFormsData.push(item);
    // console.log("HHHHHHHHHHHHHHHH", draFormsData[0].sectors.shelter)
    for (let i = 0; i < draFormsData.length; i++) {
      for (let a = 0; a < draFormsData[i].villages.length; a++) {
        villagesArray.push(draFormsData[i].villages[a].name);
      }

      for (let a = 0; a < draFormsData[i].camps.length; a++) {
        campsArray.push(draFormsData[i].camps[a].name);
      }
    }

    let Gvharray = [];
    let CampsArray = [];
    let VillagesArray = [];
    let OtherStructureArray = [];
    let responseNededArray = [];
    let nonFoodArray = [];
    let emergencyArray = [];

    downloadData.dinrform = {
      ...dinformDataset
    };



    for (let i = 0; i < item.gvhs.length; i++) {
      let GVHname = item.gvhs[i].name;

      Gvharray.push(GVHname);
    }


    for (let i = 0; i < item.villages.length; i++) {
      let Villages = item.villages[i].name;

      VillagesArray.push(Villages);
    }
    downloadDataAll.dinrform = downloadData.dinrform;



    try {
      downloadDataAll.dinrform.doaAcpc = downloadData.dinrform.doaAcpc
    } catch (error) {
      downloadDataAll.dinrform.doaAcpc = "";
    }

    try {
      downloadDataAll.dinrform.doaDcpc = downloadData.dinrform.doaDcpc
    } catch (error) {
      downloadDataAll.dinrform.doaDcpc = "";
    }

    try {
      downloadDataAll.dinrform.dodFrom =  downloadData.dinrform.dodFrom
    } catch (error) {
      downloadDataAll.dinrform.dodFrom = "";
    }

    try {
      downloadDataAll.dinrform.dodTo = downloadData.dinrform.dodTo
    } catch (error) {
      downloadDataAll.dinrform.dodTo = "";
    }

    try {
      downloadDataAll.gvhsAffected = Gvharray.join();
    } catch (error) {
      downloadDataAll.gvhsAffected = "";
    }


    try {
      downloadDataAll.other_structures_damaged = downloadDataAll.sectors.shelter.other_structures_damaged.map(value => value.name).join("|");
    } catch (error) {
      downloadDataAll.other_structures_damaged = "";
    }

    try {
      downloadDataAll.response_needed_non_food = (downloadDataAll.sectors.shelter.response_needed_non_food.map(value => value.name)).join("|");
    } catch (error) {
      downloadDataAll.response_needed_non_food = "";
    }

    try {
      let res_needed = (downloadDataAll.sectors.shelter.response_needed_emergency.map(value => value.name))

      responseNededArray.push(res_needed)
      downloadDataAll.response_needed_emergency = downloadDataAll.sectors.shelter.response_needed_emergency.map(value => value.name).join("|");
    } catch (error) {
      downloadDataAll.response_needed_emergency = "";
    }

    try {
      downloadDataAll.wash_response_needed = (downloadDataAll.sectors.wash.emergent_needs.map(value => value.name)).join("|");
    } catch (error) {
      downloadDataAll.wash_response_needed = "";
    }

    try {
      downloadDataAll.displaced_response_needed_emergency = (downloadDataAll.sectors.displaced.response_needed_emergency.map(value => value.name)).join("|");
    } catch (error) {
      downloadDataAll.displaced_response_needed_emergency = "";
    }

    try {
      downloadDataAll.displaced_non_food_items_needs = (downloadDataAll.sectors.displaced.response_needed_non_food.map(value => value.name)).join("|");
    } catch (error) {
      downloadDataAll.displaced_non_food_items_needs = "";
    }

    try {
      downloadDataAll.camps = CampsArray.join();
    } catch (error) {
      downloadDataAll.camps = "";
    }

    try {
      downloadDataAll.villages = VillagesArray.join();
    } catch (error) {
      downloadDataAll.villages = "";
    }

    try {
      downloadDataAll.is_food_available_food = returnFieldvalue(
        item,
        "food",
        "food_availability",
        "foodavailable"
      );
    } catch (error) {
      downloadDataAll.is_food_available_food = "";
    }

    try {
      downloadDataAll.medical_supply_availability = returnFieldItemvalue(
        item,
        "health",
        "available_health_medical",
        "Medical supply"
      );
    } catch (error) {
      downloadDataAll.medical_supply_availability = "";
    }

    try {
      downloadDataAll.health_personel_availability = returnFieldItemvalue(
        item,
        "health",
        "available_health_medical",
        "Health personel"
      );
    } catch (error) {
      downloadDataAll.health_personel_availability = "";
    }

    try {

      downloadDataAll.road_access =
        item.sectors.logistics.access_of_structures.map(x=>("["+x.name||x.road_name)+x.link_no+":"+(x.accessibility||x.road_access)+"]").join("|");
    } catch (error) {
      downloadDataAll.road_access = "";
    }

    try {
      downloadDataAll.food_1_2_months = sumArrayValuesNoAggregate(
        item,
        "food",
        "food_stocks_avaliability",
        "female_HH",
        "category",
        "No. of HH with 1-2 month Food Availability"
      );
    } catch (error) {
      downloadDataAll.food_1_2_months = 0;
    }

    try {
      downloadDataAll.food_2_months = sumArrayValuesNoAggregate(
        item,
        "food",
        "food_stocks_avaliability",
        "female_HH",
        "category",
        "No. of HH with more than 2 months Food Availability"
      );
    } catch (error) {
      downloadDataAll.food_2_months = 0;
    }

    try {
      downloadDataAll.food_stock_lost = sumArrayValuesNoAggregate(
        item,
        "food",
        "food_stocks_avaliability",
        "female_HH",
        "category",
        "No. of HH who lost Food Stock"
      );
    } catch (error) {
      downloadDataAll.food_stock_lost = 0;
    }

    try {
      downloadDataAll.food_less_1_month = sumArrayValuesNoAggregate(
        item,
        "food",
        "food_stocks_avaliability",
        "female_HH",
        "category",
        "No. of HH with less than 1 month Food Availability"
      );
    } catch (error) {
      downloadDataAll.food_less_1_month = 0;
    }

    try {
      downloadDataAll.food_less_1_month_male = sumArrayValuesNoAggregate(
        item,
        "food",
        "food_stocks_avaliability",
        "male_HH",
        "category",
        "No. of HH with less than 1 month Food Availability"
      );
    } catch (error) {
      downloadDataAll.food_less_1_month_male = 0;
    }

    try {
      downloadDataAll.food_1_2_months_male = sumArrayValuesNoAggregate(
        item,
        "food",
        "food_stocks_avaliability",
        "male_HH",
        "category",
        "No. of HH with 1-2 month Food Availability"
      );
    } catch (error) {
      downloadDataAll.food_1_2_months_male = 0;
    }

    try {
      downloadDataAll.food_2_months_male = sumArrayValuesNoAggregate(
        item,
        "food",
        "food_stocks_avaliability",
        "male_HH",
        "category",
        "No. of HH with more than 2 months Food Availability"
      );
    } catch (error) {
      downloadDataAll.food_2_months_male = 0;
    }

    try {
      downloadDataAll.food_stock_lost_male = sumArrayValuesNoAggregate(
        item,
        "food",
        "food_stocks_avaliability",
        "male_HH",
        "category",
        "No. of HH who lost Food Stock"
      );
    } catch (error) {
      downloadDataAll.food_stock_lost_male = 0;
    }

    try {
      downloadDataAll.food_item_damage = sumArrayValues(
        item,
        "agriculture",
        "food_item_damage",
        "number_of_kilos"
      );
    } catch (error) {
      downloadDataAll.food_item_damage = 0;
    }
    try {
      downloadDataAll.PeopleAffectedrows_female = sumArrayValues(
        item,
        "displaced",
        "PeopleAffectedrows",
        "number_displaced_by_gender_fhh"
      );
    } catch (error) {
      downloadDataAll.PeopleAffectedrows_female = 0;
    }

    try {
      downloadDataAll.PeopleAffectedrows_male = sumArrayValues(
        item,
        "displaced",
        "PeopleAffectedrows",
        "number_displaced_by_gender_mhh"
      );
    } catch (error) {
      downloadDataAll.PeopleAffectedrows_male = 0;
    }

    try {
      downloadDataAll.displaced_hh =
        parseInt(downloadDataAll.PeopleAffectedrows_female) +
        parseInt(downloadDataAll.PeopleAffectedrows_male);
    } catch (error) {
      downloadDataAll.displaced_hh = 0;
    }

    try {
      downloadDataAll.total_risk_contamination =
        parseInt(downloadDataAll.risk_contamination_fhh) +
        parseInt(downloadDataAll.risk_contamination_mhh);
    } catch (error) {
      downloadDataAll.total_risk_contamination = 0;
    }

    try {
      downloadDataAll.hectares_submerged = sumArrayValues(
        item,
        "agriculture",
        "crops_damaged",
        "hectares_submerged"
      );
    } catch (error) {
      downloadDataAll.hectares_submerged = 0;
    }

    try {
      downloadDataAll.hectares_washed_away = sumArrayValues(
        item,
        "agriculture",
        "crops_damaged",
        "hectares_washed_away"
      );
    } catch (error) {
      downloadDataAll.hectares_washed_away = 0;
    }

    try {
      downloadDataAll.impact_on_crops_hh_affected = sumArrayValues(
        item,
        "agriculture",
        "impact_on_crops",
        "hh_affected"
      );
    } catch (error) {
      downloadDataAll.impact_on_crops_hh_affected = 0;
    }

    try {
      downloadDataAll.impact_on_crops_hectares_damaged = sumArrayValues(
        item,
        "agriculture",
        "impact_on_crops",
        "hectares_damaged"
      );
    } catch (error) {
      downloadDataAll.impact_on_crops_hectares_damaged = 0;
    }

    try {
      downloadDataAll.impact_on_livestock_hh = sumArrayValues(
        item,
        "agriculture",
        "impact_on_livestock",
        "hh_affected_l"
      );
    } catch (error) {
      downloadDataAll.impact_on_livestock_hh = 0;
    }

    try {
      downloadDataAll.impact_on_livestock_la = sumArrayValues(
        item,
        "agriculture",
        "impact_on_livestock",
        "livestock_affected"
      );
    } catch (error) {
      downloadDataAll.impact_on_livestock_la = 0;
    }

    try {
      downloadDataAll.displaced_households_accommodated_male = sumArrayValues(
        item,
        "displaced",
        "displaced_households_accommodated",
        "accomodated_males_hh"
      );
    } catch (error) {
      downloadDataAll.displaced_households_accommodated_male = 0;
    }

    try {
      downloadDataAll.displaced_households_accommodated_female = sumArrayValues(
        item,
        "displaced",
        "displaced_households_accommodated",
        "accomodated_females_hh"
      );
    } catch (error) {
      downloadDataAll.displaced_households_accommodated_female = 0;
    }

    try {
      downloadDataAll.displaced_disagregated_male = sumArrayValues(
        item,
        "displaced",
        "displaced_disaggregated",
        "displaced_males"
      );
    } catch (error) {
      downloadDataAll.displaced_disagregated_male = 0;
    }

    try {
      downloadDataAll.displaced_disagregated_female = sumArrayValues(
        item,
        "displaced",
        "displaced_disaggregated",
        "displaced_females"
      );
    } catch (error) {
      downloadDataAll.displaced_disagregated_female = 0;
    }

    try {
      downloadDataAll.total_disaggregated_diplaced =
      sumArrayValues(
        item,
        "displaced",
        "displaced_disaggregated",
        "displaced_females"
      ) +
      sumArrayValues(
        item,
        "displaced",
        "displaced_disaggregated",
        "displaced_males"
      )
    } catch (error) {
      downloadDataAll.total_disaggregated_diplaced = 0;
    }

    try {
      downloadDataAll.displaced_individuals_accommodated_male = sumArrayValues(
        item,
        "displaced",
        "displaced_individuals_accommodated",
        "accomodated_males"
      );
    } catch (error) {
      downloadDataAll.displaced_individuals_accommodated_male = 0;
    }

    try {
      downloadDataAll.displaced_individuals_accommodated_female = sumArrayValues(
        item,
        "displaced",
        "displaced_individuals_accommodated",
        "accomodated_females"
      );
    } catch (error) {
      downloadDataAll.displaced_individuals_accommodated_female = 0;
    }

    try {
      downloadDataAll.education_building_functioning = sumArrayValues(
        item,
        "education",
        "impact_on_schools",
        "functioning_buildings"
      );
    } catch (error) {
      downloadDataAll.education_building_functioning = 0;
    }

    try {
      downloadDataAll.education_building_partly_functioning = sumArrayValues(
        item,
        "education",
        "impact_on_schools",
        "partially_functioning_buildings"
      );
    } catch (error) {
      downloadDataAll.education_building_partly_functioning = 0;
    }

    try {
      downloadDataAll.education_closed_buildings = sumArrayValues(
        item,
        "education",
        "impact_on_schools",
        "closed_buildings"
      );
    } catch (error) {
      downloadDataAll.education_closed_buildings = 0;
    }

    try {
      downloadDataAll.education_females_out_of_school = sumArrayValues(
        item,
        "education",
        "impact_on_schools",
        "females_out_of_school"
      );
    } catch (error) {
      downloadDataAll.education_females_out_of_school = 0;
    }

    try {
      downloadDataAll.education_males_out_of_school = sumArrayValues(
        item,
        "education",
        "impact_on_schools",
        "males_out_of_school"
      );
    } catch (error) {
      downloadDataAll.education_males_out_of_school = 0;
    }

    try {
      downloadDataAll.education_underwater = sumArrayValues(
        item,
        "education",
        "impact_on_schools",
        "underwater"
      );
    } catch (error) {
      downloadDataAll.education_underwater = 0;
    }

    try {
      downloadDataAll.education_completely_damaged = sumArrayValues(
        item,
        "education",
        "impact_on_schools",
        "completely_damaged"
      );
    } catch (error) {
      downloadDataAll.education_completely_damaged = 0;
    }

    try {
      downloadDataAll.health_partially_functioning =
        sumArrayValues(
          item,
          "health",
          "available_health_facilities",
          "partially_functioning"
        ) +
        sumArrayValues(
          item,
          "health",
          "other_health_facilities",
          "partialy_functioning"
        );
    } catch (error) {
      downloadDataAll.health_partially_functioning = 0;
    }

    try {
      downloadDataAll.health_verge_of_closing =
        sumArrayValues(
          item,
          "health",
          "available_health_facilities",
          "verge_of_closing"
        ) +
        sumArrayValues(
          item,
          "health",
          "other_health_facilities",
          "verge_of_closing"
        );
    } catch (error) {
      downloadDataAll.health_verge_of_closing = 0;
    }

    try {
      downloadDataAll.health_closed =
        sumArrayValues(
          item,
          "health",
          "available_health_facilities",
          "closed"
        ) +
        sumArrayValues(
          item,
          "health",
          "other_health_facilities",
          "closed"
        );
    } catch (error) {
      downloadDataAll.health_closed = 0;
    }

    try {
      downloadDataAll.livelihoods_slightly_affected = sumArrayValues(
        item,
        "livelihoods",
        "livelihoods_affected",
        "severely_affected"
      );
    } catch (error) {
      downloadDataAll.livelihoods_slightly_affected = 0;
    }
    try {
      downloadDataAll.livelihoods_severely_affected = sumArrayValues(
        item,
        "livelihoods",
        "livelihoods_affected",
        "slightly_affected"
      );
    } catch (error) {
      downloadDataAll.livelihoods_severely_affected = 0;
    }

    try {
      downloadDataAll.impact_on_vulnerable_persons_males = sumArrayValues(
        item,
        "protection",
        "impact_on_vulnerable_persons",
        "impacted_males"
      );
    } catch (error) {
      downloadDataAll.impact_on_vulnerable_persons_males = 0;
    }

    try {
      downloadDataAll.impact_on_vulnerable_persons_females = sumArrayValues(
        item,
        "protection",
        "impact_on_vulnerable_persons",
        "impacted_females"
      );
    } catch (error) {
      downloadDataAll.impact_on_vulnerable_persons_females = 0;
    }

    try {
      downloadDataAll.nutrition_affected_pop_male = sumArrayValues(
        item,
        "nutrition",
        "affected_population",
        "affected_males"
      );
    } catch (error) {
      downloadDataAll.nutrition_affected_pop_male = 0;
    }

    try {
      downloadDataAll.nutrition_affected_pop_female = sumArrayValues(
        item,
        "nutrition",
        "affected_population",
        "affected_females"
      );
    } catch (error) {
      downloadDataAll.nutrition_affected_pop_female = 0;
    }

    try {
      downloadDataAll.shelter_without_shelter_male = sumArrayValues(
        item,
        "shelter",
        "people_without_shelter",
        "without_shelter_males"
      );
    } catch (error) {
      downloadDataAll.shelter_without_shelter_male = 0;
    }

    try {
      downloadDataAll.shelter_without_shelter_female = sumArrayValues(
        item,
        "shelter",
        "people_without_shelter",
        "without_shelter_females"
      );
    } catch (error) {
      downloadDataAll.shelter_without_shelter_female = 0;
    }

    try {
      downloadDataAll.people_without_shelter =
        parseInt(downloadDataAll.shelter_without_shelter_male) +
        parseInt(downloadDataAll.shelter_without_shelter_female);
    } catch (error) {
      downloadDataAll.people_without_shelter = 0;
    }

    try {
      downloadDataAll.other_cement_roofs_affected = sumArrayValues(
        item,
        "shelter",
        "other_structures_damaged",
        "cement_roofs_affected"
      );
    } catch (error) {
      downloadDataAll.other_cement_roofs_affected = 0;
    }

    try {
      downloadDataAll.other_completely_damaged = sumArrayValues(
        item,
        "shelter",
        "other_structures_damaged",
        "completely_damaged"
      );
    } catch (error) {
      downloadDataAll.other_completely_damaged = 0;
    }

    try {
      downloadDataAll.other_iron_sheets_roofs_affected = sumArrayValues(
        item,
        "shelter",
        "other_structures_damaged",
        "iron_sheets_roofs_affected"
      );
    } catch (error) {
      downloadDataAll.other_iron_sheets_roofs_affected = 0;
    }

    try {
      downloadDataAll.other_nails_roofs_affected = sumArrayValues(
        item,
        "shelter",
        "other_structures_damaged",
        "nails_roofs_affected"
      );
    } catch (error) {
      downloadDataAll.other_nails_roofs_affected = 0;
    }

    try {
      downloadDataAll.other_number_roofs_affected = sumArrayValues(
        item,
        "shelter",
        "other_structures_damaged",
        "number_roofs_affected"
      );
    } catch (error) {
      downloadDataAll.other_number_roofs_affected = 0;
    }

    try {
      downloadDataAll.other_poles_roofs_affected = sumArrayValues(
        item,
        "shelter",
        "other_structures_damaged",
        "poles_roofs_affected"
      );
    } catch (error) {
      downloadDataAll.other_poles_roofs_affected = 0;
    }

    try {
      downloadDataAll.other_sand_aggregates_roofs_affected = sumArrayValues(
        item,
        "shelter",
        "other_structures_damaged",
        "sand_aggregates_roofs_affected"
      );
    } catch (error) {
      downloadDataAll.other_sand_aggregates_roofs_affected = 0;
    }

    try {
      downloadDataAll.other_underwater = sumArrayValues(
        item,
        "shelter",
        "other_structures_damaged",
        "underwater"
      );
    } catch (error) {
      downloadDataAll.other_underwater = 0;
    }

    try {
      downloadDataAll.total_structures_damaged =
        parseInt(downloadDataAll.other_cement_roofs_affected) +
        parseInt(downloadDataAll.other_completely_damaged) +
        parseInt(downloadDataAll.other_iron_sheets_roofs_affected) +
        parseInt(downloadDataAll.other_nails_roofs_affected) +
        parseInt(downloadDataAll.other_number_roofs_affected) +
        parseInt(downloadDataAll.other_sand_aggregates_roofs_affected) +
        parseInt(downloadDataAll.other_underwater) +
        parseInt(downloadDataAll.other_poles_roofs_affected) +
        parseInt(downloadDataAll.people_without_shelter);
    } catch (error) {
      downloadDataAll.people_without_shelter = 0;
    }
    // try {
    //   downloadDataAll.people_without_shelter =
    //     parseInt(downloadDataAll.shelter_without_shelter_male) +
    //     parseInt(downloadDataAll.shelter_without_shelter_female);
    // } catch (error) {
    //   downloadDataAll.people_without_shelter = 0;
    // }

    try {
      downloadDataAll.shelter_people_dead_female = sumArrayValues(
        item,
        "shelter",
        "PeopleDeadrows",
        "females_dead"
      );
    } catch (error) {
      downloadDataAll.shelter_people_dead_female = 0;
    }

    try {
      downloadDataAll.shelter_female_missing = sumArrayValues(
        item,
        "shelter",
        "PeopleMissingrows",
        "females_missing"
      );
    } catch (error) {
      downloadDataAll.shelter_female_missing = 0;
    }

    try {
      downloadDataAll.shelter_male_missing = sumArrayValues(
        item,
        "shelter",
        "PeopleMissingrows",
        "males_missing"
      );
    } catch (error) {
      downloadDataAll.shelter_male_missing = 0;
    }


    try {
      downloadDataAll.shelter_total_people_missing =
        parseInt(downloadDataAll.shelter_male_missing) +
        parseInt(downloadDataAll.shelter_female_missing);
    } catch (error) {
      downloadDataAll.shelter_total_people_missing = 0;
    }

    try {
      downloadDataAll.shelter_people_dead_male = sumArrayValues(
        item,
        "shelter",
        "PeopleDeadrows",
        "males_dead"
      );
    } catch (error) {
      downloadDataAll.shelter_people_dead_male = 0;
    }

    try {
      downloadDataAll.shelter_dead =
        parseInt(downloadDataAll.shelter_people_dead_male) +
        parseInt(downloadDataAll.shelter_people_dead_female);
    } catch (error) {
      downloadDataAll.shelter_dead = 0;
    }

    try {
      downloadDataAll.shelter_people_injured_female = sumArrayValues(
        item,
        "shelter",
        "PeopleInjuredrows",
        "females_injured"
      );
    } catch (error) {
      downloadDataAll.shelter_people_injured_female = 0;
    }

    try {
      downloadDataAll.shelter_people_injured_male = sumArrayValues(
        item,
        "shelter",
        "PeopleInjuredrows",
        "males_injured"
      );
    } catch (error) {
      downloadDataAll.shelter_people_injured_male = 0;
    }

    try {
      downloadDataAll.shelter_injured =
        parseInt(downloadDataAll.shelter_people_injured_female) +
        parseInt(downloadDataAll.shelter_people_injured_male);
    } catch (error) {
      downloadDataAll.shelter_injured = 0;
    }

    try {
      downloadDataAll.shelter_fhh_affected = sumArrayValues(
        item,
        "shelter",
        "PeopleAffectedrows",
        "damaged_fhh"
      );
    } catch (error) {
      downloadDataAll.shelter_fhh_affected = 0;
    }

    try {
      downloadDataAll.shelter_mhh_affected = sumArrayValues(
        item,
        "shelter",
        "PeopleAffectedrows",
        "damaged_mhh"
      );
    } catch (error) {
      downloadDataAll.shelter_mhh_affected = 0;
    }

    try {
      downloadDataAll.houses_fully_brown_roofs = sumArrayValues(
        item,
        "shelter",
        "PeopleAffectedrows",
        "fully_blown_roof"
      );
    } catch (error) {
      downloadDataAll.PeopleAffectedrows = 0;
    }

    try {
      downloadDataAll.houses_partly_brown_roofs = sumArrayValues(
        item,
        "shelter",
        "PeopleAffectedrows",
        "partly_blown_roof"
      );
    } catch (error) {
      downloadDataAll.PeopleAffectedrows = 0;
    }








    try {
      downloadDataAll.houses_wall_damaged = sumArrayValues(
        item,
        "shelter",
        "PeopleAffectedrows",
        "wall_damaged"
      );
    } catch (error) {
      downloadDataAll.PeopleAffectedrows = 0;
    }

    try {
      downloadDataAll.houses_burnt = sumArrayValues(
        item,
        "shelter",
        "PeopleAffectedrows",
        "burnt"
      );
    } catch (error) {
      downloadDataAll.PeopleAffectedrows = 0;
    }







    try {
      downloadDataAll.shelter_affected_hh =
        parseInt(downloadDataAll.shelter_mhh_affected) +
        parseInt(downloadDataAll.shelter_fhh_affected);
    } catch (error) {
      downloadDataAll.shelter_affected_hh = "";
    }

    outputArray.push({ all: downloadDataAll });
  }


 function sumArrayValues(item, sector, array, key) {
    if (typeof item["sectors"][sector][array] !== "undefined") {
      return item["sectors"][sector][array]
        .map(function(item) {
          return parseInt(item[key]) ? parseInt(item[key]) : 0;
        })
        .reduce((sum, value) => sum + value, 0);
    } else if (typeof item[sector][array] === "undefined") {
      return 0;
    }
  }

  function returnFieldvalue(item, sector, array, key) {
    if (typeof item["sectors"][sector][array] !== "undefined") {
      return item["sectors"][sector][array][0][key];
    } else if (typeof item["sectors"][sector][array] === "undefined") {
      return "";
    }
  }

  function returnFieldItemvalue(item, sector, array, key) {
    if (typeof item["sectors"][sector][array] !== "undefined") {
      for (let i in item["sectors"][sector][array]) {
        if (item["sectors"][sector][array][i].name === key) {
          return item["sectors"][sector][array][i].status;
        } else if (
          typeof item["sectors"][sector][array][i].key === "undefined"
        ) {
          return "";
        }
      }
    }
  }

  function sumArrayValuesNoAggregate(item, sector, array, key, filterBy, filterValue) {
    if (typeof item["sectors"][sector][array] !== "undefined") {
      return item["sectors"][sector][array]
        .filter(item => item[filterBy] === filterValue)
        .map(function(item) {
          return parseInt(item[key]) ? parseInt(item[key]) : 0;
        })
        .reduce((sum, value) => sum + value, 0);
    } else if (typeof item[sector][array] === "undefined") {
      return 0;
    }
  }
