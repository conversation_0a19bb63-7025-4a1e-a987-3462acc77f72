<template>
  <div class="content">
    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Disaster</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/districtmanager/dashboard">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">PRELIMINARY REPORTS</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">All</li>
            </ol>
          </nav>
        </div>
        <div class="col-lg-6 col-6 text-right pr-3">
          <base-dropdown
            title-classes="btn btn-sm btn-primary mr-0"
            menu-on-right
            :has-toggle="false"
          >
            <a slot="title" class="dropdown-toggle" type="neutral" size="sm">
              <i class="text-black ni ni-cloud-download-95"></i> EXPORT TO EXCEL
            </a>
            <a class="dropdown-item" @click="downloadExcel('aggregated')"
              >Aggregated</a
            >
            <a class="dropdown-item" @click="downloadExcel('disaggregated')"
              >Disaggregated</a
            >
          </base-dropdown>
          <!-- <base-button title-classes="btn btn-sm btn-primary mr-0" size="sm" type="primary" @click="downloadExcel()">
            <i class="text-black ni ni-cloud-download-95"></i>
            <span class="btn-inner--text">EXPORT TO EXCEL</span>
            <span class="btn-inner--text"></span>
          </base-button> -->
        </div>
        <!-- <base-button size="sm" type="primary" @click="downloadExcel()">
            <span class="btn-inner--icon">
              <i class="text-black ni ni-cloud-download-95  text-right"></i>
            </span>
            <span class="btn-inner--text">EXPORT TO EXCEL</span>
          </base-bu.tton> -->
      </div>
      <!-- Card stats -->
    </base-header>
    <div class="container-fluid mt--6">
      <div>
        <card
          class="no-border-card"
          body-classes="px-0 pb-1"
          footer-classes="pb-2"
        >
          <template slot="header">
            <h3 class="mb-0">Preliminary Disaster Reports</h3>
          </template>
          <div>
            <div
              class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
            >
              <div class="col-8">
                <el-select
                  class="select-primary pagination-select"
                  v-model="pagination.perPage"
                  placeholder="Per page"
                >
                  <el-option
                    class="select-primary"
                    v-for="item in pagination.perPageOptions"
                    :key="item"
                    :label="item"
                    :value="item"
                  ></el-option>
                </el-select>
              </div>

              <div class="col-2">
                <div>
                  <el-select
                    class="select-primary pagination-select"
                    v-model="filter"
                    placeholder="Period"
                  >
                    <el-option
                      class="select-primary"
                      v-for="item in dateranges"
                      :key="item"
                      :label="item"
                      :value="item"
                    ></el-option>
                  </el-select>
                </div>
              </div>

              <div>
                <base-input
                  v-model="searchQuery"
                  prepend-icon="fas fa-search"
                  placeholder="Search..."
                ></base-input>
              </div>
            </div>
            <div>
              <b-table
                responsive
                sticky-header
                :striped="striped"
                :bordered="bordered"
                :borderless="borderless"
                :sorting="disabled"
                :outlined="outlined"
                :small="small"
                :hover="hover"
                :dark="dark"
                :sort-icon="true"
                :fixed="fixed"
                :foot-clone="footClone"
                :no-border-collapse="noCollapse"
                head-variant="light"
                :table-variant="tableVariant"
                :items="filteredItems"
                :fields="tableColumns"
              >
                <template #cell(status)="row">
                  <b-badge variant="warning" v-if="row.item.status == 2"
                    ><strong class="text-white">Not Approved</strong></b-badge
                  >
                  <b-badge variant="success" v-if="row.item.status == 3"
                    ><strong class="text-dark">Approved</strong></b-badge
                  >
                </template>

                <template #cell(actions)="row">
                  <b-button
                    v-if="row.item.TA"
                    @click="review(row.item, row.index, $event.target)"
                    :disabled="row.item.status == 3"
                    style="color: white; background: #ab6f00; border:#ab6f00;"
                    class="edit text-white "
                    type="primary"
                    size="sm"
                    icon
                  >
                    <i class="fa fa-pencil"></i>
                    Approve
                  </b-button>

                  <b-button
                    v-if="row.item.TA"
                    :disabled="row.item.status == 3"
                    @click="editRecord(row, $event.target)"
                    style="color: white; background: #2ec3a3;"
                    class="edit text-white "
                    type="success"
                    size="sm"
                    icon
                  >
                    <i class="fa fa-pencil"></i>
                    edit
                  </b-button>

                  <b-button

                  v-if="row.item.TA"
                  @click="downloadBase64Data(row.item)"
                  :disabled="!row.item.images "
                  style="color: white; background: #2ec3a3;"
                  class="edit text-white "
                  type="info"
                  size="sm"
                  icon
                >
                  <i class="fa fa-download"></i>
                  Download Photos
                </b-button>
                </template>
              </b-table>
            </div>
          </div>
          <b-modal
            scrollable
            hide-backdrop
            centered
            size="sm"
            width="30"
            title="Report cover text"
            class="modal-md"
            v-model="showModal"
            hide-footer="true"
          >
            <div v-if="selectedRow">
              <textarea
                v-model="selectedRow.covertext"
                placeholder="Enter cover text"
                class="form-control"
              >
              </textarea>
            </div>
            <base-button @click="updateCoverText()">
              Save
            </base-button>
          </b-modal>
          <div
            slot="footer"
            class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
          >
            <div class>
              <p class="card-category">
                Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
                <span v-if="selectedRows.length"
                  >&nbsp; &nbsp; {{ selectedRows.length }} rows selected</span
                >
              </p>
            </div>
            <base-pagination
              class="pagination-no-border"
              v-model="pagination.currentPage"
              :per-page="pagination.perPage"
              :total="total"
            ></base-pagination>
          </div>
        </card>
      </div>
    </div>
  </div>
</template>
<script>
import { Table, TableColumn, Select, Option, Button } from 'element-ui'
import RouteBreadCrumb from '@/components/Breadcrumb/RouteBreadcrumb'
import { BasePagination } from '@/components'
import clientPaginationMixin from '@/components/PaginatedTables/clientPaginationMixin'
import { admin2s } from '../api/location/admin2s'
import { flatten } from '../../../store/flatdinrs/flatten'

import globalmixin from '../../../mixins/globalmixin'

import {
  generateExcel,
  generateTAGVHExcel,
  generateDisasterSummaryExcel
} from '../../../util/generateExcel'
import { generateCSV } from '../../../util/generateCSV'
import utils from '../../../util/dashboard'
import { MongoReports } from '../api/MongoReports'
import downloadexcel from 'vue-json-excel'
import JSZip from 'jszip'
import FileSaver from 'file-saver'
import moment from 'moment'
import swal from 'sweetalert2'
import dateformat from 'dateformat'
import { mapGetters, mapActions } from 'vuex'

import { prelimreports } from '../../dcpc/api/forms/prelimreports.js'
import { download } from '../../../util/download'

import { user } from '../../../api/user'

export default {
  mixins: [clientPaginationMixin, globalmixin],
  components: {
    BasePagination,
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    [Table.name]: Table,
    downloadexcel,
    [TableColumn.name]: TableColumn
  },
  data () {
    return {
      showModal: false,
      selectedRow: null,
      propsToSearch: ['ta', 'gvh', 'date'],
      searchQuery: '',
      filter: '',
      dateranges: ['24hrs', '48hrs', '1 Week', '2 Weeks', 'Lifetime'],

      images: [],
      dinrFormsData: {},
      villagesArray: [],
      campsArray: [],
      bufferDraFormsData: [],
      finalExcelData: [],
      TAarray: [],
      draFormsData: [],
      download: [],
      test: [],
      gvhDATA: [],
      allforms: [],
      districtsCopy: [],
      downloadData: [],
      testarray: [],
      TaData: [],
      premreports: [],
      admin2sData: [],
      AllDisasterSummaryExcel: [],
      disablebutton: false,

      gridOptions: {},
      tableColumns: [
        {
          key: 'disaster',
          label: 'Disaster',
          sortable: false
        },
        {
          key: 'district',
          label: 'District',
          sortable: false
        },
        {
          key: 'ta',
          label: 'TA',
          sortable: false
        },
        {
          key: 'gvh',
          label: 'GVH',
          sortable: false
        },

        {
          key: 'Date',
          label: 'Occured On & Submmited at ',
          sortable: false
        },
        {
          key: 'mhh',
          label: 'MHH',
          sortable: false
        },
        {
          key: 'fhh',
          label: 'FHH',
          sortable: false
        },
        {
          key: 'injured',
          label: 'Injuries',
          sortable: false
        },
        {
          key: 'dead',
          label: 'Deaths',
          sortable: false
        },
        {
          key: 'missing',
          label: 'Missing',
          sortable: false
        },
        {
          key: 'pregnant',
          label: 'PW',
          sortable: false
        },
        {
          key: 'lactactingwomen',
          label: 'LW',
          sortable: false
        },
        {
          key: 'underfive',
          label: 'U5',
          sortable: false
        },

        {
          key: 'disabled',
          label: 'disabled',
          sortable: false
        },



        {
          key: 'status',
          label: 'status',
          sortable: false
        },

        {
          key: 'actions',
          label: 'Actions'
        }
      ],
      tableData: [],
      selectedRows: [],
      totalinjured: 0,
      totaldeaths: 0,
      totalmissing: 0
    }
  },

  computed: {
    filteredItems () {
      let counter = 0

      if (this.filter == '' || 'Lifetime') {
        counter = 240000000000
      }
      if (this.filter == '24hrs') {
        counter = 24
      }
      if (this.filter == '48hrs') {
        counter = 48
      }
      if (this.filter == '1 Week') {
        counter = 168
      }
      if (this.filter == '2 Weeks') {
        counter = 336
      }

      return this.tableData.filter(
        item => moment().diff(moment(item.dateof), 'hours') <= counter
      )
    }
  },
  methods: {

    downloadExcel (type) {
      if (type == 'aggregated') {
        // generate aggregated excel sheet
        prelimreports.getReportsLatest().then(response => {
          let district = this.$session.get('user').admin2_name_en
          let filterdData = []

          for (let i = 0; i < response.length; i++) {
            if (
              response[i].status == '2' ||
              (response[i].status == '3' &&
                response[i].user.district.admin2_name_en === district)
            ) {
              filterdData.push(response[i])
            }
          }

          let summedValues = Object.values(
            filterdData.reduce(function (r, e) {
              let key = e._id.disasterdate + '|' + e._id.ta
              if (!r[key]) r[key] = e
              else {
                r[key].fhh += parseInt(e.fhh)
                r[key].mhh += parseInt(e.mhh)
                r[key].injured += parseInt(e.injured)
                r[key].dead += parseInt(e.dead)
                r[key].missing += parseInt(e.missing)
                r[key].pregnant += parseInt(e.pregnant)
                r[key].lactactingwomen += parseInt(e.lactactingwomen)
                r[key].underfive += parseInt(e.underfive)
                r[key].disabled += parseInt(e.disabled)
              }
              return r
            }, {})
          )
          var data = {}
          for (let i in summedValues) {
            this.premreports.push({
              TA: summedValues[i]._id.ta,
              Date: moment(summedValues[i].disasterdate).format('DD-MM-YYYY'),
              MHH: summedValues[i].mhh,
              FHH: summedValues[i].fhh,
              Injuries: summedValues[i].injured,
              Deaths: summedValues[i].dead,
              Missing: summedValues[i].missing,
              PW: summedValues[i].pregnant,
              LW: summedValues[i].lactactingwomen,
              U5: summedValues[i].underfive,
              disabled: summedValues[i].disabled,
              editby:
                summedValues[i].edits == undefined
                  ? ''
                  : summedValues[i].edits.user,
              editon:
                summedValues[i].edits == undefined
                  ? ''
                  : moment(summedValues[i].edits.edittime).format(
                      'DD-MM-YYYY hh:mm:ss'
                    ),
              dcpcApprover: summedValues[i].dcpcApprover,

              dcApprover: summedValues[i].dcApprover,
              sheet: 'Preliminary reports'
            })
          }

          generateTAGVHExcel(this.premreports)
        })
      } else {
        // generate disagregated excel sheet
        var data = this.tableData.map(item => {
          return {
            TA: item.TA,
            GVH: item.gvh,
            Date: item.Date,
            MHH: item.mhh,
            FHH: item.fhh,
            Injuries: item.injured,
            Deaths: item.dead,
            Missing: item.missing,
            PW: item.pregnant,
            LW: item.lactactingwomen,
            U5: item.underfive,

            dcpcApprover: item.dcpcApprover,
            dcApprover: item.dcApprover,
            editby: item.edits == undefined ? '' : item.edits.user,
            editon: item.edits == undefined ? '' : item.edits.editon,
            sheet: 'Preliminary reports'
          }
        })
        this.premreports = data
        generateTAGVHExcel(this.premreports)
      }
    },

    hasCoverText (index) {
      if (index.covertext === '') {
        return false
      } else {
        return true
      }
    },

    fetchDisaster (district) {
      return 0
    },

    editRecord (data) {
      this.$router.push({
        path: '/dcpc/PrelimReports/' + data.item.id
      })
    },

    review (index) {

      this.handleRequest({
        isApproved: false,
        status: '3',
        dcpcApprover: this.$session.get("userObj").firstName + " " + this.$session.get("userObj").lastName,
        _id: index.id
      })
    },

    addcover (row) {
      this.selectedRow = row
      this.showModal = true
    },
    updateCoverText () {
      //console.log("selected", this.selectedRow)
      prelimreports
        .updateCoverText({
          _id: this.selectedRow.id,
          covertext: this.selectedRow.covertext
        })
        .then(
          () => {
            swal.fire({
              title: 'Done Succesfully',
              text: 'Your report cover has been saved',
              type: 'success',
              animation: false
            })

            this.showModal = false
          },
          reason => {
            swal.fire({
              title: 'Failed to submit cover',
              text: 'possible invalid data (' + reason + ')',
              type: 'error',
              animation: false
            })
          }
        )
    },

    async handleRequest (data) {
      prelimreports.addAttribute(data, 'approvalMetadata').then(
        () => {
          swal.fire({
            title: 'Approved Succesfully',
            text: 'Your decision has been saved on this disaster',
            type: 'success',
            animation: false
          })

        //  console.log(data, "z")


          this.loadData()
        },
        reason => {
          swal.fire({
            title: 'Failed to submit form',
            text: 'possible invalid data (' + reason + ')',
            type: 'error',
            animation: false
          })
        }
      )
    },
    reviewSummary (index) {

       this.handleRequest({
        isApproved: false,
        status: '4',
        dcpcApprover: this.$session.get("userObj").firstName + " " + this.$session.get("userObj").lastName,
        _id: index.id
      })
    },

    formatedDate (data) {
      let finalDate = dateformat(data, 'dd-mm-yyyy')
      return finalDate
    },

    loadData () {
      prelimreports.getReportsLatest().then(response => {
        let district = this.$session.get('user').admin2_name_en
        let impacts = []
        const rows = []
        const processedRows = []

        for (const item of response) {
          if (
            item.status == '2' ||
            (item.status == '3' &&
              item.user.district.admin2_name_en === district)
          ) {
            const row = {
              ...item,
              TA: item._id.ta,
              disaster: item._id.disaster,
              id: item._id,
              idval: item.idval,
              createdon: item.createdon,
              injured: parseInt(item.injured),
              gvh: item._id.gvh,

              dcpcApprover: item.dcpcApprover,
              disaster: item._id.disaster,

              images: item.images,
              dcApprover: item.dcApprover,
              lactactingwomen: parseInt(item.lactactingwomen),
              mhh: parseInt(item.mhh),
              missing: parseInt(item.missing),
              dateof: item.disasterdate,
              editby: item.edits == undefined ? '' : item.edits.user,
              editon: item.edits == undefined ? '' : item.edits.edittime,

              timeof: new Date(item.createdon).toLocaleTimeString('it-IT'),
              underfive: parseInt(item.underfive),
              pregnant: parseInt(item.pregnant),
              district: item.user.district.admin2_name_en,
              dead: parseInt(item.dead),
              disabled: parseInt(item.disabled),
              fhh: parseInt(item.fhh),
              disasterdate: moment(item.disasterdate).format('DD-MM-YYYY'),
              Date:
                moment(item.disasterdate).format('DD-MM-YYYY') +
                ' ' +
                new Date(item.createdon).toLocaleTimeString('it-IT'),
              status: item.status
            }

            impacts.push(row)
          }
        }

        impacts = impacts.sort((a, b) => a.disaster.localeCompare(b.disaster))

        for (const impact of impacts) {
          const key = `${impact.TA}${impact.disaster}`
          if (!processedRows.includes(key)) {
            rows.push(impact)
            processedRows.push(key)
          } else {
            Object.assign(impact, {
              TA: '',
              district: '',
              Date: '',
              covertext: ''
            })

            rows.push(impact)
          }
        }
        this.tableData = rows.map((item, i) => {
          return {
            ...item,
            district: item.district,
            ta: item.TA,
            gvh: item.gvh,
            pregnant: item.pregnant,
            lw: item.lactactingwomen,
            date: item.Date,
            id: item.idval,
            dateof: item.dateof,
            status: item.status,
            images: item.images,
            disaster: item._id.disaster
          }
        })
      })
      /////////////
    }
  },
  created () {
    this.loadData()
  },
  async mounted () {
    admin2s.get().then(response => {
      //console.log(response.data);
      this.admin2sData = response.data
    })
  }
}
</script>
<style>
.no-border-card .card-footer {
  border-top: 0;
}

button[disabled] {
  cursor: not-allowed;
}
</style>
