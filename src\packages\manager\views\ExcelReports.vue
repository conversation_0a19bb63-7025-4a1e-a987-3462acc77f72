/* eslint-disable */
<template>
  <div class="content">
    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Disaster</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/manager/dashboard">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">REPORTS</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">ARCHIVES</li>
            </ol>
          </nav>
        </div>

        <div class="col-lg-6 col-5 text-right">
          <base-button
            size="sm"
            type="neutral"
            @click="downloadCSV({ filename: 'dmis_disasters.csv' })"
          >
            <i class="text-black ni ni-cloud-download-95"></i> EXPORT TO CSV
          </base-button>
        </div>
      </div>

      <!-- Card stats -->
    </base-header>
    <div class="container-fluid mt--6">
      <div>
        <vue-good-table
          ref="sr-table"
          :columns="columns"
          :rows="rows"
          :isLoading="isLoaded"
          :pagination-options="{ enabled: true, perPage: 10 }"
          :search-options="{ enabled: true }"
          max-height="600px"
        ></vue-good-table>
      </div>
    </div>
  </div>
</template>
<script>
import RouteBreadCrumb from "@/components/Breadcrumb/RouteBreadcrumb";
import { BasePagination } from "@/components";
import clientPaginationMixin from "@/components/PaginatedTables/clientPaginationMixin";
import swal from "sweetalert2";
import { Reports } from "../api/reports";
import "vue-good-table/dist/vue-good-table.css";
import { VueGoodTable } from "vue-good-table";
import _ from "underscore";
import $ from "jquery";
import moment from "moment";
import flatPickr from "flatpickr";
import "flatpickr/dist/flatpickr.css";
import "flatpickr/dist/themes/airbnb.css";

export default {
  mixins: [clientPaginationMixin],
  components: {
    VueGoodTable,
    BasePagination,
    RouteBreadCrumb,
  },
  data() {
    return {
      isLoaded: false,
      columns: [
        {
          label: "Disaster ID",
          field: "dinr",
          filterOptions: {
            enabled: true,
            placeholder: "filter",
          },
        },
        {
          label: "DRA ID",
          field: "oid",
          filterOptions: {
            enabled: true,
            placeholder: "filter",
          },
        },
        {
          label: "Disaster",
          field: "Disaster",
          filterOptions: {
            enabled: true,
            filterFn: this.columnFilterFn, //custom filter function that
            trigger: "enter",
            placeholder: "Select",
            filterDropdownItems: [""],
          },
        },
        {
          label: "Region",
          field: "Region",
          filterOptions: {
            enabled: true,
            filterFn: this.columnFilterFn, //custom filter function that
            trigger: "enter",
            placeholder: "Select",
            filterDropdownItems: [""],
          },
        },
        {
          label: "District",
          field: "District",
          filterOptions: {
            enabled: true,
            filterFn: this.columnFilterFn, //custom filter function that
            trigger: "enter",
            placeholder: "Select",
            filterDropdownItems: [""],
          },
        },
        {
          label: "TA_PCODE",
          field: "TA_PCODE",
          filterOptions: {
            enabled: true,
          },
        },

        {
          label: "TA",
          field: "TA",
          filterOptions: {
            enabled: true,
            filterFn: this.columnFilterFn, //custom filter function that
            trigger: "enter",
            placeholder: "Select",
            filterDropdownItems: [""],
          },
        },
        {
          label: "GVHs",
          field: "GVHS_affected",
          filterOptions: {
            enabled: true,
          },
        },
        {
          label: "Date_of_assessment_ACPC",
          field: "Date_of_assessment_ACPC",
          type: "date",
          dateInputFormat: "yyyy-MM-dd",
          dateOutputFormat: "dd-MM-yyyy",
          filterOptions: {
            enabled: true,
            placeholder: "dateacpc",
            filterFn: this.dateRangeFilter,
          },
        },
        {
          label: "Date_of_assessment_DCPC",
          field: "Date_of_assessment_DCPC",
          type: "date",
          dateInputFormat: "yyyy-MM-dd",
          dateOutputFormat: "dd-MM-yyyy",
          filterOptions: {
            enabled: true,
            placeholder: "datedcpc",
            filterFn: this.dateRangeFilter,
          },
        },
        {
          label: "Date_of_Disaster_from",
          field: "Date_of_Disaster_from",
          type: "date",
          dateInputFormat: "yyyy-MM-dd",
          dateOutputFormat: "dd-MM-yyyy",
          filterOptions: {
            enabled: true,
            placeholder: "datefrom",
            filterFn: this.dateRangeFilter,
          },
        },
        {
          label: "Date_of_Disaster_to",
          field: "Date_of_Disaster_to",
          type: "date",
          dateInputFormat: "yyyy-MM-dd",
          dateOutputFormat: "dd-MM-yyyy",
          filterOptions: {
            enabled: true,
            placeholder: "dateto",
            filterFn: this.dateRangeFilter,
          },
        },
        {
          label: "food_items_damaged_KGs_agriculture",
          field: "food_items_damaged_KGs_agriculture",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_of_crop_hectares_submerged_agriculture",
          field: "number_of_crop_hectares_submerged_agriculture",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_crop_hectares_washed_off_agriculture",
          field: "number_crop_hectares_washed_off_agriculture",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_crop_hectares_washed_off_agriculture",
          field: "number_crop_hectares_washed_off_agriculture",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_crop_hectares_washed_off_agriculture",
          field: "number_crop_hectares_washed_off_agriculture",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_crop_hectares_washed_off_agriculture",
          field: "number_crop_hectares_washed_off_agriculture",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_crop_hectares_washed_off_agriculture",
          field: "number_crop_hectares_washed_off_agriculture",
          type: "number",
          filterOptions: { enabled: true },
        },

        {
          label: "number_of_households_whose_crops_are_impacted_agriculture",
          field: "number_of_households_whose_crops_are_impacted_agriculture",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "of_crop_hectares_damaged_in_affected_households_agriculture",
          field: "of_crop_hectares_damaged_in_affected_households_agriculture",
          type: "number",
          filterOptions: { enabled: true },
        },

        {
          label: "hh_affected_per_impacted_livestock_agriculture",
          field: "hh_affected_per_impacted_livestock_agriculture",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_of_impacted_livestock_agriculture",
          field: "number_of_impacted_livestock_agriculture",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_male_HH_accomadeted_displaced",
          field: "number_male_HH_accomadeted_displaced",
          type: "number",
          filterOptions: { enabled: true },
        },

        {
          label: "number_male_HH_accomadeted_displaced",
          field: "number_male_HH_accomadeted_displaced",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_female_HH_accomadeted_displaced",
          field: "number_female_HH_accomadeted_displaced",
          type: "number",
          filterOptions: { enabled: true },
        },

        {
          label: "number_of_males_disaggregated_displaced",
          field: "number_of_males_disaggregated_displaced",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_of_females_disaggregated_displaced",
          field: "number_of_females_disaggregated_displaced",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_of_males_accomodated_displaced",
          field: "number_of_males_accomodated_displaced",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_of_females_accomodated_displaced",
          field: "number_of_females_accomodated_displaced",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_of_school_buildings_functioning_education",
          field: "number_of_school_buildings_functioning_education",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_of_school_buildings_underwater_education",
          field: "number_of_school_buildings_underwater_education",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_of_school_buildings_completely_damaged_education",
          field: "number_of_school_buildings_completely_damaged_education",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_of_school_buildings_partially_functioning_education",
          field: "number_of_school_buildings_partially_functioning_education",
          type: "number",
          filterOptions: { enabled: true },
        },

        {
          label: "number_of_school_buildings_closed_education",
          field: "number_of_school_buildings_closed_education",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "males_of_out_school_education",
          field: "males_of_out_school_education",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "females_of_out_school_education",
          field: "females_of_out_school_education",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "is_food_available_food",
          field: "is_food_available_food",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_of_facilities_partially_functioning_health",
          field: "number_of_facilities_partially_functioning_health",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_of_facilities_on_verge_of_closing_health",
          field: "number_of_facilities_on_verge_of_closing_health",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_facilities_closed_health",
          field: "number_facilities_closed_health",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "state_of_medical_supply_availability_health",
          field: "state_of_medical_supply_availability_health",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "state_of_health_personnel_availability_health",
          field: "state_of_health_personnel_availability_health",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_severely_affected_livelihoods",
          field: "number_severely_affected_livelihoods",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_slightly_affected_livelihoods",
          field: "number_slightly_affected_livelihoods",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "impacted_females_protection",
          field: "impacted_females_protection",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "impacted_males_protection",
          field: "impacted_males_protection",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "state_of_access_to_main_roads_logistics",
          field: "state_of_access_to_main_roads_logistics",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_of_affected_males_nutrition",
          field: "number_of_affected_males_nutrition",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_of_affected_females_nutrition",
          field: "number_of_affected_females_nutrition",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "FHH_with_safe_water_wash",
          field: "FHH_with_safe_water_wash",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "MHH_with_safe_water_wash",
          field: "MHH_with_safe_water_wash",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "FHH_with_toilet_access_wash",
          field: "FHH_with_toilet_access_wash",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "MHH_with_toilet_access_wash",
          field: "MHH_with_toilet_access_wash",
          type: "number",
          filterOptions: { enabled: true },
        },

        {
          label: "FHH_risking_contamination_wash",
          field: "FHH_risking_contamination_wash",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "MHH_risking_contamination_wash",
          field: "MHH_risking_contamination_wash",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "Houses_partly_damaged_shelter",
          field: "Houses_partly_damaged_shelter",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "Houses_underwater_shelter",
          field: "Houses_underwater_shelter",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "Houses_completely_shelter",
          field: "Houses_completely_shelter",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_of_males_without_shelter_shelter",
          field: "number_of_males_without_shelter_shelter",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_of_females_without_shelter_shelter",
          field: "number_of_females_without_shelter_shelter",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_of_female_HH_with_houses_damaged_shelter",
          field: "number_of_female_HH_with_houses_damaged_shelter",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_of_male_HH_with_houses_damaged_shelter",
          field: "number_of_male_HH_with_houses_damaged_shelter",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_of_injured_females_shelter",
          field: "number_of_injured_females_shelter",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_of_injured_males_in_category_shelter",
          field: "number_of_injured_males_in_category_shelter",
          type: "number",
          filterOptions: { enabled: true },
        },

        {
          label: "number_of_dead_females_shelter",
          field: "number_of_dead_females_shelter",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_of_dead_males_shelter",
          field: "number_of_dead_males_shelter",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_of_displaced_male_displaced",
          field: "number_of_displaced_male_displaced",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "number_of_displaced_females_displaced",
          field: "number_of_displaced_females_displaced",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "No_of_FHH_with_1_2_month_Food_Availability_food",
          field: "No_of_FHH_with_1_2_month_Food_Availability_food",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "No_of_FHH_with_less_than_1_month_Food_Availability_food",
          field: "No_of_FHH_with_less_than_1_month_Food_Availability_food",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "No_of_FHH_with_more_than_2_months_Food_Availability_food",
          field: "No_of_FHH_with_more_than_2_months_Food_Availability_food",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "No_of_FHH_who_lost_Food_Stock_food",
          field: "No_of_FHH_who_lost_Food_Stock_food",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "No_of_MHH_with_1_2_month_Food_Availability_food",
          field: "No_of_MHH_with_1_2_month_Food_Availability_food",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "No_of_MHH_with_less_than_1_month_Food_Availability_food",
          field: "No_of_MHH_with_less_than_1_month_Food_Availability_food",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "No_of_MHH_with_more_than_2_months_Food_Availability_food",
          field: "No_of_MHH_with_more_than_2_months_Food_Availability_food",
          type: "number",
          filterOptions: { enabled: true },
        },
        {
          label: "No_of_MHH_who_lost_Food_Stock_food",
          field: "No_of_MHH_who_lost_Food_Stock_food",
          type: "number",
          filterOptions: { enabled: true },
        },
      ],
      rows: [],
    };
  },
  methods: {
    myColumnFilter: function (data, filterString) {
     
    },
    dateRangeFilter(data, filterString) {
      let dateRange = filterString.split("to");
      let startDate = Date.parse(dateRange[0]);
      let endDate = Date.parse(dateRange[1]);
      return (data =
        Date.parse(data) >= startDate && Date.parse(data) <= endDate);
    },

    convertArrayOfObjectsToCSV(args) {
      var result, ctr, keys, columnDelimiter, lineDelimiter, data;

      data = args.data || null;
      if (data == null || !data.length) {
        return null;
      }

      columnDelimiter = args.columnDelimiter || ",";
      lineDelimiter = args.lineDelimiter || "\n";

      keys = Object.keys(data[0]);

      result = "";
      result += keys.join(columnDelimiter);
      result += lineDelimiter;

      data.forEach(function (item) {
        ctr = 0;
        keys.forEach(function (key) {
          if (ctr > 0) result += columnDelimiter;

          result += item[key];
          ctr++;
        });
        result += lineDelimiter;
      });

      return result;
    },
    downloadCSV(args) {
      var data, filename, link;
      var csv = this.convertArrayOfObjectsToCSV({
        data: this.$refs["sr-table"].filteredRows[0].children,
      });
      if (csv == null) return;

      filename = args.filename || "export.csv";

      if (!csv.match(/^data:text\/csv/i)) {
        csv = "data:text/csv;charset=utf-8," + csv;
      }
      data = encodeURI(csv);

      link = document.createElement("a");
      link.setAttribute("href", data);
      link.setAttribute("download", filename);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
  },
  mounted() {
    Reports.getReports().then((reports) => {
      this.isLoaded = false;
      reports.data.forEach((dra) => {
        dra.Date_of_assessment_ACPC = dra.Date_of_assessment_ACPC.split("T")[0];
        dra.Date_of_assessment_DCPC = dra.Date_of_assessment_DCPC.split("T")[0];
        dra.Date_of_Disaster_from = dra.Date_of_Disaster_from.split("T")[0];
        dra.Date_of_Disaster_to = dra.Date_of_Disaster_to.split("T")[0];
        dra.GVHS_affected = dra.GVHS_affected.split(",").join("|");
        this.rows.push(dra);
      });
      this.$refs["sr-table"].typedColumns.slice(2, 7).forEach((item, index) => {
        if (item.field === "TA_PCODE") return;
        this.$refs["sr-table"].typedColumns[
          index + 2
        ].filterOptions.filterDropdownItems = _.uniq(
          _.map(this.rows, item.field)
        )
          .sort()
          .map(function (o) {
            if (o != "") {
              return {
                value: o,
                text: o,
              };
            } else {
              return {
                value: o,
                text: "empty",
              };
            }
          });
      });

      let inputs = [
        'input[placeholder="dateacpc"]',
        'input[placeholder="datedcpc"]',
        'input[placeholder="datefrom"]',
        'input[placeholder="dateto"]',
      ];
      inputs.forEach(function (input) {
        flatPickr(input, {
          dateFormat: "m-d-Y",
          mode: "range",
          allowInput: true,
        });
      });
    });
    this.isLoaded = false;
  },
};
</script>
<style>
.no-border-card .card-footer {
  border-top: 0;
}
</style>
