import axios from 'axios'

const resource = process.env.VUE_APP_ENGINE_URL + '/forms/dra'

export class draforms {
  static get (_id = null) {
    if (_id == null) {
      return axios.get(resource).then(response => {
        return response
      })
    } else {
      return axios.get(resource + '/' + _id).then(response => {
        return response
      })
    }
  }
  static count () {
    return axios.get(resource + '/count').then(response => {
      return response
    })
  }
  static async create (data) {
    let response = await axios.post(resource, data).then(response => {
      return response
    })
    return response
  }
  static async remove (_id) {
    let response = await axios.delete(resource + '/' + _id).then(response => {
      return response
    })
    return response
  }

  static async query (query) {
    let response = await axios
      .post(resource + '/query', query)
      .then(response => {
        return response
      })
    return response
  }

  static update (data) {
    return axios.patch(resource + '/' + data._id, data).then(response => {
      return response
    })
  }
}
