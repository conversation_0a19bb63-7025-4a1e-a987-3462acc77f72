$sort-chevron-width: 5px;

.vgt-table{
  & th{
    padding: .75em 1.5em .75em .75em;
    vertical-align: middle;
    position: relative;
    &.sortable{
      button {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background: transparent;
        border: none;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        &:focus{
          outline: none;
        }
        &:after{
          content: '';
          position: absolute;
          height: 0px;
          width: 0px;
          right: 6px;
          top: 50%;
          margin-top: -7px;
          border-left: $sort-chevron-width solid transparent;
          border-right: $sort-chevron-width solid transparent;
          border-bottom: $sort-chevron-width solid $chevron-color;
        }
        &:before{
          content: '';
          position: absolute;
          height: 0px;
          width: 0px;
          right: 6px;
          top: 50%;
          margin-bottom: -7px;
          border-left: $sort-chevron-width solid transparent;
          border-right: $sort-chevron-width solid transparent;
          border-top: $sort-chevron-width solid $chevron-color;
        }
      }
    }
  }
  & th.line-numbers, & th.vgt-checkbox-col {
    padding: 0 .75em 0 .75em;
    color: $text-color;
    border-right: 1px solid $border-color;
    word-wrap: break-word;
    width: 25px;
    text-align: center;
    background: linear-gradient($thead-bg-color-1, $thead-bg-color-2);
  }
  & th.filter-th {
    padding: .75em .75em .75em .75em;
  }

  th.vgt-row-header{
    border-bottom: 2px solid $border-color;
    border-top: 2px solid $border-color;
    background-color: lighten($border-color, 10%);
    .triangle {
      width: 24px;
      height: 24px;
      border-radius: 15%;
      position: relative;
      margin: 0px 8px;
      &:after {
        content: '';
        position: absolute;
        display: block;
        left: 50%;
        top: 50%;
        margin-top: -6px;
        border-top: 6px solid transparent;
        border-bottom: 6px solid transparent;
        border-left: 6px solid $text-color;
        margin-left: -3px;
        transition: 0.3s ease transform;
      }
      &.expand:after {
        transform: rotate(90deg);
      }
    }
  }

  thead th{
    color: $text-color;
    vertical-align: bottom;
    border-bottom:  1px solid $border-color;
    padding-right: 1.5em;
    background: linear-gradient($thead-bg-color-1, $thead-bg-color-2);
    &.vgt-checkbox-col{
      vertical-align: middle;
    }
    &.sorting-asc button {
      &:after{
        border-bottom: $sort-chevron-width solid $link-color;
      }
    }
    &.sorting-desc button {
      &:before{
        border-top: $sort-chevron-width solid $link-color;
      }
    }
  }
}
