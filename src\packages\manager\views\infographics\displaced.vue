<template>
  <div>
    <div style="background:#F4F4F4">
      <div class="row pt-4">
        <div class="col">
          <div class="card">
            <div class="card-header">
              <span style="color:teal;font-size:150%">
                <b>
                  DISPLACED [
                  <span style="color:red"
                    >{{ data.Disaster }} - {{ data.District }}</span
                  >
                  ]
                </b>
                <b class="pull-right"
                  >ASSESSMENT PERIOD :
                  {{
                    dateFormate(
                      new Date(data.Date_of_Disaster_from).toLocaleDateString(
                        "en-US"
                      )
                    )
                  }}
                  -
                  {{
                    dateFormate(
                      new Date(data.Date_of_Disaster_to).toLocaleDateString(
                        "en-US"
                      )
                    )
                  }}</b
                >
              </span>
              <hr />
              <div class="row">
                <base-input label="TA" class="col-xl-3">
                  <el-select
                    v-model="selected"
                    @change="getInfoGraphicsData(selected)"
                    placeholder="select"
                  >
                    <el-option
                      v-for="option in data.TAList"
                      :key="option.label"
                      :label="option.label"
                      :value="option.value + ',' + option.label"
                    ></el-option>
                  </el-select>
                </base-input>
                <base-input
                  label="GVHs"
                  class="col-xl-9"
                  :value="data.GVHS_affected"
                  readonly
                ></base-input>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-xl-4 col-md-6">
          <stats-card
            title="Displaced people"
            type="gradient-green"
            :sub-title="
              (
                parseInt(data.number_of_males_disaggregated_displaced) +
                  parseInt(data.number_of_females_disaggregated_displaced) || 0
              ).toString()
            "
            icon="icon-ocha-affected-population"
          >
            <template slot="footer">
              <span>
                <i class="icon-ocha-affected-man"></i>
                {{ data.number_of_males_accomodated_displaced }}
              </span>
              <span class="pull-right">
                <i class="icon-ocha-affected-woman"></i>
                {{ data.number_of_females_disaggregated_displaced }}
              </span>
            </template>
          </stats-card>
        </div>

        <div class="col-xl-4 col-md-6">
          <stats-card
            title="Displaced households"
            type="gradient-green"
            :sub-title="
              (
                parseInt(data.number_of_displaced_male_displaced) +
                  parseInt(data.number_of_displaced_females_displaced) || 0
              ).toString()
            "
            icon="icon-unhcr-locations-settlement"
          >
            <template slot="footer">
              <span>
                <i class="icon-ocha-affected-man"></i>
                {{ data.number_of_displaced_male_displaced }}
              </span>
              <span class="pull-right">
                <i class="icon-ocha-affected-woman"></i>
                {{ data.number_of_displaced_females_displaced }}
              </span>
            </template>
          </stats-card>
        </div>
        <div class="col-xl-4 col-md-6">
          <stats-card
            title="Accommodated people"
            type="gradient-green"
            :sub-title="
              (
                parseInt(data.number_of_males_accomodated_displaced) +
                  parseInt(data.number_of_females_accomodated_displaced) || 0
              ).toString()
            "
            icon="icon-unhcr-locations-settlement"
          >
            <template slot="footer">
              <span>
                <i class="icon-ocha-affected-man"></i>
                {{ data.number_of_males_accomodated_displaced }}
              </span>
              <span class="pull-right">
                <i class="icon-ocha-affected-woman"></i>
                {{ data.number_of_females_accomodated_displaced }}
              </span>
            </template>
          </stats-card>
        </div>
      </div>
      <div class="row">
        <div class="col-xl-8">
          <div class="row">
            <div class="col-xl-6">
              <card>
                <template slot="header">
                  <!-- Subtitle -->
                  <h6 class="surtitle">Displacement status [ People ]</h6>
                  <!-- Title -->
                </template>
                <div>
                  <doughtNutChart
                    :data="doughnutChartData"
                    :options="doughnutChartOptions"
                  ></doughtNutChart>
                </div>
              </card>
            </div>
            <div class="col-xl-6">
              <card>
                <template slot="header">
                  <!-- Subtitle -->
                  <h6 class="surtitle">People affected</h6>
                </template>
                <div>
                  <barChart
                    :data="barChartData"
                    :options="barChartOptions"
                  ></barChart>
                </div>
              </card>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import flatPicker from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";

import RouteBreadCrumb from "@/components/Breadcrumb/RouteBreadcrumb";
import BaseHeader from "@/components/BaseHeader";
import StatsCard from "@/components/Cards/StatsCard";
import { Select, Option } from "element-ui";
import displacedMap from "./components/displacedMap";
import doughtNutChart from "./components/doughtNutChart";
import barChart from "./components/barChart";
import TagsInput from "@/components/Inputs/TagsInput";
import moment from "moment";

window.jQuery = require("jquery");

function randomScalingFactor() {
  return Math.round(Math.random() * 100);
}

export default {
  components: {
    StatsCard,
    BaseHeader,
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    flatPicker,
    displacedMap,
    doughtNutChart,
    barChart,
    TagsInput
  },
  props: ["data"],
  data() {
    return {
      TAList: [],
      selected: {},
      tags: ["Floods", "2019-01-01 to 2019-12-31"],
      doughnutChartOptions: {
        hoverBorderWidth: 20
      },
      doughnutChartData: {
        hoverBackgroundColor: "red",
        hoverBorderWidth: 10,
        labels: ["Displaced", "Accommodated"],
        datasets: [
          {
            label: "people",
            backgroundColor: ["teal", "#a97142"],
            data: [
              parseInt(this.data.number_of_males_accomodated_displaced) +
                parseInt(this.data.number_of_females_disaggregated_displaced),
              parseInt(this.data.number_of_males_accomodated_displaced) +
                parseInt(this.data.number_of_females_accomodated_displaced)
            ]
          }
        ]
      },
      barChartOptions: {
        hoverBorderWidth: 20
      },
      barChartData: {
        hoverBackgroundColor: "red",
        hoverBorderWidth: 10,
        labels: ["Displaced", "Accommodated"],
        datasets: [
          {
            label: "people",
            backgroundColor: "teal",
            data: [
              parseInt(this.data.number_of_males_accomodated_displaced) +
                parseInt(this.data.number_of_females_disaggregated_displaced),
              parseInt(this.data.number_of_males_accomodated_displaced) +
                parseInt(this.data.number_of_females_accomodated_displaced)
            ]
          }
        ]
      },

      filter: {
        disasters: "Floods",
        range: "2019-01-01 to 2019-12-31"
      },
      disasters: [
        {
          name: "Heavy Rains"
        },
        {
          name: "Earth quake"
        },
        {
          name: "Floods"
        }
      ],
      districts: [
        {
          label: "Chikwawa",
          value: "Chikwawa",
          region: "South"
        },
        {
          label: "Mangochi",
          value: "Mangochi",
          region: "South"
        },
        {
          label: "Balaka",
          value: "Balaka",
          region: "North"
        },
        {
          label: "Phalombe",
          value: "Phalombe",
          region: "Central"
        }
      ],
      regions: [
        {
          region: "South"
        },
        {
          region: "Central"
        },
        {
          region: "North"
        },
        {
          region: "East"
        }
      ],
      houses: [
        {
          id: 1,
          name: "Completely damaged",
          number: 67,
          males: 78,
          females: 59
        },
        {
          id: 2,
          name: "Partly damaged",
          number: 400,
          males: 76,
          females: 79
        },
        {
          id: 3,
          name: "Underwater",
          number: 79,
          males: 68,
          females: 99
        }
      ]
    };
  },
  methods: {
    dateFormate(dateData) {
      const cur_date = dateData;
      const formattedDate = moment(cur_date).format("DD/MM/YYYY");

      return formattedDate;
    },
    getInfoGraphicsData(selectedTA) {
      var data = selectedTA.split(",");
      
      var TA = data[1];
      var ID = data[0];
      if (TA === "All") {
        this.$emit("Dinr", ID);
      } else {
        this.$emit("Dra", ID);
      }
    },
    addRemoveValuesToTagsFromMap(district) {
      var found = this.tags.find(element => element === district);
     
      if (found != undefined) {
        var index = this.tags.indexOf(district);
        if (index !== -1) this.tags.splice(index, 1);
      } else {
        this.tags.push(disnpmtrict);
      }
    }
  },
  mounted() {
    this.selected = this.data.TAname;
  }
};
</script>
<style>
#malawiMap {
}
@import "../../../../assets/fonts/font-humanitarian.css";
</style>
