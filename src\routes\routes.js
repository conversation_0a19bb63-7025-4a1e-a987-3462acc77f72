//Router
import router from './router'

const AuthLayout = () => import('@/views/Pages/AuthLayout.vue')
// GeneralViews
const NotFound = () => import('@/views/GeneralViews/NotFoundPage.vue')

// Pages

import Login from '@/views/Pages/Login.vue'
import ForgotPassword from '../views/Pages/ForgotPassword.vue'
import ResetPassword from '../views/Pages/ResetPassword.vue'
import Passwordreset from '@/views/Pages/PasswordResetPage.vue'
import outsidereport from '@/views/Pages/outsideReport.vue'
import createNewPassword from '@/views/Pages/CreateNewPasswordPage.vue'
import ChangePasswordComponent from '@/views/Pages/changePassword.vue'


//CLUSTERUSER

const clusterDashboardLayout = () =>
  import('@/packages/clusteruser/layout/DashboardLayout.vue')
const clusterreports = () => import('@/packages/clusteruser/views/Reports.vue')

const clusterEditProfile = () =>
  import('@/packages/clusteruser/pages/UserProfile/EditProfileForm.vue')


const clusterReviewreports = () =>
  import('@/packages/clusteruser/views/Clusters/Logistics/Edit/Logistics.data.review.vue')


  const clusterEditreports = () =>
  import('@/packages/clusteruser/views/Clusters/Logistics/Edit/Logistics.data.edit.vue')

const dcpcEditreports = () =>
  import('@/packages/dcpc/views/Edit/prelim.data.edit.vue')



const dpremreportsedit = () =>
  import('@/packages/districtmanager/views/Edit/prelim.data.edit.vue')



const clusterAccountsEdit = () =>
  import('@/packages/clusteruser/pages/Accounts/Edit.vue')

//ADMIN
// Layouts
const AdminDashboardLayout = () =>
  import('@/packages/admin/layout/DashboardLayout.vue')
// Dashboard
const adminDashboard = () =>
  import('@/packages/admin/pages/Dashboard/Dashboard.vue')
//Accounts
const adminAccounts = () =>
  import('@/packages/admin/pages/Accounts/Accounts.vue')
const adminAccountsRegister = () =>
  import('@/packages/admin/pages/Accounts/Register.vue')
const adminEmailNotification = () =>
  import('@/packages/admin/pages/Accounts/Notifications.vue')
const adminAccountsEdit = () =>
  import('@/packages/admin/pages/Accounts/Edit.vue')
const adminUpload = () => import('@/packages/admin/pages/uploads/upload.vue')
const adminUploadList = () => import('@/packages/admin/pages/uploads/List.vue')
const submittedReports = () => import('@/packages/admin/pages/archives/Reports.vue')
const archivedReports = () => import('@/packages/admin/pages/archives/archiveReports.vue')
//

const dcAccountsEdit = () =>
  import('@/packages/districtcommissioner/pages/Accounts/Edit.vue')

const dmAccountsEdit = () =>
  import('@/packages/districtmanager/pages/Accounts/Edit.vue')


const mgAccountsEdit = () =>
  import('@/packages/manager/pages/Accounts/Edit.vue')

const AdminEditProfile = () =>
  import('@/packages/admin/pages/UserProfile/EditProfileForm.vue')

const managerEditProfile = () =>
  import('@/packages/manager/pages/UserProfile/EditProfileForm.vue')
const managerChangePassword = () =>
  import('@/packages/manager/pages/UserProfile/changePassword.vue')

const districtmanagerEditProfile = () =>
  import('@/packages/districtmanager/pages/UserProfile/EditProfileForm.vue')

const districtmanagerChangePassword = () =>
  import('@/packages/districtmanager/pages/Accounts/changePassword.vue')

const districtcommissionerEditProfile = () =>
  import(
    '@/packages/districtcommissioner/pages/UserProfile/EditProfileForm.vue'
  )
const districtcommissionerChangePassword = () =>
  import('@/packages/districtcommissioner/pages/UserProfile/changePassword.vue')
//

//MANAGER
// Layouts

const AcknowledgeReceipt = () =>
  import('@/packages/manager/views/AcknowledgeReceipt.vue')



const ManagerDashboardLayout = () =>
  import('@/packages/manager/layout/DashboardLayout.vue')
const reports = () => import('@/packages/manager/views/Reports.vue')

const preliminaryReports = () =>
  import('@/packages/manager/views/PreliminaryReports.vue')


const LeanseasonReports = () =>
  import('@/packages/manager/views/LeanseasonReports.vue')

const campsReports = () =>
  import('@/packages/manager/views/CampsReports.vue')


const dcpcpreliminaryReports = () =>
  import('@/packages/dcpc/views/PrelimReports.vue')

const excelreports = () => import("@/packages/manager/views/ExcelReports.vue");
const dinrreports = () => import("@/packages/manager/views/infographics.vue");
const infographics = () => import("@/packages/manager/views/infographics.vue");
const pDFReports = () => import("@/packages/manager/views/review.vue");
const pDFReportsOld = () => import("@/packages/manager/views/reviewOld.vue");

const archivedpDFReports = () => import("@/packages/admin/pages/archives/review.vue");
const archivedpDFReportsOld = () => import("@/packages/admin/pages/archives/reviewOld.vue");


const pDFPremiReports = () => import("@/packages/manager/views/reviewPrelim.vue");
const pDFSummaryReports = () =>
  import("@/packages/manager/views/reviewSummary.vue");
const pDFSummaryReportsOld = () =>
  import("@/packages/manager/views/reviewSummaryOld.vue");
const metabase = () => import("@/packages/manager/views/metabase.vue");

//Dashboard
//const managerDashboard = () => import('@/packages/manager/views/HomeDashboard.vue')
const managerDashboard = () =>
  import('@/packages/manager/views/HomeDashboard.vue')
const dcDashboard = () =>
  import('@/packages/districtcommissioner/views/HomeDashboard.vue')

const DistrictManagerDashboardLayout = () =>
  import('@/packages/districtmanager/layout/DashboardLayout.vue')
const dreports = () => import('@/packages/districtmanager/views/Reports.vue')
const pDFDMPremiReports = () =>
  import('@/packages/districtmanager/views/reviewPrelim.vue')
const dpremreports = () =>
  import('@/packages/districtmanager/views/PrelimReports.vue')
const dreportsdetailsstatus = () =>
  import("@/packages/districtmanager/views/reviewStatus.vue");
const dreportsdetailsstatusOld = () =>
  import("@/packages/districtmanager/views/reviewStatusOld.vue");

const unapproveddreports = () =>
  import('@/packages/districtmanager/views/UnapprovedReports.vue')
const approveddreports = () =>
  import('@/packages/districtmanager/views/ApprovedReports.vue')
const rejecteddreports = () =>
  import('@/packages/districtmanager/views/RejectedReports.vue')
const unapproveddetaileddreports = () =>
  import('@/packages/districtmanager/views/reviewApprovalReject.vue')
const ddRAReports = () =>
  import('@/packages/districtmanager/views/DraReports.vue')
const dinfographics = () =>
  import("@/packages/districtmanager/views/infographics.vue");
const dpDFReports = () => import("@/packages/districtmanager/views/review.vue");
const dpDFReportsOld = () => import("@/packages/districtmanager/views/reviewOld.vue");
const unApproveddpDFReports = () =>
  import('@/packages/districtmanager/views/UnapprovedReports.vue')
const UnsignedReports = () =>
  import('@/packages/districtmanager/views/UnsignedReports.vue')
const ReportStatus = () =>
  import('@/packages/districtmanager/views/reportStatus.vue')
const Trends2 = () => import('@/packages/dashboards/trends/index.vue')
const managerTrends = () =>
  import('@/packages/dashboards/trends/managerTrends.vue')

const Trends = () => import('@/packages/dashboards/trends/index2.vue')
const ReportStat = () =>
  import('@/packages/districtmanager/views/reportStatus.vue')
const Editforms = () =>
  import('@/packages/districtmanager/views/EditForms/dinrforms.vue')
const EditDra = () =>
  import(
    '@/packages/districtmanager/views/EditForms/editDraForms/draFormEdit.vue'
  )
const dpDFSummaryReports = () =>
  import("@/packages/districtmanager/views/reviewSummary.vue");
const dpDFSummaryReportsOld = () =>
  import("@/packages/districtmanager/views/reviewSummaryOld.vue");

const dmetabase = () => import('@/packages/districtmanager/views/metabase.vue')

//Dashboard
const dmanagerDashboard = () =>
  import('@/packages/districtmanager/views/HomeDashboard.vue')

//Dashboard
const dcpcDashboard = () =>
  import('@/packages/dcpc/views/HomeDashboard.vue')


//Dashboard
const dcpcDashboardLayout = () =>
  import('@/packages/dcpc/views/HomeDashboard.vue')

//Accounts
const districtManagedAccounts = () =>
  import('@/packages/districtmanager/pages/Accounts/Accounts.vue')
//const districtManagedAccountsRegister = () => import('@/packages/districtmanager/pages/Accounts/Register.vue')
const districtManagedAccountsRegister = () =>
  import('@/packages/districtmanager/pages/Accounts/Register.vue')

const districtManagedAccountsEdit = () =>
  import('@/packages/districtmanager/pages/Accounts/Edit.vue')

//Signature
const districtManagedSignatures = () =>
  import('@/packages/districtmanager/pages/Signatures/Signatures.vue')

const districtManagedSignaturesRegister = () =>
  import('@/packages/districtmanager/pages/Signatures/Register.vue')

const districtManagedSignaturesEdit = () =>
  import('@/packages/districtmanager/pages/Signatures/Edit.vue')

const DistrictcommissionerDashboardLayout = () =>
  import('@/packages/districtcommissioner/layout/DashboardLayout.vue')


const DcpcDashboardLayout = () =>
  import('@/packages/dcpc/layout/DashboardLayout.vue')

const dcreports = () =>
  import("@/packages/districtcommissioner/views/Reports.vue");
const pDFDCPremiReports = () => import("@/packages/districtcommissioner/views/reviewPrelim.vue");

const dcreportsdetailsstatus = () =>
  import('@/packages/districtcommissioner/views/reviewStatus.vue')

const dcreportsdetailsstatusOld = () =>
  import("@/packages/districtcommissioner/views/reviewStatusOld.vue");

const dcunapproveddreports = () =>
  import('@/packages/districtcommissioner/views/UnapprovedReports.vue')

const dcapproveddreports = () =>
  import('@/packages/districtcommissioner/views/ApprovedReports.vue')

const dcrejecteddreports = () =>
  import('@/packages/districtcommissioner/views/RejectedReports.vue')

const dcunapproveddetaileddreports = () =>
  import('@/packages/districtcommissioner/views/reviewApprovalReject.vue')

const dcdRAReports = () =>
  import('@/packages/districtcommissioner/views/DraReports.vue')

const dcinfographics = () =>
  import('@/packages/districtcommissioner/views/infographics.vue')

const dcpDFReports = () =>
  import('@/packages/districtcommissioner/views/review.vue')

const dcpDFReportsOld = () =>
  import("@/packages/districtcommissioner/views/reviewOld.vue");

const dcremreports = () => import("@/packages/districtcommissioner/views/PrelimReports.vue");

const dcunApproveddpDFReports = () =>
  import('@/packages/districtcommissioner/views/UnapprovedReports.vue')

const dcpDFSummaryReports = () =>
  import('@/packages/districtcommissioner/views/reviewSummary.vue')

const dcpDFSummaryReportsOld = () =>
  import("@/packages/districtcommissioner/views/reviewSummaryOld.vue");

const dcmetabase = () =>
  import('@/packages/districtcommissioner/views/metabase.vue')

//Dashboard
const dcommissionerDashboard = () =>
  import('@/packages/districtcommissioner/views/HomeDashboard.vue')

//Signature
const districtcommissionerSignatures = () =>
  import('@/packages/districtcommissioner/pages/Signatures/Signatures.vue')

const districtcommissionerSignaturesRegister = () =>
  import('@/packages/districtcommissioner/pages/Signatures/Register.vue')

const districtcommissionerSignaturesEdit = () =>
  import('@/packages/districtcommissioner/pages/Signatures/Edit.vue')
const appAbout = () => import('@/packages/about.vue')

let authPages = {
  path: '/',
  component: AuthLayout,
  name: 'Authentication',
  children: [
    {
      path: '/login',
      name: 'Login',
      component: Login
    },
    {
      path: '/forgot-password',
      name: 'ForgotPassword',
      component: ForgotPassword,
    },
    {
      path: '/reset-password',
      name: 'ResetPassword',
      component: ResetPassword,
    },
    {
      path: '/changePassword',
      name: 'changePassword',
      component: ChangePasswordComponent
    },

    { path: '*', component: NotFound }
  ]
}

let adminPages = {
  path: '/admin',
  component: AdminDashboardLayout,
  redirect: '/admin/dashboard',
  name: 'admin',
  beforeEnter: (to, from, next) => {
    if (
      router.app.$session.exists() &&
      router.app.$session.get('jwturole') == 'admin'
    ) {
      next()
    } else {
      next('/login')
    }
  },
  children: [
    {
      path: '/admin/dashboard',
      name: 'AdminDashboard',
      component: adminDashboard
    },
    {
      path: "/admin/detailsreport/:uuid",
      name: "archivedpDFReports",
      component: archivedpDFReports
    },
    {
      path: "/admin/detailsreportOld/:uuid",
      name: "archivedpDFReportsOld",
      component: archivedpDFReportsOld
    },
    {
      path: '/admin/accounts/',
      name: 'AdminAllAccounts',
      component: adminAccounts
    },
    {
      path: '/admin/accounts/register',
      name: 'AdminRegisterAccounts',
      component: adminAccountsRegister
    },
    {
      path: '/admin/accounts/notifications',
      name: 'AdminNotifications',
      component: adminEmailNotification
    },
    {
      path: '/admin/accounts/edit/:id',
      name: 'AdminEditAccounts',
      props: true,
      component: adminAccountsEdit
    },
    {
      path: '/admin/uploads/upload',
      name: 'createAdminUpload',
      props: true,
      component: adminUpload
    },
    {
      path: '/admin/uploads/list',
      name: 'ListAdminUpload',
      props: true,
      component: adminUploadList
    },
    {
      path: '/admin/archive/reports',
      name: 'submittedReports',
      props: true,
      component: submittedReports
    },
    {
      path: '/admin/archive/archivedReports',
      name: 'archivedReports',
      props: true,
      component: archivedReports
    },
    {
      path: '/admin/about',
      name: 'AboutAdmin',
      component: appAbout
    },
    {
      path: '/admin/profile/:id',
      name: 'AdminEditProfile',
      props: true,
      component: AdminEditProfile
    },
    { path: '*', component: NotFound }
  ]
}

let clusterPages = {
  path: '/clusteruser',
  component: clusterDashboardLayout,
  redirect: '/clusteruser/reports',
  name: 'Cluster',
  beforeEnter: (to, from, next) => {
    if (
      router.app.$session.exists() &&
      router.app.$session.get('jwturole') == 'cluster user' &&
      router.app.$session.get('userObj').cluster_user_type == "Cluster Lead" || router.app.$session.get('userObj').cluster_user_type == "Cluster District coordinator"
      || router.app.$session.get('userObj').cluster_user_type == "Roads Authority"
    ) {
      next()
    } else {
      next('/login')
    }
  },
  children: [
    {
      path: '/clusteruser/reports/',
      name: 'clusterreports',
      component: clusterreports
    },

    {
      path: '/clusteruser/reports/:id',
      name: 'clusterEditreports',
      props: true,
      component: clusterEditreports
    },


    {
      path: '/clusteruser/reviewreports/:id',
      name: 'clusterReviewreports',
      props: true,
      component: clusterReviewreports
    },

    {
      path: '/clusteruser/about',
      name: 'Aboutcluster',
      component: appAbout
    },

    {
      path: '/clusteruser/profile/:id',
      name: 'clusterEditProfile',
      props: true,
      component: clusterEditProfile
    },

    {
      path: '/clusteruser/accounts/edit/:id',
      name: 'clusterEditAccounts',
      props: true,
      component: clusterAccountsEdit
    },
    { path: '*', component: NotFound }
  ]
}

let managePages = {
  path: '/manager',
  component: ManagerDashboardLayout,
  redirect: '/manager/dashboard',
  name: 'Manager',
  beforeEnter: (to, from, next) => {
    if (
      router.app.$session.exists() &&
      router.app.$session.get('jwturole') == 'manager'
    ) {
      next()
    } else {
      next('/login')
    }
  },
  children: [
    {
      path: '/manager/dashboard',
      name: 'ManagerDashboard',
      component: managerDashboard
    },
    {path: '/manager/acknowledgment',
      name:'AcknowledgeReceipt',
      component:AcknowledgeReceipt
    },
    {
      path: '/manager/reports/',
      name: 'reports',
      component: reports
    },
    {
      path: '/manager/preliminary/',
      name: 'preliminary',
      component: preliminaryReports
    },


    {
      path: '/manager/leanseason/',
      name: 'leanseason',
      component: LeanseasonReports
    },

    {
      path: '/manager/camps/',
      name: 'campsReports',
      component: campsReports
    },


    {
      path: '/manager/trends/',
      name: 'trends',
      component: managerTrends
    },
    {
      path: '/manager/reports/excel',
      name: 'excelreports',
      component: excelreports
    },
    ,
    {
      path: '/manager/dinrreports/:uuid',
      name: 'dnirReports',
      component: dinrreports
    },
    {
      path: '/manager/infographics/:uuid',
      name: 'infographics',
      component: infographics
    },
    {
      path: '/manager/metabase/',
      name: 'metabase',
      component: metabase
    },
    {
      path: '/manager/detailsreport/:uuid',
      name: 'pDFReports',
      component: pDFReports
    },
    {
      path: "/manager/detailsreportOld/:uuid",
      name: "pDFReports",
      component: pDFReportsOld
    },
    {
      path: "/manager/detailsprelimreport/:_id",
      name: "pDFPremiReports",
      component: pDFPremiReports
    },
    {
      path: '/manager/summaryreport/:uuid',
      name: 'pDFSummaryReports',
      component: pDFSummaryReports
    },
    {
      path: "/manager/summaryreportOld/:uuid",
      name: "pDFSummaryReports",
      component: pDFSummaryReportsOld
    },
    {
      path: "/manager/about",
      name: "AboutManager",
      component: appAbout
    },
    {
      path: '/manager/profile/:id',
      name: 'managerEditProfile',
      props: true,
      component: managerEditProfile
    },
    {
      path: '/manager/profile/:id',
      name: 'managerChangePassword',
      props: true,
      component: managerChangePassword
    },

    {
      path: '/manager/accounts/edit/:id',
      name: 'mgEditAccounts',
      props: true,
      component: mgAccountsEdit
    },
    {
      path: '/manager/detailsprelimreport/:id',
      name: 'DetailPrelim',
      component: () => import('@/packages/manager/views/Detailprelim.vue'),
      props: true
    },
    {
      path: '/manager/reports/summary/:id',
      name: 'SummaryNeedsForm',
      component: () => import('@/packages/manager/views/ManagerSummaryWithNeedsForm.vue'),
      props: true
    },

    { path: '*', component: NotFound }



  ]
}
let DistrictmanagePages = {
  path: '/districtmanager',
  component: DistrictManagerDashboardLayout,
  //redirect: '/districtmanager/unapprovedreports',
  redirect: '/districtmanager/UnsignedReports',
  name: 'DistrictManager',
  beforeEnter: (to, from, next) => {
    if (
      router.app.$session.exists() &&
      router.app.$session.get('jwturole') == 'district manager' || router.app.$session.get('jwturole') == 'acpc'
    ) {
      next()
    } else {
      next('/login')
    }
  },
  children: [
    {
      path: '/districtmanager/dashboard',
      name: 'DistrictManagerDashboard',
      component: dmanagerDashboard
    },
    {
      path: '/districtmanager/reports/',
      name: 'dreports',
      component: dreports
    },
    {
      path: '/districtmanager/PrelimReports/',
      name: 'dpremreports',
      component: dpremreports
    },
    {
    path: '/districtmanager/detailsprelimreport/:id',
    name: 'DetailPreliminary',
    component: () => import('@/packages/districtmanager/views/Detailprelim.vue'),
    props: true
  },

    {
      path: '/districtmanager/PrelimReports/:id',
      name: 'dpremreportsedit',
      component: dpremreportsedit
    },
    {
      path: '/districtmanager/detailsprelimreport/:_id',
      name: 'pDFDMPremiReports',
      component: pDFDMPremiReports
    },
    {
      path: '/districtmanager/reportdetailsstatus/:uuid',
      name: 'dstatsureports',
      component: dreportsdetailsstatus
    },
    {
      path: "/districtmanager/reportdetailsstatusOld/:uuid",
      name: "dstatsureports",
      component: dreportsdetailsstatusOld
    },
    {
      path: "/districtmanager/metabase",
      name: "dmetabse",
      component: dmetabase
    },

    {
      path: '/districtmanager/unapprovedreports/',
      name: 'unapproveddreports',
      component: unapproveddreports
    },

    {
      path: '/districtmanager/rejectedreports/',
      name: 'rejecteddreports',
      component: rejecteddreports
    },

    {
      path: '/districtmanager/approvedreports/',
      name: 'approveddreports',
      component: approveddreports
    },
    {
      path: '/districtmanager/drareports/:uuid',
      name: 'dRAReports',
      component: ddRAReports
    },
    {
      path: '/districtmanager/infographics/:uuid',
      name: 'infographics',
      component: dinfographics
    },
    {
      path: '/districtmanager/detailsreport/:uuid',
      name: 'pDFReports',
      component: dpDFReports
    },

    {
      path: "/districtmanager/detailsreportOld/:uuid",
      name: "pDFReports",
      component: dpDFReportsOld
    },

    {
      path: "/districtmanager/unapproveddetailsreport/:uuid",
      name: "unapprovedpDFReports",
      component: unapproveddetaileddreports
    },
    {
      path: '/districtmanager/unapprovedreports',
      name: 'unapprovedReports',
      component: unApproveddpDFReports
    },
    {
      path: '/districtmanager/UnsignedReports',
      name: 'UnsignedReports',
      component: UnsignedReports
    },
    {
      path: '/districtmanager/reportStatus',
      name: 'ReportStatus',
      component: ReportStatus
    },
    {
      path: '/districtmanager/trends',
      name: 'ReportStatus',
      component: Trends
    },
    {
      path: '/districtmanager/trends2',
      name: 'ReportStatus2',
      component: Trends2
    },
    {
      path: '/districtmanager/editform/:id',
      name: 'Editforms',
      component: Editforms
    },
    {
      path: '/districtmanager/editdra/:id',
      name: 'EditDra',
      component: EditDra
    },
    {
      path: '/districtmanager/summaryreport/:uuid',
      name: 'pDFSummaryReports',
      component: dpDFSummaryReports
    },
    {
      path: "/districtmanager/summaryreportOld/:uuid",
      name: "pDFSummaryReports",
      component: dpDFSummaryReportsOld
    },

    {
      path: '/districtmanager/accounts/',
      name: 'DmAllAccounts',
      component: districtManagedAccounts
    },
    {
      path: '/districtmanager/accounts/register',
      name: 'districtManagedAccountsRegister',
      component: districtManagedAccountsRegister
    },
    {
      path: '/districtmanager/accounts/edit/:id',
      name: 'districtManagedAccountsEdit',
      props: true,
      component: districtManagedAccountsEdit
    },
    {
      path: '/districtmanager/signatures/',
      name: 'DmAllSignatures',
      component: districtManagedSignatures
    },
    {
      path: '/districtmanager/signatures/register',
      name: 'districtManagedSignaturesRegister',
      component: districtManagedSignaturesRegister
    },
    {
      path: '/districtmanager/signatures/edit/:id',
      name: 'districtManagedSignaturesEdit',
      props: true,
      component: districtManagedSignaturesEdit
    },
    {
      path: '/districtmanager/about',
      name: 'AboutDistrictManager',
      component: appAbout
    },
    {
      path: '/districtmanager/profile/:id',
      name: 'districtmanagerEditProfile',
      props: true,
      component: districtmanagerEditProfile
    },
    {
      path: '/districtmanager/profile/:id',
      name: 'districtmanagerChangePassword',
      props: true,
      component: districtmanagerChangePassword
    },
    {
      path: '/districtmanager/accounts/edit/:id',
      name: 'dmEditAccounts',
      props: true,
      component: dmAccountsEdit
    },
    { path: '*', component: NotFound }
  ]
}
let passwordReset = {
  path: '/passwordreset',
  name: 'passwordreset',
  component: Passwordreset
}
let outsideReport = {
  path: '/summaryreport/:uuid',
  name: 'dcpDFSummaryReports',
  component: dcpDFSummaryReports
}


let DcpcPages = {
  path: '/dcpc',
  component: DcpcDashboardLayout,
  //redirect: '/dcpc/unapprovedreports',
  redirect: '/dcpc/PrelimReports',
  name: 'dcpc',
  beforeEnter: (to, from, next) => {
    if (
      router.app.$session.exists() &&
      router.app.$session.get('jwturole') == 'dcpc'
    ) {
      next()
    } else {
      next('/login')
    }
  },
  children: [
    {
      path: '/dcpc/dashboard',
      name: 'dcpcDashboard',
      component: dcpcDashboard
    },
    {
      path: '/dcpc/PrelimReports/',
      name: 'dcpcpreliminaryReports',
      component: dcpcpreliminaryReports
    },

    {
      path: '/dcpc/PrelimReports/:id',
      name: 'dcpcEditreports',
      props: true,
      component: dcpcEditreports
    },

    {
      path: '/dcpc/about',
      name: 'Aboutdcpc',
      component: appAbout
    },

    { path: '*', component: NotFound }
  ]
}

let DistrictcommissionerPages = {
  path: '/districtcommissioner',
  component: DistrictcommissionerDashboardLayout,
  redirect: '/districtcommissioner/unapprovedreports/',
  name: 'DistrictCommissioner',
  beforeEnter: (to, from, next) => {
    if (
      router.app.$session.exists() &&
      router.app.$session.get('jwturole') == 'district commissioner'
    ) {
      next()
    } else {
      next('/login')
    }
  },
  children: [
    {
      path: '/districtcommissioner/dashboard',
      name: 'DistrictCommissionerDashboard',
      component: dcDashboard
    },
    {
      path: '/districtcommissioner/reports/',
      name: 'dcreports',
      component: dcreports
    },
    {
      path: "/districtcommissioner/PrelimReports/",
      name: "dcremreports",
      component: dcremreports
    },
    {
      path: "/districtcommissioner/detailsprelimreport/:_id",
      name: "pDFDCPremiReports",
      component: pDFDCPremiReports
    },
    {
      path: '/districtcommissioner/reportdetailsstatus/:uuid',
      name: 'dcstatsureports',
      component: dcreportsdetailsstatus
    },
    {
      path: '/districtcommissioner/detailsprelimreport/:id',
      name: 'DCDetailPreliminary',
      component: () => import('@/packages/districtcommissioner/views/Detailprelim.vue'),
      props: true
    },
    {
      path: "/districtcommissioner/reportdetailsstatusOld/:uuid",
      name: "dcstatsureports",
      component: dcreportsdetailsstatusOld
    },
    {
      path: "/districtcommissioner/metabase",
      name: "dcmetabse",
      component: dcmetabase
    },

    {
      path: '/districtcommissioner/unapprovedreports/',
      name: 'dcunapproveddreports',
      component: dcunapproveddreports
    },
    {
      path: '/districtcommissioner/reportStatus',
      name: 'ReportStatus',
      component: ReportStat
    },

    {
      path: '/districtcommissioner/rejectedreports/',
      name: 'dcrejecteddreports',
      component: dcrejecteddreports
    },

    {
      path: '/districtcommissioner/approvedreports/',
      name: 'dcapproveddreports',
      component: dcapproveddreports
    },
    {
      path: '/districtcommissioner/drareports/:uuid',
      name: 'dcRAReports',
      component: dcdRAReports
    },
    {
      path: '/districtcommissioner/infographics/:uuid',
      name: 'dcinfographics',
      component: dcinfographics
    },
    {
      path: '/districtcommissioner/detailsreport/:uuid',
      name: 'dcpDFReports',
      component: dcpDFReports
    },

    {
      path: "/districtcommissioner/detailsreportOld/:uuid",
      name: "dcpDFReports",
      component: dcpDFReportsOld
    },

    {
      path: "/districtcommissioner/unapproveddetailsreport/:uuid",
      name: "dcunapprovedpDFReports",
      component: dcunapproveddetaileddreports
    },
    {
      path: '/districtcommissioner/unapprovedreports',
      name: 'dcunapprovedReports',
      component: dcunApproveddpDFReports
    },
    {
      path: '/districtcommissioner/summaryreport/:uuid',
      name: 'dcpDFSummaryReports',
      component: dcpDFSummaryReports
    },
    {
      path: "/districtcommissioner/summaryreportOld/:uuid",
      name: "dcpDFSummaryReports",
      component: dcpDFSummaryReportsOld
    },
    {
      path: '/districtcommissioner/signatures/',
      name: 'DcAllSignatures',
      component: districtcommissionerSignatures
    },
    {
      path: '/districtcommissioner/signatures/register',
      name: 'districtcommissionerSignaturesRegister',
      component: districtcommissionerSignaturesRegister
    },
    {
      path: '/districtcommissioner/signatures/edit/:id',
      name: 'districtcommissionerSignaturesEdit',
      props: true,
      component: districtcommissionerSignaturesEdit
    },
    {
      path: '/districtcommissioner/about',
      name: 'AboutDC',
      component: appAbout
    },
    {
      path: '/districtcommissioner/profile/:id',
      name: 'districtcommissionerEditProfile',
      props: true,
      component: districtcommissionerEditProfile
    },
    {
      path: '/districtcommissioner/profile/:id',
      name: 'districtcommissionerChangePassword',
      props: true,
      component: districtcommissionerChangePassword
    },
    {
      path: '/districtcommissioner/accounts/edit/:id',
      name: 'dcEditAccounts',
      props: true,
      component: dcAccountsEdit
    },
    { path: '*', component: NotFound }
  ]
}

const routes = [
  passwordReset,
  outsideReport,
  {
    path: '/about',
    name: 'about',
    component: appAbout
  },

  {
    path: '/createNewPassword',
    name: 'createNewPassword',
    component: createNewPassword
  },
  {
    path: '/',
    name: 'Home',
    redirect: '/login',
    meta: {
      noBodyBackground: true
    }
  },

  DistrictcommissionerPages,
  managePages,
  adminPages,
  DistrictmanagePages,
  DcpcPages,
  clusterPages,
  authPages
]

export default routes
