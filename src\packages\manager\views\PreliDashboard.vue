<template>
  <div class="content">
    <base-header class="pb-6">
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Comprehensive Dashboard</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/manager/dashboard">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">Reports</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">All</li>
            </ol>
          </nav>
        </div>
      </div>
    </base-header>

    <div class="container-fluid mt--6">
      <!-- Pie Chart -->
      <div class="row">
        <div class="col-xl-4 col-md-6">
          <div class="card">
            <div class="card-header">
              <h3 class="mb-0">Pie Chart</h3>
            </div>
            <div class="card-body">
              <div id="pie-chart"></div>
            </div>
          </div>
        </div>

        <!-- Bar Chart -->
        <div class="col-xl-8 col-md-6">
          <div class="card">
            <div class="card-header">
              <h3 class="mb-0">Bar Chart</h3>
            </div>
            <div class="card-body">
              <div id="bar-chart"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Line Chart -->
      <div class="row mt-4">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h3 class="mb-0">Line Chart</h3>
            </div>
            <div class="card-body">
              <div id="line-chart"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Leaflet Map -->
      <div class="row mt-4">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h3 class="mb-0">Disaster Map</h3>
            </div>
            <div class="card-body">
              <div id="leaflet-map" style="height: 400px;"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as d3 from "d3";
import * as dc from "dc";
import crossfilter from "crossfilter2";
import moment from "moment";
import L from "leaflet";
import "leaflet/dist/leaflet.css";

// Mock district coordinates for testing
const districtCoordinates = {
  "Lilongwe": [-13.9669, 33.7873],
  "Blantyre": [-15.7861, 35.0058],
  "Mzuzu": [-11.4587, 34.0205],
  // Add more district mappings as needed
};

export default {
  name: 'PreliDashboard',
  data() {
    return {
      data: [],
      map: null,
    };
  },
  async mounted() {
    await this.loadData();
    this.renderCharts();
    this.initMap();
  },
  methods: {
    async loadData() {
      try {
        // Mock data for testing; replace with actual API call
        this.data = [
          { disaster: "Flood", date: moment().subtract(5, 'days').toDate(), mhh: 200, fhh: 150, district: "Lilongwe" },
          { disaster: "Drought", date: moment().subtract(10, 'days').toDate(), mhh: 300, fhh: 250, district: "Blantyre" },
          { disaster: "Cyclone", date: moment().subtract(15, 'days').toDate(), mhh: 100, fhh: 200, district: "Mzuzu" },
        ];
      } catch (error) {
        console.error("Error loading data:", error);
      }
    },
    renderCharts() {
      if (!this.data.length) return;

      const xf = crossfilter(this.data);

      // Dimension and group by district
      const districtDimension = xf.dimension(d => d.district);
      const mhhGroup = districtDimension.group().reduceSum(d => d.mhh);
      const fhhGroup = districtDimension.group().reduceSum(d => d.fhh);

      // Pie Chart
      const pieChart = dc.pieChart("#pie-chart");
      pieChart
        .dimension(districtDimension)
        .group(mhhGroup)
        .width(300)
        .height(200)
        .radius(100)
        .renderLabel(true)
        .innerRadius(30)
        .render();

      // Bar Chart
      const barChart = dc.barChart("#bar-chart");
      barChart
        .dimension(districtDimension)
        .group(fhhGroup)
        .width(600)
        .height(300)
        .x(d3.scaleBand())
        .xUnits(dc.units.ordinal)
        .elasticY(true)
        .render();

      // Line Chart
      const dateDimension = xf.dimension(d => d3.timeDay(d.date));
      const mhhByDate = dateDimension.group().reduceSum(d => d.mhh);
      const lineChart = dc.lineChart("#line-chart");
      lineChart
        .dimension(dateDimension)
        .group(mhhByDate)
        .width(800)
        .height(300)
        .x(d3.scaleTime().domain(d3.extent(this.data, d => d.date)))
        .elasticY(true)
        .brushOn(true)
        .renderArea(true)
        .render();

      // Render all charts
      dc.renderAll();
    },
    initMap() {
      this.map = L.map("leaflet-map").setView([-13.5, 34.5], 6);

      L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
        maxZoom: 18,
        attribution: '© OpenStreetMap contributors',
      }).addTo(this.map);

      // Add markers based on district names
      this.data.forEach(item => {
        const coords = districtCoordinates[item.district];
        if (coords) {
          L.marker(coords)
            .addTo(this.map)
            .bindPopup(`<strong>${item.district}</strong><br>MHH: ${item.mhh}<br>FHH: ${item.fhh}`);
        }
      });
    },
  },
};
</script>

<style>
#pie-chart, #bar-chart, #line-chart {
  width: 100%;
  height: 300px;
}

#leaflet-map {
  height: 400px;
}

.dc-chart g.row {
  fill: #5DADE2;
}
</style>
