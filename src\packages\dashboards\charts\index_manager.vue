<template>
  <div class="row" ref="printChart">
    <div class="col-12 m-1">
      <li class="list-group-item pl-3">
        <div class="row date-filters row mt-4">
          <div class="col-3">
            <!-- <span id="identifier-c"></span> -->
            <!-- <span>
              Date From :
              <input
                type="date"
                ref="chartsdatefrom"
                style="margin-left:15px;margin-right:5px;border:1px solid #008080;border-radius:4px;"
              />
            </span>

            <span>
              Date To :
              <input
                type="date"
                ref="chartsdateto"
                style="margin-left:15px;border:1px solid #008080;border-radius:4px;"
              />
            </span> -->
            <span> DATE RANGE </span>
            <span ref="chartDateRange">
              <base-input addon-left-icon="ni ni-calendar-grid-58">
                <flat-picker
                  @on-close="ChangedRange"
                  :config="{ allowInput: true, mode: 'range' }"
                  class="form-control datepicker"
                  v-model="dates.range"
                >
                </flat-picker>
              </base-input>
            </span>

            <!--  <select
              class="btn btn-outline-secondary border p-2 text-left date-filter-buttons-container-c"
            >
              <option selected>DATE CATEGORIES [Created on]</option>
            </select>
            <span style="color:black!important;font-size:18pt;font-weight:bold" class="mx-1">
              <span id="start-c"></span> -
              <span id="end-c"></span>
            </span>-->
          </div>
          <div class="col-4">
            <span>DISTRICT DISASTER DATE</span>
            <span id="identifier-c"></span>
          </div>
          <div class="col-1"></div>
          <div class="text-right col-4">
            <button
              class="reset btn btn-warning btn-sm mx-1"
              id="charts-resetall-c"
              @click="resetAll"
            >
              Reset all
            </button>
            <button
              class="reset btn btn-primary btn-sm pull-right mx-1"
              @click="printImage()"
            >
              Screenshot
            </button>
          </div>
        </div>
      </li>
    </div>
    <div class="row m-1" ref="chartbox" style="width: 100%">
      <div class="col-5 pr-0" ref="typebox">
        <ul class="list-group">
          <li class="list-group-item border rounded-0 m-0 p-3 shadow">
            <div class="row font-weight-bold px-2">
              <div class="col-6">
                <h5 class="text-black text-uppercase font-weight-bolder">
                  NUMBER OF DISASTERS
                  <br />BY TYPE
                </h5>
              </div>
              <div class="col-6 text-right">
                <h1 class="text-black text-uppercase font-weight-bolder">
                  <b>
                    <b id="disasters-count"></b>
                  </b>
                  <h5 class="m-0 p-0">DISASTERS</h5>
                </h1>
              </div>
            </div>
            <div class="row col-12">
              <span id="disasters-by-type"></span>
            </div>
          </li>
        </ul>
      </div>
      <div class="col-7 pl-1 pr-1 mr-0" ref="disasterbox">
        <ul class="list-group">
          <li class="list-group-item border rounded-0 shadow p-3 shadow">
            <div class="row font-weight-bold">
              <div class="col-6">
                <h5 class="text-black text-uppercase font-weight-bolder">
                  NUMBER OF DISASTERS PER
                  <br />PROJECT DISTRICTS
                </h5>
              </div>
              <div class="col-6 text-right">
                <h1 class="text-black text-uppercase font-weight-bolder">
                  <b>
                    <b id="districts-count"></b>
                  </b>
                  <h5 class="m-0 p-0">DISTRICTS</h5>
                </h1>
              </div>
            </div>
            <div class="row">
              <div class="dc-chart" id="disasters-per-district"></div>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <div class="row m-1 mt-1" style="width: 100%">
      <div class="col-12 pr-0" ref="peoplebox">
        <ul class="list-group">
          <li class="list-group-item border rounded-0 m-0 p-3 shadow">
            <div class="row font-weight-bold px-2">
              <div class="col-6">
                <h5 class="text-black text-uppercase font-weight-bolder">
                  NUMBER OF PEOPLE WITH
                  <br />SHELTER AFFECTED
                </h5>
              </div>
              <div class="col-6 text-right">
                <h1 class="text-black text-uppercase font-weight-bolder">
                  <b>
                    <b id="people-affected-count"></b>
                  </b>
                  <h5 class="m-0 p-0">PEOPLE</h5>
                </h1>
              </div>
            </div>
            <div class="row col-12 mr-0 pr-0">
              <div
                class="col-9"
                id="people-without-shelter"
                ref="peoplebarbox"
              ></div>
              <div
                class="
                  col-3
                  border-left border-top
                  pr-5
                  mr-0
                  text-justify
                  align-content-between
                "
                ref="peoplepiebox"
              >
                <span id="people-without-shelter-gender"></span>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <div class="row m-1" style="width: 100%">
      <div class="col-12 pr-0" ref="timebox">
        <ul class="list-group">
          <li class="list-group-item border rounded-0 m-0 p-3 shadow">
            <div class="row font-weight-bold px-2">
              <div class="col-6">
                <h5 class="text-black text-uppercase font-weight-bolder">
                  NUMBER OF DISASTERS
                  <br />PER MONTH
                </h5>
              </div>
              <div class="col-6 text-right">
                <h1 class="text-black text-uppercase font-weight-bolder">
                  <b>
                    <b id="disasters-count2"></b>
                  </b>
                  <h5 class="m-0 p-0">DISASTERS</h5>
                </h1>
              </div>
            </div>
            <div class="row">
              <div id="charts-timeseries-chart"></div>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <div class="col-12">
      <div class="col-8">
        <h4 style="color:font-weight:bold">
          <br />
          <br />

          <h3>
            <b>KEY</b>
          </h3>

          <b>HH = Household</b>
          <br />
          <i class="huma huma-person-2" style="font-size: 20pt !important"></i>=
          <b>Male or Male headed household</b> depending on where its placed
          <br />
          <i class="huma huma-person-1" style="font-size: 20pt !important"></i>
          =
          <b>Female or Female headed</b> household depending where its placed
          <br />
          <br />
          <b class="text-warning">
            For more details of the icons used on this page please go to
            <a href="https://www.unocha.org/">UN OCHA Website</a>
          </b>
        </h4>
      </div>
      <div
        class="col-4 text-right"
        style="float: right !important; margin-top: -6%"
      >
        <img
          src="../../../assets/logo.png"
          style="width: 80px"
          alt="Malawi govt Logo"
        />
      </div>
    </div>
  </div>
</template>

<script>
import * as d3 from "d3";
import * as dc from "dc";
import crossfilter from "crossfilter2";
import flatPicker from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import moment from "moment";
var $ = require("jquery");
import utils from "../../../util/dashboard";
import { mapGetters, mapActions } from "vuex";
var dcDateFilterHandler = {};
var identifierDimension = {};
let group = "chartsgroup";
export default {
  components: { flatPicker },
  props: ["dinrs", "dras", "flatData"],
  data() {
    return {
      fullrange: "",
      dates: { range: "2018-07-17 to 2018-07-19" },
      disasters: [
        {
          name: "Floods",
          id: "floods",
          hasValues: true,
          flag: 1,
          icon: "huma huma-flash-flood",
        },
        {
          name: "Heavy rains",
          id: "heavy",
          hasValues: true,
          flag: 1,
          icon: "huma huma-heavy-rain",
        },
        {
          name: "Stormy rains",
          id: "stormy",
          hasValues: true,
          flag: 1,
          icon: "huma huma-storm",
        },
        {
          name: "Strong winds (with no rains)",
          id: "winds",
          hasValues: true,
          icon: "huma huma-violent-wind",
          flag: 1,
          value: 0,
        },
        {
          name: "Hailstorm",
          id: "hailstorm",
          icon: "huma huma-storm-surge",
          hasValues: true,
          flag: 1,
        },
        {
          name: "water pollution",
          icon: "huma huma-spring-water",
          id: "water",
          hasValues: true,
          flag: 1,
        },
        {
          name: "Droughts",
          id: "drought",
          hasValues: true,
          flag: 1,
          icon: "huma huma-drought",
        },
        {
          name: "Earthquake",
          id: "earthquake",
          icon: "huma huma-earthquake",
          hasValues: true,
          flag: 1,
        },
        {
          name: "Disease and Pest outbreak",
          id: "disease",
          icon: "huma huma-insect-infestation",
          hasValues: true,
          flag: 1,
          value: 0,
        },
        {
          name: "Lightening",
          id: "lightning",
          icon: "huma huma-storm",
          hasValues: true,
          flag: 1,
        },
        {
          name: "Extreme temperatures",
          id: "temperatures",
          hasValues: true,
          flag: 1,
          icon: "huma huma-cold-wave",
          value: 0,
        },
        {
          name: "Severe storms",
          icon: "huma huma-cyclone",
          id: "severestorm",
          hasValues: true,
          flag: 1,
        },
        {
          name: "Landslides",
          icon: "huma huma-landslide-mudslide",
          id: "land",
          hasValues: true,
          flag: 1,
        },
        {
          name: "Fire",
          icon: "huma huma-fire",
          id: "fire",
          hasValues: true,
          flag: 1,
        },
        {
          name: "Epidemic",
          icon: "huma huma-epidemic",
          id: "epidemic",
          hasValues: true,
          flag: 1,
        },
        {
          name: "Explosion",
          icon: "huma huma-volcano",
          id: "explosion",
          hasValues: true,
          flag: 1,
        },
        {
          name: " Human-animal conflicts",
          id: "human",
          hasValues: true,
          icon: "huma huma-technological-disaster",
          flag: 1,
          value: 0,
        },
        {
          name: "Accident",
          icon: "huma huma-injured",
          id: "accident",
          hasValues: true,
          flag: 1,
        },
      ],
      dateRangeMethods: {
        weeks: this.weeksRange,
        months: this.monthsRange,
        years: this.yearsRange,
      },
      dateCategories: {
        "This Week": {
          type: "weeks",
          number: 0,
        },
        "Last Week": {
          type: "weeks",
          number: 6,
        },
        "This Month": {
          type: "months",
          number: 0,
        },
        "Last Month": {
          type: "months",
          number: 1,
        },
        "Last 2 Months": {
          type: "months",
          number: 2,
        },
        "Last 3 Months": {
          type: "months",
          number: 3,
        },
        "Last 6 Months": {
          type: "months",
          number: 6,
        },
        "This Year": {
          type: "years",
          number: 0,
        },
        "Last Year": {
          type: "years",
          number: 1,
        },
        "Last 2 Years": {
          type: "years",
          number: 2,
        },
        "Last 3 Years": {
          type: "years",
          number: 3,
        },
        "Last 5 Years": {
          type: "years",
          number: 5,
        },
        "Last 10 Years": {
          type: "years",
          number: 10,
        },
      },
    };
  },
  computed: {
    ...mapGetters({
      getWidth: "dims/getNewWidth",
    }),
  },
  methods: {
    getScreenSize() {
      let width = this.getWidth + this.getWidth * 0.2292981;
      //let width = this.$refs.chartbox.clientWidth;
      let height = this.$refs.chartbox.clientHeight;
      return [width, height];
    },
    resetAll() {
      //chart.filterAll();
      dcDateFilterHandler.filter(null);
      identifierDimension.filter(null);
      //checkboxDimesions.filter(null);
      this.dates.range = this.fullrange;
      //chart.filterAll(group);
      dc.redrawAll(group);
    },
    ChangedRange() {
      dcDateFilterHandler.filter(null);
      dcDateFilterHandler.filter(null);
      var ranges = this.$refs.chartDateRange
        .getElementsByClassName("datepicker")[0]
        .value.split("to");

      // var dateFrom =
      //   refs.datefrom.value === "" ? bottomDate : refs.datefrom.value;
      dcDateFilterHandler.filter([new Date(ranges[0]), new Date(ranges[1])]);
      dc.redrawAll(group);
    },
    async printImage() {
      let exportsButtons = document.getElementsByClassName("exports");
      let buttons = document.getElementsByTagName("button");
      let resetButtons = document.querySelectorAll('[type="reset"]');
      await utils.Visibility.hide([
        ...exportsButtons,
        ...buttons,
        ...resetButtons,
      ]);
      let dataUrl = await utils.Screenshot.take(this.$refs.printChart);
      utils.download(dataUrl);
      utils.Visibility.show([...exportsButtons, ...buttons, ...resetButtons]);
    },
    weeksRange(number) {
      var now = moment(this.fixed_now());
      var mon = now.startOf("week").add(1, "days");
      if (number != 0) mon = now.startOf("week").subtract(6, "days");
      var startMoment = mon.format("YYYY-MM-DD");
      var endMoment = mon.add(6, "days").format("YYYY-MM-DD");
      this.displayDates(startMoment, endMoment);
    },

    monthsRange(number) {
      var firstDate = moment(this.fixed_now()).startOf("month");
      var endDate = moment(this.fixed_now()).endOf("month");
      if (number > 0) {
        firstDate = moment(this.fixed_now())
          .startOf("month")
          .subtract("months", number);
        endDate = moment(this.fixed_now()).endOf("month").subtract(1, "months");
      }
      var startMoment = firstDate.format("YYYY-MM-DD");
      var endMoment = endDate.format("YYYY-MM-DD");
      this.displayDates(startMoment, endMoment);
    },

    yearsRange(number) {
      var firstDate = moment(this.fixed_now()).startOf("year");
      var endDate = moment(this.fixed_now()).endOf("year");
      if (number > 0) {
        firstDate = moment(this.fixed_now())
          .startOf("year")
          .subtract("years", number);
        endDate = moment(this.fixed_now()).endOf("year").subtract(1, "years");
      }
      var startMoment = firstDate.format("YYYY-MM-DD");
      var endMoment = endDate.format("YYYY-MM-DD");
      this.displayDates(startMoment, endMoment);
    },
    fixed_now() {
      return new Date();
    },
    displayDates(start, end) {
      $("#start-c").text(moment(start).format("DD/MM/YYYY"));
      $("#end-c").text(moment(end).format("DD/MM/YYYY"));
    },
    setResetChart(chart, groupname) {
      var refs = this.$refs;
      d3.select(`#charts-resetall-c`).on("click", function () {
        chart.filterAll();
        refs.chartsdatefrom.value = "";
        refs.chartsdateto.value = "";
        dcDateFilterHandler.filter(null);
        chart.filterAll(groupname);
        dc.redrawAll(groupname);
      });
    },
    getChartWidth(percentage, screenWidth) {
      return screenWidth * percentage;
    },
    calculateChartWidth(percentage) {
      let screenSize = this.getScreenSize()[0];
      return this.getChartWidth(percentage, screenSize);
    },
    dcCharts(data) {
      let self = this;

      var xf = crossfilter(data);
      var numberFormat = d3.format(",");
      const timeFormat = d3.timeFormat("%m-%Y");
      //for filtering trick

      let disastersDimension = xf.dimension(function (d) {
        return d.disaster;
      });

      let disastersGroup = disastersDimension.group();

      let disastersDimension2 = xf.dimension(function (d) {
        return d;
      });

      var districtDimension = xf.dimension(function (d) {
        return d.district;
      });

      var peopleWithShelterDimension = xf.dimension(function (d) {
        return d;
      });

      var peopleShelterHHGroup = peopleWithShelterDimension
        .group()
        .reduceSum((item) => {
          return (
            (+item.without_shelter_females || 0) +
            (+item.without_shelter_males || 0)
          );
        });

      identifierDimension = xf.dimension(function (d) {
        return d.identifier;
      });

      new dc.SelectMenu("#identifier-c", group)
        .dimension(identifierDimension)
        .group(identifierDimension.group())
        .promptText("SELECT")
        .on("postRender", function () {
          d3.select(".dc-select-menu").attr(
            "class",
            "btn btn-outline-secondary border p-2 mr-4 text-left"
          );
        });

      var dropdowns = d3
        .select(".date-filter-buttons-container-c")
        .selectAll("option")
        .data([
          "This Week",
          "Last Week",
          "This Month",
          "Last Month",
          "Last 2 Months",
          "Last 3 Months",
          "Last 6 Months",
          "This Year",
          "Last Year",
          "Last 2 Years",
          "Last 3 Years",
          "Last 5 Years",
          "Last 10 Years",
        ]);

      dropdowns = dropdowns
        .enter()
        .append("option")
        .attr("class", "dc-select-option");

      // fill the buttons with the year from the data assigned to them
      dropdowns.each(function (d) {
        this.innerHTML = d;
      });

      dropdowns.on("click", dropdownsHandler);

      dcDateFilterHandler = xf.dimension(function (d) {
        return d.created_on;
      });

      function dropdownsHandler() {
        // our year will this.innerText

        let dateCategoryProperties = self.dateCategories[this.innerText];
        dateRangeHandler(
          dateCategoryProperties.type,
          dateCategoryProperties.number
        );

        function dateRangeHandler(type, number) {
          self.dateRangeMethods[type](number);
        }

        let startDateArray = $("#start-c").text().split("/");
        let endDateArray = $("#end-c").text().split("/");
        var start =
          startDateArray[2] + "/" + startDateArray[1] + "/" + startDateArray[0];
        var end =
          endDateArray[2] + "/" + endDateArray[1] + "/" + endDateArray[0];

        dcDateFilterHandler.filter(null);
        dcDateFilterHandler.filter([new Date(start), new Date(end)]);
        dc.redrawAll(group);
      }

      new dc.NumberDisplay("#disasters-count", group)
        .group(disastersDimension2.group())
        .formatNumber(numberFormat);

      new dc.NumberDisplay("#disasters-count2", group)
        .group(disastersDimension2.group())
        .formatNumber(numberFormat);

      function bin_counter(group) {
        return {
          value: function () {
            return group.all().filter(function (kv) {
              return kv.value > 0;
            }).length;
          },
        };
      }
      dcDateFilterHandler = xf.dimension(function (d) {
        return d.created_on;
      });
      // dropdowns.on("click", dropdownsHandler);
      var refs = this.$refs;
      var maxdate = dcDateFilterHandler.top(1)[0].created_on;
      var bottomDate = dcDateFilterHandler.bottom(1)[0].created_on;
      /*  console.log("Max is :",maxdate)
     console.log("Bottom is :",bottomDate) */

      new dc.NumberDisplay("#districts-count", group)
        .group(
          bin_counter(districtDimension.group()) //.reduceCount((x) => console.log(x))
        )
        .valueAccessor((x) => x)
        .formatNumber(numberFormat);

      new dc.NumberDisplay("#people-affected-count", group)
        .group(peopleShelterHHGroup)
        .formatNumber(numberFormat);

      const pie = dc.pieChart("#disasters-by-type", group);

      let totalAmount = 0;

      //console.log(this.calculateChartWidth(1), "Pie Chart");
      let pieWidth = this.calculateChartWidth(0.34358224);
      let pieRadius = pieWidth * 0.25568181;
      let pieInnerRadius = pieWidth * 0.15625;
      let pieHeight = pieWidth * 0.56818181;
      pie
        .width(pieWidth)
        .height(pieHeight)
        .radius(pieRadius)
        .innerRadius(pieInnerRadius)
        .dimension(disastersDimension)
        .group(disastersGroup)
        .colorAccessor((d) => {
          return d.key;
        })
        .ordinalColors(["#bf6c00"])
        .on("filtered", function (chart, filter) {
          chart.selectAll("image").remove();
          var sel = filter;
          let percentage = 0,
            value = 0;
          let disastersBuffer = [];
          totalAmount = 0;

          pie.selectAll("text.pie-slice").text((d) => {
            percentage = dc.utils.printSingleValue(
              ((d.endAngle - d.startAngle) / (2 * Math.PI)) * 100
            );
            disastersBuffer.push({
              ...d.data,
              percentage,
            });
            totalAmount += parseFloat(d.data.value);
          });

          filterPiechart(sel, percentage, totalAmount, disastersBuffer, value);
        })
        .on("renderlet", (chart) => {
          if (!chart.selectAll("g.selected")._groups[0].length) {
            chart.filter(null);
            filterPiechart("", 100, totalAmount, [], 0);
          }
          var image_width = 32;
          var image_height = 32;

          chart.selectAll("image").remove();

          let iRadius = pieWidth * 0.15625;
          let oRadius = pieWidth * 0.25568181;

          chart
            .select("g.pie-slice-group")
            .selectAll("g.pie-slice")
            .append("svg:image")
            .attr("transform", function (d) {
              var arc = d3
                .arc()
                .innerRadius(iRadius)
                .outerRadius(oRadius)
                .startAngle(d.startAngle)
                .endAngle(d.endAngle);
              var x = arc.centroid(d)[0] - 10;
              var y = arc.centroid(d)[1] - 10;
              return "translate(" + x + "," + y + ")";
            })
            .attr("xlink:href", function (d) {
              if (d.data.value > 0) {
                var name = d.data.key;
                return "img/disasters/" + name + ".svg";
              }
            })
            .attr("width", image_width)
            .attr("height", image_height);
        })
        .addFilterHandler(function (filters, filter) {
          filters.length = 0; // empty the array
          filters.push(filter);
          return filters;
        });

      function filterPiechart(
        sel,
        percentage,
        totalAmount,
        disastersBuffer,
        value
      ) {
        if (sel) {
          percentage = disastersBuffer.find((i) => i.key == sel).percentage;
          value = disastersBuffer.find((i) => i.key == sel).value;
        } else {
          sel = "Disasters";
          value = totalAmount;
          percentage = 100;
        }

        //let arc = d3.arc().innerRadius(110).outerRadius(200);

        var svg = pie.select("svg");
        pie.selectAll(".piechart-center-text").remove();
        let newY = pieHeight - 30;
        svg
          .append("svg")
          .attr("width", pieWidth)
          .attr("height", newY)
          .append("g")

          .attr("transform", "translate(" + pieWidth / 2 + "," + newY / 2 + ")")
          .append("text")
          .classed("piechart-center-text font-weight-bold", true)
          .attr("text-anchor", "middle")
          .text(numberFormat(value))
          .attr("font-size", "1.5em");

        newY = pieHeight;

        svg
          .append("svg")
          .attr("width", pieWidth)
          .attr("height", newY)
          .append("g")
          .attr("transform", "translate(" + pieWidth / 2 + "," + newY / 2 + ")")
          .append("text")
          .classed("piechart-center-text font-weight-bold", true)
          .attr("text-anchor", "middle")
          .text(sel);

        newY = pieHeight + 30;

        svg
          .append("svg")
          .attr("width", pieWidth)
          .attr("height", newY)
          .append("g")
          .attr("transform", "translate(" + pieWidth / 2 + "," + newY / 2 + ")")
          .append("text")
          .classed("piechart-center-text font-weight-bold", true)
          .attr("text-anchor", "middle")
          .text(percentage + "%");
      }
      let districtsDimension = xf.dimension(function (d) {
        return d.district;
      });

      let districtsGroup = districtsDimension
        .group()
        .reduceCount((item) => item.district);

      let barchart = new dc.BarChart("#disasters-per-district", group)
        .dimension(districtsDimension)
        .group(districtsGroup);
      //904
      //console.log(this.calculateChartWidth(0.48023426), "barchart");
      let barWidth = this.calculateChartWidth(0.48023426);
      let diffr = barWidth * 0.02103465;
      let barHeight = barWidth * 0.40718926;
      barchart
        .width(barWidth - diffr)
        .height(barHeight)
        .on("renderlet", (chart) => {
          chart.selectAll("rect").attr("rx", "7");
          chart.selectAll("text.barLabel").attr("fill", "white");
          chart
            .selectAll("path.domain")
            .attr("display", "none")
            .attr("fill", "white");
          chart.selectAll("g.x").selectAll("line").attr("display", "none");
          chart.selectAll("g.y").selectAll("line").attr("display", "none");
        })
        .gap(50)
        .x(d3.scaleOrdinal().domain([...new Set(data.map((x) => x.district))]))
        .xUnits(dc.units.ordinal)
        .margins({
          top: 20,
          left: 30,
          right: 15,
          bottom: 30,
        })
        .transitionDuration(0)
        .renderHorizontalGridLines(true)
        .renderLabel(true)
        .colorAccessor((d) => {
          return d.key;
        })
        .ordinalColors(["#bf6c00"])
        .label(function (d) {
          //console.log(d);
          return d.y;
        })
        .xAxisPadding(1)
        .ordering(function (d) {
          return d.value;
        })
        .elasticX(true)
        .yAxis()
        .ticks(5);

      let districtsDimension2 = xf.dimension(function (d) {
        return d.district;
      });

      let withoutShelterGroup = districtsDimension2
        .group()
        .reduceSum(
          (item) =>
            (item.without_shelter_females || 0) +
            (item.without_shelter_males || 0)
        );

      let barchartWithoutShelter = new dc.BarChart(
        "#people-without-shelter",
        group
      )
        .dimension(districtsDimension2)
        .group(withoutShelterGroup);

      //console.log(this.calculateChartWidth(0.59394827), "barchartWS");
      let barWidthWS = this.calculateChartWidth(0.59394827);
      let diff = barWidthWS * 0.02103465;
      let barHeightWS = barWidthWS * 0.32923109;
      barchartWithoutShelter
        .width(barWidthWS - diff)
        .height(barHeightWS)
        .x(d3.scaleOrdinal().domain([...new Set(data.map((x) => x.district))]))
        .xUnits(dc.units.ordinal)
        .margins({
          top: 20,
          left: 30,
          right: 15,
          bottom: 30,
        })
        .transitionDuration(0)
        .on("renderlet", (chart) => {
          chart.selectAll("rect").attr("rx", "7");
          chart.selectAll("text.barLabel").attr("fill", "white");
          chart
            .selectAll("path.domain")
            .attr("display", "none")
            .attr("fill", "white");
          chart.selectAll("g.x").selectAll("line").attr("display", "none");
          chart.selectAll("g.y").selectAll("line").attr("display", "none");
        })
        .gap(50)

        .renderHorizontalGridLines(true)
        .renderLabel(true)
        .colorAccessor((d) => {
          return d.key;
        })
        .ordinalColors(["#bf6c00"])
        .label(function (d) {
          //console.log(d);
          return d.y;
        })
        .xAxisPadding(1)
        .ordering(function (d) {
          return d.value;
        })
        .elasticX(true)
        .yAxis()
        .ticks(5);

      let peoplewithoutShelterDimension = xf.dimension(function (d) {
        d.without_shelter_females;
      });

      var peoplewithoutShelterGroup = regroup(peoplewithoutShelterDimension, [
        "without_shelter_females",
        "without_shelter_males",
      ]);

      //console.log(this.calculateChartWidth(0.19765739), "other pie");
      let pieWidthWS = this.calculateChartWidth(0.19765739);
      let innerRadius = pieWidthWS * 0.19786382;
      let outerRadius = pieWidthWS * 0.24732978;
      let radiusSP = pieWidthWS * 0.32152871;
      let pieChartWithoutShelter = new dc.PieChart(
        "#people-without-shelter-gender",
        group
      )

        .dimension(peoplewithoutShelterDimension)
        .group(peoplewithoutShelterGroup)
        .width(pieWidthWS)
        .height(pieWidthWS)
        .on("pretransition", function (chart) {
          var image_width = 50;
          var image_height = 50;

          chart.selectAll("image").remove();

          chart
            .select("g.pie-slice-group")
            .selectAll("g.pie-slice")
            .append("svg:image")
            .attr("transform", function (d) {
              var arc = d3
                .arc()
                .innerRadius(innerRadius)
                .outerRadius(outerRadius)
                .startAngle(d.startAngle)
                .endAngle(d.endAngle);
              var x = arc.centroid(d)[0] - 25;
              var y = arc.centroid(d)[1] - 20;
              return "translate(" + x + "," + y + ")";
            })
            .attr("xlink:href", function (d) {
              if (d.data[1] > 0) {
                var name =
                  d.data[0] == "without_shelter_females" ? "Female" : "Male";
                return "img/disasters/" + name + ".svg";
              }
            })
            .attr("width", image_width)
            .attr("height", image_height);
        })
        .valueAccessor((d) => {
          return d[1];
        })
        .title((d) => {
          return d[0] == "without_shelter_females"
            ? d[1] + " females"
            : d[1] + " males";
        })
        .radius(radiusSP)

        .colorAccessor((d) => {
          return d[0];
        })
        .ordinalColors(["#bf6c00", "#bf6c00"]);

      function regroup(dim, cols) {
        var _groupAll = dim.groupAll().reduce(
          function (p, v) {
            // add
            cols.forEach(function (c) {
              p[c] += v[c];
            });
            return p;
          },
          function (p, v) {
            // remove
            cols.forEach(function (c) {
              p[c] -= v[c];
            });
            return p;
          },
          function () {
            // init
            var p = {};
            cols.forEach(function (c) {
              p[c] = 0;
            });
            return p;
          }
        );
        return {
          all: function () {
            // or _.pairs, anything to turn the object into an array
            return Object.entries(_groupAll.value());
          },
        };
      }

      var xDateDimension = xf.dimension((d) => d3.timeMonth(d.created_on));
      var xDateDimensionGroup = xDateDimension.group().reduceCount();

      var minDate = d3.min(xf.all(), function (d) {
        return d.created_on;
      });
      var maxDate = d3.max(xf.all(), function (d) {
        return d.created_on;
      });

      //console.log(this.calculateChartWidth(0.81210347), "serieschart");
      let seriesWidth = this.calculateChartWidth(0.81210347);
      let seriesHeight = this.calculateChartWidth(0.19279663);
      let difference = seriesWidth * 0.01103465;
      var series = new dc.SeriesChart("#charts-timeseries-chart", group);
      series
        .dimension(xDateDimension)
        .group(xDateDimensionGroup)
        .x(d3.scaleTime().domain([minDate, maxDate]));

      series
        .chart(function (c) {
          return new dc.LineChart(c);
        })
        .height(seriesHeight)
        .width(seriesWidth - difference)
        .brushOn(false)
        .xAxisLabel("Month, Year")
        .clipPadding(10)
        .renderHorizontalGridLines(true)
        .seriesAccessor(function (d) {
          return d.key[0];
        })
        .keyAccessor(function (d) {
          return d.key;
        })
        .valueAccessor(function (d) {
          return d.value;
        })
        .turnOnControls(true)
        .colorAccessor((d) => {
          return d.value;
        })
        .ordinalColors(["#bf6c00"])
        .title((d) => `${d.value} disasters occured`);

      series.margins().left += 20;
      series.margins().bottom += 20;
      series.margins().top += 20;

      series.clipPadding(10).elasticY(true);

      series.xAxis().tickFormat(d3.timeFormat("%b,%Y"));

      dc.renderAll(group);

      this.setResetChart(dc, group);
    },
  },
  mounted() {
    let data = this.flatData;
    this.$nextTick(function () {
      this.dcCharts(data);
    });
    var xf = crossfilter(data);
    var minDate = d3.min(xf.all(), function (d) {
      return d.created_on;
    });
    var maxDate = d3.max(xf.all(), function (d) {
      return d.created_on;
    });
    this.dates.range =
      new Date(minDate).toISOString().slice(0, 10) +
      " to " +
      new Date(maxDate).toISOString().slice(0, 10);
    this.fullrange = this.dates.range;
  },
};
</script>

<style>
#bar-chart{
  color:red !important;
}
#identifier-c select {
  padding-top: 12px !important;
  padding-bottom: 12px !important;
}
.dc-chart .pie-slice {
  cursor: pointer;
  stroke: #f0f0f0;
  stroke-width: 1 px;
}
</style>
