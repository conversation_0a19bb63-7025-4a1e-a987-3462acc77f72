<template>
  <div style="color: #32325d">
    <table width="100%" class="col-md-11">
      <tr>
        <td colspan="12" v-if="dinrFormsData.district">
          <center>
            <h2>
              <b style="text-transform: uppercase">SUMMARY REPORT FOR
                <span id="district">{{
                  dinrFormsData.district.admin2_name_en
                }}</span></b>
            </h2>
          </center>
        </td>
      </tr>
      <tr>
        <td colspan="6" style="width: 50%">
          <b>Affected TAs</b>
          :
          <span>{{
            TAarray.join() == "" ? "Not reported" : TAarray.join(", ")
          }}</span>
        </td>

        <td colspan="6" style="width: 50%">
          <b>Affected Villages</b> :
          <span>{{
            villagesArray.join() == ""
            ? "Not reported"
            : villagesArray.join(", ")
          }}</span>
        </td>
      </tr>
      <tr>
        <td colspan="6" width="50%" style="text-transform: capitalize">
          <b>Type of disasters</b>
          : <span id="disastertype"> {{ dinrFormsData.disaster }} </span>
        </td>
        <td colspan="6" width="50%">
          <b>Report submitted at</b>
          :
          {{
            dinrFormsData.createdon
            ? dateFormate(
              new Date(dinrFormsData.createdon).toLocaleDateString("en-US")
            )
            : ""
          }}
        </td>
      </tr>
      <tr>
        <td colspan="6" width="50%">
          <b>Date Disaster started</b>
          :
          <span id="disasterstart">
            {{
              dinrFormsData.dodFrom
              ? dateFormate(
                new Date(dinrFormsData.dodFrom).toLocaleDateString("en-US")
              )
              : ""
            }}
          </span>
        </td>
        <td colspan="6" width="50%">
          <b>Date Disaster ended</b>
          :
          {{
            dinrFormsData.dodTo
            ? dateFormate(
              new Date(dinrFormsData.dodTo).toLocaleDateString("en-US")
            )
            : ""
          }}
        </td>
      </tr>
      <tr>
        <td colspan="4">
          <b>Date of assessment by ADRMC/VDRMC</b>
          :
          {{
            dinrFormsData.doaAcpc
            ? dateFormate(
              new Date(dinrFormsData.doaAcpc).toLocaleDateString("en-US")
            )
            : ""
          }}
        </td>
        <td colspan="4">
          <b>Date of assessment/verification by DDRMC</b>
          :
          {{
            dinrFormsData.doaDcpc
            ? dateFormate(
              new Date(dinrFormsData.doaDcpc).toLocaleDateString("en-US")
            )
            : ""
          }}
        </td>
        <td colspan="4">
          <b>Date reported to the DEC</b>
          :
          {{
            dinrFormsData.dateReported
            ? dateFormate(
              new Date(dinrFormsData.dateReported).toLocaleDateString(
                "en-US"
              )
            )
            : ""
          }}
        </td>
      </tr>
    </table>

    <table v-for="(item, index) in draFormsData" :key="index" v-if="index == 0" class="col-md-11">
      <tr v-if="(PeopleInjuredrows && PeopleInjuredrows.length > 0) ||
          (PeopleDeadrows && PeopleDeadrows.length > 0)
          ">
        <td colspan="15" class="text-center">
          <b>GENERAL INFORMATION</b>
        </td>
      </tr>
      <tr v-if="item.shelter" style="margin:0;padding:0 !important;border:0px !important;">
        <!-- shelter -->
        <td colspan="16" style="margin:0;padding:0 !important;border:0px">
          <table width="100%" style="margin:0;padding:0 !important;border:0px !important;">
            <tr v-if="PeopleInjuredrows && PeopleInjuredrows.length > 0">
              <td colspan="6" :rowspan="2 + PeopleInjuredrows.length">
                <b>People Injured</b>
              </td>
              <td colspan="4">
                <b>TA</b>
              </td>
              <td colspan="2" width="10%">
                <b>
                  <center>Male</center>
                </b>
              </td>
              <td colspan="2" width="10%">
                <b>
                  <center>Female</center>
                </b>
              </td>
              <td style="font-weight:bold" colspan="2" width="10%">
                <b>
                  <center>Total</center>
                </b>
              </td>
            </tr>
            <tr v-if="PeopleInjuredrows && PeopleInjuredrows.length > 0" v-for="row in PeopleInjuredrows">
              <td colspan="4">{{ row.ta }}</td>
              <td colspan="2" class="right-align">
                {{ numberWithCommas(row.males || 0) }}
              </td>
              <td colspan="2" class="right-align">
                {{ numberWithCommas(row.females || 0) }}
              </td>
              <td colspan="2" style="font-weight:bold" class="right-align">
                {{
                  numberWithCommas(
                    (row.females ? parseInt(row.females) : 0) +
                    (row.males ? parseInt(row.males) : 0)
                  )
                }}
              </td>
            </tr>

            <tr v-if="PeopleInjuredrows && PeopleInjuredrows.length > 0">
              <td style="font-weight:bold" colspan="4">
                <center>Total</center>
              </td>
              <td style="font-weight:bold" colspan="2" class="right-align">
                {{
                  numberWithCommas(
                    PeopleInjuredrows
                      ? PeopleInjuredrows.map(function (item) {
                        return item.males ? parseInt(item.males) : 0;
                      }).reduce((sum, value) => sum + value)
                      : 0
                  )
                }}
              </td>
              <td style="font-weight:bold" class="right-align" colspan="2">
                {{
                  numberWithCommas(
                    PeopleInjuredrows
                      ? PeopleInjuredrows.map(function (item) {
                        return item.females ? parseInt(item.females) : 0;
                      }).reduce((sum, value) => sum + value)
                      : 0
                  )
                }}
              </td>
              <td style="font-weight:bold" colspan="2" class="right-align">
                {{
                  numberWithCommas(
                    (PeopleInjuredrows
                      ? PeopleInjuredrows.map(function (item) {
                        return item.males ? parseInt(item.males) : 0;
                      }).reduce((sum, value) => sum + value)
                      : 0) +
                    (PeopleInjuredrows
                      ? PeopleInjuredrows.map(function (item) {
                        return item.females ? parseInt(item.females) : 0;
                      }).reduce((sum, value) => sum + value)
                      : 0)
                  )
                }}
              </td>
            </tr>

            <tr v-if="PeopleMissingrows && PeopleMissingrows.length > 0">
              <td colspan="6" :rowspan="2 + PeopleMissingrows.length">
                <b>People Missing</b>
              </td>
              <td colspan="4">
                <b>TA</b>
              </td>
              <td colspan="2" width="10%">
                <b>
                  <center>Male</center>
                </b>
              </td>
              <td colspan="2" width="10%">
                <b>
                  <center>Female</center>
                </b>
              </td>
              <td style="font-weight:bold" colspan="2" width="10%">
                <b>
                  <center>Total</center>
                </b>
              </td>
            </tr>
            <tr v-if="PeopleMissingrows && PeopleMissingrows.length > 0" v-for="row in PeopleMissingrows">
              <td colspan="4">{{ row.ta }}</td>
              <td colspan="2" class="right-align">
                {{ numberWithCommas(+parseInt(row.males || 0)) }}
              </td>
              <td colspan="2" class="right-align">
                {{ numberWithCommas(+parseInt(row.females || 0)) }}
              </td>
              <td colspan="2" style="font-weight:bold" class="right-align">
                {{
                  numberWithCommas(
                    (row.females ? parseInt(row.females) : 0) +
                    (row.males ? parseInt(row.males) : 0)
                  )
                }}
              </td>
            </tr>

            <tr v-if="PeopleMissingrows && PeopleMissingrows.length > 0">
              <td style="font-weight:bold" colspan="4">
                <center>Total</center>
              </td>
              <td style="font-weight:bold" colspan="2" class="right-align">
                {{
                  numberWithCommas(
                    PeopleMissingrows
                      ? PeopleMissingrows.map(function (item) {
                        return item.males ? parseInt(item.males) : 0;
                      }).reduce((sum, value) => sum + value)
                      : 0
                  )
                }}
              </td>
              <td style="font-weight:bold" class="right-align" colspan="2">
                {{
                  numberWithCommas(
                    PeopleMissingrows
                      ? PeopleMissingrows.map(function (item) {
                        return item.females ? parseInt(item.females) : 0;
                      }).reduce((sum, value) => sum + value)
                      : 0
                  )
                }}
              </td>
              <td style="font-weight:bold" colspan="2" class="right-align">
                {{
                  numberWithCommas(
                    (PeopleMissingrows
                      ? PeopleMissingrows.map(function (item) {
                        return item.males ? parseInt(item.males) : 0;
                      }).reduce((sum, value) => sum + value)
                      : 0) +
                    (PeopleMissingrows
                      ? PeopleMissingrows.map(function (item) {
                        return item.females ? parseInt(item.females) : 0;
                      }).reduce((sum, value) => sum + value)
                      : 0)
                  )
                }}
              </td>
            </tr>

            <tr v-if="PeopleDeadrows && PeopleDeadrows.length > 0">
              <td :rowspan="2 + PeopleDeadrows.length" colspan="6">
                <b>People Dead</b>
              </td>
              <td colspan="4">
                <b>TA</b>
              </td>
              <td colspan="2" width="10%">
                <b>
                  <center>Male</center>
                </b>
              </td>
              <td colspan="2" width="10%">
                <b>
                  <center>Female</center>
                </b>
              </td>
              <td colspan="2" style="font-weight:bold" width="10%">
                <b>
                  <center>Total</center>
                </b>
              </td>
            </tr>
            <tr v-if="PeopleDeadrows && PeopleDeadrows.length > 0" v-for="row in PeopleDeadrows">
              <td colspan="4">{{ row.ta }}</td>
              <td colspan="2" class="right-align">
                {{ numberWithCommas(+parseInt(row.males || 0)) }}
              </td>
              <td colspan="2" class="right-align">
                {{ numberWithCommas(+parseInt(row.females || 0)) }}
              </td>
              <td colspan="2" style="font-weight:bold" class="right-align">
                {{
                  numberWithCommas(
                    row.males
                      ? parseInt(row.males)
                      : 0 + row.females
                        ? parseInt(row.females)
                        : 0
                  )
                }}
              </td>
            </tr>

            <tr v-if="PeopleDeadrows && PeopleDeadrows.length > 0">
              <td style="font-weight:bold" colspan="4">
                <center>Total</center>
              </td>
              <td style="font-weight:bold" class="right-align" colspan="2">
                {{
                  numberWithCommas(
                    PeopleDeadrows
                      ? PeopleDeadrows.map(function (item) {
                        return item.males ? parseInt(item.males) : 0;
                      }).reduce((sum, value) => sum + value)
                      : 0
                  )
                }}
              </td>
              <td style="font-weight:bold" class="right-align" colspan="2">
                {{
                  numberWithCommas(
                    PeopleDeadrows
                      ? PeopleDeadrows.map(function (item) {
                        return item.females ? parseInt(item.females) : 0;
                      }).reduce((sum, value) => sum + value)
                      : 0
                  )
                }}
              </td>
              <td style="font-weight:bold" class="right-align" colspan="2">
                {{
                  numberWithCommas(
                    (PeopleDeadrows
                      ? PeopleDeadrows.map(function (item) {
                        return item.males ? parseInt(item.males) : 0;
                      }).reduce((sum, value) => sum + value)
                      : 0) +
                    (PeopleDeadrows
                      ? PeopleDeadrows.map(function (item) {
                        return item.females ? parseInt(item.females) : 0;
                      }).reduce((sum, value) => sum + value)
                      : 0)
                  )
                }}
              </td>
            </tr>
          </table>
        </td>
      </tr>
      <tr style="border:none" v-if="(PeopleInjuredrows && PeopleInjuredrows.length > 0) ||
          (PeopleDeadrows && PeopleDeadrows.length > 0)
          ">
        <td class="col-md-12" colspan="15"></td>
      </tr>
      <!-- general end -->
      <tr>
        <td style="text-align:left;" colspan="1" width="12%">
          <b>CLUSTER</b>
        </td>
        <td colspan="15" class="text-center">
          <b>IMPACT ASSESSMENT</b>
        </td>
      </tr>
      <!--===========================SHELTER START HERE==================================-->
      <tr v-if="hasShelterProperties" style="margin:0;padding:0 !important;border:0px !important;">
        <!-- shelter -->
        <td colspan="16" style="margin:0;padding:0 !important;border:0px">
          <table width="100%" style="margin:0;padding:0 !important;border:0px !important;">
            <tr>
              <td width="12.2%" :rowspan="draFormsData.length +
                  3 +
                  (people_without_shelter
                    ? people_without_shelter.length + 1
                    : 0) +
                  (PeopleAffectedrows ? PeopleAffectedrows.length + 2 : 0)
                  ">
                <span class="rotated" style="font-weight: bold;"></span>
                <img src="../../../../static/cluster_shelter_100px_bluebox.png" height="150" />
              </td>
            </tr>
            <tr v-if="people_without_shelter">
              <td colspan="6"
                :rowspan="people_without_shelter && people_without_shelter.length > 0 ? people_without_shelter.length + 2 : 1"
                width="20%">
                <b>People without shelter</b>
              </td>
              <td colspan="4">
                <b>TA</b>
              </td>
              <td colspan="2" width="10%">
                <b>
                  <center>Male</center>
                </b>
              </td>
              <td colspan="2" width="10%">
                <b>
                  <center>Female</center>
                </b>
              </td>
              <td colspan="2" style="font-weight:bold" width="10%">
                <b>
                  <center>Total</center>
                </b>
              </td>
            </tr>
            <tr v-for="row in people_without_shelter">
              <td colspan="4">{{ row.ta }}</td>
              <td colspan="2" width="10%" class="right-align">
                {{ numberWithCommas(+parseInt(row.males || 0)) }}
              </td>
              <td colspan="2" width="10%" class="right-align">
                {{ numberWithCommas(+parseInt(row.females || 0)) }}
              </td>
              <td colspan="2" class="right-align" style="font-weight:bold">
                {{
                  numberWithCommas(
                    (row.females ? parseInt(row.females) : 0) +
                    (row.males ? parseInt(row.males) : 0)
                  )
                }}
              </td>
            </tr>

            <tr v-if="people_without_shelter && people_without_shelter.length > 0">
              <td style="font-weight:bold" colspan="4">
                <center>Total</center>
              </td>
              <td style="font-weight:bold" class="right-align" colspan="2">
                {{
                  numberWithCommas(
                    people_without_shelter
                      ? people_without_shelter
                        .map(function (item) {
                          return item.males ? parseInt(item.males) : 0;
                        })
                        .reduce((sum, value) => sum + value)
                      : 0
                  )
                }}
              </td>
              <td style="font-weight:bold" class="right-align" colspan="2">
                {{
                  numberWithCommas(
                    people_without_shelter
                      ? people_without_shelter
                        .map(function (item) {
                          return item.females ? parseInt(item.females) : 0;
                        })
                        .reduce((sum, value) => sum + value)
                      : 0
                  )
                }}
              </td>
              <td style="font-weight:bold" class="right-align" colspan="2">
                {{
                  numberWithCommas(
                    (people_without_shelter
                      ? people_without_shelter
                        .map(function (item) {
                          return item.males ? parseInt(item.males) : 0;
                        })
                        .reduce((sum, value) => sum + value)
                      : 0) +
                    (people_without_shelter
                      ? people_without_shelter
                        .map(function (item) {
                          return item.females ? parseInt(item.females) : 0;
                        })
                        .reduce((sum, value) => sum + value)
                      : 0)
                  )
                }}
              </td>
            </tr>

            <tr v-if="PeopleAffectedrows && PeopleAffectedrows.length > 0">
              <td colspan="6" :rowspan="2 + PeopleAffectedrows.length">
                <b>Households Affected</b>
              </td>
              <td colspan="4">
                <b>TA</b>
              </td>
              <td colspan="2" width="10%">
                <b>
                  <center>MHH</center>
                </b>
              </td>
              <td colspan="2" width="10%">
                <b>
                  <center>FHH</center>
                </b>
              </td>
              <td style="font-weight:bold" colspan="2" width="10%">
                <b>
                  <center>Total</center>
                </b>
              </td>
            </tr>
            <tr v-if="PeopleAffectedrows && PeopleAffectedrows.length > 0" v-for="row in PeopleAffectedrows">
              <td colspan="4">{{ row.ta }}</td>
              <td colspan="2" class="right-align">
                {{ numberWithCommas(+parseInt(row.males || 0)) }}
              </td>
              <td colspan="2" class="right-align">
                {{ numberWithCommas(+parseInt(row.females || 0)) }}
              </td>
              <td colspan="2" style="font-weight:bold" class="right-align">
                {{
                  numberWithCommas(
                    (row.females ? parseInt(row.females) : 0) +
                    (row.males ? parseInt(row.males) : 0)
                  )
                }}
              </td>
            </tr>

            <tr v-if="PeopleAffectedrows && PeopleAffectedrows.length > 0">
              <td style="font-weight:bold" colspan="4">
                <center>Total</center>
              </td>
              <td colspan="2" style="font-weight:bold" class="right-align">
                {{
                  numberWithCommas(
                    PeopleAffectedrows
                      ? PeopleAffectedrows.map(function (item) {
                        return item.males ? parseInt(item.males) : 0;
                      }).reduce((sum, value) => sum + value)
                      : 0
                  )
                }}
              </td>
              <td colspan="2" style="font-weight:bold" class="right-align">
                {{
                  numberWithCommas(
                    PeopleAffectedrows
                      ? PeopleAffectedrows.map(function (item) {
                        return item.females ? parseInt(item.females) : 0;
                      }).reduce((sum, value) => sum + value)
                      : 0
                  )
                }}
              </td>
              <td colspan="2" style="font-weight:bold" class="right-align">
                {{
                  numberWithCommas(
                    (PeopleAffectedrows
                      ? PeopleAffectedrows.map(function (item) {
                        return item.males ? parseInt(item.males) : 0;
                      }).reduce((sum, value) => sum + value)
                      : 0) +
                    (PeopleAffectedrows
                      ? PeopleAffectedrows.map(function (item) {
                        return item.females ? parseInt(item.females) : 0;
                      }).reduce((sum, value) => sum + value)
                      : 0)
                  )
                }}
              </td>
            </tr>

            <template v-for="(item, index) in draFormsDataOther">
              <tr
                v-if="item.sectors.shelter && (item.sectors.shelter.urgent_response_needed || item.sectors.shelter.response_needed)"
                :key="index">
                <td colspan="4">
                  <b>{{ item.admin3.admin3Name_en }}'s response needed</b>
                </td>
                <td colspan="12">
                  <u>
                    <strong>URGENT</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.shelter
                      ? item.sectors.shelter.urgent_response_needed
                        ? item.sectors.shelter.urgent_response_needed
                        : "N/A"
                      : "N/A"
                    }}
                  </li>
                  <hr style="margin:1%;" />
                  <u>
                    <strong>GENERAL</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.shelter
                      ? item.sectors.shelter.response_needed
                        ? item.sectors.shelter.response_needed
                        : "N/A"
                      : "N/A"
                    }}
                  </li>
                </td>
              </tr>
            </template>

          </table>
        </td>
      </tr>
      <!--===========================SHELTER END HERE==================================--> 

      <!--===========================DISPLACED START HERE==================================-->
      <tr v-if="hasDisplacedProperties(item)" style="margin:0;padding:0 !important;border:0px !important;">
        <td colspan="16" style="margin:0;padding:0 !important;border:0px">
          <table width="100%" style="margin:0;padding:0 !important;border:0px !important;">
            <tr>
              <td width="12.2%" v-bind:rowspan="draFormsData.length +
                  (PeopleAffectedrowsD
                    ? PeopleAffectedrowsD.length > 0
                      ? PeopleAffectedrowsD.length + 3
                      : 2
                    : 1)
                  " colspan="1">
                <span class="rotated" style="font-weight: bold;"></span>
                <img src="../../../../static/crisis_population_displacement_100px_bluebox.png" height="150" />
                
              </td>
            </tr>

            <tr v-if="PeopleAffectedrowsD && PeopleAffectedrowsD.length > 0">
              <td v-bind:rowspan="PeopleAffectedrowsD.length > 0
                    ? PeopleAffectedrowsD.length + 2
                    : PeopleAffectedrowsD.length + 1
                  " colspan="6" width="20%">
                <b>Number of Households displaced</b>
              </td>
              <td colspan="4">
                <b>TA</b>
              </td>
              <td colspan="2" width="10%">
                <b>
                  <center>MHH</center>
                </b>
              </td>
              <td colspan="2" width="10%">
                <b>
                  <center>FHH</center>
                </b>
              </td>
              <td style="font-weight:bold" colspan="2" width="10%">
                <b>
                  <center>Total</center>
                </b>
              </td>
            </tr>
            <tr v-if="PeopleAffectedrowsD && PeopleAffectedrowsD.length > 0" v-for="row in PeopleAffectedrowsD">
              <td colspan="4">{{ row.ta }}</td>
              <td colspan="2" class="right-align">
                {{ numberWithCommas(+parseInt(row.males || 0)) }}
              </td>
              <td colspan="2" class="right-align">
                {{ numberWithCommas(+parseInt(row.females || 0)) }}
              </td>
              <td style="font-weight:bold" colspan="2" class="right-align">
                {{
                  numberWithCommas(
                    parseInt(row.females ? row.females : 0) +
                    parseInt(row.males ? row.males : 0)
                  )
                }}
              </td>
            </tr>

            <tr v-if="PeopleAffectedrowsD && PeopleAffectedrowsD.length > 0">
              <td style="font-weight:bold" colspan="4">
                <center>Total</center>
              </td>
              <td style="font-weight:bold" class="right-align" colspan="2">
                {{
                  numberWithCommas(
                    PeopleAffectedrowsD
                      ? PeopleAffectedrowsD.map(function (item) {
                        return item.males ? parseInt(item.males) : 0;
                      }).reduce((sum, value) => sum + value)
                      : 0
                  )
                }}
              </td>
              <td style="font-weight:bold" class="right-align" colspan="2">
                {{
                  numberWithCommas(
                    PeopleAffectedrowsD
                      ? PeopleAffectedrowsD.map(function (item) {
                        return item.females ? parseInt(item.females) : 0;
                      }).reduce((sum, value) => sum + value)
                      : 0
                  )
                }}
              </td>
              <td style="font-weight:bold" class="right-align" colspan="2">
                {{
                  numberWithCommas(
                    (PeopleAffectedrowsD
                      ? PeopleAffectedrowsD.map(function (item) {
                        return item.males ? parseInt(item.males) : 0;
                      }).reduce((sum, value) => sum + value)
                      : 0) +
                    (PeopleAffectedrowsD
                      ? PeopleAffectedrowsD.map(function (item) {
                        return item.females ? parseInt(item.females) : 0;
                      }).reduce((sum, value) => sum + value)
                      : 0)
                  )
                }}
              </td>
            </tr>
            <template v-for="(item, index) in draFormsDataOther">

              <tr :key="index"
                v-if="item.sectors.displaced && (item.sectors.displaced.response_needed || item.sectors.displaced.urgent_response_needed)">
                <td colspan="4">
                  <b>{{ item.admin3.admin3Name_en }}'s response needed</b>
                </td>
                <td colspan="12">
                  <u>
                    <strong>URGENT</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.displaced
                      ? item.sectors.displaced.urgent_response_needed
                        ? item.sectors.displaced.urgent_response_needed
                        : "N/A"
                      : "N/A"
                    }}
                  </li>
                  <hr style="margin:1%;" />
                  <u>
                    <strong>GENERAL</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.displaced
                      ? item.sectors.displaced.response_needed
                        ? item.sectors.displaced.response_needed
                        : "N/A"
                      : "N/A"
                    }}
                  </li>
                </td>
              </tr>
            </template>
          </table>
        </td>
      </tr>
      <!--===========================DISPLACED END HERE==================================--> 

      <!--===========================AGRICULTURE START HERE==================================-->
      <tr v-if="hasAgricultureProperties(item)" style="margin:0;padding:0 !important;border:0px !important;">
        <td colspan="16" style="margin:0;padding:0 !important;border:0px">
          <table width="100%" style="margin:0;padding:0 !important;border:0px !important;">
            <tr v->
              <td width="12.2%" :rowspan="1 +
                  (crops_damaged
                    ? crops_damaged.length > 0
                      ? crops_damaged.length + 3
                      : 0
                    : 1) +
                  (impact_on_crops ? impact_on_crops.length + 2 : 0) +
                  (livelihoods_affected
                    ? livelihoods_affected.length + 3
                    : 0) +
                  (agricultureArray
                    ? agricultureArray.length + 1
                    : 0)
                  " colspan="1">
                <span class="rotated" style="font-weight: bold;"></span>
                <img src="../../../../static/other_cluster_agriculture_100px_bluebox.png" height="150" />
              </td>

              <td v-if="crops_damaged && crops_damaged.length > 0" v-bind:rowspan="crops_damaged.length > 0 ? crops_damaged.length + 2 : 1
                  " colspan="6" width="20%">
                <b>Damage per crop (Hectares)</b>
              </td>
              <td colspan="4" v-if="crops_damaged && crops_damaged.length > 0">
                <b>TA</b>
              </td>
              <td colspan="2" width="15%" v-if="crops_damaged && crops_damaged.length > 0">
                <b>Submerged</b>
              </td>
              <td colspan="2" width="15%" v-if="crops_damaged && crops_damaged.length > 0">
                <b>Washed away</b>
              </td>
              <td style="font-weight:bold" colspan="2" width="15%" v-if="crops_damaged && crops_damaged.length > 0">
                <b>
                  <center>Total</center>
                </b>
              </td>
            </tr>
            <tr v-if="crops_damaged && crops_damaged.length > 0" v-for="crop in crops_damaged">
              <td colspan="4">{{ crop.ta }}</td>
              <td colspan="2" class="right-align">
                {{ numberWithCommas(+parseInt(crop.males || 0)) }}
              </td>
              <td colspan="2" class="right-align">
                {{ numberWithCommas(+parseInt(crop.females || 0)) }}
              </td>
              <td style="font-weight:bold" colspan="2" class="right-align">
                {{
                  numberWithCommas(
                    parseInt(crop.females ? crop.females : 0) +
                    parseInt(crop.males ? crop.males : 0)
                  )
                }}
              </td>
            </tr>

            <tr v-if="crops_damaged && crops_damaged.length > 0">
              <td style="font-weight:bold" colspan="4">
                <center>Total</center>
              </td>
              <td style="font-weight:bold" class="right-align" colspan="2">
                {{
                  numberWithCommas(
                    crops_damaged
                      ? crops_damaged
                        .map(function (item) {
                          return item.males ? parseInt(item.males) : 0;
                        })
                        .reduce((sum, value) => sum + value)
                      : 0
                  )
                }}
              </td>
              <td style="font-weight:bold" class="right-align" colspan="2">
                {{
                  numberWithCommas(
                    crops_damaged
                      ? crops_damaged
                        .map(function (item) {
                          return item.females ? parseInt(item.females) : 0;
                        })
                        .reduce((sum, value) => sum + value)
                      : 0
                  )
                }}
              </td>
              <td style="font-weight:bold" class="right-align" colspan="2">
                {{
                  numberWithCommas(
                    (crops_damaged
                      ? crops_damaged
                        .map(function (item) {
                          return item.males ? parseInt(item.males) : 0;
                        })
                        .reduce((sum, value) => sum + value)
                      : 0) +
                    (crops_damaged
                      ? crops_damaged
                        .map(function (item) {
                          return item.females ? parseInt(item.females) : 0;
                        })
                        .reduce((sum, value) => sum + value)
                      : 0)
                  )
                }}
              </td>
            </tr>

            <tr v-if="impact_on_crops && impact_on_crops.length > 0">
              <td v-bind:rowspan="impact_on_crops.length > 0 ? impact_on_crops.length + 2 : 1
                  " colspan="6">
                <b>Impact on crops</b>
              </td>
              <td colspan="3">
                <b>TA</b>
              </td>
              <td colspan="3">
                <b>HH affected</b>
              </td>
              <td colspan="3">
                <b>Hectares damaged</b>
              </td>
            </tr>
            <tr v-if="impact_on_crops && impact_on_crops.length > 0" v-for="crop in impact_on_crops">
              <td colspan="3">{{ crop.ta }}</td>
              <td colspan="3" class="right-align">
                {{ numberWithCommas(+parseInt(crop.males || 0)) }}
              </td>
              <td colspan="3" class="right-align">
                {{ numberWithCommas(+parseInt(crop.females || 0)) }}
              </td>
            </tr>

            <tr v-if="impact_on_crops && impact_on_crops.length > 0">
              <td style="font-weight:bold" colspan="3">
                <center>Total</center>
              </td>
              <td style="font-weight:bold" colspan="3" class="right-align">
                {{
                  numberWithCommas(
                    impact_on_crops
                      ? impact_on_crops.reduce(
                        (sum, value) => sum + parseInt(value.males || 0),
                        0
                      )
                      : 0
                  )
                }}
              </td>
              <td style="font-weight:bold" colspan="3" class="right-align">
                {{
                  numberWithCommas(
                    impact_on_crops
                      ? impact_on_crops.reduce(
                        (sum, value) => sum + parseInt(value.females || 0),
                        0
                      )
                      : 0
                  )
                }}
              </td>
            </tr>

            <tr v-if="agricultureLivelihoodArray &&
                agricultureLivelihoodArray.length > 0
                ">
              <td v-bind:rowspan="agricultureLivelihoodArray.length > 0
                    ? agricultureLivelihoodArray.length + 1
                    : agricultureLivelihoodArray.length + 1
                  " colspan="4">
                <b class="center-align">Livelihoods affected</b>
              </td>
              <td colspan="2">
                <b class="center-align">TA</b>
              </td>
              <td colspan="2">
                <b class="center-align">Livelihood</b>
              </td>
              <td colspan="2">
                <b class="center-align">HH group</b>
              </td>
              <td colspan="2">
                <b class="center-align">Severity</b>
              </td>
              <td colspan="1">
                <b class="center-align">MHH</b>
              </td>
              <td colspan="1">
                <b class="center-align">FHH</b>
              </td>
              <td colspan="1">
                <b>Total</b>
              </td>
            </tr>
            <tr v-if="agricultureLivelihoodArray &&
                agricultureLivelihoodArray.length > 0
                " v-for="row in agricultureLivelihoodArray">
              <td colspan="2" class="left-align">{{ row.ta }}</td>
              <td colspan="2" class="left-align">{{ row.livelihood_type }}</td>
              <td colspan="2" class="left-align">
                {{ row.category }}
              </td>
              <td colspan="2" class="left-align">
                {{ row.severity }}
              </td>
              <td colspan="1" class="right-align">
                {{ numberWithCommas(+parseInt(row.male_HH || 0)) }}
              </td>
              <td colspan="1" class="right-align">
                {{ numberWithCommas(+parseInt(row.female_HH || 0)) }}
              </td>
              <td style="font-weight:bold;width:10%" colspan="1" class="right-align">
                {{
                  numberWithCommas(
                    parseInt(row.male_HH ? row.male_HH : 0) +
                    parseInt(row.female_HH ? row.female_HH : 0)
                  )
                }}
              </td>
            </tr>

            <template v-for="(item, index) in draFormsDataOther">

              <tr :key="index"
                v-if="item.sectors.agriculture && (item.sectors.agriculture.response_needed || item.sectors.agriculture.urgent_response_needed)">
                <td colspan="4">
                  <b>{{ item.admin3.admin3Name_en }}'s response needed</b>
                </td>
                <td colspan="12">
                  <u>
                    <strong>URGENT</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.agriculture
                      ? item.sectors.agriculture.urgent_response_needed
                        ? item.sectors.agriculture.urgent_response_needed
                        : "N/A"
                      : "N/A"
                    }}
                  </li>
                  <hr style="margin:1%;" />
                  <u>
                    <strong>GENERAL</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.agriculture
                      ? item.sectors.agriculture.response_needed
                        ? item.sectors.agriculture.response_needed
                        : "N/A"
                      : "N/A"
                    }}
                  </li>
                </td>
              </tr>
            </template>
          </table>
        </td>
      </tr>
      <!--=============================AGRICULTURE END HERE===================================-->

      <!--===============================FOOD SECURITY START HERE==============================-->
      <tr v-if="hasFoodProperties(item)" style="margin:0;padding:0 !important;border:0px !important;">
        <td colspan="16" style="margin:0;padding:0 !important;border:0px">
          <table width="100%" style="margin:0;padding:0 !important;border:0px !important;">
            <tr>
              <td :rowspan="draFormsData.length +
                  (food_stocks_avaliability_array
                    ? food_stocks_avaliability_array.length > 0
                      ? food_stocks_avaliability_array.length + 1
                      : food_stocks_avaliability_array.length + 1
                    : 1) +
                  1
                  " colspan="1" width="12.2%">
                <span class="rotated" style="font-weight: bold;"></span>
                <img src="../../../../static/cluster_food_security_100px_bluebox.png" height="150" />
              </td>
              <td v-if="food_stocks_avaliability_array &&
                  food_stocks_avaliability_array.length > 0
                  " v-bind:rowspan="food_stocks_avaliability_array.length > 0
          ? food_stocks_avaliability_array.length + 2
          : 1
        " colspan="5" width="30%">
                <b>Food stocks availability (HH)</b>
              </td>
              <td v-if="food_stocks_avaliability_array" colspan="4">
                <b>TA</b>
              </td>
              <td v-if="food_stocks_avaliability_array" colspan="3" width="10%">
                <b>
                  <center>MHH</center>
                </b>
              </td>
              <td v-if="food_stocks_avaliability_array" colspan="3" width="10%">
                <b>
                  <center>FHH</center>
                </b>
              </td>
              <td v-if="food_stocks_avaliability_array" style="font-weight:bold" colspan="2" width="10%">
                <b>
                  <center>Total</center>
                </b>
              </td>
            </tr>
            <tr v-if="food_stocks_avaliability_array &&
                food_stocks_avaliability_array.length > 0
                " v-for="(stock, index) in food_stocks_avaliability_array">
              <td colspan="4" v-if="index ==
                  food_stocks_avaliability_array.findIndex(
                    x => x.ta == stock.ta
                  )
                  " :rowspan="food_stocks_avaliability_array.filter(x => x.ta == stock.ta)
          .length
        ">
                {{ stock.ta }}
              </td>
              <td colspan="3" class="right-align">
                {{ parseInt(stock.male_HH || 0) }}
              </td>
              <td colspan="3" class="right-align">
                {{ parseInt(stock.female_HH || 0) }}
              </td>
              <td style="font-weight:bold" colspan="2" class="right-align">
                {{
                  parseInt(stock.female_HH ? stock.female_HH : 0) +
                  parseInt(stock.male_HH ? stock.male_HH : 0)
                }}
              </td>
            </tr>
            <tr v-if="food_stocks_avaliability_array &&
                food_stocks_avaliability_array.length > 0
                ">
              <td style="font-weight:bold" colspan="4">
                <center>Total</center>
              </td>
              <td style="font-weight:bold" colspan="3" class="right-align">
                {{
                  numberWithCommas(
                    food_stocks_avaliability_array
                      ? food_stocks_avaliability_array
                        .map(function (item) {
                          return item.male_HH ? parseInt(item.male_HH) : 0;
                        })
                        .reduce((sum, value) => sum + value)
                      : 0
                  )
                }}
              </td>
              <td style="font-weight:bold" colspan="3" class="right-align">
                {{
                  numberWithCommas(
                    food_stocks_avaliability_array
                      ? food_stocks_avaliability_array
                        .map(function (item) {
                          return item.female_HH
                            ? parseInt(item.female_HH)
                            : 0;
                        })
                        .reduce((sum, value) => sum + value)
                      : 0
                  )
                }}
              </td>
              <td style="font-weight:bold" colspan="3" class="right-align">
                {{
                  numberWithCommas(
                    (food_stocks_avaliability_array
                      ? food_stocks_avaliability_array
                        .map(function (item) {
                          return item.male_HH ? parseInt(item.male_HH) : 0;
                        })
                        .reduce((sum, value) => sum + value)
                      : 0) +
                    (food_stocks_avaliability_array
                      ? food_stocks_avaliability_array
                        .map(function (item) {
                          return item.female_HH
                            ? parseInt(item.female_HH)
                            : 0;
                        })
                        .reduce((sum, value) => sum + value)
                      : 0)
                  )
                }}
              </td>
            </tr>
            <template v-for="(item, index) in draFormsDataOther">

              <tr :key="index"
                v-if="item.sectors.food && (item.sectors.food.response_needed || item.sectors.food.urgent_response_needed)">
                <td colspan="2">
                  <b>{{ item.admin3.admin3Name_en }}'s response needed</b>
                </td>
                <td colspan="18">
                  <u>
                    <strong>URGENT</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.food
                      ? item.sectors.food.urgent_response_needed
                        ? item.sectors.food.urgent_response_needed
                        : "N/A"
                      : "N/A"
                    }}
                  </li>
                  <hr style="margin:1%;" />
                  <u>
                    <strong>GENERAL</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.food
                      ? item.sectors.food.response_needed
                        ? item.sectors.food.response_needed
                        : "N/A"
                      : "N/A"
                    }}
                  </li>
                </td>
              </tr>
            </template>
          </table>
        </td>
      </tr>
      <!--===============================FOOD SECURITY END HERE==============================-->

      <!--===============================EDUCATION START HERE================================-->

      <tr v-if="hasEducationProperties(item)" style="margin:0;padding:0 !important;border:0px !important;">
        <td colspan="16" style="margin:0;padding:0 !important;border:0px">
          <table width="100%" style="margin:0;padding:0 !important;border:0px !important;">
            <tr>
              <td width="12.2%" :rowspan="1 +
                  (impact_on_schools
                    ? impact_on_schools.length > 0
                      ? impact_on_schools.length +
                      impact_on_schools.length +
                      4
                      : 1
                    : 1) +
                  draFormsData.length
                  " colspan="1">
                <span class="rotated" style="font-weight: bold;"></span>
                <img src="../../../../static/cluster_education_100px_bluebox.png" height="150" />

              </td>
            </tr>
            <tr>
              <td v-if="impact_on_schools" colspan="3" width="20%" :rowspan="impact_on_schools.length > 0
                    ? impact_on_schools.length + 2
                    : 1
                  ">
                <b>Leaners out of school</b>
              </td>
              <td v-if="impact_on_schools" colspan="3" width="21%">
                <b>TA</b>
              </td>
              <td v-if="impact_on_schools" colspan="3">
                <b>Male</b>
              </td>
              <td v-if="impact_on_schools" colspan="3">
                <b>Female</b>
              </td>
              <td v-if="impact_on_schools" colspan="3">
                <b>Total</b>
              </td>
            </tr>
            <template v-if="impact_on_schools && impact_on_schools.length > 0">
              <tr v-for="(row, index) in impact_on_schools" :key="index">
                <td colspan="3" width="21%">{{ row.ta }}</td>
                <td colspan="3" class="right-align">
                  {{ numberWithCommas(row.females_out_of_school || 0) }}
                </td>
                <td colspan="3" class="right-align">
                  {{ numberWithCommas(row.males_out_of_school || 0) }}
                </td>
                <td colspan="3" class="right-align">
                  {{ numberWithCommas((+row.males_out_of_school || 0) + (+row.females_out_of_school || 0)) }}
                </td>
              </tr>
            </template>

            <tr v-if="impact_on_schools && impact_on_schools.length > 0
              ">
              <td colspan="3" width="21%">
                <center>
                  <b>Total</b>
                </center>
              </td>
              <td style="font-weight:bold" colspan="3" class="right-align">
                {{
                  numberWithCommas(
                    impact_on_schools
                      ? impact_on_schools
                        .map(function (item) {
                          return item.females_out_of_school
                            ? parseInt(item.females_out_of_school)
                            : 0;
                        })
                        .reduce(
                          (sum, value) => parseInt(sum) + parseInt(value)
                        )
                      : 0
                  )
                }}
              </td>
              <td style="font-weight:bold" colspan="3" class="right-align">
                {{
                  numberWithCommas(
                    impact_on_schools
                      ? impact_on_schools
                        .map(function (item) {
                          return item.males_out_of_school
                            ? parseInt(item.males_out_of_school)
                            : 0;
                        })
                        .reduce(
                          (sum, value) => parseInt(sum) + parseInt(value)
                        )
                      : 0
                  )
                }}
              </td>

              <td style="font-weight:bold" colspan="3" class="right-align">
                {{
                  numberWithCommas(
                    (impact_on_schools
                      ? impact_on_schools
                        .map(function (item) {
                          return item.males_out_of_school
                            ? parseInt(item.males_out_of_school)
                            : 0;
                        })
                        .reduce(
                          (sum, value) => parseInt(sum) + parseInt(value)
                        )
                      : 0) + (impact_on_schools
                        ? impact_on_schools
                          .map(function (item) {
                            return item.females_out_of_school
                              ? parseInt(item.females_out_of_school)
                              : 0;
                          })
                          .reduce(
                            (sum, value) => parseInt(sum) + parseInt(value)
                          )
                        : 0
                    )
                  )
                }}
              </td>
            </tr>
            <template v-for="(item, index) in draFormsDataOther">

              <tr :key="index"
                v-if="item.sectors.education && (item.sectors.education.response_needed || item.sectors.education.urgent_response_needed)">

                <td colspan="2">
                  <b>{{ item.admin3.admin3Name_en }}'s response needed</b>
                </td>
                <td colspan="14">
                  <u>
                    <strong>URGENT</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.education
                      ? item.sectors.education.urgent_response_needed
                        ? item.sectors.education.urgent_response_needed
                        : "N/A"
                      : "N/A"
                    }}
                  </li>
                  <hr style="margin:1%;" />
                  <u>
                    <strong>GENERAL</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.education
                      ? item.sectors.education.response_needed
                        ? item.sectors.education.response_needed
                        : "N/A"
                      : "N/A"
                    }}
                  </li>
                </td>
              </tr>
            </template>
          </table>
        </td>
      </tr>
      <!--=================================EDUCATION END HERE=================================-->

      <!--=================================HEALTH START HERE=================================-->
      <tr v-if="hasHealthProperties(item)" style="margin:0;padding:0 !important;border:0px !important;">
        <td colspan="16" style="margin:0;padding:0 !important;border:0px">
          <table width="100%" style="margin:0;padding:0 !important;border:0px !important;">
            <tr>
              <td width="12.2%" :rowspan="(available_health_facilities
                    ? available_health_facilities.length > 0
                      ? available_health_facilities.length + 1
                      : 0
                    : 0) +
                  draFormsData.length +
                  2
                  " colspan="1">
                <span class="rotated" style="font-weight: bold;"></span>
                <img src="../../../../static/cluster_health_100px_bluebox.png" height="150" />
              </td>
            </tr>
            <tr v-if="available_health_facilities &&
                available_health_facilities.length > 0
                ">
              <td :rowspan="available_health_facilities.length > 0
                    ? available_health_facilities.length + 2
                    : 1
                  " colspan="5" width="20%">
                <b>Health facility availability</b>
              </td>
              <td v-if="available_health_facilities" colspan="4">
                <b>TA</b>
              </td>
              <td v-if="available_health_facilities" colspan="2" width="25%">
                <b>Health Facility</b>
              </td>
              <td v-if="available_health_facilities" colspan="2" width="15%">
                <b>Status</b>
              </td>
              <!-- <td v-if="available_health_facilities" colspan="2" width="15%">
                <b>Closed</b>
              </td> -->
            </tr>

            <tr v-if="available_health_facilities &&
                available_health_facilities.length > 0
                " v-for="(row, index) in available_health_facilities">
              <td colspan="4" v-if="index ==
                  available_health_facilities.findIndex(
                    x => x.name == row.name
                  )
                  " :rowspan="available_health_facilities.filter(x => x.name == row.name)
          .length
        ">
                {{ row.ta }}
              </td>
              <td colspan="2" class="left-align">
                {{ row.facility_name }} &nbsp; {{ row.name }}
              </td>
              <td colspan="2" class="left-align">
                {{ row.status }}
              </td>
              <!-- <td colspan="2" class="right-align">
                {{ numberWithCommas(row.closed) }}
              </td> -->
            </tr>

            <tr v-if="available_health_facilities &&
              available_health_facilities.length > 0
              ">
              <!-- <td style="font-weight:bold" colspan="2" class="right-align">
                {{
                  numberWithCommas(
                    available_health_facilities
                      ? available_health_facilities
                          .map(function(item) {
                            return item.closed ? parseInt(item.closed) : 0;
                          })
             .             .reduce((sum, value) => sum + value)
                      : 0
                  )
                }}
              </td> -->
            </tr>

            <tr v-if="other_health_facilities && other_health_facilities.length > 0
              " v-for="row in other_health_facilities">
              <td colspan="4">{{ row.ta }}</td>
              <td colspan="2" class="right-align">
                {{ row.name }}
              </td>
              <td colspan="2" class="right-align">
                {{ row.status }}
              </td>
              <!-- <td colspan="2" class="right-align">
                {{ numberWithCommas(+parseInt(row.closed || 0)) }}
              </td> -->
            </tr>

            <tr v-if="other_health_facilities && other_health_facilities.length > 0
              ">
              <td style="font-weight:bold" colspan="4">
                <center>Total</center>
              </td>
              <td style="font-weight:bold" colspan="2" class="right-align">
                <!-- {{
                  numberWithCommas(
                    other_health_facilities
                      ? other_health_facilities
                          .map(function(item) {
                            return item.partially_functioning
                              ? parseInt(item.partially_functioning)
                              : 0;
                          })
                          .reduce((sum, value) => sum + value)
                      : 0
                  )
                }} -->
              </td>
              <td style="font-weight:bold" colspan="2" class="right-align">
                <!-- {{
                  numberWithCommas(
                    other_health_facilities
                      ? other_health_facilities
                          .map(function(item) {
                            return item.verge_of_closing
                              ? parseInt(item.verge_of_closing)
                              : 0;
                          })
                          .reduce((sum, value) => sum + value)
                      : 0
                  )
                }} -->
              </td>
              <td style="font-weight:bold" colspan="2" class="right-align">
                <!-- {{
                  numberWithCommas(
                    other_health_facilities
                      ? other_health_facilities
                          .map(function(item) {
                            return item.closed ? parseInt(item.closed) : 0;
                          })
                          .reduce((sum, value) => sum + value)
                      : 0
                  )
                }} -->
              </td>
            </tr>

            <template v-for="(item, index) in draFormsDataOther">

              <tr :key="index"
                v-if="item.sectors.health && (item.sectors.health.response_needed || item.sectors.health.urgent_response_needed)">

                <td colspan="2" width="20%">
                  <b>{{ item.admin3.admin3Name_en }}'s response needed</b>
                </td>
                <td colspan="14">
                  <u>
                    <strong>URGENT</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.health
                      ? item.sectors.health.urgent_response_needed
                        ? item.sectors.health.urgent_response_needed
                        : "N/A"
                      : "N/A"
                    }}
                  </li>
                  <hr style="margin:1%;" />
                  <u>
                    <strong>GENERAL</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.health
                      ? item.sectors.health.response_needed
                        ? item.sectors.health.response_needed
                        : "N/A"
                      : "N/A"
                    }}
                  </li>
                </td>
              </tr>
            </template>
          </table>
        </td>
      </tr>
      <!--=================================HEALTH END HERE===================================-->

      <!--=================================NUTRITION START HERE==============================-->
      <tr v-if="hasNutritionProperties(item)" style="margin:0;padding:0 !important;border:0px !important;">
        <td colspan="16" style="margin:0;padding:0 !important;border:0px">
          <table width="100%" style="margin:0;padding:0 !important;border:0px !important;">
            <tr v-if="affected_population">
              <td width="12.2%" :rowspan="(nutritionAffected_populationArray
                    ? nutritionAffected_populationArray.length > 0
                      ? nutritionAffected_populationArray.length + 1
                      : nutritionAffected_populationArray.length + 1
                    : 1) + draFormsData.length
                  " colspan="1">
                <span class="rotated" style="font-weight: bold;"></span>
                <img src="../../../../static/cluster_nutrition_100px_bluebox.png" height="150" />
              </td>
              <td :rowspan="nutritionAffected_populationArray.length > 0
                    ? nutritionAffected_populationArray.length + 1
                    : nutritionAffected_populationArray.length + 1
                  " colspan="6" width="20%">
                <b>Affected Population</b>
              </td>

              <td colspan="4">
                <b>Category</b>
              </td>
              <td colspan="2" width="10%">
                <b>
                  <center>Male</center>
                </b>
              </td>
              <td colspan="2" width="10%">
                <b>
                  <center>Female</center>
                </b>
              </td>
              <td style="font-weight:bold" width="10%">
                <b>
                  <center>Total</center>
                </b>
              </td>
            </tr>
            <tr v-if="nutritionAffected_populationArray &&
                nutritionAffected_populationArray.length > 0
                " v-for="row in nutritionAffected_populationArray">
              <td colspan="4">{{ row.name }}</td>
              <td colspan="2" class="right-align">
                {{ numberWithCommas(parseInt(row.affected_males || 0)) }}
              </td>
              <td colspan="2" class="right-align">
                {{ numberWithCommas(row.affected_females) }}
              </td>
              <td style="font-weight:bold" class="right-align">
                {{
                  numberWithCommas(
                    parseInt(row.affected_males ? row.affected_males : 0) +
                    parseInt(row.affected_females ? row.affected_females : 0)
                  )
                }}
              </td>
            </tr>
            <template v-for="(item, index) in draFormsDataOther">

              <tr :key="index"
                v-if="item.sectors.nutrition && (item.sectors.nutrition.response_needed || item.sectors.nutrition.urgent_response_needed)">

                <td colspan="4">
                  <b>{{ item.admin3.admin3Name_en }}'s response needed</b>
                </td>
                <td colspan="12">
                  <u>
                    <strong>URGENT</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.nutrition
                      ? item.sectors.nutrition.urgent_response_needed
                        ? item.sectors.nutrition.urgent_response_needed
                        : "N/A"
                      : "N/A"
                    }}
                  </li>
                  <hr style="margin:1%;" />
                  <u>
                    <strong>GENERAL</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.nutrition
                      ? item.sectors.nutrition.response_needed
                        ? item.sectors.nutrition.response_needed
                        : "N/A"
                      : "N/A"
                    }}
                  </li>
                </td>
              </tr>
            </template>
          </table>
        </td>
      </tr>
      <!--=================================NUTRITION END HERE==============================-->

      <!--=================================PROTECTION START HERE==============================-->
      <tr v-if="hasProtectionProperties(item)"
        style="margin:0;padding:0 !important;border:0px !important;">
        <td colspan="16" style="margin:0;padding:0 !important;border:0px">
          <table width="100%" style="margin:0;padding:0 !important;border:0px !important;">
            <tr v-if="impact_on_vulnerable_persons">
              <td width="12.2%" :rowspan="(impact_on_vulnerable_persons.length > 0
                    ? impact_on_vulnerable_persons.length + 1
                    : impact_on_vulnerable_persons.length) +
                  draFormsData.length +
                  1
                  ">
                <span class="rotated" style="font-weight: bold;"></span>
                <img src="../../../../static/cluster_protection_100px_bluebox.png" height="150" />
              </td>
              <td :rowspan="impact_on_vulnerable_persons.length > 0
                    ? impact_on_vulnerable_persons.length + 2
                    : 1 + impact_on_vulnerable_persons.length
                  " colspan="6" width="20%">
                <b>Impact on vulnerable population</b>
              </td>
              <td colspan="4">
                <b>TA</b>
              </td>
              <td colspan="2">
                <b>
                  <center>Male</center>
                </b>
              </td>
              <td colspan="2">
                <b>
                  <center>Female</center>
                </b>
              </td>
              <td style="font-weight:bold" colspan="2">
                <b>
                  <center>Total</center>
                </b>
              </td>
            </tr>
            <tr v-if="impact_on_vulnerable_persons &&
                impact_on_vulnerable_persons.length > 0
                " v-for="row in impact_on_vulnerable_persons">
              <td colspan="4">{{ row.ta }}</td>
              <td colspan="2" width="10%" class="right-align">
                {{ numberWithCommas(row.males) }}
              </td>
              <td colspan="2" width="10%" class="right-align">
                {{ numberWithCommas(row.females) }}
              </td>
              <td colspan="2" style="font-weight:bold" width="10%" class="right-align">
                {{
                  numberWithCommas(
                    (row.females ? parseInt(row.females) : 0) +
                    (row.males ? parseInt(row.males) : 0)
                  )
                }}
              </td>
            </tr>

            <tr v-if="impact_on_vulnerable_persons &&
                impact_on_vulnerable_persons.length > 0
                ">
              <td style="font-weight:bold" colspan="4">
                <center>Total</center>
              </td>
              <td style="font-weight:bold" colspan="2" class="right-align">
                {{
                  numberWithCommas(
                    impact_on_vulnerable_persons
                      ? impact_on_vulnerable_persons
                        .map(function (item) {
                          return item.males ? parseInt(item.males) : 0;
                        })
                        .reduce((sum, value) => sum + value)
                      : 0
                  )
                }}
              </td>
              <td style="font-weight:bold" colspan="2" class="right-align">
                {{
                  numberWithCommas(
                    impact_on_vulnerable_persons
                      ? impact_on_vulnerable_persons
                        .map(function (item) {
                          return item.females ? parseInt(item.females) : 0;
                        })
                        .reduce((sum, value) => sum + value)
                      : 0
                  )
                }}
              </td>
              <td style="font-weight:bold" colspan="2" class="right-align">
                {{
                  numberWithCommas(
                    (impact_on_vulnerable_persons
                      ? impact_on_vulnerable_persons
                        .map(function (item) {
                          return item.males ? parseInt(item.males) : 0;
                        })
                        .reduce((sum, value) => sum + value)
                      : 0) +
                    (impact_on_vulnerable_persons
                      ? impact_on_vulnerable_persons
                        .map(function (item) {
                          return item.females ? parseInt(item.females) : 0;
                        })
                        .reduce((sum, value) => sum + value)
                      : 0)
                  )
                }}
              </td>
            </tr>
            <template v-for="(item, index) in draFormsDataOther">

              <tr :key="index"
                v-if="item.sectors.protection && (item.sectors.protection.response_needed || item.sectors.protection.urgent_response_needed)">

                <td colspan="4">
                  <b>{{ item.admin3.admin3Name_en }}'s response needed</b>
                </td>
                <td colspan="12">
                  <u>
                    <strong>URGENT</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.protection
                      ? item.sectors.protection.urgent_response_needed
                        ? item.sectors.protection.urgent_response_needed
                        : "N/A"
                      : "N/A"
                    }}
                  </li>
                  <hr style="margin:1%;" />
                  <u>
                    <strong>GENERAL</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.protection
                      ? item.sectors.protection.response_needed
                        ? item.sectors.protection.response_needed
                        : "N/A"
                      : "N/A"
                    }}
                  </li>
                </td>
              </tr>
            </template>
          </table>
        </td>
      </tr>
      <!--=================================PROTECTION END HERE==============================-->

      <!--=================================WASH START HERE==================================-->
      <tr v-if="hasWashProperties(item)" style="margin:0;padding:0 !important;border:0px !important;">
        <td colspan="16" style="margin:0;padding:0 !important;border:0px">
          <table width="100%" style="margin:0;padding:0 !important;border:0px !important;">
            <tr>
              <td :rowspan="8 + draFormsData.length" colspan="1" width="12.2%">
                <span class="rotated" style="font-weight: bold;"></span>
                <img src="../../../../static/cluster_WASH_100px_bluebox.png" height="150" />
              </td>
              <td rowspan="2" colspan="6">
                <b>Households without safe water</b>
              </td>
              <td colspan="3" width="10%">
                <b>
                  <center>MHH</center>
                </b>
              </td>
              <td colspan="3" width="10%">
                <b>
                  <center>FHH</center>
                </b>
              </td>
              <td style="font-weight:bold" colspan="3" width="10%">
                <b>
                  <center>Total</center>
                </b>
              </td>
            </tr>

            <tr v-if="washArray && washArray.length > 0">
              <td colspan="3" class="right-align">
                {{
                  numberWithCommas(
                    washArray
                      .map(function (item) {
                        return item.with_safe_water_mhh
                          ? parseInt(item.with_safe_water_mhh)
                          : 0;
                      })
                      .reduce((sum, value) => sum + value)
                  )
                }}
              </td>
              <td colspan="3" class="right-align">
                {{
                  numberWithCommas(
                    washArray
                      .map(function (item) {
                        return item.with_safe_water_fhh
                          ? parseInt(item.with_safe_water_fhh)
                          : 0;
                      })
                      .reduce((sum, value) => sum + value)
                  )
                }}
              </td>
              <td colspan="3" style="font-weight:bold" class="right-align">
                {{
                  numberWithCommas(
                    washArray
                      .map(function (item) {
                        return item.with_safe_water_fhh
                          ? parseInt(item.with_safe_water_fhh)
                          : 0;
                      })
                      .reduce((sum, value) => sum + value) +
                    washArray
                      .map(function (item) {
                        return item.with_safe_water_mhh
                          ? parseInt(item.with_safe_water_mhh)
                          : 0;
                      })
                      .reduce((sum, value) => sum + value)
                  )
                }}
              </td>
            </tr>

            <tr v-if="washArray && washArray.length > 0">
              <td rowspan="2" colspan="6">
                <b>Households without access to toilets</b>
              </td>
              <td colspan="3">
                <b>
                  <center>MHH</center>
                </b>
              </td>
              <td colspan="3">
                <b>
                  <center>FHH</center>
                </b>
              </td>
              <td colspan="3">
                <b>
                  <center>Total</center>
                </b>
              </td>
            </tr>
            <tr v-if="washArray && washArray.length > 0">
              <td style="font-weight:bold" colspan="3" class="right-align">
                {{
                  numberWithCommas(
                    washArray
                      .map(function (item) {
                        return item.access_to_toilets_mhh
                          ? parseInt(item.access_to_toilets_mhh)
                          : 0;
                      })
                      .reduce((sum, value) => sum + value)
                  )
                }}
              </td>
              <td style="font-weight:bold" colspan="3" class="right-align">
                {{
                  numberWithCommas(
                    washArray
                      .map(function (item) {
                        return item.access_to_toilets_fhh
                          ? parseInt(item.access_to_toilets_fhh || 0)
                          : 0;
                      })
                      .reduce((sum, value) => sum + value)
                  )
                }}
              </td>
              <td style="font-weight:bold" colspan="3" class="right-align">
                {{
                  numberWithCommas(
                    washArray
                      .map(function (item) {
                        return item.access_to_toilets_fhh
                          ? parseInt(item.access_to_toilets_fhh)
                          : 0;
                      })
                      .reduce((sum, value) => sum + value) +
                    washArray
                      .map(function (item) {
                        return item.access_to_toilets_mhh
                          ? parseInt(item.access_to_toilets_mhh || 0)
                          : 0;
                      })
                      .reduce((sum, value) => sum + value)
                  )
                }}
              </td>
            </tr>

            <tr v-if="washArray && washArray.length > 0">
              <td rowspan="2" colspan="6">
                <b>Households have risk of contamination</b>
              </td>
              <td colspan="3">
                <b>
                  <center>MHH</center>
                </b>
              </td>
              <td colspan="3">
                <b>
                  <center>FHH</center>
                </b>
              </td>
              <td style="font-weight:bold" colspan="3">
                <center>Total</center>
              </td>
            </tr>
            <tr v-if="washArray && washArray.length > 0">
              <td style="font-weight:bold" colspan="3" class="right-align">
                {{
                  numberWithCommas(
                    washArray
                      .map(function (item) {
                        return item.risk_contamination_mhh
                          ? parseInt(item.risk_contamination_mhh)
                          : 0;
                      })
                      .reduce((sum, value) => sum + value)
                  )
                }}
              </td>
              <td style="font-weight:bold" colspan="3" class="right-align">
                {{
                  numberWithCommas(
                    washArray
                      .map(function (item) {
                        return item.risk_contamination_fhh
                          ? parseInt(item.risk_contamination_fhh)
                          : 0;
                      })
                      .reduce((sum, value) => sum + value)
                  )
                }}
              </td>
              <td style="font-weight:bold" colspan="3" class="right-align">
                {{
                  numberWithCommas(
                    washArray
                      .map(function (item) {
                        return item.risk_contamination_fhh
                          ? parseInt(item.risk_contamination_fhh)
                          : 0;
                      })
                      .reduce((sum, value) => sum + value) +
                    washArray
                      .map(function (item) {
                        return item.risk_contamination_fhh
                          ? parseInt(item.risk_contamination_fhh)
                          : 0;
                      })
                      .reduce((sum, value) => sum + value)
                  )
                }}
              </td>
            </tr>
            <template v-for="(item, index) in draFormsDataOther">

              <tr :key="index"
                v-if="item.sectors.wash && (item.sectors.wash.response_needed || item.sectors.wash.urgent_response_needed)">

                <td colspan="2" width="20%">
                  <b>{{ item.admin3.admin3Name_en }}'s response needed</b>
                </td>
                <td colspan="14">
                  <u>
                    <strong>URGENT</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.wash
                      ? item.sectors.wash.urgent_response_needed
                        ? item.sectors.wash.urgent_response_needed
                        : "N/A"
                      : "N/A"
                    }}
                  </li>
                  <hr style="margin:1%;" />
                  <u>
                    <strong>GENERAL</strong>
                  </u>
                  <br />
                  <li style="margin-left:2%" class="qcont">
                    {{
                      item.sectors.wash
                      ? item.sectors.wash.response_needed
                        ? item.sectors.wash.response_needed
                        : "N/A"
                      : "N/A"
                    }}
                  </li>
                </td>
              </tr>
            </template>
          </table>
        </td>
      </tr>
      <!--====================================WASH END HERE================================-->
      
      <!--===========================================================================================================================-->
      <!--=====================LOGISTICS START HERE==============-->
      <!--============================================================================================================================-->
      <tr v-if="hasLogisticsProperties(item)" style="margin:0;padding:0 !important;border:0px !important;">
                <td colspan="15" style="margin:0;padding:0 !important;border:0px">
                  <table width="100%" style="margin:0;padding:0 !important;border:0px !important;">
                    <tr>
                      <td :rowspan="
                        4 +
                          (item.logistics.impacted_structures
                            ? item.logistics.impacted_structures
                                .length
                            : 0) +
                          (item.logistics.impacted_telecoms
                            ? item.logistics.impacted_telecoms
                                .length
                            : 0)
                      " width="8%" class="clearfix">
                        <center><img src="../../../../static/cluster_logistics_100px_bluebox.png" height="150" /></center>
                      </td>
                      <!-- structures start -->
                      <td v-if="item.logistics.impacted_structures" :rowspan="
                        1 + item.logistics.impacted_structures.length
                      " colspan="4" style="width:20%">
                        <strong>Impact on Stractures</strong>
                      </td>
                      <td colspan="2" v-if="item.logistics.impacted_structures" style="width:20%">
                        <b>Category</b>
                      </td>
                      <td v-if="item.logistics.impacted_structures" colspan="3" style="width:15%">
                        <b>Lat</b>
                      </td>
                      <td v-if="item.logistics.impacted_structures" colspan="3" style="width:15%">
                        <b>Long</b>
                      </td>
                      <td colspan="3" v-if="item.logistics.impacted_structures" style="width:20%">
                        <b>Road Length (m)</b>
                      </td>
                      <td colspan="3" v-if="item.logistics.impacted_structures" style="width:20%">
                        <b>Accessibility</b>
                      </td>
                      <td colspan="3" v-if="item.logistics.impacted_structures" style="width:20%">
                        <b>Road Condition</b>
                      </td>
                      <td colspan="3" v-if="item.logistics.impacted_structures" style="width:20%">
                        <b>Surface Condition</b>
                      </td>
                      
                    </tr>
                    <tr v-if="item.logistics.impacted_structures"
                      v-for="row in item.logistics.impacted_structures">
                      <td colspan="2" style="width:20%">{{ row.name == "Other"?"Other("+row.other_structure+")":row.name }}</td>
                      <td colspan="3" style="width:15%">{{ row.gps_lat }}</td>
                      <td colspan="3" style="width:15%">{{ row.gps_lng }}</td>
                      <td colspan="3" style="width:20%">{{ row.road_length }}</td>
                      <td colspan="3" style="width:20%">{{ row.road_access }}</td>
                      <td colspan="3" style="width:20%">{{ row.road_condition }}</td>
                      <td colspan="3" style="width:20%">{{ row.surface_condition }}</td>
                      
                    </tr>
                    <!--impacted telecoms-->
                    <tr>
                      <td v-if="item.logistics.impacted_telecoms" :rowspan="
                        1 + item.logistics.impacted_telecoms.length
                      " colspan="4" style="width:20%">
                        <strong>Impact on Telecommunications</strong>
                      </td>
                      <td colspan="4" v-if="item.logistics.impacted_telecoms" style="width:20%">
                        <b>Category</b>
                      </td>
                      <td colspan="4" v-if="item.logistics.impacted_telecoms" style="width:20%">
                        <b>Status</b>
                      </td>
                      <td colspan="5" v-if="item.logistics.impacted_telecoms" style="width:20%">
                        <b>Lat</b>
                      </td>
                      <td colspan="5" v-if="item.logistics.impacted_telecoms" style="width:20%">
                        <b>Long</b>
                      </td>
                    </tr>
                    <tr v-if="item.logistics.impacted_telecoms"
                      v-for="row in item.logistics.impacted_telecoms">
                      <td colspan="4" style="width:20%">{{ row.name == "Other"?"Other("+row.other_telecommunications_type+")":row.name}}</td>
                      <td colspan="4" style="width:20%">{{ row.status }}</td>
                      <td colspan="5" style="width:20%">{{ row.telecoms_gps_lat }}</td>
                      <td colspan="5" style="width:20%">{{ row.telecoms_gps_long }}</td>
                    </tr>
                    <!-- structures end  -->
                    <tr>
                      <td colspan="4" style="width:20%">
                        <strong>Emergency Needs</strong>
                      </td>
                      <td colspan="20">

                        <li style="margin-left:2%" class="qcont">
                          {{
                          item.logistics.emergent_needs==undefined ? "Not reported" :
                          item.logistics.emergent_needs.join(", ")
                          }}
                        </li>
                      </td>
                    </tr>
                    <tr>
                      <td colspan="4" style="width:20%">
                        <strong>Response Needed</strong>
                      </td>
                      <td colspan="20" style="width:70%">
                        <u>
                          <strong>URGENT</strong>
                        </u>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          {{
                          item.logistics
                          ? item.logistics.urgent_response_needed
                          ? item.logistics.urgent_response_needed
                          : "N/A"
                          : "N/A"
                          }}
                        </li>
                        <hr style="margin:1%;" />
                        <u>
                          <strong>GENERAL</strong>
                        </u>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          {{
                          item.logistics
                          ? item.logistics.response_needed
                          ? item.logistics.response_needed
                          : "N/A"
                          : "N/A"
                          }}
                        </li>
                      </td>
                    </tr>

                  </table>
                </td>
              </tr>
      <!--======================================================================================================================-->
      <!--============LOGISTICS END HERE====================-->
      <!--======================================================================================================================-->

      <!--======================================================================================================================-->
      <!--=============ENVIRONMENT START HERE===============-->
      <!--======================================================================================================================-->
      <tr v-if="hasEnvironmentProperties(item)" style="margin:0;padding:0 !important;border:0px !important;">
                <td colspan="15" style="margin:0;padding:0 !important;border:0px">
                  <table width="100%" style="margin:0;padding:0 !important;border:0px !important;">
                    <tr>
                      <td :rowspan="
                        3 +
                          (item.environment.impact_on_environment
                            ? item.environment.impact_on_environment
                                .length
                            : 0)
                      " width="8%" class="clearfix">
                        <center><img src="../../../../static/other_cluster_environment_100px_bluebox.png" height="150" /></center>
                      </td>
                      <td v-if="item.environment.impact_on_environment" :rowspan="
                        1 +
                          item.environment.impact_on_environment
                            .length
                      " colspan="3" style="width:20%">
                        <strong>Impact on structures</strong>
                      </td>
                      <td v-if="item.environment.impact_on_environment" colspan="9" style="width:60%">
                        <b>Question</b>
                      </td>
                      <td v-if="item.environment.impact_on_environment" colspan="2" width="10%">
                        <b>Response</b>
                      </td>
                    </tr>
                    <tr v-if="item.environment.impact_on_environment" v-for="row in item.environment
                    .impact_on_environment">
                      <td colspan="9" style="width:60%">
                        {{ row.impact_question }}
                      </td>
                      <td colspan="2" width="10%">{{ row.response }}</td>
                    </tr>
                    <tr>
                      <td colspan="3" style="width:20%">
                        <strong>Response Needed</strong>
                      </td>
                      <td colspan="11">
                        <b style="padding-bottom:20px">
                          <u>
                            <strong>URGENT</strong>
                          </u>
                        </b>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          {{
                          item.environment
                          ? item.environment.urgent_response_needed
                          ? item.environment
                          .urgent_response_needed
                          : "N/A"
                          : "N/A"
                          }}
                        </li>
                        <hr style="margin:1%;" />
                        <b style="padding-bottom:20px">
                          <u>
                            <strong>GENERAL</strong>
                          </u>
                        </b>
                        <br />
                        <li style="margin-left:2%" class="qcont">
                          {{
                          item.environment
                          ? item.environment.response_needed
                          ? item.environment.response_needed
                          : "N/A"
                          : "N/A"
                          }}
                        </li>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
    <!--======================================================================================================================-->
    <!--============ENVIRONMENT END HERE==================-->
    <!--======================================================================================================================-->
     
    </table>
  </div>
</template>
<script>
import { MongoReports } from "../api/MongoReports";
import downloadexcel from "vue-json-excel";
import html2pdf from "html2pdf.js";
import EventBus from "../../../helpers/event-bus";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import moment from "moment";
import fs from "fs";

export default {
  components: {
    downloadexcel
  },

  data() {
    return {
      completely_damaged: [],
      partially_damaged: [],
      facility_list: [],
      uploadedImages: [],
      isLoaded: false,
      draFormsDataOther: [],
      food_stocks_avaliability_array: [],
      impact_on_schools_array: [],
      nutritionAffected_populationArray: [],
      numberOfTAs: "",
      dinrFormsData: {},
      villagesArray: [],
      bufferDraFormsData: [],
      draFormsData: [],
      download: [],
      TAarray: [],
      downloadData: [],
      otherData: [],
      vhhousesvalue: "",
      numberOfTAs: "",
      shelterArray: [],
      displacedArray: [],
      agricultureArray: [],
      cropDamageLossArray: [],
      healthArray: [],
      washArray: [],
      livelihoodsArray: [],
      protectionArray: [],
      foodArray: [],
      nutritionArray: [],
      educationArray: [],
      environmentArray: [],
      people_without_shelter: [],
      PeopleAffectedrows: [],
      PeopleAffectedrowsD: [],
      PeopleInjuredrows: [],
      PeopleDeadrows: [],
      PeopleMissingrows: [],
      crops_damaged: [],
      food_item_damage: [],
      food_stocks_avaliability: [],
      impact_on_schools: [],
      available_health_facilities: [],
      other_health_facilities: [],
      livelihoods_affected: [],
      affected_population: [],
      impact_on_crops: [],
      impact_on_vulnerable_persons: []
    };
  },
  methods: {
    hasImages(dinr) {
      if (Object.keys(dinr).includes("disasterImages")) {
        return dinr.disasterImages.length > 0;
      }

      return false;
    },
    hasAgricultureProperties(item) {
      const agricultureProperties = [
        'crops_damaged',
        'emergent_needs',
        'facilities_damaged',
        'impact_on_crops',
        'impact_on_livestock',
        'livelihoods_affected',
        'partially_damaged',
        'response_needed_crops',
        'response_needed_livestock',
      ];
      return (
        item &&
        item.agriculture &&
        agricultureProperties.some(prop => item.agriculture[prop] && item.agriculture[prop].length > 0)
      );
    },


    hasEducationProperties(item) {
      const educationProperties = [
        'emergent_needs',
        'impact_on_schools',
      ];
      return (
        item &&
        item.education &&
        educationProperties.some(prop => item.education[prop] && item.education[prop].length > 0)
      );
    },


    hasEnvironmentProperties(item) {
      const environmentProperties = [
        'impact_on_environment',
        // Add any new properties here
      ];

      return (
        item &&
        item.environment &&
        environmentProperties.some(prop => item.environment[prop] && item.environment[prop].length > 0)
      );
    },


    hasFoodProperties(item) {
      const foodProperties = [
        'emergent_needs',
        'food_access',
        'food_item_damage',
        'food_stocks_avaliability',
        'impact_on_price',
      ];

      return (
       item &&
       item.food &&
       foodProperties.some(prop => item.food[prop] && item.food[prop].length > 0)
       );
    },


    hasHealthProperties(item) {
      const healthProperties = [
        'available_health_facilities',
        'emergent_needs',
        'available_health_medical',
        'compromised_services',
        'emergent_children_needs',
        'risk_out_disease_outbreak',
      ];

      return (
       item &&
       item.health &&
       healthProperties.some(prop => item.health[prop] && item.health[prop].length > 0)
       );
    },

    hasLogisticsProperties(item) {
      const logisticsProperties = [
        // 'access_of_structures',
        'emergent_needs',
        'impacted_structures',
        'impacted_telecoms',
        // 'other_access_to_structures',
        // 'other_impacted_structures',
      ];
      return (
       item &&
       item.logistics &&
       logisticsProperties.some(prop => item.logistics[prop] && item.logistics[prop].length > 0)
       );
    },

    hasNutritionProperties(item) {
      const nutritionProperties = [
        'affected_population',
        // 'emergent_needs',
        // 'impact_on_nutrition_programmes',
        'lostLivelihood',
      ];
      return (
       item &&
       item.nutrition &&
       nutritionProperties.some(prop => item.nutrition[prop] && item.nutrition[prop].length > 0)
       );
    },

    hasProtectionProperties(item) {
      const protectionProperties = [
        'impact_on_vulnerable_persons',
        'protection_assessments',
        'protection_awareness',
        'protection_concerns',
        'protection_mainstreaming',
        'protection_needs',
        'protection_services',
        'protection_supplies',
        'protection_trainings',
      ];
      return (
       item &&
       item.protection &&
       protectionProperties.some(prop => item.protection[prop] && item.protection[prop].length > 0)
       );
    },

    hasShelterProperties(item) {
      const shelterProperties = [
        //'ImpactOnHouses',
        'PeopleAffectedrows',
        'PeopleDeadrows',
        'PeopleInjuredrows',
        'PeopleMissingrows',
        'camp_info',
        'other_structures_damaged',
        'people_without_shelter',
        'response_needed_emergency',
        'response_needed_non_food'
      ];

      return (

       item &&
       item.shelter &&
       shelterProperties.some(prop => item.shelter[prop] && item.shelter[prop].length > 0)
       );
    },

    hasWashProperties(item) {
      const washProperties = [
      'risk_of_water_contamination',
      'hh_affected_by_water_contamination',
         'accessToToilets',
        'withoutSafeDrinkingWater',
        'impact_on_water_sources',
        'emergent_needs',
        'number_of_facilities_affected',
        'other_water_sources_impacted',
        //'people_without_shelter',
        'risk_of_water_contamination_other_sources'

      ];
      return (
       item &&
       item.wash &&
       washProperties.some(prop => item.wash[prop] && item.wash[prop].length > 0)
       );
    },

    hasDisplacedProperties(item) {
      const displacedProperties = [
        'PeopleAffectedrows',
        // 'displaced_disaggregated',
        // 'displaced_households_accommodated',
        // 'displaced_individuals_accommodated',
        // 'hummanitarian_assistance',
        // 'response_needed_emergency',
        // 'response_needed_non_food'
      ];
      return (
       item &&
       item.displaced &&
       displacedProperties.some(prop => item.displaced[prop] && item.displaced[prop].length > 0)
       );
    },

    renderImage(location) {
      const imageType = location.split(".").pop();
      const base64Data = fs.readFileSync(location, { encoding: "base64" });
      const dataURI = `data:image/${imageType};base64,${base64Data}`;

      return dataURI;
    },
    numberWithCommas(number) {
      try {
        var parts = number.toString().split(".");
        parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        return parts.join(".");
      } catch (e) { }
    },
    arrayAggregator(arrayData) {
      var result = [
        arrayData.reduce((acc, n) => {
          for (var prop in n) {
            if (acc.hasOwnProperty(prop)) {
              if (isNaN(acc[prop])) {
                acc[prop] = acc[prop] + "," + n[prop];
              } else acc[prop] = parseFloat(acc[prop]) + parseFloat(n[prop]);
            } else acc[prop] = n[prop];
          }
          return acc;
        }, {})
      ];
    },
    dateFormate(dateData) {
      const cur_date = dateData;
      const formattedDate = moment(cur_date).format("DD/MM/YYYY");

      return formattedDate;
    },
    sumArraysShelter(
      data,
      objectname,
      member,
      males,
      females,
      cMales,
      cFemales
    ) {
      for (let i = 0; i < data.length; i++) {
        try {
          if (objectname == "displaced" && i > 0) {
            member = member.replace("D", "");
          }
          let sumFemales = 0;
          let sumMales = 0;
          let x = data[i];
          let actualObject = x[objectname];

          if (actualObject[member]) {
            actualObject[member].forEach(function (obj) {
              sumFemales += parseInt(obj[females] || 0);
              sumFemales += parseInt(obj[cFemales] || 0);
              sumMales += parseInt(obj[males] || 0);
              sumMales += parseInt(obj[cMales] || 0);
            });

            if (objectname == "displaced") {
              member = member + "D";
            }

            this[member].push({
              ta: data[i].admin3.admin3Name_en,
              females: sumFemales,
              males: sumMales
            });
          }
        } catch (error) { }
      }
    },
    sumArrays(data, objectname, member, males, females) {
      // alert(data.length)

      if (objectname == "displaced") {
        member = member.replace("D", "");
      }

      for (let i = 0; i < data.length; i++) {
        try {
          let sumFemales = 0;
          let sumMales = 0;
          let x = data[i];
          let actualObject = x[objectname];

          if (actualObject[member]) {
            actualObject[member].forEach(function (obj) {
              sumFemales += parseInt(obj[females] || 0);
              sumMales += parseInt(obj[males] || 0);
            });

            if (objectname == "displaced") {
              this[member + "D"].push({
                ta: data[i].admin3.admin3Name_en,
                females: sumFemales,
                males: sumMales
              });
            } else {
              this[member].push({
                ta: data[i].admin3.admin3Name_en,
                females: sumFemales,
                males: sumMales
              });
            }
          }
        } catch (error) {

        }
      }
    },

    sumEducationArrays(
      data,
      objectname,
      member,
      females_out_of_school,
      males_out_of_school
    ) {
      console.log(data,
        objectname,
        member,
        females_out_of_school,
        males_out_of_school);

      for (let i = 0; i < data.length; i++) {
        try {
          let sum1 = 0,
            sum2 = 0,
            sum3 = 0,
            sum4 = 0,
            sum5 = 0,
            sum6 = 0;

          let x = data[i];
          let actualObject = x[objectname];
          if (actualObject[member]) {
            actualObject[member].forEach(function (obj) {
              sum5 += parseInt(obj[females_out_of_school] || 0);
              sum6 += parseInt(obj[males_out_of_school] || 0);
            });

            this[member].push({
              ta: data[i].admin3.admin3Name_en,
              females_out_of_school: sum5,
              males_out_of_school: sum6
            });
          }
        } catch (error) { }
      }
    },

    sumHealthArrays(
      data,
      objectname,
      member,
      partially_functioning,
      verge_of_closing,
      closed
    ) {
      // alert(data.length);

      for (let i = 0; i < data.length; i++) {
        try {
          let sum1 = 0,
            sum2 = 0,
            sum3 = 0;

          let x = data[i];
          let actualObject = x[objectname];
          if (actualObject[member]) {
            actualObject[member].forEach(function (obj) {
              sum1 += parseInt(
                obj[partially_functioning] ? obj[partially_functioning] : 0
              );
              sum2 += parseInt(obj[verge_of_closing] || 0);
              sum3 += parseInt(obj[closed] || 0);
            });

            this[member].push({
              ta: data[i].admin3.admin3Name_en,
              verge_of_closing: sum1,
              partially_functioning: sum2,
              closed: sum3
            });
          }
        } catch (error) { }
      }
    },
    //alert(JSON.stringify(this.PeopleAffectedrows));

    printdiv(printpage) {
      var headstr = "<html><head><title></title></head><body>";
      var footstr = "</body>";
      var newstr = document.all.item(printpage).innerHTML;
      var oldstr = document.body.innerHTML;
      document.body.innerHTML = headstr + newstr + footstr;
      window.print();
      document.body.innerHTML = oldstr;
      location.reload();
      return false;
    },
    formatdate(data) {
      const cur_date = data;
      const formattedDate = moment(cur_date).format("YYYY/MM/DD hh:mm");

      return formattedDate;
    }
  },
  computeLogo() {
    return "../../../../static/logo.png";
  },
  async mounted() {
    //Get DINR Form

    this.shelterArray = [];
    this.displacedArray = [];
    this.agricultureArray = [];
    this.healthArray = [];
    this.washArray = [];
    this.livelihoodsArray = [];
    this.protectionArray = [];
    this.foodArray = [];
    this.nutritionArray = [];
    this.educationArray = [];
    this.cropDamageLossArray = [];
    this.environmentArray = [];
    this.draFormsData = [];

    //console.log(this.$route);

    //initialise number TAs in DNIR
    this.numberOfTAs = 0;

    let count = 0;

    //Downloading PDF
    EventBus.$on("printSummaryPDF", async self => {
      var element = document.getElementById("section-to-print");
      var disasterdistrict = document.getElementById("district").innerHTML;
      var disastertype = document.getElementById("disastertype").innerHTML;
      var disasterdate = document.getElementById("disasterstart").innerHTML;

      var opt = {
        margin: 0,
        filename: disasterdate + "_" + disasterdistrict + "_" + disastertype,
        pagebreak: { mode: ["avoid-all", "css", "img"] },
        image: { type: "jpeg", quality: 1 },
        html2canvas: { scale: 2, useCORS: true, allowTaint: true },
        jsPDF: { unit: "in", format: "a4", orientation: "p" }
      };

      html2pdf()
        .set(opt)
        .from(element)
        .save();
      self.modalShow = false;
    });
    //Get DRA Form

    MongoReports.getOneDinr(
      this.$route.params.uuid /* , this.$session.get("jwt") */
    ).then(response => {
      this.dinrFormsData = response.data;

      /////////////
      if ("disasterImages" in response.data) {
        this.uploadedImages = response.data.disasterImages;
      }
    });

    this.food_stocks_avaliability_array = [];
    this.nutritionAffected_populationArray = [];
    (this.agricultureLivelihoodArray = []), (this.impact_on_schools_Array = []);
    var self = this;

    MongoReports.getDras(
      this.$route.params.uuid /* , this.$session.get("jwt") */
    ).then(response => {
      this.bufferDraFormsData = response.data;

      this.downloadData.dinrform = {};

      this.bufferDraFormsData.forEach(item => {
        this.downloadData.all = item;

        this.TAarray.push(item.admin3.admin3Name_en.trim());

        for (let i in item.villages) {
          item.villages[i] && item.villages[i].name
            ? this.villagesArray.push(item.villages[i].name)
            : "";
        }

        this.TAarray = this.TAarray.sort();
        this.villagesArray = this.villagesArray
          .join()
          .replace(/ /g, "")
          .split(",")
          .sort()
          .filter(function (item, pos, self) {
            return self.indexOf(item) == pos;
          });

        this.villagesArray = this.villagesArray.sort();

        if (!item.sectors) {
          return;
        }

        this.numberOfTAs++;


        this.draFormsDataOther.push(item);
        var preparedData = {};
        preparedData.sectors = item.sectors;

        preparedData.sectors.admin3 = item.admin3;
        //console.log(preparedData.sectors);
        this.draFormsData.push(preparedData.sectors);
        this.shelterArray.push(item.sectors.shelter);
        this.displacedArray.push(item.sectors.displaced);
        console.log(item.sectors.agriculture)
        this.agricultureArray.push(item.sectors.agriculture);
        this.healthArray.push(item.sectors.health);
        if (item.sectors.food && item.sectors.food.food_stocks_avaliability)
          for (
            let i = 0;
            i < item.sectors.food.food_stocks_avaliability.length;
            i++
          ) {
            item.sectors.food.food_stocks_avaliability[i].ta =
              item.admin3.admin3Name_en;
            item.sectors.food.food_stocks_avaliability[i]
              ? this.food_stocks_avaliability_array.push(
                item.sectors.food.food_stocks_avaliability[i]
              )
              : {};
          }

        if (
          item.sectors.nutrition &&
          item.sectors.nutrition.affected_population
        )
          for (
            let i = 0;
            i < item.sectors.nutrition.affected_population.length;
            i++
          ) {
            item.sectors.nutrition.affected_population[i].ta =
              item.admin3.admin3Name_en;
            item.sectors.nutrition.affected_population[i]
              ? this.nutritionAffected_populationArray.push(
                item.sectors.nutrition.affected_population[i]
              )
              : {};
          }

        if (
          item.sectors.agriculture &&
          item.sectors.agriculture.livelihoods_affected
        ) {
          for (
            let i = 0;
            i < item.sectors.agriculture.livelihoods_affected.length;
            i++
          ) {
            item.sectors.agriculture.livelihoods_affected[i].ta =
              item.admin3.admin3Name_en;
            item.sectors.agriculture.livelihoods_affected[i]
              ? this.agricultureLivelihoodArray.push(
                item.sectors.agriculture.livelihoods_affected[i]
              )
              : {};
          }
        }

        if (
          item.sectors.health &&
          item.sectors.health.available_health_facilities
        )
          for (
            let i = 0;
            i < item.sectors.health.available_health_facilities.length;
            i++
          ) {
            item.sectors.health.available_health_facilities[i].ta =
              item.admin3.admin3Name_en;
            item.sectors.health.available_health_facilities[i]
              ? self.available_health_facilities.push(
                item.sectors.health.available_health_facilities[i]
              )
              : {};
          }

        if (item.sectors.education && item.sectors.education.impact_on_schools)
          for (
            let i = 0;
            i < item.sectors.education.impact_on_schools.length;
            i++
          ) {
            try {
              item.sectors.education.impact_on_schools[i]
                ? self.impact_on_schools_Array.push(
                  item.sectors.education.impact_on_schools[i]
                )
                : {};
            } catch (error) {

            }

          }

        try {
          item.sectors.wash ? this.washArray.push(item.sectors.wash) : 0;
        } catch (error) {

        }
        try {
          item.sectors.livelihoods ? this.livelihoodsArray.push(item.sectors.livelihoods) : 0;
        } catch (error) {

        }
        try {
          item.sectors.protection ? this.protectionArray.push(item.sectors.protection) : 0;
        } catch (error) {

        }
        try {
          item.sectors.food ? this.foodArray.push(item.sectors.food) : 0;
        } catch (error) {

        }
        try {
          item.sectors.nutrition ? this.nutritionArray.push(item.sectors.nutrition) : 0;
        } catch (error) {

        }
        try {
          item.sectors.education ? this.educationArray.push(item.sectors.education) : 0;
        } catch (error) {

        }
        try {
          item.sectors.environment ? this.environmentArray.push(item.sectors.environment) : 0;
        } catch (error) {

        }

      });





      var food_stocks_avaliability_arrayResult = [];
      var nutritionAffected_populationArrayResult = [];
      var impact_on_schools_ArrayResult = [];

      this.food_stocks_avaliability_array.reduce(function (res, value) {
        if (!res[value.availability_food_stocks]) {
          res[value.availability_food_stocks] = {
            ta: value.ta,
            category: value.availability_food_stocks,
            female_HH: 0,
            male_HH: 0
          };
          food_stocks_avaliability_arrayResult.push(
            res[value.availability_food_stocks]
          );
        }
        res[value.availability_food_stocks].female_HH += parseInt(
          value.female_HH || 0
        );
        res[value.availability_food_stocks].male_HH += parseInt(
          value.male_HH || 0
        );

        return res;
      }, {});

      this.food_stocks_avaliability_array = food_stocks_avaliability_arrayResult;

      this.nutritionAffected_populationArray.reduce(function (res, value) {
        if (!res[value.name]) {
          res[value.name] = {
            ta: value.ta,
            name: value.name,
            affected_females: 0,
            affected_males: 0
          };
          nutritionAffected_populationArrayResult.push(res[value.name]);
        }
        res[value.name].affected_females += parseInt(
          value.affected_females || 0
        );
        res[value.name].affected_males += parseInt(value.affected_males || 0);
        return res;
      }, {});

      this.nutritionAffected_populationArray = nutritionAffected_populationArrayResult;
      //console.log(this);
      // this.impact_on_schools_Array.reduce(function(res, value) {
      //   if (!res[value.structure_type]) {
      //     res[value.structure_type] = {
      //       school_type: value.school_type ? value.school_type : "",
      //       structure_type: value.structure_type ? value.structure_type : "",
      //       partially_functioning: 0,
      //       completely_damaged: 0,
      //       roofs_affected: 0,
      //       underwater: 0,
      //       females_out_of_school: 0,
      //       males_out_of_school: 0
      //     };
      //     impact_on_schools_ArrayResult.push(res[value.structure_type]);
      //   }
      //   res[value.structure_type].partially_functioning += parseInt(
      //     value.partially_functioning ? value.partially_functioning : 0
      //   );
      //   res[value.structure_type].completely_damaged += parseInt(
      //     value.completely_damaged ? value.completely_damaged : 0
      //   );

      //   res[value.structure_type].roofs_affected += parseInt(
      //     value.roofs_affected ? value.roofs_affected : 0
      //   );
      //   res[value.structure_type].underwater += parseInt(
      //     value.underwater ? value.underwater : 0
      //   );

      //   res[value.structure_type].females_out_of_school += parseInt(
      //     value.females_out_of_school ? value.females_out_of_school : 0
      //   );
      //   res[value.structure_type].males_out_of_school += parseInt(
      //     value.males_out_of_school ? value.males_out_of_school : 0
      //   );
      //   return res;
      // }, {});

      //this.impact_on_schools_Array = impact_on_schools_ArrayResult;

      //console.log(this.impact_on_schools_Array);

      this.villagesArray = this.villagesArray
        .join()
        .split(",")
        .filter(function (item, pos, self) {
          return self.indexOf(item) == pos;
        });

      this.sumArrays(
        this.draFormsData,
        "shelter",
        "people_without_shelter",
        "without_shelter_males",
        "without_shelter_females"
      );

      this.sumArraysShelter(
        this.draFormsData,
        "shelter",
        "PeopleMissingrows",
        "males_missing",
        "females_missing"
      );
      this.sumArrays(
        this.draFormsData,
        "shelter",
        "PeopleAffectedrows",
        "damaged_mhh",
        "damaged_fhh"
      );

      this.sumArrays(
        this.draFormsData,
        "displaced",
        "PeopleAffectedrowsD",
        "number_displaced_by_gender_mhh",
        "number_displaced_by_gender_fhh"
      );

      this.sumArrays(
        this.draFormsData,
        "shelter",
        "PeopleInjuredrows",
        "males_injured",
        "females_injured"
      );

      this.sumArrays(
        this.draFormsData,
        "shelter",
        "PeopleDeadrows",
        "males_dead",
        "females_dead"
      );

      this.sumArrays(
        this.draFormsData,
        "agriculture",
        "crops_damaged",
        "hectares_submerged",
        "hectares_washed_away"
      );

      this.sumArrays(
        this.draFormsData,
        "agriculture",
        "livelihoods_affected",
        "livelihood_type"
      );

      this.sumArrays(
        this.draFormsData,
        "agriculture",
        "impact_on_crops",
        "hh_affected",
        "hectares_damaged"
      );

      this.sumArrays(
        this.draFormsData,
        "food",
        "food_stocks_avaliability",
        "male_HH",
        "female_HH"
      );

      this.sumEducationArrays(
        this.draFormsData,
        "education",
        "impact_on_schools",
        "females_out_of_school",
        "males_out_of_school"
      );

      /*   this.sumEducationArrays(
          this.draFormsData,
          "education",
          "impact_on_schools",
          "roofs_affected",
          "partially_functioning",
          "underwater",
          "completely_damaged",
          "females_out_of_school",
          "males_out_of_school"
        );
   */
      // this.sumHealthArrays(
      //   this.draFormsData,
      //   "health",
      //   "available_health_facilities",
      //   "partially_functioning",
      //   "verge_of_closing",
      //   "closed"
      // );

      // this.sumHealthArrays(
      //   this.draFormsData,
      //   "health",
      //   "other_health_facilities",
      //   "partially_functioning",
      //   "verge_of_closing",
      //   "closed"
      // );

      // this.sumArrays(
      //   this.draFormsData,
      //   "livelihoods",
      //   "livelihoods_affected",
      //   "severely_affected",
      //   "slightly_affected"
      // );

      this.sumArrays(
        this.draFormsData,
        "nutrition",
        "affected_population",
        "affected_males",
        "affected_females"
      );

      this.sumArrays(
        this.draFormsData,
        "protection",
        "impact_on_vulnerable_persons",
        "impacted_males",
        "impacted_females"
      );
    });
  }
};
</script>

<style scoped>
.modal-body .tags-input__wrapper {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.el-tag.el-tag--primary {
  position: relative !important;
  display: inline;
}

.image-previewer,
.row {
  display: flex;
  flex-wrap: wrap;
  padding: 10px;
  justify-items: baseline;
}

.image-previewer .img,
.row .img {
  height: 300px;
  border-radius: 10px;
  cursor: pointer;
}

.image-previewer .image--wrapper {
  position: relative;
  margin: 10px;
}

.row .image--wrapper {
  position: relative;
  margin: 10px;
}

.image-previewer .row .image--wrapper {
  margin: 10px;
}

.image--wrapper .img--overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  z-index: 10;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

.image--wrapper .img--overlay .btn {
  background: rgb(238, 5, 5);
  width: 100%;
  position: absolute;
  bottom: 0;
}

.progress,
.progress-bar {
  height: 30px;
  font-weight: bold;
}

.inner {
  overflow: hidden;
}

.inner img {
  transition: all 1.5s ease;
}

.inner:hover img {
  transform: scale(2);
  display: flex;
  flex-wrap: wrap;
}

table {
  border-collapse: collapse;
  width: 100%;
  margin: 0 auto;
}

table,
th,
td {
  border: 1px solid black;
  margin-top: 1%;
}

td {
  padding: 1%;
}

.rotated {
  writing-mode: tb-rl;
  transform: rotate(-180deg);
  color: teal;
  padding-left: 30px;
}

.noborder {
  border: none;
}

.vertical {
  writing-mode: vertical-rl;
}

.qcont:first-letter {
  text-transform: capitalize;
}

.right-align {
  text-align: right;
}

@media print {
  .section-not-to-print {
    visibility: hidden;
  }

  #section-to-print {
    visibility: visible;
  }
}
</style>
