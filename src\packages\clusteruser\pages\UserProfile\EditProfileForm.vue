<template>
  <div>
    <div class="vld-parent">
      <loading
        :active.sync="isLoading"
        :can-cancel="false"
        :is-full-page="fullPage"
      ></loading>
    </div>

    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Edit</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/manager/dashboard">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">Accounts</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">
                Profile
              </li>
            </ol>
          </nav>
        </div>
        <div class="col-lg-6 col-5 text-right"></div>
      </div>
    </base-header>
    <div class="container-fluid mt--6 mr-2">
      <div class="row justify-content-center">
        <div class="col-lg-8 card-wrapper">
          <!-- Grid system -->
          <card class="card">
            <!-- Card header -->
            <div slot="header" class="row align-items-center">
              <div class="col-8">
                <h3 class="mb-0">Profile</h3>
              </div>
              <div class="col-4 text-right">
                 <base-button type="primary" @click="editprof" class="btn btn-sm btn-primary">Edit profile</base-button>
              </div>
            </div>
            <!-- Card body -->
            <div class="row">
              <div class="col-lg-12">
                <p class="mb-0">Personal details</p>
              </div>
            </div>
            <hr />
            <form class="needs-validation" @submit.prevent="validate">
              <div class="form-row">
                <div class="col-md-12">
                  <base-input
                    label="First name"
                    name="First name"
                    disabled="true"
                    placeholder="First name"
                    :error="getError('First name')"
                    :valid="isValid('First name')"
                    v-validate="'required'"
                    v-model="model.firstName"
                  ></base-input>
                </div>
              </div>
              <div class="form-row">
                <div class="col-md-12">
                  <base-input
                    label="Last name"
                    name="Last name"
                    disabled="true"
                    placeholder="Last name"
                    :error="getError('Last name')"
                    :valid="isValid('Last name')"
                    v-validate="'required'"
                    v-model="model.lastName"
                  ></base-input>
                </div>
              </div>
              <div class="row">
                <div class="col-lg-12">
                  <p class="mb-0">Contact details</p>
                </div>
              </div>
              <hr />

              <div class="form-row">
                <div class="col-md-12">
                  <base-input
                    label="Email"
                    name="Email"
                    disabled="true"
                    placeholder="Email"
                    :error="getError('Email')"
                    :valid="isValid('Email')"
                    v-validate="'required|email'"
                    v-model="model.email"
                  ></base-input>
                </div>
              </div>
              <div class="form-row">
                <div class="col-md-12">
                  <base-input
                    label="Phone"
                    type="number"
                    disabled="true"
                    name="Phone"
                    placeholder="Phone"
                    :error="getError('Phone')"
                    :valid="isValid('Phone')"
                    v-validate="'required'"
                    v-model="model.phone"
                  ></base-input>
                </div>
              </div>
              <div class="row">
                <div class="col-lg-12">
                  <p class="mb-0">Administrative details</p>
                </div>
              </div>
              <hr />

              <div class="form-row">
                <div class="col-md-12">
                  <base-input label="Role">
                    <select
                      class="form-control"
                      disabled="true"
                      v-model="model.roleName"
                    >
                      <option
                        v-for="item in rolesData"
                        :key="item.name"
                        :value="item.name"
                      >
                        {{ item.name }}
                      </option>
                    </select>
                  </base-input>
                </div>
              </div>
              <div class="form-row" v-show="model.roleName === 'officer'">
                <div class="col-md-12">
                  <base-input label="District Office">
                    <select class="form-control" v-model="model.district">
                      <option
                        v-for="item in admin2sData"
                        :key="item.admin2_pcode"
                        :value="item"
                      >
                        {{ item.admin2_name_en }}
                      </option>
                    </select>
                  </base-input>
                </div>
                <div class="col-md-6">
                  <base-input label="Forms">
                    <el-select
                      v-model="model.forms"
                      multiple
                      filterable
                      placeholder="Forms"
                    >
                      <el-option
                        v-for="option in selectOptions"
                        :key="option.label"
                        :label="option.label"
                        :value="option.value"
                      ></el-option>
                    </el-select>
                  </base-input>
                </div>
              </div>
            </form>
          </card>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { accounts } from "../../api/accounts/accounts";
import { roles } from "../../api/accounts/roles";
import { admin2s } from "../../api/location/admin2s";
import swal from "sweetalert2";
import flatPicker from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import { Select, Option } from "element-ui";
// Import component
import Loading from "vue-loading-overlay";
// Import stylesheet
import "vue-loading-overlay/dist/vue-loading.css";
export default {
  components: {
    Loading,
    flatPicker,
    [Select.name]: Select,
    [Option.name]: Option,
  },
  data() {
    return {
      id: null,
      validated: false,
      model: {
        firstName: "",
        lastName: "",
        email: "",
        roleName: "",
        nrbNumber: "N/A",
        employeeNumber: "N/A",
        statusId: 1,
        phone: "",
        forms: [],
      },
      selectOptions: [
        {
          label: "DINR Forms",
          value: "DINR Forms",
        },
        {
          label: "5WS Forms",
          value: "5WS Forms",
        },
      ],
      rolesData: [],
      admin2sData: [],
      isLoading: false,
      fullPage: true,
    };
  },
  methods: {
    validate() {
      this.$validator.validateAll().then((response) => {
        if (response === true) {
          this.validated = true;
          this.submit();
        } else response === false;
        {
          this.validated = false;
        }
      });
    },
    submit() {
      if (this.validated === true) {
        this.isLoading = true;
        accounts.update(this.model, this.$session.get("jwt")).then(
          (response) => {
            swal({
              title: "Succesfully updated client details",
              type: "success",
              confirmButtonClass: "btn btn-success btn-fill",
              buttonsStyling: false,
            });
            this.isLoading = false;
          },
          (reason) => {
            swal({
              title: "Failed to update client",
              text: "possible error in details (" + reason + ")",
              type: "error",
              confirmButtonClass: "btn btn-success btn-fill",
              buttonsStyling: false,
            });
            this.isLoading = false;
          }
        );
      }
    },
    getError(name) {
      return this.errors.first(name);
    },
    isValid(name) {
      return this.validated && !this.errors.has(name);
    },
    editprof(){
      this.$router.push({ name: "mgEditAccounts", params: { id: this.$session.get("jwtuid")} })
    }
  },
  mounted() {
    this.id = this.$route.params.id;
    accounts.getOne(this.id, this.$session.get("jwt")).then((response) => {
      this.model = response;
      this.status = this.model.status == "1" ? true : false;
    });
    roles.get(this.$session.get("jwt")).then((response) => {
      this.rolesData = response.data;
    });
    admin2s.get().then((response) => {
      this.admin2sData = response.data;
    });
  },
};
</script>
<style>
</style>
