<template>
  <div>
    <div class="row-col">
      <!-- Header -->
      <div style="position: center;">
        <div class="header mt-3">
          <div class="container">
            <div class="header-body text-center">
              <div class="row justify-content-center">
                <div class="col-12 text-center">
                  <p align="center" class="mt-1">
                    <img width="80" src="../../../static/logo.png" />
                  </p>
                </div>
                <div class="col-12">
                  <h1 class="text-lead text-primary display-4">
                    <b>DRMIS : WEB APP</b>
                  </h1>
                  <p class="text-lead text-primary">
                    (Disaster Risk Management Information System)
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="container-fluid bg-image-container">
          <div class="row justify-content-center align-items-center">
            <div class="col-lg-10 col-md-12 col-sm-12 col-12">
              <div class="card bg-secondary border-0 mb-0 my-0 py-0">
                <div class="card-body px-3 pb-1">
                  <form role="form" @submit.prevent="requestPasswordReset" class="my-0 py-1">
                    <div class="text-center text-muted mb-4 my-0 py-0">
                      <small><b class="text-primary">Enter your email to reset password</b></small>
                    </div>
                    <base-input
                      alternative
                      class="round"
                      type="text"
                      prepend-icon="ni ni-email-83"
                      placeholder="Email"
                      v-model="email"
                    ></base-input>

                    <div
                      class="round"
                      alternative
                    >
                      <base-button type="primary" native-type="submit" class="button">Send Reset Link</base-button>
                    </div>

                    <div
                      class="round"
                      alternative
                    >
                      <p class="text-left text-muted mb-0">
                        <a @click.prevent="$router.push({ name: 'Login' })" class="text-left text-primary mb-0">
                          Back to Sign In
                        </a>
                      </p>
                    </div>
                  </form>

                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import swal from 'sweetalert2';
import axios from 'axios';

export default {
  data() {
    return {
      email: '',
      message: '',
      error: '',
    };
  },
  methods: {
    async requestPasswordReset() {
  try {
    const response = await axios.post(
      `${process.env.VUE_APP_AUTH_URL}/api/Accounts/request-password-reset`,
      {
        email: this.email,
      }
    );

    if (response.status === 200 && response.data) {
      this.message = response.data.message || 'Reset link has been sent.';
      this.error = '';
      swal.fire({
        text: this.message,
        icon: 'success',
      }).then(() => {

        this.$router.push('/reset-password');
      });
    } else {
      throw new Error('Unexpected response format.');
    }
  } catch (err) {
    // Handle errors
    console.error('Error occurred:', err); // Debugging line
    this.error =
      err &&
      err.response &&
      err.response.data &&
      err.response.data.error &&
      err.response.data.error.message
        ? err.response.data.error.message
        : 'An error occurred.';
    this.message = '';
    swal.fire({
      text: this.error || 'Failed to send reset link. Please try again.',
      icon: 'error',
    });
  }
}

}}
</script>

<style scoped>
.boxhead a {
  text-decoration: none;
}
.card-header {
  padding-left: 0px;
}
.modal-dialog {
  border: 1px solid red;
}
form {
  padding: 15px;
  background-color: #ffff;
  border-radius: 15px;
}
.button {
  width: 100%; /* Make the button take up the full width */
  background-color: #2ec3a3;
  border: none;
  color: #fff;
  padding: 10px;
  margin-top: 10px; /* Add some top margin for spacing */
  border-radius: 5px;
  cursor: pointer;
}
.round {
  border-radius: 15px;
  background: #fff;
  padding-right: 0px;
  padding-left: 9px;
}

.rounded-left {
  border-radius: 30px 0px 0px 30px !important;
}

ul {
  list-style-type: none;
}

ul > li {
  line-height: 200%;
  font-weight: bold;
}

.b-popover {
  top: 12px !important;
  border-radius: 30px 0px 0px 30px !important;
}

.arrow {
  display: none !important;
}
.links-container {
  display: flex; /* Use flexbox layout */
  justify-content: space-between; /* Align items with space between them */
}
</style>
