import axios from "axios";

const resource = process.env.VUE_APP_RELAY_SERVICE_URL + "/form";
const ping = process.env.VUE_APP_RELAY_SERVICE_URL + "/";

export class Relay {
  static post(data) {
    return axios.post(resource + "/", {
      data
    }).then(response => response.data);
  }
  // static sendMail(data) {
  //   return axios
  //     .post(resource + "/sendmail", data )
  //     .then(response => response.data);
  // }
  static async sendMail(data) {
    const response = await axios.post(resource + "/sendmail", data).then((response) => response.data).catch((e) => console.log("error sending to relay", e))

    return response.data
  }
  static async ping() {
    const response = await axios.get(ping).catch((e) => e)
    return response.data
  }
}
