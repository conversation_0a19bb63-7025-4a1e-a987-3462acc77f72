import axios from 'axios';

const resource = process.env.VUE_APP_MYSQL_URL


export class Reports {
    static getReports() {
        return axios.get(resource + "/data/").then(response => { return response });
    }
    static getOneReport(id) {
        return axios.get(resource + "/data/" + id).then(response => { return response.data });
    }
	
	static getDinrReport(dinrId) {
        return axios.get(resource + "/dinr/" + dinrId).then(response => { return response });
    }

    static getDraDetailedReport(draId) {
        return axios.get(resource + "/dra/" + draId).then(response => { return response });
    }

    static getDraDetailedReports(dinrId) {
        return axios.get(resource + "/dras/" + dinrId).then(response => { return response.data });
    }

    static getAggregatedReports(filter = { "endYear": new Date().getFullYear(), "startYear": new Date().getFullYear(), "Disaster": "Floods" }) {
       
        if (!(Object.keys(filter).length === 0 && filter.constructor === Object)) {
            if (filter.district != "" | filter.startYear != "" | filter.endYear != "") {
                if (!(filter.Disaster && filter.Disaster != "")) {
                    filter.Disaster = "Floods"
                }

                if (!(filter.startYear && filter.startYear != "")) {
                    filter.startYear = new Date().getFullYear()
                }

                if (filter.endYear && filter.endYear != "") {
                    filter.endYear = new Date().getFullYear()
                }

            }
        } else {
            filter = { "endYear": new Date().getFullYear(), "startYear": new Date().getFullYear(), "Disaster": "Floods" }
        }
        return axios.post(resource + "/AggregatedDras/", filter).then(response => { return response.data });
    }

}
