<template>
  <div>
    <div class="vld-parent">
      <loading :active.sync="isLoading" :can-cancel="false" :is-full-page="fullPage"></loading>
    </div>

    <base-header class="pb-6" type>
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 d-inline-block mb-0">Edit</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links">
              <li class="breadcrumb-item">
                <router-link to="/districtmanager/dashboard">
                  <i class="fas fa-home"></i>
                </router-link>
              </li>
              <li class="breadcrumb-item">
                <a href="#">Accounts</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">Edit</li>
            </ol>
          </nav>
        </div>
        <div class="col-lg-6 col-5 text-right"></div>
      </div>
    </base-header>
    <div class="container-fluid mt--6">
      <div class="row justify-content-center">
        <div class="col-lg-8 card-wrapper">
          <!-- Grid system -->
          <card class="card">
            <!-- Card header -->
            <h3 slot="header" class="mb-0">Change Password</h3>

            <!-- Card body -->
            <form class="needs-validation" @submit.prevent="validate">
              <!-- Change Password starts --> 
               <div class="col-md-12">
                  <base-input
                    type="password"
                    label="Current Password"
                    name="Current-Password"
                    placeholder="Current Password"
                    v-validate="'required|min:6'"
                    v-model="currentpassword"
                    :error="getError('Current-Password')"
                    :valid="isValid('Current-Password')"
                  ></base-input>
                </div>
                <div class="col-md-12">
                  <base-input
                    type="password"
                    label="New Password"
                    name="Password"
                    placeholder="New Password"
                    v-validate="'required|min:6'"
                    v-model="password"
                    :error="getError('Password')"
                    :valid="isValid('Password')"
                  ></base-input>
                </div>


                <div class="col-md-12">
                  <base-input
                    label="Confirm Password"
                    type="password"
                    name="confirm-password"
                    placeholder="Confirm Password"
                    v-validate="'required|min:6|confirmed:password'"
                    v-model="confirmpassword"
                    data-vv-as="password"
                    :error="getError('Confirm-Password')"
                    :valid="isValid('Confirm-Password')"
                  ></base-input>
                </div>
              <!-- Change Password end-->
              <hr />
              <base-button type="primary" native-type="submit">Update</base-button>
            </form>
          </card>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { accounts } from "../../api/accounts/accounts";
import { auth } from "@/api/auth";
import { roles } from "../../api/accounts/roles";
import { admin2s } from "../../api/location/admin2s";
import swal from "sweetalert2";
import flatPicker from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import { Select, Option } from "element-ui";
// Import component
import Loading from "vue-loading-overlay";
// Import stylesheet
import "vue-loading-overlay/dist/vue-loading.css";
export default {
  components: {
    Loading,
    flatPicker,
    [Select.name]: Select,
    [Option.name]: Option
  },
  data() {
    return {
      id: null,
      validated: false,
      currentpassword: "",
      password: "",
      confirmpassword: "",
      usersArr:null,
      model: { 
      },
      selectOptions: [
        {
          label: "DINR Forms",
          value: "DINR Forms"
        },
        {
          label: "5WS Forms",
          value: "5WS Forms"
        }
      ],
      rolesData: [],
      admin2sData: [],
      isLoading: false,
      fullPage: true
    };
  },
  methods: {
    handleSignOut() {
      this.$session.destroy();
      this.$router.push({ name: "Login", params: { session: "lost" } });
    },
   async validate() {
     let userdata = {
       password: this.currentpassword,
       email: this.model.email
     }
     if(this.currentpassword !== ""){
      var response = auth.login(userdata)
            response.then(
                response => {
                   if(this.password !== this.confirmpassword){
                      swal({
                            text: "Password Mismatch",
                            type: "error",
                            confirmButtonClass: "btn btn-success btn-fill",
                            buttonsStyling: false
                          });
                      }else{
                          this.submit();
                          this.handleSignOut();
                      }
                  },
                reason => {
                   swal({
                      text: "Wrong Password",
                      type: "error",
                      confirmButtonClass: "btn btn-success btn-fill",
                      buttonsStyling: false
                    });
                }
        );
     }
    },
    async submit() {
        this.isLoading = false;
        this.model['password'] = this.password
        accounts.update(this.model, this.$session.get("jwt")).then(
          swal({
              title: "Password updated succesfully, Please Login",
              type: "success",
              confirmButtonClass: "btn btn-success btn-fill",
              buttonsStyling: false
            }) 
        )
    },
    getError(name) {
      return this.errors.first(name);
    },
    isValid(name) {
      return this.validated && !this.errors.has(name);
    }
  },
  mounted() {
    this.id = this.$route.params.id;
    accounts.getOne(this.id, this.$session.get("jwt")).then(response => {
      this.model = response;
      this.oldemail = this.model.email
      this.status = this.model.status == "1" ? true : false;
    });
    roles.get(this.$session.get("jwt")).then(response => {
      this.rolesData = response.data;
    });
    admin2s.get().then(response => {
      this.admin2sData = response.data;
    });
  }
};
</script>
<style>
</style>
