<template>
  <div class="container mt-4">
    <div class="d-flex justify-content-end">
      <!-- <button @click="exportToPDF" class="btn btn-primary mt-3">Export to PDF</button> -->

      <b-button v-if="reportDetails[0] && reportDetails[0].images && reportDetails[0].images.length > 0"
        @click="exportToPDFWithImages" class="edit bg-primary text-white"
        type="primary" size="sm" icon>
<i class="text-black ni ni-cloud-download-95"></i> Export to PDF
</b-button>

<!-- Button for reports without images -->
<b-button v-else
        @click="exportToPDFWithoutImages" class="edit bg-primary text-white"
        type="primary" size="sm" icon>
<i class="text-black ni ni-cloud-download-95"></i> Export to PDF
</b-button>
    </div>
    <h2 class="mb-4">Detailed Preliminary Reports</h2>

    <b-alert v-if="!loading && reportDetails.length === 0" variant="warning" show>
      No detailed information found for the selected report.
    </b-alert>

    <div v-else-if="loading">
      <div class="text-center">
        <b-spinner small></b-spinner>
        <p>Loading report details...</p>
      </div>
    </div>
    <div v-else  ref="pdfContent">

      <!-- Combined GVH Details and Impact Information -->
      <div class="p-4 shadow-sm" style="background-color: white; border-radius: 5px;">
        <div class="col-12 d-flex flex-column justify-content-center align-items-center text-center">
          <!-- Centered Image -->
          <img src="../../../../static/logo.png" height="90" />

          <!-- Centered Text -->
          <dl class="headings">
            <dd class="text-center" style="font-size:22px; color: #32325d; margin-top: 10px;">
              <p>
                <b><strong>Government of Malawi</strong></b>
                <br />
                <span>
                  <b>Department of Disaster Management Affairs (DoDMA)</b> <br>
                  <b>Preliminary Disaster Report</b>
                </span>
              </p>
            </dd>
          </dl>
        </div>

        <!-- Header with TA name, date, and disaster type -->
        <div class="d-flex justify-content-between align-items-center p-3">
          <h4 class="mb-0">

            <strong>District : {{ reportDetails[0].user.district.admin2_name_en }} <br> Affected  TA : {{ reportDetails[0]._id.ta }} <br> Date  of  Disaster :  {{reportDetails[0].disasterdate |  formatDates}} <br> Date  reported :  {{ reportDetails[0].createdon | formatDate }}</strong>
          </h4>
          <span class="badge badge-primary">{{ reportDetails[0]._id.disaster }}</span>
        </div>

        <!-- Camps Section immediately below TA name, with reduced spacing -->
        <div style="color: black; padding-left: 1rem;">
          <p class="mb-0"><strong>Camps:</strong> {{ reportDetails[0].camps  || 'No Camps'}}</p>
        </div>

        <hr>
        <!-- Camps Section immediately below the TA name, without d-flex to ensure alignment -->




        <div v-for="(report, index) in reportDetails" :key="index" class="report-section">
          <!-- Location & GVH Details -->
          <div class="location-details text-center mb-3">
            <p><strong><b>GVH: {{ report.gvh }}</b></strong></p>
          </div>

          <!-- Impact Details in Rows -->
          <div class="impact-details row">
            <!-- Impact on Households -->
            <div class="col-md-4 mb-3">
              <h6 class="text-muted">Impact on Households</h6>
              <p><strong>MHH:</strong> {{ report.mhh || 0 }}</p>
              <p><strong>FHH:</strong> {{ report.fhh || 0 }}</p>
            </div>

            <!-- People Missing, Dead, Injured -->
            <div class="col-md-4 mb-3">
              <h6 class="text-muted">People Missing, Dead, Injured</h6>
              <p><strong>Injured:</strong> {{ report.injured || 0 }}</p>
              <p><strong>Dead:</strong> {{ report.dead || 0 }}</p>
              <p><strong>Missing:</strong> {{ report.missing || 0 }}</p>
              <p><strong>Displaced:</strong>{{report.displaced || 0}} </p>
            </div>

            <!-- Impact on Women & Children -->
            <div class="col-md-4 mb-3">
              <h6 class="text-muted">Impact on Women & Children</h6>
              <p><strong>Pregnant:</strong> {{ report.pregnant || 0 }}</p>
              <p><strong>Lactating:</strong> {{ report.lactactingwomen || 0 }}</p>
              <p><strong>Under Five:</strong> {{ report.underfive || 0 }}</p>
              <p><strong>Disabled:</strong> {{ report.disabled || 0 }}</p>
            </div>
          </div>

          <hr class="my-4" v-if="index < reportDetails.length - 1" style="border-top: 1px dotted #ddd;" />

          <!-- Dotted Line Separator for Each GVH Section -->

        </div>
        <hr>
        <div class="d-flex flex-wrap align-items-center" style="color: black;">
          <p class="m-0 w-100"><strong>Urgent Needs:</strong> {{ reportDetails[0].comments }}</p>
          <p class="m-0 w-100"><strong>Reported By:</strong> {{ reportDetails[0].user.firstName }} {{ reportDetails[0].user.lastName }} - {{ reportDetails[0].user.organisation }}</p>

          <div v-if="reportDetails[0].images && reportDetails[0].images.length" class="image-gallery">
            <img
              v-for="(image, index) in reportDetails[0].images"
              :key="index"
              :src="`${baseUrl}/preliminary/images/${image}`"
              alt="Report Image"
              class="img-fluid"
            />
          </div>


      </div>


    </div>
  </div>
</div>
</template>

<script>
import { BAlert } from 'bootstrap-vue';
import { prelimreports } from '../../districtmanager/api/forms/prelimreports.js';
import moment from 'moment';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

export default {
  name: 'DetailPrelim',
  props: {
    id: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      baseUrl: process.env.VUE_APP_ENGINE_URL,
      reportDetails: [],
      loading: true // Initialize loading state
    };
  },
  created() {
    this.fetchReportDetails();
  },
  methods: {
    exportToPDFWithImages: function () {

      console.log("Im clickeddd heeereeee")
  var element = this.$refs.pdfContent;
  var _this = this;
  var baseUrl = process.env.VUE_APP_ENGINE_URL ;

  if (
    _this.reportDetails &&
    _this.reportDetails[0] &&
    _this.reportDetails[0].images &&
    _this.reportDetails[0].images.length
  ) {
    var imageUrls = _this.reportDetails[0].images.map(function (image) {
      return baseUrl + "/preliminary/images/" + image
    });

    _this
      .preloadImages(imageUrls)
      .then(function () {
        html2canvas(element, {
          scale: 2, // Higher scale for better quality
          useCORS: true, // Enable CORS to handle external images
          allowTaint: false, // Prevent tainted canvas
        })
          .then(function (canvas) {
            var imgData = canvas.toDataURL("image/png");
            var pdf = new jsPDF("p", "mm", "a4"); // PDF in A4 size
            var pdfWidth = pdf.internal.pageSize.getWidth();
            var pdfHeight = (canvas.height * pdfWidth) / canvas.width;

            // Add the captured image to the PDF
            pdf.addImage(imgData, "PNG", 0, 0, pdfWidth, pdfHeight);

            // Add urgent needs and reporter details on a new page if needed


            // Save the PDF file
            pdf.save(
              "Preliminary_Report_" +
                _this.reportDetails[0]._id.ta +
                ".pdf"
            );
          })
          .catch(function (error) {
            console.error("Error exporting to PDF:", error);
            alert("Failed to export to PDF. Please try again.");
          });
      })
      .catch(function (error) {
        console.error("Error preloading images:", error);
        alert("Failed to preload images. Please check image URLs.");
      });
  }
},

exportToPDFWithoutImages() {
  this.loading = true;
  const element = this.$refs.pdfContent; // Reference to the element containing the report

  html2canvas(element, {
    scale: 2, // Increase scale for better resolution
    useCORS: true, // Handle external images if they exist, not crucial here but good to keep if there's any external resource
  }).then(canvas => {
    const imgData = canvas.toDataURL("image/png"); // Convert canvas to a data URL
    const pdf = new jsPDF("p", "mm", "a4"); // Initialize a new PDF document
    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = (canvas.height * pdfWidth) / canvas.width;

    // Add the image to the PDF, resizing it to fit the page
    pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight);

    // Save the PDF with a dynamic filename based on the report details
    pdf.save(`Preliminary_Report_${this.reportDetails[0]._id.ta}.pdf`);

    this.loading = false; // Set loading false to indicate the process is complete
  }).catch(error => {
    console.error("Error exporting to PDF:", error);
    alert("Failed to export to PDF. Please try again."); // Alert the user in case of an error
    this.loading = false; // Ensure loading is set to false on error as well
  });
},




async preloadImages(images) {
  const promises = images.map((src) => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = resolve;
      img.onerror = reject;
      img.src = src;
    });
  });
  return Promise.all(promises);
},

    async fetchReportDetails() {
      this.loading = true; // Set loading to true at the start of the fetch
      try {
        const response = await prelimreports.getReportsLatest();
        const reports = response.filter(item => item.idval === this.id);

        if (reports.length > 0) {
          this.reportDetails = reports;
        } else {
          this.reportDetails = [];
        }
      } catch (error) {
        console.error("Failed to fetch report details:", error);
        this.reportDetails = [];
      } finally {
        this.loading = false; // Set loading to false after fetching
      }
    }
  },
  filters: {
    formatDate(value) {
      return moment(value).format('Do MMMM YYYY hh:mm A');
    },
    formatDates(value){

      return moment (value).format('Do MMMM YYYY')
    }
  }
}
</script>

<style scoped>
.container {
  margin-top: 20px;
}
.badge {
  font-size: 0.9rem;
  padding: 0.4rem 0.6rem;
}
.header-section {
  font-size: 1.2rem;
}
.report-section {
  border-radius: 5px;
  padding: 20px;
}
.text-muted {
  font-size: 0.9rem;
  font-weight: 600;
}
.image-gallery {
  display: flex; /* Align items in a row */
  flex-wrap: wrap; /* Wrap to the next line if needed */
  gap: 20px; /* Space between images */
  justify-content: center; /* Center align the gallery */
  padding: 20px;
}

.image-gallery img {
  max-width: 300px; /* Limit the width of the images */
  height: auto; /* Maintain the natural aspect ratio */
  border-radius: 8px; /* Optional: Slight rounding for a modern look */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Optional: Add a subtle shadow */
}
</style>
