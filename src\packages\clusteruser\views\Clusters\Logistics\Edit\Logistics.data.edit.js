import { Table, TableColumn, Select, Option } from 'element-ui'
import RouteBreadCrumb from '@/components/Breadcrumb/RouteBreadcrumb'
import { BasePagination } from '@/components'
import clientPaginationMixin from '@/components/PaginatedTables/clientPaginationMixin'
import dateformat from 'dateformat'
import { mapGetters, mapActions } from 'vuex'
import { clusterreports } from '../../../../../../api/cluster/index.js'

import swal from 'sweetalert2'
export default {
  mixins: [clientPaginationMixin],
  components: {
    BasePagination,
    RouteBreadCrumb,
    [Select.name]: Select,
    [Option.name]: Option,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn
  },
  data () {
    return {
      report: [],

      updatedData: [],
      structrures: [
        'Road',
        'Bridge',
        'Warehouse/depot facility',
        'Culvet',
        'Dyke',
        'Irrigation infrastructure',
        'Railway',
        'Port/harbour',
        'Other'
      ],

      vehicle_size: [
        'None/Not passable',
        'Boat',
        'Non-motorized',
        'By foot',
        'Motorbike',
        '4WD vehicle',
        "Small truck (like lorry yaying'ono 10 mt)",
        'No limitations, all trucks'
      ],

      condition_of: [
        'Normal/Operational',
        'Slightly damaged',
        'remains operational',
        'Significantly damaged',
        'not operational',
        'Under repair',
        'Do not know'
      ],

      surface_condition: [
        'Slightly flooded (passable)',
        'Significantly flooded (not passable)',
        'Muddy',
        'Dry, normal'
      ],

      infrastructure: [
        'Telephone line',
        'VHF/HF radio',
        'Cellphone',
        'National radio station',
        'Community radio station',
        'Television'
      ],

      function_of: ['Functioning', 'Partially functioning', 'Not functioning']
    }
  },
  methods: {
    ...mapActions('clusterreports', {
      getclusterreportsAction: 'get'
    }),

    formatedDate (data) {
      let finalDate = dateformat(data, 'dd-mm-yyyy hh:mm:ss')
      return finalDate
    },

    updateData (dataObject, editedData) {
      let editedDataObject = Object.assign({}, editedData[0])
      if (dataObject.editcounter >= 5) {
        swal({
          text: 'Form has reached edit limit',
          type: 'error',
          toast: true,
          timer: 4000,
          position: 'top-end',
          confirmButtonClass: 'btn btn-success btn-fill',
          buttonsStyling: false
        })
      } else {
        let dateOfupdate = new Date().toLocaleString()
        let editedby = this.$session.get('jwtuser')
        dataObject.editcounter = dataObject.editcounter + 1
        this.updatedData.push({
          editedby: editedby,
          editedon: dateOfupdate,
          updateddata: editedDataObject
        })

        dataObject.editedhistory = [...this.updatedData]
        this.confirmSubmission(dataObject)
      }
    },

    confirmSubmission (data) {
      clusterreports.update(data).then(
        response => {
          swal({
            title: 'Succesfully updated report details',
            type: 'success',
            confirmButtonClass: 'btn btn-success btn-fill',
            buttonsStyling: false
          })
        },
        reason => {
          swal({
            title: 'Failed to update client',
            text: 'possible error in details (' + reason + ')',
            type: 'error',
            confirmButtonClass: 'btn btn-success btn-fill',
            buttonsStyling: false
          })
        }
      )
    }
  },

  async mounted () {
    let clusterdata = [...(await this.getclusterreportsAction())]
    this.report = clusterdata.filter(
      report => report._id == this.$route.params.id
    )
  }
}
