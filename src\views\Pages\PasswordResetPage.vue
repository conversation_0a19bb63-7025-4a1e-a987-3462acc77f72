<template>
  <div style="height:100vh;text-align:center;padding-top:20%;">
    <div v-if="first">
      <h2>Your password has been reset</h2>
      <p>
        Your new password has been sent to your email.
      </p>
    </div>
     <div v-else>
      <h2>Password reset failed.</h2>
      <p>
        Press the button to manually reset.
      </p>
    </div>
  </div>
</template>
<script>
import { generatePassword } from "../../api/pass";
import userPass from "../../api/pass";
import { Relay } from "../../packages/districtmanager/api/relay";
import { accounts } from "../../packages/admin/api/accounts/accounts";
import { auth } from "@/api/auth";
export default {
  data() {
    return {
        first: true,
        usersArr: null
    };
  },
  methods: {
    async resetPassword(email) {
      let pass = await generatePassword();
      //console.log(pass, "ttttttttttttttttttttttttttttt");
       let token = null;
      let user = userPass.userPass
      await auth.login(user).then(response => {
        token = response.id;
      });
      await accounts.get(token).then(response => {
        this.usersArr = response.data.filter(u => u.email === email);
        //console.log(this.usersArr, "mmmmmmmmmmmmmmmmmmmmmmm");

      });
      if (this.usersArr[0]) {
            var changeUser = this.usersArr[0]
            changeUser.password = pass
           changeUser.isNewPassword = true
            //console.log("THE user",)
       await     accounts.update(changeUser,token).then((response) => {
         
                Relay.sendMail({
                    email: email,
                    password: pass,
                    status: "password"
                })
            })


        }
    }
  },
  mounted() {
     //alert(JSON.stringify(this.$route.query.email))
    this.resetPassword(this.$route.query.email);
  }
};
</script>
