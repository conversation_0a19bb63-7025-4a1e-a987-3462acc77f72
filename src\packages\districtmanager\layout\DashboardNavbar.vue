<template>
  <base-nav
    container-classes="container-fluid"
    class="navbar-top border-bottom navbar-expand"
    :class="{ 'bg-success navbar-dark': type === 'default' }"
  >
    <div class="div" v-show="routeName=='DistrictManagerDashboard'">
      <h1  class="row mx-0 text-uppercase" id="header-title">
        {{this.$session.get("user").admin2_name_en}} Disasters reporting dashboard
        <span class="ml-3"></span>(
        <span id="start-year"></span> -
        <span id="end-year"></span>)
      </h1>
      <span
        style="color:#bf6c00;
               font-weigh: bold;
               font-size:large;
               display: inline-block;
"
        class="row mx-0"
      >
        <i>
          This is Overview Dashboard which reports disasters in {{this.$session.get("user").admin2_name_en}} since December 2019
        </i>
      </span>
    </div>
    <!-- Navbar links -->
    <ul class="navbar-nav align-items-center ml-md-auto">
      <li class="nav-item d-xl-none">
        <!-- Sidenav toggler -->
        <div
          class="pr-3 sidenav-toggler"
          :class="{
            active: $sidebar.showSidebar,
            'sidenav-toggler-dark\\': type === 'default',
            'sidenav-toggler-light': type === 'light',
          }"
          @click="toggleSidebar"
        >
          <div class="sidenav-toggler-inner text-primary">
            <i class="sidenav-toggler-line text-primary"></i>
            <i class="sidenav-toggler-line text-primary"></i>
            <i class="sidenav-toggler-line text-primary"></i>
          </div>
        </div>
      </li>
    </ul>
    <base-dropdown has-toggle="false" menu-on-right>
      <base-button
        slot="title-container"
        type="secondary"
        round
        outline
        class="mx-3 p-0"
      >
        <div>
          <span class="avatar avatar-sm rounded-circle">
            <i class="fa fa-bell"></i>
          </span>
          <span
            class="
              badge
              m-0
              p-0
              badge-md badge-circle badge-floating badge-danger
              border-white
            "
            style="background-color:#ff8c00;"
            ><span style="color:white;">{{getBell.length}}</span></span
          >
        </div>
      </base-button>
      <div
        class="list-group"
        :style="{
          width: '310px',
        }"
      >
        <span class="dropdown-item dropdown-header"
          >{{ getBell.length }} Unsigned Report(s)</span
        >
        <div class="dropdown-divider"></div>
        <a
          v-for="(bell, index) in getBell.slice(0, 5)"
          :key="index"
          class="list-group-item list-group-item-action"
          @click="review(bell.id)"
        >
          <i class="ni ni-single-copy-04 mr-2"></i> {{ bell.disaster }}
          <!-- <span class="float-right text-muted text-sm">3 mins</span> -->
        </a>
        <div class="dropdown-divider"></div>
        <a v-if="getBell.length > 5" class="dropdown-item dropdown-footer"
        @click="reviewAll()"
          >See All</a
        >
      </div>
    </base-dropdown>
    <!-- <base-button round outline class="mx-3 p-0" type="secondary"><div>

         <span class="avatar avatar-sm rounded-circle">
            <i class="fa fa-bell"></i>
          </span>
        <span
          class="
            badge m-0 p-0 badge-sm badge-circle badge-floating badge-danger
            border-white
          "
          >4</span
        >

    </div></base-button> -->

    <base-dropdown
      class="navbar-nav ml-auto ml-md-0"
      data-toggle="dropdown"
      title-classes="btn btn-sm btn-neutral mr-0"
      menu-on-right
      :has-toggle="false"
    >
      <template slot="title" style="text-transform: capitalize">
        <div class="media align-items-center">
          <span class="avatar avatar-sm rounded-circle">
            <i class="ni ni-circle-08"></i>
          </span>
          <div class="media-body ml-2 d-none d-lg-block">
            <span
              class="mb-0 text-sm font-weight-bold"
              style="text-transform: capitalize"
              >{{ this.$session.get("jwtuser") }}</span
            >
          </div>
        </div>
      </template>

      <a class="dropdown-item"  @click="handleProfile()">Profile</a>
      <a class="dropdown-item"  @click="changePassword()">Change Password</a>
      <button class="dropdown-item" @click="handleSignOut()">Logout</button>
    </base-dropdown>
  </base-nav>
</template>

<script>
import { CollapseTransition } from "vue2-transitions";
import { BaseNav, Modal } from "@/components";
import { mapGetters, mapActions } from "vuex";
import { MongoReports } from "../api/MongoReports";
import { user } from "@/api/user";

export default {
  components: {
    CollapseTransition,
    BaseNav,
    Modal,
  },
  props: {
    type: {
      type: String,
      default: "light", // default|light
      description:
        "Look of the dashboard navbar. Default (Green) or light (gray)",
    },
  },
  computed: {
    ...mapGetters({
      getBell: "dims/getNewBell",

    }),
    routeName() {
      const { name } = this.$route;
      return this.capitalizeFirstLetter(name);
    },
  },
  data() {
    return {
      activeNotifications: false,
      showMenu: false,
      searchModalVisible: false,
      searchQuery: "",
      notBell: [],
    };
  },
  methods: {
  
    ...mapActions("dims", {
      addBell: "addBell",
    }),
    review(row) {
      this.$router.push({
        path: "/districtmanager/unapproveddetailsreport/" + row
      });
    },
    reviewAll() {
      this.$router.push({
        path: "/districtmanager/UnsignedReports"
      });
    },
    capitalizeFirstLetter(string) {
      return string.charAt(0).toUpperCase() + string.slice(1);
    },
    toggleNotificationDropDown() {
      this.activeNotifications = !this.activeNotifications;
      
    },
    closeDropDown() {
      this.activeNotifications = false;
    },
    toggleSidebar() {
      this.$sidebar.displaySidebar(!this.$sidebar.showSidebar);
     
    },
    hideSidebar() {
      this.$sidebar.displaySidebar(false);
    },
    handleSignOut() {
      this.$session.destroy();
      this.$router.push({ name: "Login", params: { session: "lost" } });
    },

    handleProfile(){

      this.$router.push({ name: "districtmanagerEditProfile", params: { id: this.$session.get("jwtuid")} });
    },
    changePassword(){
      this.$router.push({ name: "districtmanagerChangePassword", params: { id: this.$session.get("jwtuid")} });
    }
  },
  created() {
    
    MongoReports.getUnapprovedDinrs().then((response) => {
     
      let data = response.data.filter((data) =>
        data.district.admin2_name_en.includes(
          this.$session.get("user").admin2_name_en
        )
      ).filter(
          item =>
            !item ||
            ((!item.isApproved && item.isApproved == false) &&
              !item &&
              (!item.isRejected && item.isRejected == false) ||
              (!item.approvalMetadata ||
                !item.approvalMetadata.signature ||
                (!item.isRejected || item.isRejected == false) && item.approvalMetadata.signature.length == 0))
        );


      data.sort(function (a, b) {
        return new Date(b.createdon) - new Date(a.createdon);
      });

      let bell = [];

      for (let i = 0; i < data.length; i++) {
        let row = data[i]._id;

        data[i].notification.forEach((el) => {
          if (el.show == 1) {
            data[i].notStat = el.status;
            data[i].variant = el.variant;
            if (
              el.show == 1 &&
              el.status == "Waiting approval" &&
              el.variant == "info"
            ) {
              bell.push({
                id: row,
                disaster: el.disaster,
              });
             
            }
          }
        });
      }
      this.addBell(bell);
     
    });
  },
};
</script>
