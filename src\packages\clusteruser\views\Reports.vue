<template>
  <div class="content">
    <logistics
      v-if="$session.get('userObj').cluster.includes('Logistics') && $session.get('userObj').cluster_user_type == 'Cluster Lead' || $session.get('userObj').cluster_user_type == 'Roads Authority'">
    </logistics>
    <logisticsDistrict
      v-if="$session.get('userObj').cluster.includes('Logistics') && $session.get('userObj').cluster_user_type == 'Cluster District coordinator'">
    </logisticsDistrict>
  </div>
</template>
<script>

import logistics from "./Clusters/Logistics/Logistics.table.vue";

import logisticsDistrict from "./Clusters/Logistics/District.based.Logistics.table";

export default {

  components: {
    logistics,
    logisticsDistrict
  },
}

</script>
<style>
.no-border-card .card-footer {
  border-top: 0;
}

button[disabled] {
  cursor: not-allowed;
}</style>
