
export default {
  components: {},
  props: ['logistics', 'max'],
  data () {
    return {
      maxValue: this.max,
      impacted_structures: [],
      dataset: {},
      other_impacted_structures: [],
      impacted_telecoms: [],
      other_access_to_structures: [],
      response_needed: '',
      access_of_structures: [{}],
      telecoms: [
        { name: 'Telephone line' },
        { name: 'VHF/HF radio' },
        { name: 'Cellphones' },
        { name: 'Nation radio station' },
        { name: 'Community radio station' },
        { name: 'Television' }
      ],
      radio: {
        radio1: 'radio1',
        radio2: 'radio3'
      },
      structure_types: [
        { name: 'Roads' },
        { name: 'Bridges' },
        { name: 'Culverts' },
        { name: '<PERSON><PERSON>' },
        { name: 'Irrigation Infrastructure' },
        { name: 'Water sources' }
      ]
    }
  },
  computed: {},

  methods: {
    addItemRow (member, array_name, static_data = [], key_value) {
      this.$emit(
        'addItemRow',
        'logistics',
        member,
        array_name,
        static_data,
        key_value
      )
    },
    removeItemRow (member, array_name, static_data = [], index, key_value) {
      this.$emit(
        'removeItemRow',
        'logistics',
        member,
        array_name,
        static_data,
        index,
        key_value
      )
    },
    allowOnlyText () {
      $(function () {
        $('#inputTextBox').keypress(function (event) {
          var inputValue = event.charCode
          if (
            !(inputValue >= 65 && inputValue <= 120) &&
            inputValue != 32 && inputValue != 0
          ) {
            event.preventDefault()
          }
        })
      })
    },
    save () {
      this.logistics.response_needed = this.response_needed
      this.logistics.urgent_response_needed = this.urgent_response_needed
      this.logistics.access_of_structures = this.access_of_structures.filter(
        value => Object.keys(value).length !== 0
      )
      this.logistics.impacted_structures = this.impacted_structures.filter(
        value => Object.keys(value).length !== 0
      )
      this.logistics.impacted_telecoms = this.impacted_telecoms.filter(
        value => Object.keys(value).length !== 0
      )
      this.logistics.other_impacted_structures = this.other_impacted_structures.filter(
        value => Object.keys(value).length !== 0
      )
      this.logistics.other_access_to_structures = this.other_access_to_structures.filter(
        value => Object.keys(value).length !== 0
      )
      this.$emit('save', this.logistics, 'logistics')
    },
    autosave () {
      this.logistics.response_needed = this.response_needed
      this.logistics.urgent_response_needed = this.urgent_response_needed
      this.logistics.access_of_structures = this.access_of_structures.filter(
        value => Object.keys(value).length !== 0
      )
      this.logistics.impacted_structures = this.impacted_structures.filter(
        value => Object.keys(value).length !== 0
      )
      this.logistics.impacted_telecoms = this.impacted_telecoms.filter(
        value => Object.keys(value).length !== 0
      )
      this.logistics.other_impacted_structures = this.other_impacted_structures.filter(
        value => Object.keys(value).length !== 0
      )
      this.logistics.other_access_to_structures = this.other_access_to_structures.filter(
        value => Object.keys(value).length !== 0
      )
      this.$emit('autosave', this.logistics, 'logistics')
    }
  },

  beforeMount () {
    this.logistics = typeof this.logistics !== 'undefined' ? this.logistics : {}

    setInterval(this.autosave, 1200)

    let dataset = JSON.parse(localStorage.getItem('logistics').data)

    this.other_access_to_structures =
      dataset.other_access_to_structures.length == 0
        ? this.other_access_to_structures
        : dataset.other_access_to_structures

    this.impacted_structures =
      typeof dataset.impacted_structures === 'undefined'
        ? this.impacted_structures
        : dataset.impacted_structures

    this.impacted_telecoms =
      typeof dataset.impacted_telecoms === 'undefined'
        ? this.impacted_telecoms
        : dataset.impacted_telecoms

    this.access_of_structures =
      dataset.access_of_structures.length === 0
        ? this.access_of_structures
        : dataset.access_of_structures

    this.other_impacted_structures =
      typeof dataset.other_impacted_structures === 'undefined'
        ? this.other_impacted_structures
        : dataset.other_impacted_structures

    this.response_needed =
      typeof dataset.response_needed === 'undefined'
        ? this.response_needed
        : dataset.response_needed

    this.urgent_response_needed =
      typeof dataset.urgent_response_needed === 'undefined'
        ? this.urgent_response_needed
        : dataset.urgent_response_needed
  }
}
