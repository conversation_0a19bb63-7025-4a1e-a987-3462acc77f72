export const set = (state, val) => {
  state.dras = val
}

export const update = (state, val) => {
  state.dras.splice(state.dras.map(o => o.id).indexOf(val.id), 1, val)
}

export const remove = (state, val) => {
  state.dras = state.dras.filter(x => {
    return x.id != val
  })
}

export const setOne = (state, val) => {
  state.dras.push(val)
}

// roles

export const setroles = (state, val) => {
  state.roles = val
}
