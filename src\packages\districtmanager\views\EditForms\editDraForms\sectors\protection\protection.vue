<template>
  <div>
    <h2 slot="header" class="mb-0">PROTECTION</h2>
    <hr class="mt-3 mb-3" />

    <h2>
      <b class="alert-suc"
        >Extent of Impact of disaster on vulnerable population</b
      >
    </h2>
    <form>
      <div
        class="row"
        v-for="(value, index) in impact_on_vulnerable_persons"
        v-bind:key="index"
      >
        <div class="col-md">
          <base-input
            label="Please choose population type"
            data-toggle="tooltip"
            data-placement="top"
            title="Choose a population type"
          >
            <select
              class="form-control"
              v-model="value.name"
              @change="
                TestPopulationType(
                  value.name,
                  impact_on_vulnerable_persons,
                  'impacted_males'
                )
              "
            >
              <option
                v-for="type in population_type"
                v-bind:value="type.name"
                >{{ type.name }}</option
              >
            </select>
          </base-input>
        </div>
        <div class="col-md">
          <base-input
            label="Males"
            type="number"
            placeholder="# of males"
            v-model.number="value.impacted_males"
            data-toggle="tooltip"
            data-placement="top"
            title="NUmber of impacted males"
            oninput="validity.valid||(value='');"
            min="0"
            :disabled="
              value.name === 'Pregnant women' ||
                value.name === 'Lactating women'
            "
            @input="
              init_totals(
                'impacted_males',
                impact_on_vulnerable_persons,
                'impacted_males'
              )
            "
            v-bind:max="maxValue"
          />
        </div>
        <div class="col-md">
          <base-input
            label="Females"
            v-model.number="value.impacted_females"
            oninput="validity.valid||(value='');"
            data-toggle="tooltip"
            data-placement="top"
            title="Number of impacted females"
            type="number"
            min="0"
            :rules="[v => !!v || 'value is required']"
            @input="
              init_totals(
                'impacted_females',
                impact_on_vulnerable_persons,
                'impacted_females'
              )
            "
            v-bind:max="maxValue"
            placeholder="# of females"
          />
        </div>
        <div class="col-md pt-5">
          <base-button
            size="sm"
            type="warning"
            v-if="impact_on_vulnerable_persons.length > 0"
            class="btn-icon-only rounded-circle noprint"
            data-toggle="tooltip"
            data-placement="top"
            title="Remove impact on vulnerable persons"
            @click="
              removeItemRow(
                'impact_on_vulnerable_persons',
                impact_on_vulnerable_persons,
                population_type,
                index,
                'name',
                ['impacted_females', 'impacted_males']
              )
            "
            >X</base-button
          >
        </div>
      </div>
    </form>
    <base-button
      size="md"
      type="info"
      data-toggle="tooltip"
      data-placement="top"
      title="Add impact on vulnerable persons"
      class="btn-icon-only rounded-circle noprint"
      @click="
        addItemRow(
          'impact_on_vulnerable_persons',
          impact_on_vulnerable_persons,
          population_type,
          'name'
        )
      "
    >
      <i class="ni ni-fat-add"></i>
    </base-button>
    <hr />
    <div class="row">
      <div class="col-md">
        <h5 slot="header" class="mb-0">
          Total Vulnerable persons affected : {{ tt_vulnerable_persons }}
        </h5>
      </div>
    </div>

    <hr />

    <h2>
      <b class="//alert-suc">What are the major protection concerns</b>
    </h2>

    <form>
      <div
        class="row"
        v-for="(value, index) in protection_concerns"
        v-bind:key="index"
      >
        <div class="col-md">
          <div class="col-md">
            <base-input label="Please choose protection concerns">
              <select
                class="form-control"
                v-model="value.name"
                data-toggle="tooltip"
                data-placement="top"
                title="Please choose a protection concern"
              >
                <option v-for="type in concerns" v-bind:value="type.name">{{
                  type.name
                }}</option>
              </select>
            </base-input>
          </div>
        </div>
        <div class="col-md pt-5">
          <base-button
            size="sm"
            type="warning"
            data-toggle="tooltip"
            data-placement="top"
            title="Remove protection concern"
            v-if="protection_concerns.length > 0"
            class="btn-icon-only rounded-circle noprint"
            @click="
              removeItemRow(
                'protection_concerns',
                protection_concerns,
                concerns,
                index,
                'name',
                []
              )
            "
            >X</base-button
          >
        </div>
      </div>
    </form>
    <base-button
      size="md"
      type="info"
      data-toggle="tooltip"
      data-placement="top"
      title="Add protection service"
      class="btn-icon-only rounded-circle noprint"
      @click="
        addItemRow('protection_concerns', protection_concerns, concerns, 'name')
      "
    >
      <i class="ni ni-fat-add"></i>
    </base-button>

    <hr />

    <h2>
      <b class="//alert-suc">Protection Service Available</b>
    </h2>

    <form>
      <div
        class="row"
        v-for="(value, index) in protection_services"
        v-bind:key="index"
      >
        <div class="col-md">
          <div class="col-md">
            <base-input label="Please choose protection service">
              <select
                class="form-control"
                v-model="value.name"
                data-toggle="tooltip"
                data-placement="top"
                title="Choose protection service"
              >
                <option v-for="type in services" v-bind:value="type.name">{{
                  type.name
                }}</option>
              </select>
            </base-input>
          </div>
        </div>
        <div class="col-md pt-5">
          <base-button
            size="sm"
            type="warning"
            v-if="protection_services.length > 0"
            class="btn-icon-only rounded-circle noprint"
            data-toggle="tooltip"
            data-placement="top"
            title="Remove protection service"
            @click="
              removeItemRow(
                'protection_services',
                protection_services,
                services,
                index,
                'name',
                []
              )
            "
            >X</base-button
          >
        </div>
      </div>
    </form>
    <base-button
      size="md"
      type="info"
      data-toggle="tooltip"
      data-placement="top"
      title="Add protection service"
      class="btn-icon-only rounded-circle noprint"
      @click="
        addItemRow('protection_services', protection_services, services, 'name')
      "
    >
      <i class="ni ni-fat-add"></i>
    </base-button>

    <hr />
    <b>Response Needed for the Protection Cluster</b>
    <hr class="mt-3 mb-3" />
    <base-input label="General Response needed for Protection cluster">
      <textarea
        class="form-control"
        id="exampleFormControlTextarea3"
        data-toggle="tooltip"
        data-placement="top"
        title="Type the general response needed"
        placeholder="Type the response needed for the Protection cluster"
        v-model="response_needed"
        rows="3"
      ></textarea>
    </base-input>
    <base-input label="Urgent Response needed for Protection cluster">
      <textarea
        class="form-control"
        id="exampleFormControlTextarea3"
        data-toggle="tooltip"
        data-placement="top"
        title="Type the urgent response needed"
        placeholder="Type the urgent response needed for the Protection cluster"
        v-model="urgent_response_needed"
        rows="3"
      ></textarea>
    </base-input>
    <base-button
      size="lg"
      data-toggle="tooltip"
      data-placement="top"
      title="Save and goto next section"
      type="primary"
      @click.stop="save"
      class="noprint"
    >
      Save & Continue
    </base-button>
  </div>
</template>
<script src="./index.js"/>

