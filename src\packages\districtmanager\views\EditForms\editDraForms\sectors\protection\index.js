
export default {
  $_veeValidate: {
    validator: "new"
  },
  components: {},
  props: ["protection", "max"],
  data() {
    return {
      maxValue: this.max,
      response_needed: "",
      dataset : {},
      impact_on_vulnerable_persons: [],
      protection_concerns: [],
      protection_services:[],
      impacted_males: 0,
      impacted_females: 0,
      population_type: [
        { name: "Elderly affected" },
        { name: "Children affected" },
        { name: "Chronically ill" },
        { name: "Disabled" },
        { name: "Pregnant women" },
        { name: "Lactating women" }
      ],
      services: [
        { name: "Police services" },
        { name: "Community victim support unit" },
        { name: "Police victim support unit" },
        { name: "One stop center services" },
        { name: "Community policing" },
        { name: "Child protection workers" },
        { name: "Children corners" },
        { name: "Safe spaces" },
        { name: "Phychosocial support services" },
        { name: "CBCC centers" }
      ],
      concerns: [
        { name: "Gender Based Violence" },
        { name: "Risk of Gender based violence" },
        { name: "Missing persons" },
        { name: "Separated/unaccompaned children" },
        { name: "Traumatized children/adults" },
        { name: "Discrimination" },
        { name: "Gender Based Violence" },
        { name: "Risk of Gender based violence" },
        { name: "Missing persons" },
        { name: "Separated/unaccompaned children" },
        { name: "Traumatized children/adults" },
        { name: "Discrimination" },
        { name: "Trafficking" },
        { name: "Risk of Trafficking" },
        { name: "Threat from host community" },
        { name: "Violence between displaced communities" },
        { name: "Loss of legal documents" },
        { name: "Theft of property left behind" },
        { name: "Theft of property at place of displacement" },
        { name: "Corruption/abuse of authority" },
        { name: "Sexual violence/risk thereof" },
        { name: "Physical violence/risk thereof" },
        { name: "Emotional violence/risk thereof" },
        { name: "Neglect of childrn/risk thereof" },
        { name: "Sexual abuse/exploitation by humanitarian actors" },
        { name: "Witchcraft" },
        { name: "Wild animal attacks" },
        { name: "Lighting of camps" },
        { name: "Illiness" },
        { name: "Forced migration"},
        ]
    };
  },
  computed: {
    tt_vulnerable_persons() {
      return (
        parseInt(
          this.impacted_males === undefined || this.impacted_males.length === 0
            ? 0
            : this.impacted_males
        ) +
        parseInt(
          this.impacted_females === undefined ||
            this.impacted_females.length === 0
            ? 0
            : this.impacted_females
        )
      );
    }
  },

  methods: {
    TestPopulationType(population_type, incomingArray) {
      for (let i = 0; i < incomingArray.length; i++) {
        if (
          population_type === "Pregnant women" ||
          population_type === "Lactating women"
        ) {
          incomingArray[i].impacted_males = 0;
          this.init_totals(
            "impacted_males",
            this.impact_on_vulnerable_persons,
            "impacted_males"
          );
        }
      }
    },
    addItemRow(member, array_name, static_data = [], key_value) {
      this.$emit(
        "addItemRow",
        "protection",
        member,
        array_name,
        static_data,
        key_value
      );
    },
    removeItemRow(
      member,
      array_name,
      static_data = [],
      index,
      key_value,
      arrayTotals
    ) {
      this.$emit(
        "removeItemRow",
        "protection",
        member,
        array_name,
        static_data,
        index,
        key_value
      );
      let arrayCopy = [];
      for (let i = 0; i < arrayTotals.length; i++) {
        if (array_name == "") {
          let dynamic_element = arrayTotals[i];
          arrayCopy.push({ dynamic_element: 0 });
          this.init_totals(arrayTotals[i], arrayCopy, arrayTotals[i]);
        } else {
          this.init_totals(arrayTotals[i], array_name, arrayTotals[i]);
        }
      }
    },
    init_totals(key, array_name, member) {
      this[member] = array_name
        .map(function(item) {
          return parseInt(item[key]) ? parseInt(item[key]) : 0;
        })
        .reduce((sum, value) => sum + value, 0);
    },
    save() {
      this.protection.response_needed = this.response_needed
      this.protection.urgent_response_needed = this.urgent_response_needed
      this.protection.impact_on_vulnerable_persons = this.impact_on_vulnerable_persons.filter(
        value => Object.keys(value).length !== 0
      );

      this.protection.protection_concerns = this.protection_concerns.filter(
        value => Object.keys(value).length !== 0
      );

      this.protection.protection_services = this.protection_services.filter(
        value => Object.keys(value).length !== 0
      );

      this.$emit("save", this.protection, "protection");
    },
    autosave() {
      this.protection.response_needed = this.response_needed
     this.protection.urgent_response_needed = this.urgent_response_needed
      this.protection.impact_on_vulnerable_persons = this.impact_on_vulnerable_persons.filter(
        value => Object.keys(value).length !== 0
      );

      this.protection.protection_concerns = this.protection_concerns.filter(
        value => Object.keys(value).length !== 0
      );

      this.protection.protection_services = this.protection_services.filter(
        value => Object.keys(value).length !== 0
      );

      this.$emit("autosave", this.protection, "protection");
    }
  },

  beforeMount() {


    this.protection =
      typeof this.protection !== "undefined" ? this.protection : {};

 setInterval(this.autosave, 1200)


      let dataset = JSON.parse(locallocalStorage.getItem('protection').data);


    this.impact_on_vulnerable_persons =
      typeof dataset.impact_on_vulnerable_persons === "undefined"
        ? this.impact_on_vulnerable_persons
        : dataset.impact_on_vulnerable_persons;

         this.protection_concerns =
      typeof dataset.protection_concerns === "undefined"
        ? this.protection_concerns
        : dataset.protection_concerns;

     this.protection_services =
      typeof dataset.protection_services === "undefined"
        ? this.protection_services
        : dataset.protection_services;

    this.response_needed =
        typeof dataset.response_needed === "undefined"
          ? this.response_needed
          : dataset.response_needed;

      this.urgent_response_needed =
        typeof dataset.urgent_response_needed === "undefined"
          ? this.urgent_response_needed
          : dataset.urgent_response_needed;


    this.init_totals(
      "impacted_males",
      this.impact_on_vulnerable_persons,
      "impacted_males"
    );
    this.init_totals(
      "impacted_females",
      this.impact_on_vulnerable_persons,
      "impacted_females"
    );
  }
};
